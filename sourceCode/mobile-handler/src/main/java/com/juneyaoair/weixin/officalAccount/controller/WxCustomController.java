package com.juneyaoair.weixin.officalAccount.controller;

import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.member.SalutationEnum;
import com.juneyaoair.appenum.member.ValidateModeEnum;
import com.juneyaoair.baseclass.common.base.ScoreCrmRule;
import com.juneyaoair.baseclass.common.response.BaseResponse;
import com.juneyaoair.baseclass.request.qiYeWeiXin.WxError;
import com.juneyaoair.baseclass.response.crm.MemberLoginResponse;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.service.challenge.mongo.IChallengeMongoService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.service.IMemberMileageAggrService;
import com.juneyaoair.mobile.handler.util.OrderUtil;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.*;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtCrmMileageRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.PtRegisterRequest;
import com.juneyaoair.thirdentity.member.request.PtSendMileage;
import com.juneyaoair.thirdentity.member.response.MemberBasicInfoSoaModel;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.member.response.PtRegisterResponse;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import com.juneyaoair.weixin.bean.scene.SceneQrcode;
import com.juneyaoair.weixin.bean.user.WxUsersInfoDetails;
import com.juneyaoair.weixin.bean.user.paramWxUsers;
import com.juneyaoair.weixin.binding.FastRegBindReq;
import com.juneyaoair.weixin.binding.FastRegBindResp;
import com.juneyaoair.weixin.dao.scene.IWxSceneQrcodeDao;
import com.juneyaoair.weixin.dao.user.IWxUserDao;

import com.juneyaoair.weixin.officalAccount.service.MemberbindService;
import com.juneyaoair.weixin.user.UserBindingRequest;
import com.juneyaoair.weixin.user.UserQueryReq;
import com.juneyaoair.weixin.user.UserQueryResp;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * Created by zc on 2017/4/12.
 */
@Controller
@RequestMapping("/wxCustom")
public class WxCustomController extends BaseController {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IWxUserDao wxUserDao;
    @Autowired
    private MemberbindService memberbindService;
    @Autowired
    private IWxSceneQrcodeDao wxSceneQrcodeDao;
    @Autowired
    private IMemberMileageAggrService memberMileageService;

    @Autowired
    private CrmWSClient crmWSClient;
    @Autowired
    private IChallengeMongoService challengeMongoService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private HandConfig handConfig;


    @RequestMapping(path = "/unbinding.do",method = POST)
    @ResponseBody
    public WxError removeMember(@RequestBody Map<String, String> map) {
        log.info("要解除会员绑定的openId为：{}" , map.get("openId"));
        String openId = map.get("openId");
        WxError result = new WxError();
        if (StringUtil.isNullOrEmpty(openId)) {
            result.setErrmsg("页面异常，请重新打开网页");
            result.setErrcode(WSEnum.ERROR.getResultCode());
            return result;
        }
        try {
            WxUsersInfoDetails wxUsersInfoDetails = new WxUsersInfoDetails();
            wxUsersInfoDetails.setIsBinding("F");
            wxUsersInfoDetails.setFfpCardNo("");
            wxUsersInfoDetails.setFfpId("");
            wxUsersInfoDetails.setOpenId(openId);
            wxUsersInfoDetails.setType(0);
            wxUserDao.updateWxUserInfo(wxUsersInfoDetails);
            result.setErrcode(WSEnum.SUCCESS.getResultCode());
            log.info("openId为{}的会员解除微信绑定成功" , openId);
        } catch (Exception e) {
            log.error("解除绑定会员异常" + e.getMessage(), e);
            result.setErrmsg("解除绑定会员异常");
            result.setErrcode(WSEnum.ERROR.getResultCode());
            return result;
        }
        return result;
    }
    @ApiOperation(value = "查询会员绑定记录")
    @InterfaceLog
    @ResponseBody
    @RequestMapping(path = "/queryIsBinding.do",method = POST)
    public UserQueryResp queryIsBinding(@RequestBody UserQueryReq userQueryReq) {
        UserQueryResp response = new UserQueryResp();
        String openId = userQueryReq.getOpenId();
        String ffpId = userQueryReq.getFfpId();
        String ffpCardNo = userQueryReq.getFfpCardNo();
        String channelCode = userQueryReq.getChannelNo();
        if (StringUtils.isBlank(channelCode)) {
            response.setErrcode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrmsg("渠道号不能为空");
            return response;
        }
        if (ffpId == null && StringUtils.isBlank(ffpCardNo) && StringUtils.isBlank(openId)) {
            response.setErrcode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrmsg("查询条件不能为空");
            return response;
        }
        if ((ffpId == null && !StringUtils.isBlank(ffpCardNo))
                || (ffpId != null && StringUtils.isBlank(ffpCardNo))) {
            response.setErrcode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrmsg("会员ID和卡号必须同时存在");
            return response;
        }
        try {
            log.debug("【大的】查询绑定开始：{}" , JsonUtil.objectToJson(userQueryReq));
            paramWxUsers paramWxUsers = new paramWxUsers();
            paramWxUsers.setOpenId(openId);
            paramWxUsers.setFfpId(ffpId);
            paramWxUsers.setFfpCardNo(ffpCardNo);
            paramWxUsers.setType(0);
            WxUsersInfoDetails wxUsersInfoDetails = wxUserDao.queryWxUser(paramWxUsers);
            if (wxUsersInfoDetails != null) {
                BeanUtils.copyNotNullProperties(wxUsersInfoDetails, response);
            }
            log.debug("【WxCustomController】查询绑定结束：{}",JsonUtil.objectToJson(response));
            response.setErrcode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            log.error("验证绑定异常" + e.getMessage(), e);
            response.setErrcode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrmsg("验证绑定异常");
            return response;
        }
        return response;
    }

    @RequestMapping(path="/memberBinding.do",method = POST,produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public UserQueryResp memberBinding(@RequestBody @Validated UserBindingRequest userBindingRequest, BindingResult bindingResult, HttpServletRequest request) {
        UserQueryResp response = new UserQueryResp();
        String ip = getClientIP(request);
        if (bindingResult.hasErrors()) {
            response.setErrcode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrmsg(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return response;
        }
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String channleNo = userBindingRequest.getChannelNo();
        if(HandlerConstants.MWEB_CHANNEL_CODE.equals(channleNo)){
            channleNo = HandlerConstants.M_CHANNEL_CODE;
        }
        String openId = userBindingRequest.getOpenId();
        String ffpId = userBindingRequest.getFfpId();
        String ffpCardNo = userBindingRequest.getFfpCardNo();
        boolean flag = this.checkKeyInfo(ffpId, userBindingRequest.getLoginKeyInfo(), channleNo);
        if (!flag) {
            response.setErrcode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrmsg("客户信息验证失败");
            return response;
        }
        String[] result = memberbindService.bindingMember(openId, String.valueOf(ffpId), ffpCardNo, channleNo, this.getCilentPwd(channleNo));
        if (result[0].equals(WSEnum.SUCCESS.getResultCode())) {
            paramWxUsers paramWxUsers = new paramWxUsers();
            paramWxUsers.setOpenId(openId);
            paramWxUsers.setType(0);
            WxUsersInfoDetails ffpUserDTO = wxUserDao.queryWxUser(paramWxUsers);
            BeanUtils.copyNotNullProperties(ffpUserDTO, response);
            try {
                String channelCode = HandlerConstants.W_CHANNEL_CODE;
                Map<String, String> activityMap = new HashMap<>();
                //首次绑定送积分,B内部使用，首次绑定
                if (ffpUserDTO.getIsBinding().equals("B")) {
                    activityMap.put("18scbdwx", "首次绑定送10点积分");
                    if (!StringUtil.isNullOrEmpty(ffpUserDTO.getBindingSource())) {
                        SceneQrcode mpSceneQrcode = wxSceneQrcodeDao.querySceneQrcodeBySid(ffpUserDTO.getBindingSource());
                        if (mpSceneQrcode.getName().indexOf("汉兰科贸") != -1) {
                            activityMap.put("HLKMWXTG", "汉兰科贸微信发展");
                        }
                    }
                    //更新状态为T
                    WxUsersInfoDetails wxUsersInfoDetails = new WxUsersInfoDetails();
                    wxUsersInfoDetails.setIsBinding("T");
                    wxUsersInfoDetails.setOpenId(openId);
                    wxUsersInfoDetails.setType(0);
                    wxUserDao.updateWxUserInfo(wxUsersInfoDetails);
                    log.info("首次绑定完成更新绑定状态成功");
                    //获取积分规则，生成请求参数
                    if (CollectionUtils.isNotEmpty(handConfig.getCrmRuleList())) {
                        List<ScoreCrmRule> scoreCrmRuleList = handConfig.getCrmRuleList().stream().filter(scoreCrmRule -> "firstBind".equals(scoreCrmRule.getActivityCode())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(scoreCrmRuleList)) {
                            log.info("firstBind未查询到符合条件的积分规则");
                        } else {
                            ScoreCrmRule scoreCrmRule = scoreCrmRuleList.get(0);
                            if (scoreCrmRule.getIntegral() > 0) {
                                Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                                PtCrmMileageRequest<PtSendMileage> ptCrmMileageRequest = new PtCrmMileageRequest<>(channelCode, getClientPwd(channelCode), HandlerConstants.CRM_API_VERSION);
                                PtSendMileage ptSendMileage = new PtSendMileage(ffpCardNo, scoreCrmRule.getIntegral(), OrderUtil.orderNo(headChannelCode), scoreCrmRule.getActivityRuleId(), scoreCrmRule.getPartnerCode());
                                ptCrmMileageRequest.setData(ptSendMileage);
                                memberMileageService.sendMileage(ptCrmMileageRequest, headMap);
                            }
                        }
                    }
                }
                 response.setErrcode(result[0]);
            } catch (Exception e) {
                log.info("绑定送积分失败：用户{}，原因:" ,ffpId,e);
            }

        } else {
            response.setErrcode(result[0]);
            response.setErrmsg(result[1]);
        }
        return response;
    }

    @NotDuplicate
    @RequestMapping(path = "/fastRegBind.do",method = POST,produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @InterfaceLog
    public FastRegBindResp fastRegBind(@RequestBody @Validated FastRegBindReq req,BindingResult bindingResult, HttpServletRequest request, HttpServletResponse httpServletResponse) {
        FastRegBindResp response = new FastRegBindResp();
        String headChannelCode =  request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String ip = getClientIP(request);
        if (bindingResult.hasErrors()) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return response;
        }
        //验证码验证
        String regCacheKey = "SMS:" + req.getMobile() + req.getVerType();
        String verCode = apiRedisService.getData(regCacheKey);
        if(StringUtil.isNullOrEmpty(verCode)){
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo("验证码已失效");
            return response;
        }else{
            if(!StringUtil.isNullOrEmpty(verCode)&&!verCode.equalsIgnoreCase(req.getVerCode())){
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo("验证码错误");
                return response;
            }
            apiRedisService.removeData(regCacheKey);//清除已验证的验证码
        }
        paramWxUsers paramWxUsers = new paramWxUsers();
        paramWxUsers.setOpenId(req.getOpenId());
        paramWxUsers.setType(0);
        WxUsersInfoDetails ffpUserDTO = wxUserDao.queryWxUser(paramWxUsers);
        if (ffpUserDTO == null) {
            try {
                log.info("快速注册绑定 数据库中没有微信信息，开始去根据openId获取微信信息，openid:{}", req.getOpenId());
                String url = HandlerConstants.WEIXIN_MEMBER_SAVE + HandlerConstants.GET_USERINFO_FOR_OPENID_NOTFOUND + "?openid=" + req.getOpenId();
                String ret = this.doGetJson(url, new HashMap<>());
                log.info("快速注册绑定 调取微信获取用户信息结果：{}，openid:{}", ret, req.getOpenId());
                BaseResponse resp = (BaseResponse) JsonUtil.jsonToBean(ret, BaseResponse.class);
                if (!resp.getResultCode().equals("10001")) {
                    log.error("快速注册绑定 微信信息获取失败请重新关注,openid:{}", req.getOpenId());
                    response.setErrorInfo("微信信息获取失败，请重新关注");
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    return response;
                }
                String details = JsonUtil.objectToJson(resp.getObjData());
                ffpUserDTO = (WxUsersInfoDetails) JsonUtil.jsonToBean(details, WxUsersInfoDetails.class);
            } catch (Exception e) {
                log.error("{}快速注册绑定出错 获取微信信息出错：", MdcUtils.getRequestId(),e);
                response.setErrorInfo("快速注册绑定出错");
                response.setResultCode(WSEnum.ERROR.getResultCode());
                return response;
            }
        } else if ("T".equals(ffpUserDTO.getIsBinding()) || "B".equals(ffpUserDTO.getIsBinding())) {
            log.info("{}微信号已经绑定,openid:{}", MdcUtils.getRequestId(),req.getOpenId());
            response.setErrorInfo("微信号已经绑定");
            response.setResultCode(WSEnum.REPEAT_UBMISSION.getResultCode());
            return response;
        }
        //根据手机号查询会员库
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest>  ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(req.getMobile(),request, ChannelCodeEnum.WEIXIN.getChannelCode(), items);
        PtCRMResponse<PtMemberDetail> ptCRMResponseDetail = memberService.memberDetail(ptApiCRMRequest);
        String ffpId = "";
        String ffpCardNo = "";
        if(ptCRMResponseDetail.getCode() == 0){
            MemberBasicInfoSoaModel memberBasicInfoSoaModel = ptCRMResponseDetail.getData().getBasicInfo();
            com.juneyaoair.weixin.bean.user.paramWxUsers paramWxUsers1 = new paramWxUsers();
            if (null != memberBasicInfoSoaModel && !String.valueOf(memberBasicInfoSoaModel.getMemberId()).isEmpty() && !memberBasicInfoSoaModel.getCardNO().isEmpty()) {
                paramWxUsers1.setFfpId(String.valueOf(memberBasicInfoSoaModel.getMemberId()));
                paramWxUsers1.setFfpCardNo(memberBasicInfoSoaModel.getCardNO());
                ffpId = String.valueOf(memberBasicInfoSoaModel.getMemberId());
                ffpCardNo = memberBasicInfoSoaModel.getCardNO();
            }else{
                throw new ServiceException("会员信息不完整");
            }
            paramWxUsers1.setType(0);
            WxUsersInfoDetails wxUsersInfoDetails = wxUserDao.queryWxUser(paramWxUsers1);
            if (null != wxUsersInfoDetails) {
                log.info("{}该会员已绑定微信，会员号：{},openId:{}" , MdcUtils.getRequestId(),wxUsersInfoDetails.getFfpId() , wxUsersInfoDetails.getOpenId());
                response.setErrorInfo("该会员已绑定微信");
                response.setResultCode(WSEnum.REPEAT_UBMISSION.getResultCode());
                return response;
            }
        } else if (ptCRMResponseDetail.getCode() == 100002) {
            //会员手机号对应账户不存在
        }else{
            response.setErrorInfo("查询详情发生异常");
            response.setResultCode(WSEnum.ERROR.getResultCode());
            return response;
        }
        //处理输护照名字大小写问题
        String dealName = req.getPassengerNm();
        if ("PP".equals(req.getCertType()) && StringUtils.isNotBlank(dealName)) {
            dealName = dealPsgPPName(dealName);
        }
        log.info("对输入的名字处理，处理前：{},处理后：{}" ,req.getPassengerNm() ,dealName);
        String realChannel=req.getChannelCode();
        if ("MWEB".equals(req.getChannelCode())) {
            req.setChannelCode("MOBILE");
        }
        String channelCode = req.getChannelCode();
        String certTypeReq = req.getCertType();
        CertificateType certificateType = null;
        log.info("快速绑定查detr 请求req:{}" , JsonUtil.objectToJson(req));
        if (certTypeReq.equals("NI")) {
            certificateType = CertificateType.IDCard;
        } else if (certTypeReq.equals("PP")) {
            certificateType = CertificateType.Passport;
        }
        String certificateNo = req.getCertNo();
        Map<String, String> map = new HashMap<>();
        String channelPwd = "";
        if (channelCode.equals("MOBILE")) {
            channelPwd = HandlerConstants.M_CLIENT_PWD;
        } else {
            channelPwd = HandlerConstants.W_CLIENT_PWD;
        }
        //卡号为空说明手机号未注册过执行注册过程
        if(StringUtils.isBlank(ffpCardNo)){
            log.info("{}fastRegBind 开始注册绑定操作，memberFastRegistBind开始时间：{}。channelCode:{},channelPwd:{},passengerNm:{},certType:{},certNo:{},mobile:{},openId:{}" ,MdcUtils.getRequestId(),DateUtils.getDateStringAllDate(new Date()) , channelCode , channelPwd ,req.getPassengerNm() ,certificateType , certificateNo , req.getMobile() ,req.getOpenId());
            PtApiCRMRequest<PtRegisterRequest> ptApiRequest = buildRegisterReq(request, req, channelCode, response);
            if (ptApiRequest == null && !WSEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
                return response;
            }
            PtCRMResponse<PtRegisterResponse> ptCRMResponse = memberService.register(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                if (ptCRMResponse.getCode() == 0) {  //注册成功
                    //手机号之前未注册会员，快速注册绑定成功
                    ffpId = String.valueOf(ptCRMResponse.getData().getMemberId());
                    ffpCardNo = ptCRMResponse.getData().getMemberCardNo();
                } else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setErrorInfo(ptCRMResponse.getMsg());
                    log.debug("绑定失败,快速注册绑定接口返回信息：{}" , JsonUtil.objectToJson(ptCRMResponse));
                    return response;
                }
            } else {
                if (StringUtils.isNotBlank(ptCRMResponse.getInnerMsg()) && ptCRMResponse.getInnerMsg().contains("联系方式命中黑名单")) {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setErrorInfo(WSEnum.BLACK_USER_REGISTER.getResultInfo());
                    return response;
                } else if(ptCRMResponse.getCode() == 102010){
                    log.debug("{102010}微信用户绑定成功,OPEN_ID= {} ,会员卡号={}" ,req.getOpenId() , ffpCardNo);
                    response.setFfpCardNo(ffpCardNo);
                    response.setFfpId(ffpId);
                    String key = HandlerConstants.W_CHANNEL_CODE.equals("MWEB") ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                    String loginKey = EncoderHandler.encode("MD5", ffpId + key).toUpperCase();
                    response.setLoginKeyInfo(loginKey);
                    response.setResultCode("20001");
                    response.setErrorInfo("您已经注册过会员");
                }else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setErrorInfo(ptCRMResponse.getMsg());
                    return response;
                }
            }
        }
        String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
        try {
            if ("F".equals(ffpUserDTO.getIsBinding()) && StringUtil.isNullOrEmpty(ffpUserDTO.getBindingDate()) && StringUtil.isNullOrEmpty(ffpUserDTO.getBindingSource())) {
                //绑定送10积分
                map.put("18scbdwx", "首次绑定送10点积分");
                if (!StringUtil.isNullOrEmpty(req.getQrsource())) {
                    SceneQrcode mpSceneQrcode = wxSceneQrcodeDao.querySceneQrcodeBySid(swap(req.getQrsource()));
                    if (mpSceneQrcode != null && mpSceneQrcode.getName().indexOf("汉兰科贸") != -1) {
                        map.put("HLKMWXTG", "汉兰科贸微信发展");
                    }
                }
            }
            //更新
            ffpUserDTO.setFfpId(ffpId);
            ffpUserDTO.setFfpCardNo(ffpCardNo);
            ffpUserDTO.setIsBinding("T");
            ffpUserDTO.setBindingDate(DateUtils.getDateStringAllDate(new Date()));
            String qrSource = req.getQrsource();
            ffpUserDTO.setBindingSource(swap(qrSource));
            ffpUserDTO.setType(0);
            wxUserDao.updateWxUserInfo(ffpUserDTO);
            //获取积分规则，生成请求参数
            if (CollectionUtils.isNotEmpty(handConfig.getCrmRuleList())) {
                List<ScoreCrmRule> scoreCrmRuleList = handConfig.getCrmRuleList().stream().filter(scoreCrmRule -> "firstBind".equals(scoreCrmRule.getActivityCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scoreCrmRuleList)) {
                    log.info("firstBind未查询到符合条件的积分规则");
                } else {
                    ScoreCrmRule scoreCrmRule = scoreCrmRuleList.get(0);
                    if (scoreCrmRule.getIntegral() > 0) {
                        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                        PtCrmMileageRequest<PtSendMileage> ptCrmMileageRequest = new PtCrmMileageRequest<>(channelCode, getClientPwd(channelCode), HandlerConstants.CRM_API_VERSION);
                        PtSendMileage ptSendMileage = new PtSendMileage(ffpCardNo, scoreCrmRule.getIntegral(), OrderUtil.orderNo(headChannelCode), scoreCrmRule.getActivityRuleId(), scoreCrmRule.getPartnerCode());
                        ptCrmMileageRequest.setData(ptSendMileage);
                        memberMileageService.sendMileage(ptCrmMileageRequest, headMap);
                    }
                }
            }
            String loginKey = EncoderHandler.encode("MD5", ffpUserDTO.getFfpId() + key).toUpperCase();
            if (StringUtil.isNullOrEmpty(response.getResultCode())) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
            }
            response.setFfpCardNo(ffpUserDTO.getFfpCardNo());
            response.setFfpId(ffpUserDTO.getFfpId());
            response.setNickName(ffpUserDTO.getNickName());
            response.setLoginKeyInfo(loginKey);
        } catch (Exception e) {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("绑定失败，写入异常");
            return response;
        }
        log.info("{},fastRegBind 结束注册绑定操作，memberFastRegistBind结束时间：{}。返回结果：{}" , MdcUtils.getRequestId(),DateUtils.getDateStringAllDate(new Date()), JsonUtil.objectToJson(response));
        Map maps = new HashMap<>();
        String loginKey = EncoderHandler.encode("MD5", ffpUserDTO.getFfpId() + key).toUpperCase();
        MemberLoginResponse memberLoginResponse =new MemberLoginResponse();
        memberLoginResponse.setId(Long.parseLong(ffpUserDTO.getFfpId()));
        memberLoginResponse.setMemberID(ffpUserDTO.getFfpCardNo());
        memberLoginResponse.setLoginKeyInfo(loginKey);
        response.setMemberLoginResponse(memberLoginResponse);
        maps.put("memberInfo", JsonUtil.objectToJson(memberLoginResponse));
        maps.put("memberCardNo",ffpUserDTO.getFfpCardNo());
        maps.put("channelCode",realChannel);
        String token = TokenUtils.signAndCache(maps,ffpUserDTO.getFfpCardNo());
        httpServletResponse.setHeader("token",token);
        httpServletResponse.setHeader("Access-Control-Expose-Headers","token");
        return response;
    }

    /**
     * 福卡注册请求
     *
     * @param request
     * @param
     * @return
     */
    private PtApiCRMRequest<PtRegisterRequest> buildRegisterReq(HttpServletRequest request, FastRegBindReq source, String channelCode ,FastRegBindResp resp) {
        PtRegisterRequest target = new PtRegisterRequest();
        BeanUtils.copyProperties(source, target);
        target.setMobile(PhoneUtil.formatMobile("86", source.getMobile()));
        CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkShowCode(source.getCertType());
        if(certificateTypeEnum != null){
            String cLastName=source.getCLastName(); //中文姓
            String cFirstName=source.getCFirstName(); //中文名
            String eLastName=source.getELastName(); //英文姓
            String eFirstName=source.getEFirstName(); //英文名
            target.setCLastName(cLastName);
            target.setCFirstName(cFirstName);
            target.setELastName(eLastName);
            target.setEFirstName(eFirstName);
            //身份证类型的根据证件判断下基本信息
            if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certificateTypeEnum.getShowCode())) {
                Pattern pattern = Pattern.compile(PatternCommon.ID_NUMBER);
                Matcher matcher = pattern.matcher(source.getCertNo());
                if (matcher.matches()) {
                    String birthDate = CertUtil.certNoToDate(source.getCertNo());
                    if (StringUtil.isNullOrEmpty(birthDate)) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setErrorInfo("请输入正确的证件号！");
                        return null;
                    }
                    Date birDate = DateUtils.toDate(birthDate, DateUtils.YYYY_MM_DD_PATTERN);
                    target.setBirthday(birDate.getTime());
                    String sex = CertUtil.checkSex(source.getCertNo());
                    target.setSex(sex);
                    target.setSalutationCode("M".equals(sex) ? SalutationEnum.MR.geteName() : SalutationEnum.MS.geteName());
                    if (StringUtil.isNullOrEmpty(cLastName)) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setErrorInfo("请输入中文姓！");
                        return null;
                    }
                    if (StringUtil.isNullOrEmpty(cFirstName)) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setErrorInfo("请输入中文名！");
                        return null;
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setErrorInfo("请输入正确的证件号！");
                    return null;
                }
            } else if(CertificateTypeEnum.PASSPORT.getShowCode().equals(certificateTypeEnum.getShowCode())) {
                if (StringUtil.isNullOrEmpty(eLastName)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setErrorInfo("请输入英文姓！");
                    return null;
                }
                if (StringUtil.isNullOrEmpty(eFirstName)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setErrorInfo("请输入英文名！");
                    return null;
                }
                //护照性别未知
                target.setSex("U");
            } else{
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("请输入正确的证件类型！");
                return null;
            }
            target.setCertificateType(certificateTypeEnum.geteName());
        }else{
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("请输入正确的证件类型！");
            return null;
        }
        target.setCertificateNumber(source.getCertNo());
        target.setReferrer(source.getQrsource());
        target.setSubmitDate(System.currentTimeMillis());
        target.setValidateMode(ValidateModeEnum.NO_VALID.validMethod);
        PtApiCRMRequest<PtRegisterRequest> ptApiCRMRequest = new PtApiCRMRequest();
        Header header = buildHeader(request, "-1", "");
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        ptApiCRMRequest.setData(target);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        return ptApiCRMRequest;
    }

    protected Header buildHeader(HttpServletRequest request, String ffpId, String token) {
        Header header = new Header();
        header.setClientIP(this.getClientIP(request));
        header.setClientVersion("");
        if (StringUtil.isNullOrEmpty(ffpId)) {
            ffpId = "-1";
        }
        header.setMemberId(Long.valueOf(ffpId));
        header.setToken(token);
        header.setTimestamp(System.currentTimeMillis());
        return header;
    }

    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    //因为场景二维码的编号输入了汉字，临时替换处理下
    private String swap(String sid) {
        String result = sid;
        if (sid.equals("yanguorong")) {//1
            result = "闫国荣";
        } else if (sid.equals("yulili")) {//2
            result = "于莉莉";
        } else if (sid.equals("wangnianjie")) {//3
            result = "王年节";
        } else if (sid.equals("tulingling")) {//4
            result = "涂玲玲";
        } else if (sid.equals("shiweiwei")) {//5
            result = "施巍巍";
        } else if (sid.equals("maowenjuan")) {//6
            result = "毛文娟";
        } else if (sid.equals("mayuanyuan")) {//7
            result = "马园园";
        } else if (sid.equals("lvyahong")) {//8
            result = "吕亚红";
        } else if (sid.equals("liuhui")) {//9
            result = "刘慧";
        } else if (sid.equals("liubaojun")) {//10
            result = "刘宝骏";
        } else if (sid.equals("liyunyun")) {//11
            result = "李云云";
        } else if (sid.equals("liwenya")) {//12
            result = "李文亚";
        } else if (sid.equals("jiangwenxia")) {//13
            result = "江文霞";
        } else if (sid.equals("jiangmanli")) {//14
            result = "江曼丽";
        } else if (sid.equals("gaotianping")) {//15
            result = "高天萍";
        } else if (sid.equals("dongfangjun")) {//16
            result = "董方君";
        } else if (sid.equals("chenwanru")) {// 17
            result = "陈婉如";
        } else if (sid.equals("HANLAN")) {// 18
            result = "HANLAN";
        } else if (sid.equals("chenghuan")) {// 19
            result = "陈欢";
        } else if (sid.equals("chengyujie")) {// 20
            result = "程宇杰";
        } else if (sid.equals("hanhuimin")) {// 21
            result = "韩慧敏";
        } else if (sid.equals("liurong")) {// 22
            result = "刘蓉";
        } else if (sid.equals("wangyuan")) {// 23
            result = "汪媛";
        } else if (sid.equals("yezuxiang")) {// 24
            result = "叶祖香";
        } else if (sid.equals("hanhuiminnew")) {// 26
            result = "韩慧敏new";
        } else if (sid.equals("caotingting")) {// 27
            result = "曹婷婷";
        } else if (sid.equals("huyanqi")) {// 28
            result = "胡颜琦";
        } else if (sid.equals("liuzhiya")) {// 29
            result = "刘志亚";
        } else if (sid.equals("xutingting")) {// 30
            result = "许婷婷";
        } else if (sid.equals("zoumengmei")) {// 31
            result = "邹梦梅";
        } else if (sid.equals("yaolu-zazhi")) {
            result = "姚璐-杂志";
        }

        return result;
    }

    //绑定送积分
    private void boundSendPoints(Map<String, String> map, String channelCode, String ffpId, String ffpCardNo, String describe) {
        String bundActivityCode = "";
        String bindingLog = "";
        for (Map.Entry<String, String> key : map.entrySet()) {
            bundActivityCode = key.getKey();
            bindingLog = map.get(key.getKey());
            log.debug( "{}{}送积分开始，会员ID：{},会员卡号：{},活动码：{}",describe , bindingLog , ffpId , ffpCardNo ,bundActivityCode);
            PointMarketActivityResponseForClient crmResp = crmWSClient.pointMarketActivity(channelCode, this.getCilentPwd(channelCode), ffpId, ffpCardNo, bundActivityCode, bindingLog);
            log.debug("会员送积分返回：{}" , JsonUtil.objectToJson(crmResp));
            if (crmResp.getMessageHeader().getErrorCode().equals("S000")) {
                log.info( "{}成功：用户{}", bindingLog , ffpId);
                //成功
            } else {

                log.error( "{}失败：用户{}失败原因：{}", bindingLog, ffpId , crmResp.getMessageHeader().getDescription());
            }
        }
    }

    //对旅客护照有MS，MR处理掉
    private String dealPsgPPName(String originalName) {
        String nameAfterDeal = "";
        if (!StringUtil.isNullOrEmpty(originalName)) {
            nameAfterDeal = StringUtils.upperCase(originalName);
            if (nameAfterDeal.endsWith(" MS") || nameAfterDeal.endsWith(" MR")) {
                nameAfterDeal = nameAfterDeal.substring(0, nameAfterDeal.lastIndexOf(' '));
            }
        }
        return nameAfterDeal;
    }

}
