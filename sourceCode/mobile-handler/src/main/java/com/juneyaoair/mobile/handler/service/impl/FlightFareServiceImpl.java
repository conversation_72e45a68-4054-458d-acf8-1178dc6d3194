package com.juneyaoair.mobile.handler.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.flight.response.NearFlightRecommend;
import com.juneyaoair.baseclass.request.av.NearbyFlightsCheckReq;
import com.juneyaoair.baseclass.request.av.SoldOutRecommendationReq;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IFlightFareService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.commonflycity.CommonFlyCityBO;
import com.juneyaoair.thirdentity.commonflycity.CommonFlyCityDetailDTO;
import com.juneyaoair.thirdentity.commonflycity.CommonFlyCityReqDTO;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.json.ResultJson;
import com.juneyaoair.utils.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class FlightFareServiceImpl implements IFlightFareService {


    @Autowired
    private HandConfig handConfig;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private IBasicService basicService;

    // 邻近航线
    private final static String SOLD_OUT_PAGE_MODULE_NEARBY = "1";

    // 热门推荐
    private final static String SOLD_OUT_PAGE_MODULE_RECOMMENDATION = "2";



    /**
     * 查询 共飞城市"邻近航线、热门推荐“类型数据
     * <p>
     * "邻近航线":  到达地相同，出发地在【邻近航线】共飞组内 （出发地邻近）
     * <p>
     * "热门推荐":  出发地相同，目的地在【热门推荐】共飞组内
     *
     * @param request
     * @return
     */
    @Override
    public List<NearFlightRecommend> getCFCAirRoute(SoldOutRecommendationReq request) {
        try {
            List<NearFlightRecommend> cfcModuleList = new ArrayList<>();
            if("Y".equals(handConfig.getUseFlightRecommend())){
                // 邻近航线模块还是热门推荐模块
                NearFlightRecommend cfcNearBy = getCFCModuleByRuleType(request, SOLD_OUT_PAGE_MODULE_NEARBY);
                NearFlightRecommend cfcRecommendation = getCFCModuleByRuleType(request, SOLD_OUT_PAGE_MODULE_RECOMMENDATION);
                // 合并数据
                if (cfcNearBy != null) {
                    cfcModuleList.add(cfcNearBy);
                }
                if (cfcRecommendation != null) {
                    cfcModuleList.add(cfcRecommendation);
                }
            }
            return cfcModuleList;
        } catch (Exception e) {
            log.error("queryPopularRecommendationFlight error", e);
        }
        return new ArrayList<>();
    }

    @Override
    public BaseResp nearbyFlightsCheck(NearbyFlightsCheckReq nearbyFlightsCheckReqBaseReq) {
        BaseResp baseResp = new BaseResp();
        AirPortInfoDto depAirport = localCacheService.getLocalAirport(nearbyFlightsCheckReqBaseReq.getDepAirport());
        String depZone = depAirport.getCityTimeZone();
        String depTime= nearbyFlightsCheckReqBaseReq.getDepDateTime();
        if ((!StringUtil.isNullOrEmpty(depZone)) &&
                (!StringUtil.isNullOrEmpty(depTime))) {
            //添加夏、冬令时处理
            if (!"8".equals(depZone)) {
                depZone = FlightUtil.convertSummerOrWinterTime(depZone, depTime, depAirport);
            }
        }
        String curDate = DateUtils.getCurrentDateTimeStr();
        if (DateUtils.filterDateTimeWithZone(curDate, "8", depTime,depZone, handConfig.getCloseSellTime())) {
            baseResp.setResultInfo(WSEnum.ALERT_ERROR_NEARBY_FLIGHTS_60_MESSAGE.getResultInfo());
            baseResp.setResultCode(WSEnum.ALERT_ERROR_NEARBY_FLIGHTS_60_MESSAGE.getResultCode());
        }else if (DateUtils.filterDateTimeWithZone(curDate, "8", depTime,depZone, handConfig.getCloseSellPromptTime())){
            baseResp.setResultInfo(WSEnum.ALERT_ERROR_NEARBY_FLIGHTS_65_MESSAGE.getResultInfo());
            baseResp.setResultCode(WSEnum.ALERT_ERROR_NEARBY_FLIGHTS_65_MESSAGE.getResultCode());
        }else {
            baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        }
        return baseResp;
    }

    private NearFlightRecommend getCFCModuleByRuleType(SoldOutRecommendationReq request, String ruleType) {

        //微信，微信小程序渠道国际低价日历  转MOBILE
        String  channelCode = basicService.getChannelCode(request.getChannelCode(),request.getTripType());
        request.setChannelCode(channelCode);
        
        List<CommonFlyCityDetailDTO> nearByRouteOrRecommendation = new ArrayList<>();
        String key;
        boolean isSetToRedis = true;
        boolean isNearby = SOLD_OUT_PAGE_MODULE_NEARBY.equals(ruleType);
        //
        // 从redis中获取,如果没有则调用接口
        if (isNearby) {
            key = RedisKeyConfig.COMMON_FLYING_CITY + ":" + request.getSendCode() + ":" + request.getTripType();
        } else {
            key = RedisKeyConfig.COMMON_FLYING_CITY + ":" + request.getArrCode() + ":" + request.getTripType() + ":" + "2";
        }
        //
        String redisResult = apiRedisService.getData(key);
        if (StringUtil.isNullOrEmpty(redisResult)) {
            CommonFlyCityReqDTO cfcReq = new CommonFlyCityReqDTO();
            if (isNearby) {
                cfcReq.setRuleType(SOLD_OUT_PAGE_MODULE_NEARBY);
                cfcReq.setCityCode(request.getSendCode());
            } else {
                cfcReq.setRuleType(SOLD_OUT_PAGE_MODULE_RECOMMENDATION);
                cfcReq.setCityCode(request.getArrCode());
            }
            ResultJson<List<CommonFlyCityDetailDTO>> nearByRouteOrRecommendationResult = this.fetchCommFlyCity(cfcReq);
            if (nearByRouteOrRecommendationResult != null) {
                nearByRouteOrRecommendation = nearByRouteOrRecommendationResult.getResultObject();
                isSetToRedis = true;
                redisResult = new Gson().toJson(nearByRouteOrRecommendationResult);
            }

        } else {
            ResultJson<List<CommonFlyCityDetailDTO>> resultJson = (ResultJson<List<CommonFlyCityDetailDTO>>) JsonUtil.jsonToBean(redisResult, new TypeToken<ResultJson<List<CommonFlyCityDetailDTO>>>() {
            }.getType());
            nearByRouteOrRecommendation = resultJson.getResultObject();
            isSetToRedis = false;
        }
        //
        //
        if (!StringUtil.isNullOrEmpty(nearByRouteOrRecommendation)) {
            //默认存放一天 获取的是缓存数据不再存入redis
            if (isSetToRedis) {
                apiRedisService.putData(key, redisResult, 60L * 60 * 24);
            }
            Map<String, CommonFlyCityBO> minMap = new HashMap<>();
            for (CommonFlyCityDetailDTO cfcDto : nearByRouteOrRecommendation) {
                //
                CommonFlyCityBO commonFlyCityBO = new CommonFlyCityBO();
                BeanUtils.copyProperties(cfcDto, commonFlyCityBO);
                //
                String cityCode = cfcDto.getCityCode();
                // 邻近航线跳过 出发城市 相同的
                if (isNearby) {
                    if (request.getSendCode().equals(cityCode)) {
                        continue;
                    }
                } else {
                    // 热门推荐跳过 到达城市 相同的
                    if (request.getArrCode().equals(cityCode)) {
                        continue;
                    }
                }
                //
                String commokey = "";
                // 邻近航线，到达地相同
                if (isNearby) {
                    commokey = "M" + "CNY" + request.getFlightType() + request.getDepartureDate() + cityCode + request.getArrCode();
                } else {
                    // 热门推荐，出发地相同
                    commokey = "M" + "CNY" + request.getFlightType() + request.getDepartureDate() + request.getSendCode() + cityCode;
                }
                //
                String minKey = RedisKeyConfig.COMMON_FLIGHT_MIN_PRICE + request.getChannelCode() + ":" + commokey;
                if (HandlerConstants.TRIP_TYPE_I.equals(request.getTripType())) {
                    minKey = RedisKeyConfig.COMMON_FLIGHT_TAXINFO + request.getChannelCode() + ":" + commokey;
                }
                String minPrice = apiRedisService.getData(minKey);
                // ？refactor
                if (!StringUtil.isNullOrEmpty(minPrice)) {
                    Double curMinPrice = Double.parseDouble(minPrice);
                    commonFlyCityBO.setMinPrice(curMinPrice);
                    if (minMap.isEmpty()) {
                        minMap.put("min", commonFlyCityBO);
                    } else {
                        if (curMinPrice < minMap.get("min").getMinPrice()) {
                            minMap.put("min", commonFlyCityBO);
                        }
                    }
                }
            }
            if (!minMap.isEmpty()) {
                CommonFlyCityBO min = minMap.get("min");
                //当查询航班没有时，展示配置的共飞城市中的最低价航线
                NearFlightRecommend flightRecommend = new NearFlightRecommend();
                BeanUtils.copyProperties(request, flightRecommend);
                if (isNearby) {
                    flightRecommend.setDepCityCode(min.getCityCode());
                    flightRecommend.setDepCityName(min.getCityName());
                    flightRecommend.setArrCityCode(request.getArrCode());
                    flightRecommend.setArrCityName(localCacheService.getLocalCity(request.getArrCode()).getCityName());
                } else {
                    flightRecommend.setDepCityCode(request.getSendCode());
                    flightRecommend.setDepCityName(localCacheService.getLocalCity(request.getSendCode()).getCityName());
                    flightRecommend.setArrCityCode(min.getCityCode());
                    flightRecommend.setArrCityName(min.getCityName());
                }
                flightRecommend.setPrice(min.getMinPrice());
                flightRecommend.setRuleType(ruleType);
                return flightRecommend;
            }
        }
        return null;
    }

    private ResultJson<List<CommonFlyCityDetailDTO>> fetchCommFlyCity(CommonFlyCityReqDTO cfcReq) {
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_COMMONCITY_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(cfcReq, url, null, handConfig.getConnectTimeout(), handConfig.getReadTimeout());
        if (httpResult.isResult()) {
            ResultJson<List<CommonFlyCityDetailDTO>> resultJson = (ResultJson<List<CommonFlyCityDetailDTO>>) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<ResultJson<List<CommonFlyCityDetailDTO>>>() {
            }.getType());
            return resultJson;
        } else {
            log.error("fetchCommFlyHotRecommendation error,httpResult:{}", httpResult);
            return null;
        }
    }


}
