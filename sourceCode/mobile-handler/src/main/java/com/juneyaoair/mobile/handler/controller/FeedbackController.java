package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.request.feedback.FeedbackReq;
import com.juneyaoair.baseclass.request.jidou.BeanQueryReq;
import com.juneyaoair.baseclass.request.jidou.BeanUseReq;
import com.juneyaoair.baseclass.request.jidou.JiDouDetailReq;
import com.juneyaoair.baseclass.request.jidou.JiDouQueryReq;
import com.juneyaoair.baseclass.response.BaseResponse;
import com.juneyaoair.baseclass.response.jidou.BeanQueryResponse;
import com.juneyaoair.baseclass.response.jidou.BeanUseResponse;
import com.juneyaoair.baseclass.response.jidou.JidouDetailResponse;
import com.juneyaoair.baseclass.response.jidou.JidouQueryResponse;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.feedback.Feedback;
import com.juneyaoair.mobile.core.bean.jidou.JiDou;
import com.juneyaoair.mobile.core.service.feedback.IFeedbackService;
import com.juneyaoair.mobile.core.service.jidou.IJiDouService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.util.FileUtils;
import com.juneyaoair.thirdentity.request.jidou.BeanQueryRequest;
import com.juneyaoair.thirdentity.request.jidou.BeanUseRequest;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;

/**
 * Created by jiyuan on 2016-07-21.
 */
@RequestMapping("/feedback")
@RestController
@Api(value="服务反馈相关操作")
public class FeedbackController extends BassController {
    private static final String SIGN_YES = "Y";
    private static final String SIGN_NO = "N";
    private static final String ZERO= "0";
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService redisService;

    @Autowired
    private IFeedbackService feedbackService;

    @Autowired
    private IJiDouService jiDouService;

    //反馈信息
    @RequestMapping(value = "/savefeed",method = RequestMethod.POST)
    @ApiOperation(value = "提交反馈", notes = "提交反馈")
    public BaseResponse addFeedback(@RequestBody FeedbackReq feedbackReq, HttpServletRequest request){
        String message = " ip：" + getClientIP(request) + " 用户id：" + feedbackReq.getFfpId() + " 用户卡号：" + feedbackReq.getChannelCode();
        BaseResponse result = new BaseResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<FeedbackReq>> violations = validator.validate(feedbackReq);
        if(CollectionUtils.isNotEmpty(violations)){
            log.info("服务反馈输入参数有问题================:{}" , message);
            result.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            result.setErrorInfo(violations.iterator().next().getMessage());
            return result;
        }

        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(feedbackReq.getFfpId(), feedbackReq.getLoginKeyInfo(), feedbackReq.getChannelCode());
        if(!flag){
            log.info("服务反馈用户验证失败================{},",message);
            result.setResultCode(WSEnum.ERROR_LONGIN_CHK_ERROR.getResultCode());
            result.setErrorInfo(WSEnum.ERROR_LONGIN_CHK_ERROR.getResultInfo());
            return  result;
        }

        int affectLine=0;
        Feedback selfeedConditon = new Feedback();
        selfeedConditon.setFfpId(feedbackReq.getFfpId());
        List<Feedback> feedbackList = feedbackService.findFeedInfoOnCondition(selfeedConditon);
        try{
            if(!StringUtil.isNullOrEmpty(feedbackList)){
                log.info("今天已经提交过反馈================:{}",message);
                result.setResultCode(WSEnum.ERROR.getResultCode());
                result.setErrorInfo("今天已经提交过反馈");
                return result;
            }
            affectLine = feedbackService.insertFeed(createfeedbak( feedbackReq));

            if(affectLine>0){
                log.info("服务反馈保存成功================:{}",message);
                result.setResultCode(WSEnum.SUCCESS.getResultCode());
                result.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return result;
            }
            log.info("服务反馈保存失败================:{}",message);
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return result;
        }catch (Exception e){
            log.info("服务反馈保存发生异常================:{}",message);
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return result;
        }
    }

    //吉豆签到
    @RequestMapping(value = "/savejidou",method = RequestMethod.POST)
    public BeanUseResponse addJidou(@RequestBody BeanUseReq beanUseReq, HttpServletRequest request){
        BeanUseResponse resp = new BeanUseResponse();
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        resp.setErrorInfo("该活动已结束！");
        return resp;
        /*String message = " ip：" + getClientIP(request) + " 用户id：" + beanUseReq.getFfpId() + " 用户卡号：" + beanUseReq.getFfpCardNo();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BeanUseReq>> violations = validator.validate(beanUseReq);
        if(null!=violations && violations.size()>0){
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(beanUseReq.getFfpId(), beanUseReq.getLoginKeyInfo(), beanUseReq.getChannelCode());
        if(!flag){
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return  resp;
        }

        //前一天签到记录查询 及 每月的第一天或最后一天
        log.info("查询前一天签到记录查询开始================"+message);
        JiDou reslutPreviousDay = thePreviousDayJidouQuery(beanUseReq);
        log.info("查询前一天签到记录查询结束：{reslutPreviousDay： "+reslutPreviousDay==null ? null:JsonUtil.gson.toJson(reslutPreviousDay) +message+"}");

        log.info("查询当天签到记录查询开始================"+message);
        JiDou reslutCurrentDay = theCurrentDayJidouQuery(beanUseReq);
        log.info("查询当天签到记录查询结束：{reslutCurrentDay： "+reslutCurrentDay==null ? null:JsonUtil.gson.toJson(reslutCurrentDay) +message+"}");

        //防止重复签到
        if (null !=reslutCurrentDay && SIGN_YES.equals(reslutCurrentDay.getIsSignIn())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("今天已经签到过了！");
            return resp;
        }
        BeanUseRequest beanUseRequest = createJidouRequest(beanUseReq,beanUseReq.getChannelCode(), reslutPreviousDay);

        //第三方接口吉豆积分增减
        log.info("统一接口吉豆积分增减操作开始：================"+message);
        HttpResult serviceResult = doPost(beanUseRequest, HandlerConstants.URL_FARE + HandlerConstants.SUB_CHANGE_BEAN_QUANTITY);
        if ((null != serviceResult && serviceResult.isResult()))
            try {
            resp = (BeanUseResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), BeanUseResponse.class);
            if (resp.getResultCode().equals("1001")) {
                log.info("统一接口吉豆积分增减操作结束：{成功" + message + "}");
                JiDou jiDou = createJidouInsertParam(beanUseReq, reslutPreviousDay);
                log.info("本地吉豆积分领取操作开始:================" + message);
                int affectLine = jiDouService.insertDou(jiDou);
                if (affectLine > 0) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    log.info("本地吉豆积分领取操作结束: {成功" + message + "}");
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("吉豆更新失败:" + resp.getErrorInfo());
                    log.info("本地吉豆积分领取操作结束: {失败" + message + "}");
                }
                return resp;

            } else {
                log.debug("吉豆更新失败", resp.getErrorInfo());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("吉豆更新失败:" + resp.getErrorInfo());
                log.info("统一接口吉豆积分增减操作结束：{失败" + message + "}");
                return resp;
            }
        } catch (Exception e) {
            log.error("吉豆更新失败"+e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("吉豆更新失败:" + resp.getErrorInfo());
            return resp;
        }
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        resp.setErrorInfo("查询网络出错");
        log.info("统一接口吉豆积分增减操作结束：{查询网络出错" + message + "}");
        return  resp;*/
    }

    //初始化页面时吉豆查询
    @RequestMapping(value = "/findjidou",method = RequestMethod.POST)
    public JidouQueryResponse findJidouCount(@RequestBody JiDouQueryReq jiDouQueryReq, HttpServletRequest request){
        String message = " ip：" + getClientIP(request) + " 用户id：" + jiDouQueryReq.getFfpId() + " 用户卡号：" + jiDouQueryReq.getChannelCode();
        JidouQueryResponse resp = new JidouQueryResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<JiDouQueryReq>> violations = validator.validate(jiDouQueryReq);
        if(CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("吉豆查询条件不正确");
            return  resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(jiDouQueryReq.getFfpId(), jiDouQueryReq.getLoginKeyInfo(), jiDouQueryReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return  resp;
        }
        String channelCode = jiDouQueryReq.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        BeanQueryRequest beanQueryRequest = new BeanQueryRequest(HandlerConstants.VERSION, channelCode, jiDouQueryReq.getFfpId(),userNo);
        //第三方接口吉豆积分增减
        log.info("查询统一接口吉豆信息开始================:{}",message);
        HttpResult serviceResult = doPost(beanQueryRequest, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_BEAN);
        if ((null != serviceResult && serviceResult.isResult())) {

            BeanQueryResponse beanQueryResponse = (BeanQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(),BeanQueryResponse.class);
            try{
                int beanAmount = 0;
                if(beanQueryResponse.getResultCode().equals("1001")){
                    beanAmount = beanQueryResponse.getBeanAmount();
                }
                //查询第三方不正常时吉豆   数结果都为0
                log.info("查询统一接口吉豆信息结束,查询结果:{},吉豆数为:{}" , beanAmount , message );

                JiDou jiDou = new JiDou();
                jiDou.setFfpId(jiDouQueryReq.getFfpId());
                jiDou.setCardNo(jiDouQueryReq.getFfpCardNo());
                jiDou.setValidDate(DateUtils.convertDateToString(new Date(),DATE_FORMAT));

                //查询当前天吉豆
                log.info("查询当天的吉豆信息开始================:{}",message);
                JiDou currentJiDouResult = jiDouService.findCurrentDou(jiDou);
                if (null != currentJiDouResult) {
                    resp.setIsSignIn(SIGN_YES);
                    resp.setSignedNum(currentJiDouResult.getSignNum());
                    String json = currentJiDouResult.toString() + message;
                    log.info("查询当天的吉豆信息结束:{}" ,json );
                } else {
                    //查询前一天吉豆
                    log.info("查询前一天的吉豆信息开始================:{}",message);
                    jiDou.setValidDate(DateUtils.convertDateToString(DateUtils.addOrLessDay(new java.util.Date(), -1),DATE_FORMAT));
                    JiDou yedayJiDouResult = jiDouService.findCurrentDou(jiDou);
                    if (null != yedayJiDouResult) {
                        resp.setSignedNum(yedayJiDouResult.getSignNum());
                        String json = JsonUtil.getGson().toJson(yedayJiDouResult) + message;
                        log.info("查询前一天的吉豆信息结束:{}" , json);
                    } else {
                        resp.setSignedNum(ZERO);
                        log.info("查询前一天的吉豆信息结束yedayJiDouResult为null:{}",message);
                    }
                    resp.setIsSignIn(SIGN_NO);
                }
                //处理已签到超过30天,重新计算签到天数
                int signedNum=Integer.parseInt(resp.getSignedNum());
                if (signedNum>30){
                    resp.setSignedNum(String.valueOf(signedNum%30));
                    if(signedNum%30==0){
                        resp.setSignedNum("30");
                    }
                }
                // 设置吉豆redis缓存
                log.info("添加吉豆规则到redis开始================:{}",message);
                String jidouRule =  redisService.getData("jidouRule");
                if(!StringUtil.isNullOrEmpty(jidouRule)){
                    resp.setRule(JsonUtil.jsonToList(jidouRule));
                } else {
                    resp.setRule(JsonUtil.jsonToList(FileUtils.readJson()));
                    redisService.replaceData("jidouRule",FileUtils.readJson(),DateUtils.dateDiffBySecond(DateUtils.getCurrentTimeStr()));
                }
                log.info("添加吉豆规则到redis结束,成功结果:{}",message);

                resp.setBeanAmount(beanAmount);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            }catch (Exception e){
                String resultJson = JsonUtil.objectToJson(serviceResult);
                log.error("查询吉豆结果:{}" , resultJson);
                resp.setBeanAmount(0);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo("吉豆数查询结果为0");
                return resp;
            }
        }
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        resp.setErrorInfo("查询网络出错");
        log.error("查询统一接口吉豆信息查询结果:网络出错,查询方法：findJidouCount(),:{}",message);
        return  resp;
    }

    /**
     * 查询获得吉豆的详细信息
     * @return
     */
    @RequestMapping(value = "/queryJiDouDetail",method = RequestMethod.POST )
    public JidouDetailResponse queryJiDouDetail(@RequestBody JiDouDetailReq jiDouQueryReq, HttpServletRequest request){
        String message = " ip：" + getClientIP(request) + " 用户id：" + jiDouQueryReq.getFfpId() + " 用户卡号：" + jiDouQueryReq.getChannelCode();
        JidouDetailResponse resp = new JidouDetailResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<JiDouDetailReq>> violations = validator.validate(jiDouQueryReq);
        if(CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("吉豆查询条件不正确");
            return  resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(jiDouQueryReq.getFfpId(), jiDouQueryReq.getLoginKeyInfo(), jiDouQueryReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return  resp;
        }
        log.info("查询吉豆详细信息开始================:{}",message);
        try {
            Map<String, Object> paramMap = new HashMap<>();
            if (StringUtil.isNullOrEmpty(jiDouQueryReq.getStartDate())) {
                paramMap.put("startDate", DateUtils.convertDate2Str(
                        DateUtils.addOrLessMonth(new Date(), -6),DATE_FORMAT));
            } else {
                paramMap.put("startDate", jiDouQueryReq.getStartDate());
            }
            if (StringUtil.isNullOrEmpty(jiDouQueryReq.getEndDate())) {
                paramMap.put("endDate", DateUtils.convertDate2Str(DateUtils.addOrLessDay(new Date(),1),DATE_FORMAT));
            } else {
                paramMap.put("endDate", jiDouQueryReq.getEndDate());
            }
            paramMap.put("ffpId", jiDouQueryReq.getFfpId());
            paramMap.put("cardNo", jiDouQueryReq.getFfpCardNo());
            String mapJson = JsonUtil.getGson().toJson(paramMap);
            log.info("查询吉豆详细paramMap:{}" , mapJson );
            List list = jiDouService.findDouDetail(paramMap);
            int total=0;
            if(!StringUtil.isNullOrEmpty(list)){
                total = jiDouService.findTotalDou(paramMap);
            }
            resp.setBeanAmount(total);
            resp.setList(list);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        }catch (Exception e){
            log.error("查询吉豆详细出错:{}",e.getLocalizedMessage());
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return  resp;
        }
        log.info("查询吉豆详细信息结束================:{}",message);

        return resp;
    }


    //从第三方查询吉豆
    @RequestMapping(value = "/queryBean",method = RequestMethod.POST)
    public BeanQueryResponse queryBeanCount(@RequestBody BeanQueryReq beanQueryReq,HttpServletRequest request){
        String message = " ip：" + getClientIP(request) + " 用户id：" + beanQueryReq.getFfpId() + " 用户卡号：" + beanQueryReq.getChannelCode();
        BeanQueryResponse resp = new BeanQueryResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BeanQueryReq>> violations = validator.validate(beanQueryReq);
        if(CollectionUtils.isNotEmpty(violations)){
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return  resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(beanQueryReq.getFfpId(), beanQueryReq.getLoginKeyInfo(), beanQueryReq.getChannelCode());
        if(!flag){
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return  resp;
        }
        log.info("查询统一接口吉豆信息开始================:{}",message);
        BeanQueryRequest beanQueryRequest = createBeanQueryRequest(beanQueryReq, beanQueryReq.getChannelCode());
        HttpResult serviceResult = doPost(beanQueryRequest, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_BEAN);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (BeanQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), BeanQueryResponse.class);
                if(resp.getResultCode().equals("1001")){
                    log.info("查询统一接口吉豆信息结束,查询结果:{},吉豆数为:{} " , resp.getBeanAmount() , message );
                } else {
                    log.debug("查询吉豆结果:{}" , resp.getErrorInfo());
                    resp.setFfpCardId(beanQueryReq.getFfpId());
                    resp.setFfpCardNo(beanQueryReq.getFfpCardNo());
                    resp.setBeanAmount(0);
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return  resp;
            } catch (Exception e) {
                //查询出错也返回0
                String reqJson = JsonUtil.objectToJson(beanQueryRequest);
                log.error("查询吉豆出错结果:{}" , reqJson);
                resp.setFfpCardId(beanQueryReq.getFfpId());
                resp.setFfpCardNo(beanQueryReq.getFfpCardNo());
                resp.setBeanAmount(0);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return  resp;
            }
        }else{
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            log.error("查询统一接口吉豆信息,查询结果:网络出错 ,查询方法：queryBeanCount(),:{}" ,message);
            return  resp;
        }
    }

    //前一天签到记录查询
    private JiDou thePreviousDayJidouQuery(BeanUseReq beanUseReq) {
        JiDou jiDou = new JiDou();
        jiDou.setFfpId(beanUseReq.getFfpId());
        jiDou.setCardNo(beanUseReq.getFfpCardNo());
        jiDou.setValidDate(DateUtils.convertDateToString(DateUtils.addOrLessDay(new Date(), -1),DATE_FORMAT));

        return jiDouService.findCurrentDou(jiDou);
    }
    //当天签到记录查询
    private JiDou theCurrentDayJidouQuery(BeanUseReq beanUseReq) {
        JiDou jiDou = new JiDou();
        jiDou.setFfpId(beanUseReq.getFfpId());
        jiDou.setCardNo(beanUseReq.getFfpCardNo());
        jiDou.setValidDate(DateUtils.convertDateToString(new Date(),DATE_FORMAT));

        return jiDouService.findCurrentDou(jiDou);
    }

    //吉豆领取条件
    private BeanUseRequest createJidouRequest(BeanUseReq beanUseReq, String channelCode,JiDou reslutPreviousDay){
        String userNo = getChannelInfo(channelCode, "10");
        int signedNum = 1;
        String remark = "";
        if (null != reslutPreviousDay) {
            signedNum = Integer.valueOf(reslutPreviousDay.getSignNum()) + 1;
            if(signedNum>=31){//连续签到超过30天重新开始
                signedNum=1;
            }
        }
        Integer activityBeanAmount = FileUtils.getJidouFromRule(signedNum-1);
        //签到领取吉豆
        return new BeanUseRequest(
                HandlerConstants.VERSION,
                channelCode,
                beanUseReq.getFfpId(),
                beanUseReq.getFfpCardNo(),
                activityBeanAmount,
                "Q",
                remark,
                userNo
        );
    }

    //从第三方查询吉豆条件
    private BeanQueryRequest createBeanQueryRequest(BeanQueryReq beanQueryReq, String channelCode){
        String userNo = getChannelInfo(channelCode, "10");
        return new BeanQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                beanQueryReq.getFfpId(),
                userNo
        );
    }

    //吉豆插入参数做成
    private JiDou createJidouInsertParam (BeanUseReq beanUseReq ,JiDou reslutPreviousDay) {
        JiDou jiDou = new JiDou();
        Date date=new Date();
        jiDou.setNowDate(date);
        jiDou.setDeviceId(beanUseReq.getDeviceId());
        jiDou.setValidDate(DateUtils.convertDateToString(date,DATE_FORMAT));
        if (null == reslutPreviousDay) {//没有前一天的签到记录
            jiDou.setFfpId(beanUseReq.getFfpId());
            jiDou.setIsSignIn(SIGN_YES);
            jiDou.setCardNo(beanUseReq.getFfpCardNo());
            jiDou.setSignNum("1");
            String str = String.valueOf(FileUtils.getJidouFromRule(0));
            jiDou.setRevType(SystemConstants.JIDOU_REV_TYPE_Q);
            jiDou.setJidouNum(str);
            jiDou.setJidouCnt("");
            jiDou.setComments("");
        } else {
            if ( SIGN_YES.equals(reslutPreviousDay.getIsSignIn())){
                jiDou.setFfpId(beanUseReq.getFfpId());
                jiDou.setIsSignIn(SIGN_YES);
                jiDou.setCardNo(beanUseReq.getFfpCardNo());
                int signNum = Integer.valueOf(reslutPreviousDay.getSignNum())+1;
                if(signNum>=31){
                    signNum=1;
                }
                jiDou.setSignNum(String.valueOf(signNum));
                String str = String.valueOf(FileUtils.getJidouFromRule(signNum-1));
                jiDou.setJidouNum(str);
                jiDou.setRevType(SystemConstants.JIDOU_REV_TYPE_Q);
                jiDou.setJidouCnt("");
                jiDou.setComments("");
            }
        }
        return  jiDou;
    }
    //反馈信息保存参数做成
    private Feedback createfeedbak(FeedbackReq feedbackReq){
        Feedback feedback = new Feedback();
        feedback.setFfpId(feedbackReq.getFfpId());
        feedback.setFfpCardNo(feedbackReq.getFfpCardNo());
        feedback.setChannelCode(feedbackReq.getChannelCode());
        feedback.setContent(StringEscapeUtils.escapeHtml4(feedbackReq.getContent()));
        feedback.setFeedType(null != feedbackReq.getFeedType() && !feedbackReq.getFeedType().equals("") ? feedbackReq.getFeedType() : "");
        feedback.setName(feedbackReq.getName());
        feedback.setEmail(feedbackReq.getEmail());
        feedback.setComments(feedbackReq.getComment());
        feedback.setPhone(feedbackReq.getPhone());
        feedback.setDeviceId(feedbackReq.getDeviceId());
        feedback.setPushId(feedbackReq.getPushId());
        return  feedback;
    }


}



