package com.juneyaoair.mobile.handler.service.impl;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.salecoupon.request.QueryProductCache;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.IProductCacheService;
import com.juneyaoair.mobile.handler.util.ChannelUtils;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.salecoupon.v2.common.DateLimits;
import com.juneyaoair.thirdentity.salecoupon.v2.common.Product;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ProductInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ResourceBase;
import com.juneyaoair.thirdentity.salecoupon.v2.request.ProductQueryRequestDto;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-11-25 16:52
 */
@Service
public class ProductCacheServiceImpl implements IProductCacheService {

    /**
     * 缓存
     * key : channelCode + productType
     */
    private Map<String, QueryProductCache> productInfosMap = new ConcurrentHashMap<>();

    /**
     * 下次清理缓存时间
     */
    private LocalDateTime nextCleanCacheTime;

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    @Autowired
    private OrderManage orderManage;

    @Override
    public List<ProductInfo> obtainProductCache(String productType, String channelCode, String ip) {
        // 一级缓存
        String key = channelCode + productType;
        QueryProductCache cache = this.getCache(key);
        if (null != cache) {
            return cache.getProductInfos();
        }
        // 二级缓存
        String redisKey = RedisKeyConfig.QUERY_PRODUCT_CACHE + key;
        String redisCache = apiRedisService.getData(redisKey);
        if (StringUtils.isNotBlank(redisCache)) {
            List<ProductInfo> productInfoList = JsonUtil.fromJson(redisCache, new TypeToken<List<ProductInfo>>(){}.getType());
            if (CollectionUtils.isNotEmpty(productInfoList)) {
                this.updateCache(key, productInfoList);
                return productInfoList;
            }
        }
        // 查询
        PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, ChannelUtils.getChannelInfo(channelCode, "10"));
        ProductQueryRequestDto productQueryRequestDto = new ProductQueryRequestDto();
        productQueryRequestDto.setSearchTypes(productType);
        ptRequest.setRequest(productQueryRequestDto);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        PtResponse<List<ProductInfo>> ptResponse = orderManage.queryProducts(ptRequest, headMap);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode()) && CollectionUtils.isNotEmpty(ptResponse.getResult())) {
            List<ProductInfo> productInfoList = ptResponse.getResult().parallelStream().map(productInfo -> createProductInfoSign(productInfo)).collect(Collectors.toList());
            this.updateCache(key, productInfoList);
            apiRedisService.putData(redisKey, JsonUtil.objectToJson(productInfoList),6 * 30 * 60L);//存放6小时
            return productInfoList;
        }
        return Lists.newArrayList();
    }

    @Override
    public void clearCache() {
        this.productInfosMap.clear();
        this.nextCleanCacheTime = null;
    }

    /**
     * 更新缓存信息
     * @param key
     * @param productInfoList
     */
    private void updateCache(String key, List<ProductInfo> productInfoList) {
        QueryProductCache cache = new QueryProductCache();
        cache.setLocalDateTime(LocalDateTime.now());
        cache.setExpireTime(LocalDateTime.now().plusHours(1));
        cache.setProductInfos(productInfoList);
        this.putCache(key, cache);
    }

    /**
     * 获取缓存内容
     * @param key
     * @return
     */
    private QueryProductCache getCache(String key) {
        cleanCache();
        QueryProductCache cache = productInfosMap.get(key);
        return cache;
    }

    /**
     * 放入缓存
     * @param key
     * @param cache
     */
    private void putCache(String key, QueryProductCache cache) {
        cleanCache();
        productInfosMap.put(key, cache);
    }

    /**
     * 清理过期缓存
     */
    private void cleanCache() {
        if (!productInfosMap.isEmpty() && productInfosMap.size() > 10) {
            LocalDateTime now = LocalDateTime.now();
            if (nextCleanCacheTime == null || nextCleanCacheTime.isBefore(now)) {
                Iterator<Map.Entry<String, QueryProductCache>> iterator = productInfosMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, QueryProductCache> entry = iterator.next();
                    QueryProductCache cache = entry.getValue();
                    if (cache.getLocalDateTime() == null || cache.getLocalDateTime().isBefore(now)) {
                        iterator.remove();
                    }
                }
            }
            nextCleanCacheTime = now.plusMinutes(60);
        }
    }

    private ProductInfo createProductInfoSign(ProductInfo productInfo) {
        ResourceBase resourceBase = productInfo.getResourceInfo();
        List<Product> productList = productInfo.getProducts();
        if (CollectionUtils.isNotEmpty(productList)) {
            productList.stream().forEach(product -> {
                product.setAvailDate("");
                product.setUnAvailDate("");
                product.setSign(createProSign(resourceBase, product));
                if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(product.getProductType())
                        || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(product.getProductType()) ){
                    product.setAvailPrdStockFlag("no".equals(product.getStockFlag()) || ("yes".equals(product.getStockFlag()) && product.getAvailPrdStock() > 0));
                    product.setAvailPrdStock(0);
                    product.setAvailDate(dateLimitFilter(product.getProductRuleLimit().getFlightDate().getSuit(),1));
                    if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(product.getProductType())){
                        String unAvailDate = dateLimitFilter(product.getProductRuleLimit().getFlightDate().getNotSuit(), 0);
                        product.setUnAvailDate(StringUtils.isNotBlank(unAvailDate) ? "(不含"+unAvailDate+")" :"");
                    }
                }
            });
        }
        return productInfo;
    }

    private String createProSign(ResourceBase resourceBase, Product product) {
        String originalKey = product.createSignField(resourceBase.getResourceId());
        return EncoderHandler.encodeByMD5(originalKey);
    }

    private static String dateLimitFilter(List<DateLimits> dateLimits, int limitType) {
        if (CollectionUtils.isNotEmpty(dateLimits)) {
            for (DateLimits dateLimit : dateLimits) {
                //可用
                if (limitType == 1) {
                    return dateLimit.getStartDate()+"至"+dateLimit.getEndDate();
                }else if(limitType == 0){
                    //不可用
                    return dateLimit.getStartDate()+"至"+dateLimit.getEndDate();
                }
            }
        }
        return "";
    }

}
