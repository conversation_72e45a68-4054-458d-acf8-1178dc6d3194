package com.juneyaoair.mobile.handler.controller;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.ResponseCode;
import com.juneyaoair.baseclass.common.response.ResponseData;
import com.juneyaoair.baseclass.member.response.TicketInfoQueryResponse;
import com.juneyaoair.baseclass.request.coupons.CustomCouponUseDTO;
import com.juneyaoair.baseclass.request.coupons.CustomCouponUseRequest;
import com.juneyaoair.baseclass.request.prepaymentBaggage.QueryPrepaymentBaggageSegmentReq;
import com.juneyaoair.baseclass.response.coupons.CustomCouponUseResponse;
import com.juneyaoair.baseclass.response.prepaymentBaggage.QueryPrepaymentBaggageSegmentResp;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.service.IExtraBaggageService;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberCertificateSoaModelV2;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.salecoupon.v2.response.ProductAvailableRespDto;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.SensitiveInfoHider;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拆分NewCouponController 预付费行李部分
 *
 * @see NewCouponController
 */
@RequestMapping("/new/coupon")
@RestController
@Api(value = "NewCouponController", tags = {"新权益券服务"})
public class PrePaymentBaggageController extends BassController {

    @Autowired
    private IMemberService memberService;
    @Autowired
    private IExtraBaggageService iExtraBaggageService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    /**
     * @param req           前端仅传 UserInfoMust
     * @param bindingResult
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "预付费行李-我的行程", notes = "根据票号,或证件号查询航段")
    @RequestMapping(value = "/myItinerary", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<QueryPrepaymentBaggageSegmentResp>> myItinerary(@RequestBody @Validated BaseReq<QueryPrepaymentBaggageSegmentReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<List<QueryPrepaymentBaggageSegmentResp>> resp = new BaseResp<>();
        //
        String channelCode = req.getChannelCode();
        List<QueryPrepaymentBaggageSegmentResp> resData = new ArrayList<>();
        //
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                , MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName
                , MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiRequestCert = CRMReqUtil.buildMemberDetailReq(req.getRequest().getFfpCardNo(), req.getRequest().getFfpId(), request, channelCode, items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequestCert);
        if (!CrmUtil.judgeRealNameStatus(ptCRMResponse.getData().getRealVerifyInfos())) {
            resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
            resp.setResultInfo(WSEnum.NO_REAL_NAME.getResultInfo());
            return resp;
        }
        // 取证件
        List<String> certNOs = new ArrayList<>();
        if (!ptCRMResponse.getData().getCertificateInfo().isEmpty()) {
            certNOs = ptCRMResponse.getData().getCertificateInfo().stream().map(MemberCertificateSoaModelV2::getCertificateNumber).collect(Collectors.toList());
        }
        //
        List<TicketInfoQueryResponse> tickInfo = iExtraBaggageService.queryFlightSegment(request, req.getRequest().getFfpCardNo(), req.getRequest().getFfpId(), certNOs, null);
        //过滤宠物票、占座行李特殊客票
        tickInfo = tickInfo.stream().filter(tick -> StringUtils.isNotBlank(tick.getFare_basis_code()) &&
                        !(tick.getFare_basis_code().endsWith("PET") || tick.getFare_basis_code().endsWith("CBBG")))
                .collect(Collectors.toList());
        //
        if (tickInfo.isEmpty()) {
            resp.setObjData(resData);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultCode());
            return resp;
        }
        // 此处前端仅传 UserInfoMust, QueryPrepaymentBaggageSegmentReq仅为封装
        resData = iExtraBaggageService.convertToQueryPrepaymentBaggageSegmentResp(tickInfo, req, true);
        //
        resp.setObjData(resData);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        //  额外行李购买证件信息脱敏
        this.prepaymentBaggageCertNoDesensitization(resp);
        //
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "使用定制优惠券", notes = "使用定制优惠券")
    @RequestMapping(value = "/useCustomCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp useCustomCoupon(@RequestBody @Validated BaseReq<CustomCouponUseDTO> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp  resp = new BaseResp();
        CustomCouponUseRequest<CustomCouponUseDTO> customCouponUseRequest =new CustomCouponUseRequest();
        String ip = this.getClientIP(request);
        CustomCouponUseDTO customCouponUseDTO =  req.getRequest();
        customCouponUseRequest.setData(req.getRequest());
        customCouponUseRequest.setChannelNo("MOBILE");
        customCouponUseRequest.setFfpNo(req.getRequest().getFfpCardNo());
        customCouponUseRequest.setFfpId(req.getRequest().getFfpId());
        customCouponUseRequest.setOriginIp(ip);
        customCouponUseRequest.setData(customCouponUseDTO);
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.COUPON_USE_CUSTOM_COUPON;
        HttpResult httpResult = HttpUtil.doPostClient(customCouponUseRequest, url);
        if (httpResult.isResult()) {
            CustomCouponUseResponse responseData = JsonUtil.fromJson(httpResult.getResponse(), new TypeToken<CustomCouponUseResponse>() {
            }.getType());
            if (ResponseCode.SUCCESS.getCode().equals(responseData.getResultCode())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            }else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            }

        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求关注航班网络异常");
        }
        return resp;

    }




    /**
     * copy from {@link com.juneyaoair.mobile.handler.controller.NewCouponController#prepaymentBaggageCertNoDesensitization}
     *
     * @param resp
     */
    private void prepaymentBaggageCertNoDesensitization(BaseResp<List<QueryPrepaymentBaggageSegmentResp>> resp) {
        resp.getObjData().forEach(queryPrepaymentBaggageSegmentResp -> {
            // 页面证件信息
            String certNo = queryPrepaymentBaggageSegmentResp.getBaggagePassengerInfo().getIdNo();
            String desensitizedCertNo = SensitiveInfoHider.hideMiddleSensitiveInfo(certNo);
            queryPrepaymentBaggageSegmentResp.getBaggagePassengerInfo().setIdNo(desensitizedCertNo);
            String tktNo = queryPrepaymentBaggageSegmentResp.getTicketNumber();
            apiRedisService.putData(RedisKeyConfig.createUpIdInfo(tktNo), certNo, 60 * 10L);

            // BaggageSegmentInfoList仅在由数据接口返回时，包含证件信息，也隐藏
            queryPrepaymentBaggageSegmentResp.getBaggageSegmentInfoList().forEach(v -> {
                if (certNo.equals(v.getTravellerNumber())) {
                    v.setTravellerNumber(desensitizedCertNo);
                }
            });
        });
    }
}

