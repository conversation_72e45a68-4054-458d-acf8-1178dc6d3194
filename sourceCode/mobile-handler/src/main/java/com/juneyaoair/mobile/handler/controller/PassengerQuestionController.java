package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.baseclass.airbookstore.resp.PassengerResponse;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.passengerquestion.*;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.AccountType;
import com.juneyaoair.mobile.webservice.client.crm.MemberAccountQueryResponseForClient;
import com.juneyaoair.mobile.webservice.client.crm.MemberInfoQueryResponseForClient;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MileageAccountQueryResponse;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.order.stateNotice.CommentCompleteNotice;
import com.juneyaoair.thirdentity.request.order.stateNotice.request.QueryCouponProductOrderRequest;
import com.juneyaoair.thirdentity.request.order.stateNotice.request.UpdateCouponStatusRequest;
import com.juneyaoair.thirdentity.request.order.stateNotice.response.UpdateCouponStatusResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname PassengerQuestionController
 * @Description 调查问卷，对接旅客服务网
 * @Date 2019/10/30 16:45
 * @Created by yzh
 */
@RestController
@RequestMapping("passengerQuestion")
@Api(value = "旅客问卷服务")
public class PassengerQuestionController extends BassController {

    @Autowired
    private CrmWSClient crmClient;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IMemberService memberService;

    private static final String DATA = "data";
    private static final String MEMBER_ID = "memberId";
    private static final String MEMBER_NO = "memberNo";
    private static final String FEEBACK_ID = "feebackId";
    private static final String FEEBACK_QSTTOPLIST = "feebackQstTopList";
    private static final String TOPIC_CLASS = "topicSubclass";
    private static final String QOS_STFSFEED_BACK = "qosStfsFeedback";
    private static final String PRO_ADDOCIA_KEY = "proAssociaKey";
    private static final String SERVICE_NAME = "旅客服务网问卷";
    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    @RequestMapping(value = "getPassengerQuestion", method = RequestMethod.POST)
    @ApiOperation(value = "五星评价获取问卷", notes = "从旅客服务网获取五星评价问卷")
    public BaseResp<QuestionQueryRespDTO> getPassengerQuestion(@RequestBody BaseReq<QueryTopicListReq> req,HttpServletRequest request) {
        BaseResp<QuestionQueryRespDTO> resp = new BaseResp<>();
        QueryTopicListReq topicListReq = req.getRequest();
        //非空校验
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        Set<ConstraintViolation<BaseReq<QueryTopicListReq>>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }

        if(StringUtil.isNullOrEmpty(req.getRequest().getType()) || StringUtil.isNullOrEmpty(req.getRequest().getProAssociaKey())){
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }
        //判断是否登录
        boolean flag = this.checkKeyInfo(topicListReq.getFfpId(), topicListReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //查询积分与姓名
        //查询积分
        String memberName = "";
        String memberScore = "0";
        MemberInfoQueryResponseForClient memberInfoquery = crmClient.memberInfoquery(Long.valueOf(topicListReq.getFfpId()), req.getChannelCode(), getChannelInfo(req.getChannelCode(), "40"));
        if (memberInfoquery != null && "S000".equals(memberInfoquery.getMessageHeader().getErrorCode())) {
            memberName = memberInfoquery.getMemberQueryInfo().getCustomerInfo().getCLastName() + memberInfoquery.getMemberQueryInfo().getCustomerInfo().getCFirstName();
            memberInfoquery.getMemberQueryInfo().getCustomerInfo().getCFirstName();
            if (StringUtil.isNullOrEmpty(memberName)) {//无姓名展示卡号
                memberName = memberInfoquery.getMemberQueryInfo().getMemberID();
            }
        }
        MemberAccountQueryResponseForClient clientResponse = crmClient.memberAccountQuery(topicListReq.getFfpId(), "", "", req.getChannelCode(), getChannelInfo(req.getChannelCode(), "40"));
        if (clientResponse != null && "S000".equals(clientResponse.getMessageHeader().getErrorCode())) {
            AccountType sumAccount = clientResponse.getMemberAccount().getSumAccount();
            memberScore = String.valueOf(sumAccount.getClubMiles() + sumAccount.getSupplierMiles() + sumAccount.getExtraMiles() + sumAccount.getPromotionMiles()
                    - sumAccount.getRedeemMiles() - sumAccount.getExpiredMiles());
        }
        try {
            PassengerFiveStarQuestionQueryResp fiveStarQuestionQueryResp = queryFiveStarQuestion(topicListReq);
            if(null == fiveStarQuestionQueryResp){
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
            if (fiveStarQuestionQueryResp.getCode() != 0) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(StringUtils.isNotBlank(fiveStarQuestionQueryResp.getMsg()) ? fiveStarQuestionQueryResp.getMsg() : WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
            //问卷分类数据
            QosStfsQuestionnaireB2cVo data = fiveStarQuestionQueryResp.getData();

            List<QosStfsTopicListB2cVo> topicList = data.getTopicList();
            //集合未空 数据异常
            if (CollectionUtils.isEmpty(topicList)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
            QuestionQueryRespDTO questionQueryRespDTO = new QuestionQueryRespDTO();
            List<FiveStarQuestionGroup> fiveStarQuestionGroupList = new ArrayList<>();
            for (QosStfsTopicListB2cVo qosStfsTopicListB2cVo : topicList) {
                List<QosStfsTopic> questionList = qosStfsTopicListB2cVo.getTopicList();
                if(CollectionUtils.isEmpty(questionList)){
                   continue;
                }
                for (QosStfsTopic qosStfsTopic : questionList) {
                    FiveStarQuestionGroup fiveStarQuestionGroup = new FiveStarQuestionGroup();
                    BeanUtils.copyProperties(qosStfsTopic,fiveStarQuestionGroup);
                    fiveStarQuestionGroupList.add(fiveStarQuestionGroup);
                }
            }
            questionQueryRespDTO.setQuestId(data.getQuestId());
            questionQueryRespDTO.setQuestTitle(data.getQuestTitle());
            questionQueryRespDTO.setFiveStarQuestionGroupList(fiveStarQuestionGroupList);
            resp.setObjData(questionQueryRespDTO);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());

        } catch (Exception e) {
            log.error("获取问卷信息出现异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }
    //提交问卷
    @RequestMapping(value = "passengerQuestionCommit", method = RequestMethod.POST)
    @ApiOperation(value = "五星评分提交答案", notes = "五星评分提交答案")
    public BaseResp passengerQuestionCommit(@RequestBody BaseReq<AddFiveStarTopicReq> req,HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        String ip =  this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        String reqId = headChannelCode+StringUtil.newGUID() + request.getRequestURI();
        saveReqInfo(SERVICE_NAME,reqId,ip,reqJson);
        AddFiveStarTopicReq addTopicReq = req.getRequest();
        List<FiveStarCommitTopicData> topicList = addTopicReq.getTopicList();
        /*for (FiveStarCommitTopicData fiveStarCommitTopicData : topicList) {
            if(StringUtil.isNullOrEmpty(fiveStarCommitTopicData.getFeebackAnswer()) || StringUtil.isNullOrEmpty(fiveStarCommitTopicData.getAnswerTriggContent())){
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("请填写所有");
                return resp;
            }
        }*/
        //校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq<AddFiveStarTopicReq>>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //判断是否登录
        boolean flag = this.checkKeyInfo(addTopicReq.getFfpId(), addTopicReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }

        QosStfsFeedbackFlightInfo qosStfsFeedbackFlightInfo = new QosStfsFeedbackFlightInfo();
        qosStfsFeedbackFlightInfo.setFlightNo(req.getRequest().getFlightNo());
        qosStfsFeedbackFlightInfo.setTicketNo(req.getRequest().getTktNo());


        //问卷构建请求参数
        List<FiveStarCommitData> commitData = crateCommitList(addTopicReq);
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put(FEEBACK_QSTTOPLIST, commitData);
        jsonObject.put(TOPIC_CLASS, "2");
        jsonObject.put("memberId",addTopicReq.getFfpId());
        jsonObject.put(MEMBER_NO, addTopicReq.getFfpCardNo());
        jsonObject.put(PRO_ADDOCIA_KEY, addTopicReq.getProAssociaKey());
        jsonObject.put(QOS_STFSFEED_BACK, qosStfsFeedbackFlightInfo);
        if("Lounge".equals(addTopicReq.getType()) || "LoungeCoupon".equals(addTopicReq.getType())){
            //休息室
            jsonObject.put("questType", "2");
        }
        if("RescheduleCoupon".equals(addTopicReq.getType()) || "Reschedule".equals(addTopicReq.getType())){
            //改期券
            jsonObject.put("questType", "3");
        }
        if("Upgrade".equals(addTopicReq.getType())|| "UpgradeCoupon".equals(addTopicReq.getType())){
            //升舱券
            jsonObject.put("questType", "4");
        }
        postJson.put(DATA, jsonObject);
        //调用旅客服务网查询数据
        String passagerResp = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/quality/qosStfsFeedback/addFeebackMsg");
        //String passagerResp = this.passagerPost(postJson, "http://172.22.3.11:8601/qosStfsFeedback/addFeebackMsg");
        try {
            PassengerResponse questionAddResp = JsonMapper.buildNormalMapper().fromJson(passagerResp, PassengerResponse.class);
            if (questionAddResp.getCode() == 0) {
                //调订单接口，改变券码的状态为已评论的  CRM_API_URL + "/Order/Comment/StateNotice"
                UpdateCouponStatusRequest orderRequest = createOrderReqParam(req);
                HttpResult result = doPostClient(orderRequest, HandlerConstants.URL_FARE_API + "/Order/Comment/CommentStateNotice");
                //HttpResult result = doPostClient(orderRequest, "https://horder.hoair.cn/Order/Comment/CommentStateNotice");
                if (result.isResult()) {
                    UpdateCouponStatusResponse orderResponse = (UpdateCouponStatusResponse)JsonUtil.jsonToBean(result.getResponse(), UpdateCouponStatusResponse.class);
                    if("1001".equals(orderResponse.getResultCode())){
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    }else{
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(orderResponse.getErrorInfo());
                        return resp;
                    }
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(StringUtils.isNotBlank(questionAddResp.getMsg())? questionAddResp.getMsg() : WSEnum.ERROR.getResultInfo());
                return resp;
            }
        } catch (Exception e) {
            saveError(SERVICE_NAME,reqId,ip,reqJson,e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
        return resp;
    }

    private QueryCouponProductOrderRequest createQueryCouponInfoParam(BaseReq<AddFiveStarTopicReq> req) {
        QueryCouponProductOrderRequest request = new QueryCouponProductOrderRequest();
        request.setChannelCode(req.getChannelCode());
        request.setUserNo(getChannelInfo(req.getChannelCode(), "10"));
       /* request.setFfpId(req.getRequest().getFfpId());
        request.setFfpCardNo(req.getRequest().getFfpCardNo());*/
        request.setIsRemoved(0);
        request.setSearchType(0);
        request.setPageNo(1);
        request.setPageSize(20);
        request.setVoucherNo(req.getRequest().getProAssociaKey());
        return request;
    }

    private List<FiveStarCommitData> crateCommitList(AddFiveStarTopicReq addTopicReq) {
        List<FiveStarCommitData> result = new ArrayList<>();
        List<FiveStarCommitTopicData> topicList = addTopicReq.getTopicList();
        for (FiveStarCommitTopicData fiveStarCommitTopicData : topicList) {
            List<FeebackAnswer> feebackAnswerList = fiveStarCommitTopicData.getFeebackAnswer();
            if(!StringUtil.isNullOrEmpty(feebackAnswerList)){
                for (FeebackAnswer feebackAnswer : feebackAnswerList) {
                    if(!StringUtil.isNullOrEmpty(feebackAnswer.getAnswerNo())){
                        FiveStarCommitData fiveStarCommitData = new FiveStarCommitData();
                        fiveStarCommitData.setFeebackAnswerContent(feebackAnswer.getAnswerContent());
                        fiveStarCommitData.setQuestId(addTopicReq.getQuestId());
                        fiveStarCommitData.setTopicId(fiveStarCommitTopicData.getId());
                        fiveStarCommitData.setFeebackAnswer(feebackAnswer.getAnswerNo());
                        result.add(fiveStarCommitData);
                    }
                }
            }else{
                if(!StringUtil.isNullOrEmpty(fiveStarCommitTopicData.getAnswerTriggContent())){
                    FiveStarCommitData fiveStarCommitData = new FiveStarCommitData();
                    fiveStarCommitData.setFeebackAnswerContent(fiveStarCommitTopicData.getAnswerTriggContent());
                    fiveStarCommitData.setQuestId(addTopicReq.getQuestId());
                    fiveStarCommitData.setTopicId(fiveStarCommitTopicData.getId());
                    result.add(fiveStarCommitData);
                }
            }
        }
        return result;
    }

    private UpdateCouponStatusRequest createOrderReqParam(BaseReq<AddFiveStarTopicReq> req) {
        UpdateCouponStatusRequest request = new UpdateCouponStatusRequest();
        request.setVersion(req.getVersionCode());
        request.setChannelCode(req.getChannelCode());
        request.setUserNo(getChannelInfo(req.getChannelCode(), "10"));
        CommentCompleteNotice commentCompleteNotice = new CommentCompleteNotice();
        commentCompleteNotice.setCommentState("COMMENTED");
        commentCompleteNotice.setCouponCode(req.getRequest().getProAssociaKey());
        request.setRequest(commentCompleteNotice);
        return request;
    }

    private PassengerFiveStarQuestionQueryResp queryFiveStarQuestion(QueryTopicListReq topicListReq) {
        PassengerFiveStarQuestionQueryResp resp = new PassengerFiveStarQuestionQueryResp();
        //构建查询条件
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put(FEEBACK_ID, topicListReq.getFeebackId());
        jsonObject.put(MEMBER_ID,topicListReq.getFfpId());//会员id
        jsonObject.put(MEMBER_NO, topicListReq.getFfpCardNo()); //会员卡号
        if("Lounge".equals(topicListReq.getType()) || "LoungeCoupon".equals(topicListReq.getType())){
            //休息室
            jsonObject.put("questType", "2"); //类型
        }
        if("RescheduleCoupon".equals(topicListReq.getType()) || "Reschedule".equals(topicListReq.getType())){
            //改期券
            jsonObject.put("questType", "3"); //类型
        }
        if("Upgrade".equals(topicListReq.getType())|| "UpgradeCoupon".equals(topicListReq.getType())){
            //升舱券
            jsonObject.put("questType", "4"); //类型
        }
        jsonObject.put("proAssociaKey", topicListReq.getProAssociaKey()); //券码
        postJson.put(DATA, jsonObject);
        //调用旅客服务网查询数据
        String result = passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/quality/qosStfsFeedback/selectTopicByFeebackId2");
        //String result = passagerPost(postJson, "http://172.22.3.11:8601/qosStfsFeedback/selectTopicByFeebackId");
        //String result = passagerPost(postJson, "http://172.22.3.11:8601/qosStfsFeedback/selectTopicByFeebackId2");
        log.info("调用旅客服务网接口{},参数{},响应{}", HandlerConstants.URL_PASSAGER_API + "/quality/qosStfsFeedback/selectTopicByFeebackId2", JsonUtil.objectToJson(postJson), result);
        resp = JsonMapper.buildNormalMapper().fromJson(result, PassengerFiveStarQuestionQueryResp.class);
        return resp;
    }


    //根据反馈的id获取问卷
    @RequestMapping(value = "getTopicList", method = RequestMethod.POST)
    @ApiOperation(value = "获取问卷", notes = "从旅客服务网获取问卷")
    public BaseResp<QuestionQueryResp> getTopicList(@RequestBody @Validated BaseReq<QueryTopicListReq> req, BindingResult bindingResult,HttpServletRequest request) {
        BaseResp<QuestionQueryResp> resp = new BaseResp<>();
        QueryTopicListReq topicListReq = req.getRequest();
        String channelCode = req.getChannelCode();
        String ip = this.getClientIP(request);
        //校验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //判断是否登录
        boolean flag = this.checkKeyInfo(topicListReq.getFfpId(), topicListReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //查询积分与姓名
        //查询积分
        String memberName = "";
        String memberScore = "0";
        String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName,
                MemberDetailRequestItemsEnum.ADDRESSINFOS.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(request, req.getRequest().getFfpId(), "");
        PtApiCRMRequest ptApiCRMRequests = new PtApiCRMRequest();
        ptApiCRMRequests.setHeader(header);
        ptApiCRMRequests.setChannel(channelCode);
        ptApiCRMRequests.setChannelPwd(getChannelInfo(channelCode, "40"));
        ptApiCRMRequests.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponses = memberService.memberDetail(ptApiCRMRequests);
        if (ptCRMResponses.getCode() ==0){
            memberName = (ptCRMResponses.getData().getBasicInfo().getCLastName()+ptCRMResponses.getData().getBasicInfo().getCFirstName());
        }else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(ptCRMResponses.getMsg());
            resp.setResultInfo(ptCRMResponses.getMsg());
            return resp;
        }
        PtCRMResponse<MileageAccountQueryResponse> ptCRMResponse = memberService.mileageAccountQuery(ptApiCRMRequests);
        if (ptCRMResponse.getCode() ==0){
            memberScore = String.valueOf(ptCRMResponse.getData().getTotalBill().getAvailableMiles());
        }else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(ptCRMResponses.getMsg());
            resp.setResultInfo(ptCRMResponses.getMsg());
            return resp;
        }
        try {
            PassengerQuestionQueryResp questionQueryResp = queryQuestion(topicListReq.getFeebackId(),topicListReq.getFfpId(),topicListReq.getFfpCardNo());
            if (questionQueryResp == null) {
                resp.setResultCode(WSEnum.NO_DATA.getResultInfo());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
            if (questionQueryResp.getCode() != 0) {
                resp.setResultCode(WSEnum.NO_DATA.getResultInfo());
                resp.setResultInfo(StringUtils.isNotBlank(questionQueryResp.getMsg()) ? questionQueryResp.getMsg() : WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
            //问卷分类数据
            List<QuestionGroup> questionGroups = questionQueryResp.getData();
            //集合未空 数据异常
            if (CollectionUtils.isEmpty(questionGroups)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultInfo());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
            //判断问卷是否作答完毕
            if (questionHaveComplete(questionGroups)) {
                QuestionQueryResp respData = new QuestionQueryResp();
                respData.setQuestionClassiftyData(questionGroups);
                respData.setComplete(true);
                respData.setMemberName(memberName);
                respData.setMemberScore(memberScore);
                respData.setTakeScore(haveReceivePoint(topicListReq.getFeebackId()));
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(respData);
                return resp;
            }
            //答案排序
            questionGroups.forEach(group -> sortTopicAnswer(group.getTopicList()));
            //设置父id
            questionGroups.forEach(group -> group.getTopicList().forEach(topicDetail -> addFatherId(topicDetail, topicDetail.getId())));
            //去重
            questionGroups.forEach(group -> {
                List<TopicDetail> topicList = group.getTopicList();
                HashSet<String> mainIdList = new HashSet<>();
                for (TopicDetail topicDetail : topicList) {
                    mainIdList.add(topicDetail.getId());
                }
                //设置题型
                dealTopicType(topicList);
                removeDuplicateTopic(topicList, mainIdList);
                dealNullAnswerTopicList(topicList);
            });
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            QuestionQueryResp respData = new QuestionQueryResp();
            respData.setQuestionClassiftyData(questionGroups);
            respData.setMemberName(memberName);
            respData.setMemberScore(memberScore);
            respData.setComplete(false);
            resp.setObjData(respData);
            return resp;
        } catch (Exception e) {
            log.error("获取问卷信息出现异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    //给子类问题加上父类id
    private void addFatherId(TopicDetail topicDetail, String fatherId) {
        topicDetail.getAnswerList().forEach(answerDetail -> {
            if (CollectionUtils.isNotEmpty(answerDetail.getTopicList())) {
                answerDetail.getTopicList().forEach(answertopicDetail -> {
                    answertopicDetail.setFatherId(fatherId);
                    addFatherId(answertopicDetail, fatherId);
                });
            }
        });
    }

    //提交问卷
    @RequestMapping(value = "commmitAnswer", method = RequestMethod.POST)
    @ApiOperation(value = "提交答案", notes = "提交问卷答案")
    public BaseResp commmitAnswer(@RequestBody BaseReq<AddTopicReq> req,HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        String ip =  this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        String reqId = headChannelCode+StringUtil.newGUID() + request.getRequestURI();
        saveReqInfo(SERVICE_NAME,reqId,ip,reqJson);
        AddTopicReq addTopicReq = req.getRequest();
        //校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq<AddTopicReq>>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //判断是否登录
        boolean flag = this.checkKeyInfo(addTopicReq.getFfpId(), addTopicReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //餐食问题提交后问卷被置为作答完毕状态，所以提交餐食问题时校验其他问题是否作答完毕
//        if ("5".equals(addTopicReq.getTopicClass())) {
//            PassengerQuestionQueryResp questionQueryResp = queryQuestion(addTopicReq.getFeebackId());
//            if (questionQueryResp.getCode() == 0 && questionQueryResp.getData() != null) {
//                boolean canApplyMealQuestion = isCanApplyMealQuestion(questionQueryResp);
//                if (!canApplyMealQuestion) {
//                    resp.setResultCode(WSEnum.ERROR.getResultCode());
//                    resp.setResultInfo("请先完成其他模块问题");
//                    return resp;
//                }
//            } else {
//                resp.setResultCode(WSEnum.ERROR.getResultCode());
//                resp.setResultInfo("问卷状态异常");
//                return resp;
//            }
//        }
        //问题集合
        String feebackId = addTopicReq.getFeebackId();
        List<CommitTopicData> topicList = addTopicReq.getTopicList();
        //添加问题feebackId 处理答案id 和 不满意答案id
        topicList.forEach(topic -> {
            topic.setFeebackId(feebackId);
            topic.setFeebackAnswer(topic.getAnswerNo());
            topic.setUnsatisfyFbkAnswer(topic.getOptionChoose());
        });
        //构建请求参数
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put(FEEBACK_QSTTOPLIST, topicList);
        jsonObject.put(TOPIC_CLASS, addTopicReq.getTopicClass());
        jsonObject.put("memberId",addTopicReq.getFfpId());
        jsonObject.put(MEMBER_NO, addTopicReq.getFfpCardNo());
        postJson.put(DATA, jsonObject);
        postJson.put("memberId",addTopicReq.getFfpCardNo());
        //调用旅客服务网查询数据
        String passagerResp = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/quality/qosStfsFeedback/addFeebackMsg");
        //String passagerResp = this.passagerPost(postJson, "http://172.22.3.11:8601/qosStfsFeedback/addFeebackMsg");
        try {
            PassengerResponse questionAddResp = JsonMapper.buildNormalMapper().fromJson(passagerResp, PassengerResponse.class);
            if (questionAddResp.getCode() == 0) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(StringUtils.isNotBlank(questionAddResp.getMsg())? questionAddResp.getMsg() : WSEnum.ERROR.getResultInfo());
                return resp;
            }
        } catch (Exception e) {
            saveError(SERVICE_NAME,reqId,ip,reqJson,e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    //判断除了问卷问题是否还有其他未完成的问题
    private boolean isCanApplyMealQuestion(PassengerQuestionQueryResp questionQueryResp) {
        List<QuestionGroup> groups = questionQueryResp.getData();
        boolean canApplyMealQuestion = true;
        List<QuestionGroup> collect = groups.stream().filter(group -> 5 != group.getTopicSubclass()).collect(Collectors.toList());
        for (QuestionGroup questionGroup : collect) {
            if (CollectionUtils.isNotEmpty(questionGroup.getTopicList())) {
                canApplyMealQuestion = false;
                break;
            }
        }
        return canApplyMealQuestion;
    }

    //领取积分
    @RequestMapping(value = "receivePoint", method = RequestMethod.POST)
    @ApiOperation(value = "领取问卷积分", notes = "领取问卷积分")
    public BaseResp receivePoint(@RequestBody BaseReq<QueryTopicListReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        QueryTopicListReq reqData = req.getRequest();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        String channelCode = req.getChannelCode();
        //校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq<QueryTopicListReq>>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //判断是否登录
        boolean flag = this.checkKeyInfo(reqData.getFfpId(), reqData.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //判断问卷是否作答完毕
        PassengerQuestionQueryResp questionQueryResp = queryQuestion(reqData.getFeebackId(),reqData.getFfpId(),reqData.getFfpCardNo());
        if (questionQueryResp == null) {
            resp.setResultCode(WSEnum.NO_DATA.getResultInfo());
            resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
            return resp;
        }
        //问卷查询异常
        boolean topicError = questionQueryResp.getCode() != 0;
        //问卷查询正常但是未完成作答
        boolean topicNotComplete = questionQueryResp.getCode() == 0 && !questionHaveComplete(questionQueryResp.getData());
        if (topicError || topicNotComplete) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请先将问卷作答完毕");
            return resp;
        }
        //构建查询条件
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put(FEEBACK_ID, reqData.getFeebackId());
        jsonObject.put(MEMBER_ID, reqData.getFfpId());
        jsonObject.put(MEMBER_NO, reqData.getFfpCardNo());
        postJson.put(DATA, jsonObject);
        //查询用户保存的证件信息
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                , MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName
                , MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiRequestCert = CRMReqUtil.buildMemberDetailReq(reqData.getFfpCardNo(), reqData.getFfpId(), request, channelCode, items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequestCert);
        //判断账号是否进行实名认证
        if (!CrmUtil.judgeRealNameStatus(ptCRMResponse.getData().getRealVerifyInfos())) {
            resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
            resp.setResultInfo(WSEnum.NO_REAL_NAME.getResultInfo());
            return resp;
        }
        //调用旅客服务网领取积分
        String result = passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/quality/qosStfsFeedback/getHotel");
        if (result != null) {
            PassengerResponse passengerResponse = JsonMapper.buildNormalMapper().fromJson(result, PassengerResponse.class);
            if (passengerResponse.getCode() == 0 && (boolean) passengerResponse.getData()) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(passengerResponse.getMsg());
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("网络请求异常！");
            return resp;
        }

    }


    //排序问题列表答案
    private void sortTopicAnswer(List<TopicDetail> topicList) {
        if (CollectionUtils.isEmpty(topicList)) {
            return;
        }
        topicList.forEach(topicDetail -> {
            List<AnswerDetail> answerList = topicDetail.getAnswerList();
            sortAnswer(answerList);
            answerList.forEach(answerDetail -> sortTopicAnswer(answerDetail.getTopicList()));
            //不满意答案排序
            if (topicDetail.getUnsatisQuestion() != null) {
                List<UnsatisAnswerDetail> unsatisAnswerList = topicDetail.getUnsatisQuestion().getUnsatisAnswerList();
                boolean haveNullUnsatisAnswerNo = haveNullUnsatisAnswerNo(unsatisAnswerList);
                if (!haveNullUnsatisAnswerNo) {
                    unsatisAnswerList.sort(Comparator.comparing(UnsatisAnswerDetail::getOptionChoose));
                }
            }
        });
    }

    //排序答案
    private void sortAnswer(List<AnswerDetail> answerList) {
        //判断answerlist中是否有null的answerNo
        boolean haveNullAnswerNo = haveNullAnswerNo(answerList);
        if (!haveNullAnswerNo) {
            answerList.sort(Comparator.comparing(AnswerDetail::getAnswerNo));
        }
    }

    //判断是否有空的答案序号
    private Boolean haveNullAnswerNo(List<AnswerDetail> answerList) {
        if (CollectionUtils.isEmpty(answerList)) {
            return true;
        }
        for (AnswerDetail answerDetail : answerList) {
            if (answerDetail.getAnswerNo() == null) {
                return true;
            }
        }
        return false;
    }

    //判断不满意答案集合是否有空的答案序号
    private Boolean haveNullUnsatisAnswerNo(List<UnsatisAnswerDetail> unsatisAnswerList) {
        if (CollectionUtils.isEmpty(unsatisAnswerList)) {
            return true;
        }
        for (UnsatisAnswerDetail answerDetail : unsatisAnswerList) {
            if (answerDetail.getOptionChoose() == null) {
                return true;
            }
        }
        return false;
    }

    //查询问卷
    private PassengerQuestionQueryResp queryQuestion(String feebackId,String memberId, String memberCard) {
        //构建查询条件
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put(FEEBACK_ID, feebackId);
        jsonObject.put("memberId",memberId);//会员id
        jsonObject.put(MEMBER_NO, memberCard); //会员卡号
        postJson.put(DATA, jsonObject);
        //调用旅客服务网查询数据
        String result = passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/quality/qosStfsFeedback/selectTopicByFeebackId");
        //String result = passagerPost(postJson, "http://172.22.3.11:8601/qosStfsFeedback/selectTopicByFeebackId");
        return JsonMapper.buildNormalMapper().fromJson(result, PassengerQuestionQueryResp.class);
    }

    //判断是否领取过积分
    private Boolean haveReceivePoint(String feebackId) {
        //构建查询条件
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put(FEEBACK_ID, feebackId);
        postJson.put(DATA, jsonObject);
        //调用旅客服务网查询数据
        String result = passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/quality/qosStfsFeedback/isSendHotel");
        PassengerResponse passengerResponse = JsonMapper.buildNormalMapper().fromJson(result, PassengerResponse.class);
        if (passengerResponse.getCode() == 0) {
            return (boolean) passengerResponse.getData();
        } else {
            return true;
        }
    }

    //处理空元素集合
    private void dealNullAnswerTopicList(List<TopicDetail> topicList) {
        if (CollectionUtils.isEmpty(topicList)) {
            return;
        }
        for (TopicDetail topicDetail : topicList) {
            List<AnswerDetail> answerList = topicDetail.getAnswerList();
            for (AnswerDetail answerDetail : answerList) {
                if (isEmptyList(answerDetail.getTopicList())) {
                    answerDetail.setTopicList(null);
                }
            }
        }
    }

    //移除选项中与主题干重复的问题
    private void removeDuplicateTopic(List<TopicDetail> topicList, HashSet<String> mainIdList) {
        if (CollectionUtils.isEmpty(topicList) || CollectionUtils.isEmpty(mainIdList)) {
            return;
        }
        topicList.forEach(topicDetail -> topicDetail.getAnswerList().stream().filter(answerDetail -> CollectionUtils.isNotEmpty(answerDetail.getTopicList())).forEach(answerDetail -> {
            List<TopicDetail> answerTopicList = answerDetail.getTopicList();
            for (int i = 0; i < answerTopicList.size(); i++) {
                if (answerTopicList.get(i) != null && mainIdList.contains(answerTopicList.get(i).getId())) {
                    answerTopicList.set(i, null);
                }
            }
        }));
    }

    private boolean isEmptyList(List list) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        for (Object o : list) {
            if (o != null) {
                return false;
            }
        }
        return true;
    }

    //处理题型
    private void dealTopicType(List<TopicDetail> topicList) {
        if (CollectionUtils.isEmpty(topicList)) {
            return;
        }
        for (TopicDetail topicDetail : topicList) {
            if (topicDetail == null) {
                break;
            }
            if ("1".equals(topicDetail.getTopicType())) {
                topicDetail.setTopicContent(topicDetail.getTopicContent() + "(单选)");
            } else if ("2".equals(topicDetail.getTopicType())) {
                topicDetail.setTopicContent(topicDetail.getTopicContent() + "(多选)");
            }
            topicDetail.getAnswerList().forEach(answerDetail -> dealTopicType(answerDetail.getTopicList()));
        }
    }

    private boolean questionHaveComplete(List<QuestionGroup> questionGroups) {
        int emptyTopicListNum = 0;
        for (QuestionGroup questionGroup : questionGroups) {
            //计算有几个空的模块
            if (CollectionUtils.isEmpty(questionGroup.getTopicList())) {
                emptyTopicListNum++;
            }
        }
        //如果空模块个数与总模块个数相同，则表示问卷完成
        return emptyTopicListNum == questionGroups.size();
    }
}
