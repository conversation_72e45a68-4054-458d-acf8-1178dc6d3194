package com.juneyaoair.mobile.filter;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberLevelEnum;
import com.juneyaoair.baseclass.common.base.DeviceInfo;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.comm.AliTokenMemberInfo;
import com.juneyaoair.baseclass.member.comm.ThirdAccountInfo;
import com.juneyaoair.baseclass.member.comm.WXTokenMemberInfo;
import com.juneyaoair.baseclass.member.response.LoginResponse;
import com.juneyaoair.baseclass.response.crm.MemberLoginInfo;
import com.juneyaoair.baseclass.response.crm.MemberLoginResponse;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.interceptor.bean.MobileTokenUserInfo;
import com.juneyaoair.mobile.interceptor.bean.MobileVerifyTokenResp;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.http.ReqUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.VersionNoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

/**
 * <AUTHOR>
 * @description 登陆后过滤敏感信息
 * @date 2018/10/8  13:34.
 */
@Component
public class LoginFilter implements Filter {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("【LoginFilter过滤器】系统自动初始化拦截过滤器..........");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        String uri = httpServletRequest.getRequestURI();
        String contentType = request.getContentType();
        String ip = IPUtil.getIpAddr(httpServletRequest);
        String channelCode = httpServletRequest.getHeader("channelCode");
        String[] array = {ChannelCodeEnum.MWEB.getChannelCode(), ChannelCodeEnum.WEIXIN.getChannelCode(),
                ChannelCodeEnum.WXAPP.getChannelCode(), ChannelCodeEnum.CHECKIN.getChannelCode(),
                ChannelCodeEnum.MP_ALIPAY.getChannelCode(), ChannelCodeEnum.HW5G.getChannelCode(),
                ChannelCodeEnum.ALIPAY_PLUG.getChannelCode(), ChannelCodeEnum.ALIPAY_WINDOW.getChannelCode()
        };
        List<String> channelList = new ArrayList<>(Arrays.asList(array));

        //目前只处理json请求
        if (ReqUtil.isJsonReq(httpServletRequest) && channelList.contains(channelCode)) {
            try {
                //返回参数处理
                ResponseWrapper responseWrapper = new ResponseWrapper(httpServletResponse);
                chain.doFilter(request, responseWrapper);
                //处理返回信息
                ServletOutputStream out = response.getOutputStream();
                try {
                    //取返回的json串
                    byte[] bytes = responseWrapper.getBytes();
                    String result = new String(bytes, "utf-8");
                    //返回给前端显示数据
                    BaseResp<LoginResponse> loginResponseResp = (BaseResp<LoginResponse>) JsonUtil.jsonToBean(result, new TypeToken<BaseResp<LoginResponse>>() {
                    }.getType());
                    if (WSEnum.SUCCESS.getResultCode().equals(loginResponseResp.getResultCode())) {
                        //前端只返回姓名和卡号非敏感信息
                        LoginResponse loginResponse = loginResponseResp.getObjData();
                        MemberLoginResponse memberLoginResponse = loginResponse.getMemberLoginResponse();
                        // 生成token需要使用的对象
                        MemberLoginInfo memberLoginInfo = new MemberLoginInfo();
                        memberLoginInfo.setId(memberLoginResponse.getId());
                        memberLoginInfo.setMemberID(memberLoginResponse.getMemberID());
                        memberLoginInfo.setToken(memberLoginResponse.getToken());
                        ThirdAccountInfo thirdAccountInfo = loginResponse.getThirdPartInfo();
                        // 账号状态校验，判断用户账号是否没有被封禁
                        if (memberLoginInfo != null
                                && memberLoginInfo.getMemberID() != null
                                && memberLoginInfo.getId() != 0) {
                            MobileVerifyTokenResp mobileVerifyTokenResp = TokenUtils.verifyIsClosedNewest(String.valueOf(memberLoginInfo.getId()), memberLoginInfo.getMemberID(), (HttpServletRequest) request);
                            if (!"success".equals(mobileVerifyTokenResp.getCode())) {
                                BaseResp baseResp = new BaseResp();
                                baseResp.setResultCode(WSEnum.TONGDUN_FAIL_LOGIN.getResultCode());
                                baseResp.setResultInfo(mobileVerifyTokenResp.getMessage());
                                baseResp.setErrorInfo(mobileVerifyTokenResp.getMessage());
                                out.write(JsonUtil.objectToJson(baseResp).getBytes("UTF-8"));
                                return;
                            }
                        }
                        //header存token
                        responseWrapper.setHeader("token", recordLoginTokenInfo(channelCode, memberLoginInfo, thirdAccountInfo));
                        responseWrapper.setHeader("Access-Control-Expose-Headers", "token");
                        //if(channelList.contains(channelCode)){
                        //信息过滤
                        memberLoginResponse = getMemberLoginResponse(memberLoginResponse);
                        loginResponse.setMemberLoginResponse(memberLoginResponse);
                        loginResponse.setToken("");
                        loginResponse.setExpiryTime("");
                        log.info("【LoginFilter过滤器】改动后数据：" + JsonUtil.objectToJson(loginResponseResp));
                        out.write(JsonUtil.objectToJson(loginResponseResp).getBytes("UTF-8"));
                        //}
                    } else {
                        out.write(result.getBytes("UTF-8"));
                    }
                } catch (Exception e) {
                    log.error("【LoginFilter过滤器】异常信息", e);
                } finally {
                    out.flush();
                    out.close();
                }

            } catch (Exception e) {
                //filter异常的时候
                log.error("【LoginFilter过滤器】,IP地址:{},请求路径{},发生异常信息", ip, uri, e);
                BaseResp resp = setResult(WSEnum.ERROR.getResultCode(), "请求异常！");
                sendResp(response, resp);
                return;
            }
        }
        //处理移动端(MOBILE)登录业务，只处理json格式的请求
        else if (ReqUtil.isJsonReq(httpServletRequest) && channelCode.equals(ChannelCodeEnum.MOBILE.getChannelCode())) {
            try {
                //请求request处理
                JsonParameterRequestWrapper requestWrapper = new JsonParameterRequestWrapper((HttpServletRequest) request);

                String bodyMessage = requestWrapper.getBodyMessage();
//                BaseReq<DeviceInfo> baseReq = JsonMapper.buildNonNullMapper().fromJson(bodyMessage, BaseReq.class, DeviceInfo.class);
                BaseReq<DeviceInfo> baseReq = (BaseReq<DeviceInfo>) JsonUtil.jsonToBean(bodyMessage, new TypeToken<BaseReq<DeviceInfo>>() {
                }.getType());
                //判断版本号是否小于该版本号，如果小于则直接跳过
                int verInt = VersionNoUtil.toVerInt(requestWrapper.getHeader("clientVersion"));


                //返回参数处理
                ResponseWrapper responseWrapper = new ResponseWrapper(httpServletResponse);
                chain.doFilter(requestWrapper, responseWrapper);
                //处理返回信息
                ServletOutputStream out = response.getOutputStream();
                try {
                    //获取json
                    byte[] bytes = responseWrapper.getBytes();
                    String result = new String(bytes, "utf-8");
                    log.info("【LoginFilter过滤器，移动端登录】改动前返回数据：" + result);
                    //获取数据
                    BaseResp<LoginResponse> loginResponseResp = (BaseResp<LoginResponse>) JsonUtil.jsonToBean(result, new TypeToken<BaseResp<LoginResponse>>() {
                    }.getType());
                    //只有成功才会执行
                    if (WSEnum.SUCCESS.getResultCode().equals(loginResponseResp.getResultCode())) {
                        //根据request获取用户的设备id
                        String deviceId = getDeviceIdIdByDeviceInfo(baseReq);
                        /*if (StringUtils.isBlank(deviceId)){
                            BaseResp resp = setResult(WSEnum.ERROR_REQUEST_PARAMS.getResultCode(), "设备id为空！");
                            out.write(JsonUtil.objectToJson(resp).getBytes("UTF-8"));
                            return;
                        }*/
                        //获取用户信息
                        LoginResponse loginResponse = loginResponseResp.getObjData();
                        MemberLoginResponse originMemberInfo = loginResponse.getMemberLoginResponse();
                        //支付宝会返回成功，但是没有登录信息
                        if (originMemberInfo == null
                                || originMemberInfo.getMemberID() == null
                                || originMemberInfo.getId() == 0) {
                            out.write(JsonUtil.objectToJson(loginResponseResp).getBytes("UTF-8"));
                            out.flush();
                            out.close();
                            return;
                        }
                        // 账号状态校验，判断用户账号是否没有被封禁
                        MobileVerifyTokenResp mobileVerifyTokenResp = TokenUtils.verifyIsClosedNewest(String.valueOf(originMemberInfo.getId()), originMemberInfo.getMemberID(), (HttpServletRequest) request);
                        if (!mobileVerifyTokenResp.getCode().equals("success")) {
                            BaseResp baseResp = new BaseResp();
                            baseResp.setResultCode(WSEnum.TONGDUN_FAIL_LOGIN.getResultCode());
                            baseResp.setResultInfo(mobileVerifyTokenResp.getMessage());
                            baseResp.setErrorInfo(mobileVerifyTokenResp.getMessage());
                            out.write(JsonUtil.objectToJson(baseResp).getBytes("UTF-8"));
                            return;
                        }
                        //如果版本小于该版本则跳出
                        if (verInt < TokenUtils.MOBILE_TOKEN_VERSION) {
                            out.write(JsonUtil.objectToJson(loginResponseResp).getBytes("UTF-8"));
                            out.flush();
                            out.close();
                            return;
                        }
                        //信息过滤
                        MemberLoginResponse memberLoginResponse = getMemberLoginResponse(originMemberInfo);
                        //生成token中的参数
                        MobileTokenUserInfo mobileTokenUserInfo = new MobileTokenUserInfo();
                        mobileTokenUserInfo.setUserId(memberLoginResponse.getId());
                        mobileTokenUserInfo.setMemberId(memberLoginResponse.getMemberID());
                        mobileTokenUserInfo.setDeviceId(deviceId == null ? "" : deviceId);
                        /**
                         * 根据用户id，会员卡号，设备id保存到生成token，并根据用户id和设备id保存到redis中
                         */
                        String token = TokenUtils.mobileCreateTokenAndSaveRedis(channelCode, mobileTokenUserInfo);
                        if (token == null) {
                            throw new Exception("token生成失败");
                        }
                        memberLoginResponse.setToken(token);
                        loginResponse.setMemberLoginResponse(memberLoginResponse);
                        log.info("【LoginFilter过滤器,移动端登录】改动后数据：" + JsonUtil.objectToJson(loginResponseResp));
                        out.write(JsonUtil.objectToJson(loginResponseResp).getBytes("UTF-8"));
                    } else {
                        out.write(JsonUtil.objectToJson(loginResponseResp).getBytes("UTF-8"));
                    }
                } catch (Exception e) {
                    log.error("【LoginFilter过滤器】异常信息", e);
                    BaseResp resp = setResult(WSEnum.ERROR.getResultCode(), "请求异常！");
                    out.write(JsonUtil.objectToJson(resp).getBytes("UTF-8"));
                } finally {
                    out.flush();
                    out.close();
                }
            } catch (Exception e) {
                //filter异常的时候
                log.error("【LoginFilter过滤器，移动端登录】,IP地址:{},请求路径{},发生异常信息", ip, uri, e);
                BaseResp resp = setResult(WSEnum.ERROR.getResultCode(), "请求异常！");
                sendResp(response, resp);
                return;
            }
        } else {
            log.info("【LoginFilter过滤器】,IP地址:{},请求路径{},contentType:{},非指定的json请求", ip, uri, contentType);
            chain.doFilter(request, response);
        }
    }

    private MemberLoginResponse getMemberLoginResponse(MemberLoginResponse originMemberInfo) {
        MemberLoginResponse memberLoginResponse = new MemberLoginResponse();
        memberLoginResponse.setName(originMemberInfo.getName());
        memberLoginResponse.setTitle(originMemberInfo.getTitle());
        memberLoginResponse.setLevelName(MemberLevelEnum.findLevelNameByLevelCode(originMemberInfo.getMemberLevelCode()));
        memberLoginResponse.setMemberLevelCode(originMemberInfo.getMemberLevelCode());
        memberLoginResponse.setcLastName(originMemberInfo.getcLastName());
        memberLoginResponse.setcFirstName(originMemberInfo.getcFirstName());
        memberLoginResponse.seteFirstName(originMemberInfo.geteFirstName());
        memberLoginResponse.seteLastName(originMemberInfo.geteLastName());
        memberLoginResponse.setMemberID(originMemberInfo.getMemberID());
        memberLoginResponse.setMemberTel(originMemberInfo.getMemberTel());
        memberLoginResponse.setSex(originMemberInfo.getSex());
        memberLoginResponse.setMemberEmail(originMemberInfo.getMemberEmail());
        //TODO 为了兼容新老手机网站，暂时返回这些参数
        memberLoginResponse.setId(originMemberInfo.getId());
        memberLoginResponse.setLoginKeyInfo(originMemberInfo.getLoginKeyInfo());
        memberLoginResponse.setCustomerCertificateInfos(originMemberInfo.getCustomerCertificateInfos());
        return memberLoginResponse;
    }

    /**
     * 根据request获取设备id
     *
     * @param baseReq
     * @return 设备id
     * @throws IOException
     */
    private String getDeviceIdIdByDeviceInfo(BaseReq<DeviceInfo> baseReq) throws IOException {
        DeviceInfo deviceInfo = baseReq.getRequest();
        return deviceInfo.getDeviceId();
    }

    @Override
    public void destroy() {
        log.info("【LoginFilter过滤器】系统自动销毁拦截过滤器..........");
    }

    /**
     * 设置输出的结果
     *
     * @param msg
     * @return
     */
    private BaseResp setResult(String code, String msg) {
        BaseResp resp = new BaseResp();
        resp.setResultCode(code);
        resp.setResultInfo(msg);
        return resp;
    }

    /**
     * 输出
     *
     * @param response
     * @param obj
     * @throws IOException
     */
    private void sendResp(ServletResponse response, BaseResp obj) throws IOException {
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.print(JsonUtil.objectToJson(obj));
        writer.close();
        response.flushBuffer();
    }

    private String recordLoginTokenInfo(String channelCode, MemberLoginInfo memberLoginInfo, ThirdAccountInfo thirdAccountInfo) {
        Map map = new HashMap<>();
        if (!ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode) && !ChannelCodeEnum.CHECKIN.getChannelCode().equals(channelCode)) {
            map.put(HandlerConstants.TOKEN_MEMBERINFO, JsonUtil.objectToJson(memberLoginInfo));
        } else if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(channelCode) ||
                   ChannelCodeEnum.ALIPAY_PLUG.getChannelCode().equals(channelCode) ||
                   ChannelCodeEnum.ALIPAY_WINDOW.getChannelCode().equals(channelCode)) {
            AliTokenMemberInfo aliTokenMemberInfo = new AliTokenMemberInfo(memberLoginInfo.getId(), memberLoginInfo.getMemberID());
            map.put(HandlerConstants.TOKEN_MEMBERINFO, JsonUtil.objectToJson(aliTokenMemberInfo));
        } else {
            // 小程序渠道token大小限制，只保存用户id和卡号
            WXTokenMemberInfo wxTokenMemberInfo = new WXTokenMemberInfo(memberLoginInfo.getId(), memberLoginInfo.getMemberID());
            map.put(HandlerConstants.TOKEN_MEMBERINFO, JsonUtil.objectToJson(wxTokenMemberInfo));
        }
        if (thirdAccountInfo != null && (ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode) && ChannelCodeEnum.CHECKIN.getChannelCode().equals(channelCode))) {
            map.put(HandlerConstants.WX_OPENID, thirdAccountInfo.getOpenId());
        } else if (thirdAccountInfo != null &&
                (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(channelCode) ||
                 ChannelCodeEnum.ALIPAY_PLUG.getChannelCode().equals(channelCode) ||
                 ChannelCodeEnum.ALIPAY_WINDOW.getChannelCode().equals(channelCode))) {
            map.put(HandlerConstants.USER_ID, thirdAccountInfo.getUid());
        }
        map.put(HandlerConstants.TOKEN_CRMTOKEN, memberLoginInfo.getToken());
        map.put(HandlerConstants.TOKEN_MEMBERCARDNO, memberLoginInfo.getMemberID());
        map.put(HandlerConstants.TOKEN_CHANNELCODE, channelCode);
        String token = TokenUtils.signAndCache(map, memberLoginInfo.getMemberID());
        return token;
    }

}
