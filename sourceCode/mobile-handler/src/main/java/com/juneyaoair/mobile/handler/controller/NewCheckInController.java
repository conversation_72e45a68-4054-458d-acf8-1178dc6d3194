
package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseRequestDTO;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.request.checkin.CheckInOpenTime;
import com.juneyaoair.baseclass.request.checkin.CheckInQuery;
import com.juneyaoair.baseclass.request.checkin.DetrMemInfo;
import com.juneyaoair.baseclass.request.newCheckinSeat.*;
import com.juneyaoair.baseclass.response.newCheckinSeat.*;
import com.juneyaoair.cuss.dto.booking.common.AirportCheckInTimeDTO;
import com.juneyaoair.cuss.dto.booking.common.CheckInInfoDTO;
import com.juneyaoair.cuss.dto.booking.common.DetrTicketFlightDTO;
import com.juneyaoair.cuss.dto.booking.common.DetrTicketsDTO;
import com.juneyaoair.cuss.dto.booking.request.checkin.*;
import com.juneyaoair.cuss.dto.booking.request.ume.GetBoardingPassRequestDTO;
import com.juneyaoair.cuss.dto.booking.request.ume.GetBoardingPassStatusRequestDTO;
import com.juneyaoair.cuss.dto.booking.response.checkin.*;
import com.juneyaoair.cuss.dto.booking.response.ume.GetBoardingPassResponseDTO;
import com.juneyaoair.cuss.dto.booking.response.ume.GetBoardingPassStatusResponseDTO;
import com.juneyaoair.cuss.dto.booking.response.ume.UMECheckInTravelInfo;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.thirdentity.request.checkin.QueryCheckInTicketRequest;
import com.juneyaoair.thirdentity.response.checkin.CheckInTicket;
import com.juneyaoair.thirdentity.response.checkin.QueryCheckInTicketResp;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 16:02 2018/6/26
 * @Modified by:
 */
@RequestMapping("newCheckInService")
@RestController
@Api(value = "NewCheckInController")
public class NewCheckInController extends BassController {
    /**最新一次检验章状态
     *
     */
    private static final String LAST_UPDATE_STATUS_IMG_URL = "NEWCHECKIN:BOARDINGPASS:STATUSIMGURL";
    /**
     * CheckIn访问类型
     */
    private static final String CHECK_IN_TYPE = "CheckIn";
    /**
     * 值机开放时间
     */
    private static final String REDIS_CHECK_IN_OPEN_TIME = "checkInOpenTime";

    private static final String RESULT = "result";

    @Autowired
    private LocalCacheService localCacheService;
    /**
     * 获取当前旅客的所有行程的航班信息
     *
     */
    @RequestMapping(value = "/getCheckInInfo", method = RequestMethod.POST)
    @ApiOperation(value = "获取当前旅客的所有行程的航班信息", notes = "获取当前旅客的所有行程的航班信息")
    public CheckInInfoResponse getCheckInInfo(@RequestBody DetrMemInfo detrMemInfo, HttpServletRequest request) {
        CheckInInfoResponse resultResp = new CheckInInfoResponse();
        String reqId = StringUtil.newGUID() + "_getCheckInInfo";
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(detrMemInfo.getIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = detrMemInfo.getIp();
            if (!this.checkToken(detrMemInfo.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                resultResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resultResp.setErrorInfo("您的访问出意外啦！");
                return resultResp;
            }
        }
        if (!this.chkDayVisit(clientIp, HandlerConstants.CHECKIN_SOURCE, CHECK_IN_TYPE, 120)) {
            resultResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resultResp.setErrorInfo("访问过于频繁，请稍后再试！");
            return resultResp;
        }
        if(!detrMemInfo.getChannelCode().equals(HandlerConstants.PDA_CHANNEL_CODE)){
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(detrMemInfo.getFfpId(), detrMemInfo.getLoginKeyInfo(), detrMemInfo.getChannelCode().equals("PDA") ? HandlerConstants.M_CHANNEL_CODE : detrMemInfo.getChannelCode());
            if (!flag) {
                resultResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resultResp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultCode());
                return resultResp;
            }
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<DetrMemInfo>> violations = validator.validate(detrMemInfo);
            if (CollectionUtils.isNotEmpty(violations)) {
                resultResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resultResp.setErrorInfo(violations.iterator().next().getMessage());
                return resultResp;
            }
        }
        //1.0查询当前旅客所有行程的航班信息
        BaseRequestDTO<SyprAllRequestDTO> baseSyprRequestDTO = new BaseRequestDTO<>();
        baseSyprRequestDTO.setIp(clientIp);
        baseSyprRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        baseSyprRequestDTO.setChannelCode(getChannelInfo(detrMemInfo.getChannelCode(), "50"));
        baseSyprRequestDTO.setUserNo(getChannelInfo(detrMemInfo.getChannelCode(), "10"));
        SyprAllRequestDTO syprAllRequest = new SyprAllRequestDTO();
        syprAllRequest.setCertificateType(detrMemInfo.getCertificateType());
        syprAllRequest.setCertificateNo(detrMemInfo.getCertificateNumber());
        baseSyprRequestDTO.setRequest(syprAllRequest);
        log.debug("请求号:{} 请求IP：{},当前获取情况：{},证件号：{},渠道:{}", reqId, clientIp, this.getClientIPStr(request), detrMemInfo.getCertificateNumber(), getChannelInfo(detrMemInfo.getChannelCode(), "50"));
        HttpResult syprResult;
        try {
            syprResult = this.doPostClient(baseSyprRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_SYPR_ALL);
            log.info("请求号:{},查询当前旅客所有行程的航班信息结果:{}", reqId, syprResult);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resultResp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resultResp.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return resultResp;
        }
        BaseResultDTO<SyprAllResponseDTO> syprResp = JsonMapper.buildNonNullMapper().fromJson(syprResult.getResponse(), BaseResultDTO.class, SyprAllResponseDTO.class);
        if (null == syprResp || !"1001".equals(syprResp.getResultCode())) {
            resultResp.setResultCode(WSEnum.NO_DATA.getResultCode());
            if (null == syprResp) {
                resultResp.setErrorInfo("接口查询出错2!");
            } else {
                resultResp.setErrorInfo(syprResp.getErrorMsg());
            }
            return resultResp;
        }
        //2.0验证姓名
        SyprAllResponseDTO syprAllResponse = syprResp.getResult();
        try {
            if(null == syprAllResponse || CollectionUtils.isEmpty(syprAllResponse.getSyprList())){
                resultResp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resultResp.setErrorInfo("未查到可值机客票或者不在可值机时间范围");
                return resultResp;
            }
            Map<String, String> map = checkPsgName(detrMemInfo.getPassengerNm(), syprAllResponse.getSyprList());
            if (!map.get(RESULT).equals("OK")) {
                resultResp.setErrorInfo(map.get("message"));
                resultResp.setResultCode(WSEnum.ERROR.getResultCode());
                return resultResp;
            }
        } catch (Exception e) {
            log.error("姓名校验出现错误", e);
            resultResp.setResultCode(WSEnum.ERROR.getResultCode());
            resultResp.setErrorInfo("姓名校验出现错误");
            return resultResp;
        }

        CheckInInfoDetail detail = null;
        List<CheckInInfoDetail> details = new ArrayList<>();
        //获取机场信息
        for(CheckInInfoDTO checkInInfoDTO : syprAllResponse.getSyprList()){
            detail = new CheckInInfoDetail();
            AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(checkInInfoDTO.getDepAirport(),checkInInfoDTO.getFlightDate());
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(checkInInfoDTO.getArrAirport(),checkInInfoDTO.getFlightDate());
            BeanUtils.copyNotNullProperties(checkInInfoDTO,detail);
            detail.setDepAirportName(depAirportInfo.getAirPortName());
            detail.setArrAirportName(arrAirportInfo.getAirPortName());
            detail.setDepCityName(depAirportInfo.getCityName());
            detail.setArrCityName(arrAirportInfo.getCityName());
            details.add(detail);
        }
        resultResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resultResp.setCheckInInfoList(details);
        return resultResp;
    }

    //

    //验证姓名
    private Map<String, String> checkPsgName(String name, List<CheckInInfoDTO> list) {
        Map<String, String> result = new HashMap<>();
        //中文姓名
        String regex = "^[\\u4e00-\\u9fa5]+(·[\\u4e00-\\u9fa5]+)*$";
        //特殊旅客姓名
        String specialPsg = name + "\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*(MS)?\\s*(MR)?\\s*(MISS)?\\s*(GM)?\\s*";
        String psgName = "";
        int cnt = 0;
        for (CheckInInfoDTO checkInInfo : list) {
            if (name.matches(regex)) {
                //中文
                psgName = checkInInfo.getPsrName();
            } else {
                //英文姓名
                psgName = checkInInfo.getPsrEnName();
            }
            if (psgName.matches(specialPsg)) {
                cnt++;
            } else {
                log.error("姓名不匹配，输入为：{},航段信息中为：{}", name, psgName);
            }
        }
        if (cnt > 0) {
            result.put(RESULT, "OK");
        } else {
            result.put(RESULT, "fail");
            result.put("message", "存在航段姓名不匹配");
        }
        return result;
    }

    /**
     *  获取航班旅客信息
     */
    @RequestMapping(value = "/getFlightPsgInfo", method = RequestMethod.POST)
    @ApiOperation(value = "获取航班旅客信息", notes = "获取航班旅客信息")
    public CheckInInfoResponse getFlightPsgInfo(@RequestBody QueryPsgCheckInfoRequest queryPsgCheckInfoRequest, HttpServletRequest request) {
        CheckInInfoResponse resp = new CheckInInfoResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryPsgCheckInfoRequest>> violations = validator.validate(queryPsgCheckInfoRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(queryPsgCheckInfoRequest.getFfpId(), queryPsgCheckInfoRequest.getLoginKeyInfo(), queryPsgCheckInfoRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }

        String clientIp = "";
        if (StringUtil.isNullOrEmpty(queryPsgCheckInfoRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = queryPsgCheckInfoRequest.getClientIp();
        }
        BaseRequestDTO<SyprRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        baseRequestDTO.setChannelCode(getChannelInfo(queryPsgCheckInfoRequest.getChannelCode(), "50"));
        baseRequestDTO.setUserNo(getChannelInfo(queryPsgCheckInfoRequest.getChannelCode(), "10"));
        SyprRequestDTO syprRequestDTO = new SyprRequestDTO();
        BeanUtils.copyNotNullProperties(queryPsgCheckInfoRequest,syprRequestDTO);
        baseRequestDTO.setRequest(syprRequestDTO);
        HttpResult result;
        try {
            result = this.doPostClient(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_SYPR);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return resp;
        }
        BaseResultDTO<SyprResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, SyprResponseDTO.class);
        if (!"1001".equals(res.getResultCode())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(res.getErrorMsg());
            return resp;
        }
        List<CheckInInfoDTO> list = (List<CheckInInfoDTO>) res.getResult();
        CheckInInfoDetail detail = null;
        List<CheckInInfoDetail> details = new ArrayList<>();
        //获取机场信息
        for(CheckInInfoDTO checkInInfoDTO : list){
            detail = new CheckInInfoDetail();
            AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(checkInInfoDTO.getDepAirport(),checkInInfoDTO.getFlightDate());
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(checkInInfoDTO.getArrAirport(),checkInInfoDTO.getFlightDate());
            BeanUtils.copyNotNullProperties(checkInInfoDTO,detail);
            detail.setDepAirportName(depAirportInfo.getAirPortName());
            detail.setArrAirportName(arrAirportInfo.getAirPortName());
            detail.setDepCityName(depAirportInfo.getCityName());
            detail.setArrCityName(arrAirportInfo.getCityName());
            details.add(detail);
        }
        resp.setCheckInInfoList(details);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }


    /**
     * 提取办理值机座位信息
     */
    @RequestMapping(value = "/getSeatChart", method = RequestMethod.POST)
    @ApiOperation(value = "提取办理值机座位信息", notes = "提取办理值机座位信息")
    public FlightSeatChartInfoResp getSeat(@RequestBody SeatChartMapQuery seatChart, HttpServletRequest request) {
        FlightSeatChartInfoResp resp = new FlightSeatChartInfoResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<SeatChartMapQuery>> violations = validator.validate(seatChart);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(seatChart.getFfpId(), seatChart.getLoginKeyInfo(), seatChart.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }

        String clientIp = "";
        if (StringUtil.isNullOrEmpty(seatChart.getIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = seatChart.getIp();
        }

        BaseRequestDTO<SeatMapRequestDTO> baseSeatMapRequest = new BaseRequestDTO<>();
        SeatMapRequestDTO seatMapRequest = new SeatMapRequestDTO();
        baseSeatMapRequest.setIp(clientIp);
        baseSeatMapRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        baseSeatMapRequest.setChannelCode(getChannelInfo(seatChart.getChannelCode(), "50"));
        baseSeatMapRequest.setUserNo(getChannelInfo(seatChart.getChannelCode(), "10"));
        seatMapRequest.setArrAirportCode(seatChart.getToCity());
        seatMapRequest.setDepAirportCode(seatChart.getFromCity());
        seatMapRequest.setFlightClass(seatChart.getFlightClass());
        seatMapRequest.setFlightDate(seatChart.getFlightDate());
        seatMapRequest.setFlightNo(seatChart.getFlightNo());
        baseSeatMapRequest.setRequest(seatMapRequest);
        HttpResult result;
        try {
            result = this.doPostClient(baseSeatMapRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_QUERY_SEAT_MAP);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return resp;
        }
        BaseResultDTO<SeatMapResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, SeatMapResponseDTO.class);
        if (null == res || res.getResult().getSeatMapList().isEmpty()) {
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return resp;
        }
        BeanUtils.copyProperties(res.getResult(), resp);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }

    /**
     * 单人值机
     */
    @RequestMapping(value = "/checkIn", method = RequestMethod.POST)
    @ApiOperation(value = "单人值机", notes = "单人值机")
    public SingleCheckInInfoResponse doCheckIn(@RequestBody CheckInInfoRequest checkInInfo, HttpServletRequest request) {
        SingleCheckInInfoResponse resp = new SingleCheckInInfoResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInInfoRequest>> violations = validator.validate(checkInInfo);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(checkInInfo.getFfpId(), checkInInfo.getLoginKeyInfo(), checkInInfo.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (checkInInfo.getChannelCode().equals(HandlerConstants.W_CHANNEL_CODE) && ("").equals(checkInInfo.getWxOpenId())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("渠道信息错误：只能通过微信值机!");
            return resp;
        }
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(checkInInfo.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = checkInInfo.getClientIp();
            if (!this.checkToken(checkInInfo.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("您的请求出意外啦！");
                return resp;
            }
        }
        //值机请求参数
        if(!StringUtil.isNullOrEmpty(checkInInfo.getSnrCode())){
            checkInInfo.setSnr( checkInInfo.getSnrCode().equals("Y"));
        }
        if(!StringUtil.isNullOrEmpty(checkInInfo.getChdCode())){
            checkInInfo.setSnr(checkInInfo.getChdCode().equals("CHD"));
        }

        BaseRequestDTO<PsrCheckInRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        baseRequestDTO.setChannelCode(getChannelInfo(checkInInfo.getChannelCode(), "50"));
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        baseRequestDTO.setUserNo(getChannelInfo(checkInInfo.getChannelCode(), "10"));
        PsrCheckInRequestDTO checkinBean = new PsrCheckInRequestDTO();
        BeanUtils.copyNotNullProperties(checkInInfo, checkinBean);
        baseRequestDTO.setRequest(checkinBean);
        checkinBean.setMemberId(checkInInfo.getFfpId());
        checkinBean.setMemberNo(checkInInfo.getCardNo());
        String logStr = JsonUtil.objectToJson(baseRequestDTO);
        log.info("请求值机:{}", logStr);
        HttpResult result;
        try {
            result = this.doPostClient(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_PSR_CHECK_IN);
            log.info("请求值机结果:{}", result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setErrorInfo("请求值机失败");
            return resp;
        }
        BaseResultDTO<PsrCheckInResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, PsrCheckInResponseDTO.class);
        if (res != null && res.getResultCode().equals("1001") && !StringUtil.isNullOrEmpty(res.getResult().getBoardStream())) {
            this.clearDayVisit("iplimit", clientIp, HandlerConstants.CHECKIN_SOURCE, CHECK_IN_TYPE);
            //获取机场信息
            Map<String,AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(),clientIp);
            //国际国内标识
            resp.setTripType(outInternational(checkInInfo.getDepAirportCode(),checkInInfo.getArrAirportCode(),airportMap));
            BeanUtils.copyNotNullProperties(res.getResult(), resp);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info("请求值机结果:{}", logStr2);
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR_CHECK_NULL.getResultCode());
            resp.setErrorInfo(null == res ? "值机发生错误，请重新值机." : "值机发生错误" + res.getErrorMsg());
            return resp;
        }
    }


    private String outInternational(String depAirPort, String arrAirPort, Map<String, AirPortInfoDto> airportMap) {
        try {
            AirPortInfoDto dep = airportMap.get(depAirPort);
            AirPortInfoDto arr = airportMap.get(arrAirPort);
            if (HandlerConstants.TRIP_TYPE_I.equals(dep.getIsInternational()) || HandlerConstants.TRIP_TYPE_I.equals(arr.getIsInternational())) {
                return HandlerConstants.TRIP_TYPE_I;
            } else {
                return HandlerConstants.TRIP_TYPE_D;
            }
        } catch (Exception e) {
            return HandlerConstants.TRIP_TYPE_D;
        }
    }
    /**
     * 取消值机
     * @see CheckInSeatController#cancelCheckIn
     */
    @RequestMapping(value = "/cancelCheckIn", method = RequestMethod.POST)
    @ApiOperation(value = "取消值机", notes = "取消值机")
    @Deprecated
    public CancelCheckInResponse doConcelCheckIn(@RequestBody CancelCheckInInfoRequest conCheckInInfo, HttpServletRequest request) {
        CancelCheckInResponse resp = new CancelCheckInResponse();
        String logStr = JsonUtil.objectToJson(conCheckInInfo);
        log.debug("取消值机request:{}", logStr);
        //验证IP
        String clientIp = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CancelCheckInInfoRequest>> violations = validator.validate(conCheckInInfo);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(conCheckInInfo.getFfpId(), conCheckInInfo.getLoginKeyInfo(), conCheckInInfo.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //取消值机参数封装
        BaseRequestDTO<CancelPsrRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequestDTO.setChannelCode(getChannelInfo(conCheckInInfo.getChannelCode(), "50"));
        baseRequestDTO.setUserNo(getChannelInfo(conCheckInInfo.getChannelCode(), "10"));
        CancelPsrRequestDTO cancelPsrRequestDTO = new CancelPsrRequestDTO();
        BeanUtils.copyNotNullProperties(conCheckInInfo, cancelPsrRequestDTO);
        baseRequestDTO.setRequest(cancelPsrRequestDTO);
        HttpResult result;
        try {
            result = this.doPostClient(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_CANCEL_PSR);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return resp;
        }
        BaseResultDTO res = (BaseResultDTO) JsonUtil.jsonToBean(result.getResponse(), BaseResultDTO.class);
        if (null != res && ("1001").equals(res.getResultCode())) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR_CON_CHECK_NULL.getResultCode());
            if (null != res) {
                resp.setErrorInfo(res.getErrorMsg());
            }
            return resp;
        }
    }

    /**
     * 旅客值机信息查询
     */
    @RequestMapping(value = "/queryCheckinInfo", method = RequestMethod.POST)
    @ApiOperation(value = "旅客值机信息查询", notes = "旅客值机信息查询")
    public SingleCheckInInfoResponse queryCheckinInfo(@RequestBody CheckInQueryRequest checkInQueryRequest, HttpServletRequest request) {
        SingleCheckInInfoResponse resp = new SingleCheckInInfoResponse();
        String logStr = JsonUtil.objectToJson(checkInQueryRequest);
        log.debug("旅客值机信息查询request:{}", logStr);
        //验证IP
        String clientIp = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInQueryRequest>> violations = validator.validate(checkInQueryRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(checkInQueryRequest.getFfpId(), checkInQueryRequest.getLoginKeyInfo(), checkInQueryRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }

        //参数封装
        BaseRequestDTO<CheckInInfoQueryRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequestDTO.setChannelCode(getChannelInfo(checkInQueryRequest.getChannelCode(), "50"));
        baseRequestDTO.setUserNo(getChannelInfo(checkInQueryRequest.getChannelCode(), "10"));
        CheckInInfoQueryRequestDTO checkInInfoQueryRequestDTO = new CheckInInfoQueryRequestDTO();
        BeanUtils.copyNotNullProperties(checkInQueryRequest, checkInInfoQueryRequestDTO);
        HttpResult result;
        try {
            result = this.doPostClient(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_QUERY_CHECK_IN_INFO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return resp;
        }
        BaseResultDTO<CheckInInfoResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, CheckInInfoResponseDTO.class);
        if (null != res && ("1001").equals(res.getResultCode())) {
            BeanUtils.copyNotNullProperties(res.getResult(),resp);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR_CON_CHECK_NULL.getResultCode());
            if (null != res) {
                resp.setErrorInfo(res.getErrorMsg());
            }
            return resp;
        }
    }

    /**
     * 获取天气信息
     */
    @RequestMapping(value = "/queryWeather", method = RequestMethod.POST)
    @ApiOperation(value = "获取天气信息", notes = "获取天气信息")
    public WeatherResponse queryWeather(@RequestBody WeatherRequest weatherRequest, HttpServletRequest request) {
        WeatherResponse resp = new WeatherResponse();
        String logStr = JsonUtil.objectToJson(weatherRequest);
        log.debug("获取天气信息request:{}", logStr);
        //验证IP
        String clientIp = weatherRequest.getClientIp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WeatherRequest>> violations = validator.validate(weatherRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }

        //参数封装
        BaseRequestDTO<WeatherRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequestDTO.setChannelCode(getChannelInfo(weatherRequest.getChannelCode(), "50"));
        baseRequestDTO.setUserNo(getChannelInfo(weatherRequest.getChannelCode(), "10"));
        CheckInInfoQueryRequestDTO checkInInfoQueryRequestDTO = new CheckInInfoQueryRequestDTO();
        BeanUtils.copyNotNullProperties(weatherRequest, checkInInfoQueryRequestDTO);
        HttpResult result;
        try {
            result = this.doPostClient(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_QUERY_WEATHER);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return resp;
        }
        BaseResultDTO<WeatherResponseDTO> weatherResp = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, CheckInInfoResponseDTO.class);
        if (null != weatherResp && ("1001").equals(weatherResp.getResultCode())) {
            BeanUtils.copyNotNullProperties(weatherResp.getResult(),resp);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR_CON_CHECK_NULL.getResultCode());
            if (null != weatherResp) {
                resp.setErrorInfo(weatherResp.getErrorMsg());
            }
            return resp;
        }
    }

    /**
     * 提取已办理或待办理值机数据
     */
    @RequestMapping(value = "/queryCheckInInfo", method = RequestMethod.POST)
    @ApiOperation(value = "提取已办理或待办理值机数据", notes = "提取已办理或待办理值机数据")
    public CheckInInfoResponse getCheckInInfo(@RequestBody CheckInQuery checkInQuery, HttpServletRequest request) {
        CheckInInfoResponse resp = new CheckInInfoResponse();
        QueryCheckInTicketResp queryCheckInTicketResp = new QueryCheckInTicketResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInQuery>> violations = validator.validate(checkInQuery);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String clientIp = this.getClientIP(request);
        String logStr = JsonUtil.objectToJson(checkInQuery);
        log.info("客户端:{} 查询我的机票,请求参数:{}, ", clientIp, logStr);
        BaseRequestDTO<CheckInInfoQueryByMemberIdRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        CheckInInfoQueryByMemberIdRequestDTO checkInInfoQuery = new CheckInInfoQueryByMemberIdRequestDTO();
        checkInInfoQuery.setMemberId(checkInQuery.getFfpId());
        baseRequestDTO.setRequest(checkInInfoQuery);
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequestDTO.setChannelCode(getChannelInfo(checkInQuery.getChannelCode(), "50"));
        baseRequestDTO.setUserNo(getChannelInfo(checkInQuery.getChannelCode(), "10"));
        HttpResult result;
        //获取机场信息
        try {
            //从网上值机取
            String logStr2 = JsonUtil.objectToJson(baseRequestDTO);
            log.debug("根据会员id提取值机数据request：{}", logStr2);
            result = this.doPostClient(baseRequestDTO,HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_QUERY_CHECKININFO_BY_MEMBERID);
            log.debug("根据会员id提取值机数据response：{}", result);
            //从统一订单中获取可办理航班信息
            String userNo = this.getChannelInfo(checkInQuery.getChannelCode(), "10");
            QueryCheckInTicketRequest req = new QueryCheckInTicketRequest(HandlerConstants.VERSION, checkInQuery.getChannelCode(), userNo, checkInQuery.getFfpId());
            HttpResult serviceResult = this.doPost(req, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_CHECKINTICKET);
            if (null != serviceResult && serviceResult.isResult()) {
                queryCheckInTicketResp = (QueryCheckInTicketResp) JsonUtil.jsonToBean(serviceResult.getResponse(), QueryCheckInTicketResp.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询我的机票出错");
            return resp;
        }
        // 网上值机
        BaseResultDTO<CheckInInfoQueryByMemberIdResponseDTO> ckiInfo = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, CheckInInfoQueryByMemberIdResponseDTO.class);
        //两个请求成功返回
        List<CheckInInfoDetail> checkInInfos = new ArrayList<>();
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ckiInfo.getResultCode())|| UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(queryCheckInTicketResp.getResultCode())) {
            //值机系统成功返回
            if ("1001" .equals(ckiInfo.getResultCode())) {
                //处理值机系统中的数据
                String logStr2 = JsonUtil.objectToJson(ckiInfo);
                log.info("遍历值机列表{}", logStr2);
                CheckInInfoQueryByMemberIdResponseDTO checkInInfoQueryResponse = ckiInfo.getResult();
                if (!StringUtil.isNullOrEmpty(checkInInfoQueryResponse.getCheckInInfos())) {
                    for (CheckInInfoDTO cki : checkInInfoQueryResponse.getCheckInInfos()) {
                        CheckInInfoDetail checkInInfo = new CheckInInfoDetail();
                        BeanUtils.copyProperties(cki, checkInInfo);
                        //处理计划起飞时间
                        if (!StringUtil.isNullOrEmpty(checkInInfo.getFlightDate())
                                && !StringUtil.isNullOrEmpty(checkInInfo.getSchDeptTime())) {
                            String hour = checkInInfo.getSchDeptTime().substring(0, 2);
                            String min = checkInInfo.getSchDeptTime().substring(2);
                            checkInInfo.setFlightDate(checkInInfo.getFlightDate() + " " + hour + ":" + min);
                        }
                        AirPortInfoDto deptAirportInfo =  localCacheService.getLocalAirport(checkInInfo.getDepAirport(),checkInInfo.getFlightDate());
                        AirPortInfoDto arrAirportInfo =  localCacheService.getLocalAirport(checkInInfo.getArrAirport(),checkInInfo.getFlightDate());
                        checkInInfo.setDepAirportName(deptAirportInfo.getAirPortName());
                        checkInInfo.setArrAirportName(arrAirportInfo.getAirPortName());
                        checkInInfo.setDepCityName(deptAirportInfo.getCityName());
                        checkInInfo.setArrCityName(arrAirportInfo.getCityName());
                        checkInInfos.add(checkInInfo);
                    }
                }
            }
            //统一订单成功返回
            if ("1001".equals(queryCheckInTicketResp.getResultCode())
                    && !StringUtil.isNullOrEmpty(queryCheckInTicketResp.getCheckInTicketList())) {
                //当前时间
                //处理统一订单中的信息
                List<CheckInInfoDetail> syprListCheckIn = new ArrayList<>();
                String logStr2 = JsonUtil.objectToJson(queryCheckInTicketResp);
                log.info("遍历订单列表:{}", logStr2);
                for (CheckInTicket checkInTicket : queryCheckInTicketResp.getCheckInTicketList()) {
                    boolean flag = false;
                    CheckInInfoDetail sypr;
                    //处理票号的“-”
                    if (checkInTicket.getTicketNo().contains("-")) {
                        checkInTicket.setTicketNo(checkInTicket.getTicketNo().replace("-", ""));
                    }
                    //比对值机时间
                    //遍历列表，已值机的不再填入syprList
                    String stat = returnCheckInOpenTime(checkInQuery.getChannelCode(), checkInTicket.getDepAirport(), checkInTicket.getDepDateTime(), clientIp);
                    if (!stat.equals(HandlerConstants.CHECKIN_STATUS_INVALID)) {
                        for (CheckInInfoDetail temp : checkInInfos) {
                            //存在重复的客票信息
                            if (temp.getEtCode().equals(checkInTicket.getTicketNo())
                                    && temp.getDepAirport().equals(checkInTicket.getDepAirport())
                                    && temp.getArrAirport().equals(checkInTicket.getArrAirport())) {
                                flag = true;
                                break;
                            }
                        }
                        //不存在已值机信息
                        if (!flag) {
                            //将统一订单信息复制到返回对象中
                            sypr = createSYPRSegInfo(checkInTicket);
                            if (sypr != null) {
                                //值机状态
                                sypr.setCheckInStatus(stat);
                                syprListCheckIn.add(sypr);
                                String logStr3 = JsonUtil.objectToJson(sypr);
                                log.info("成功添加{}", logStr3);
                            }
                        }
                    }
                }
                //合并两个结果集
                checkInInfos.addAll(syprListCheckIn);
                Collections.sort(checkInInfos, comparatorSYPRSegInfo());

            }
            String logStr2 = JsonUtil.objectToJson(checkInInfos);
            log.info("排序后的结果:{}", logStr2);
            resp.setCheckInInfoList(checkInInfos);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR_CON_CHECK_NULL.getResultCode());
            resp.setErrorInfo(ckiInfo.getErrorMsg());
            return resp;
        }

    }


    /**
     * 值机开放时间
     */
    @RequestMapping(value = "/checkInTimeInfo", method = RequestMethod.POST)
    @ApiOperation(value = "值机开放时间", notes = "值机开放时间")
    public CheckInOpenTimeResp doCheckInTime(@RequestBody CheckInOpenTime checkInOpenTime, HttpServletRequest request) {
        CheckInOpenTimeResp resp = new CheckInOpenTimeResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInOpenTime>> violations = validator.validate(checkInOpenTime);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String clientIp = this.getClientIP(request);
        String logStr = JsonUtil.objectToJson(checkInOpenTime);
        log.info("客户端:{} 值机开放时间,请求参数:{}", clientIp, logStr);
        BaseRequestDTO<AirportForCheckInTimeRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        AirportForCheckInTimeRequestDTO checkInTimeRequestDTO = new AirportForCheckInTimeRequestDTO();
        checkInTimeRequestDTO.setAll(true);
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequestDTO.setChannelCode(getChannelInfo(checkInOpenTime.getChannelCode(), "50"));
        baseRequestDTO.setUserNo(getChannelInfo(checkInOpenTime.getChannelCode(), "10"));
        baseRequestDTO.setRequest(checkInTimeRequestDTO);
        HttpResult result;
        try {
            result = this.doPostClient(baseRequestDTO,HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_QUERY_CHECKIN_OPEN_TIME);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询值机开放时间出错");
            return resp;
        }
        BaseResultDTO<AirportCheckInTimeResponseDTO> rs = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, AirportCheckInTimeResponseDTO.class);

        if (("1001").equals(rs.getResultCode())) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            List<AirportCheckInTime> list = new ArrayList<>();
            AirportCheckInTime airportCheckInTime = null;
            //获取机场信息
            Map<String,AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(),clientIp);
            for(AirportCheckInTimeDTO openTime : rs.getResult().getAirportCheckInTimes()){
                airportCheckInTime = new AirportCheckInTime();
                BeanUtils.copyNotNullProperties(openTime,airportCheckInTime);
                AirPortInfoDto airPortInfo = airportMap.get(openTime.getAirportCode());
                airportCheckInTime.setAirportName(airPortInfo.getInfo()+"机场");
                airportCheckInTime.setCityName(airPortInfo.getCityName());
                list.add(airportCheckInTime);
            }
            resp.setAirportCheckInTimeList(list);
            apiRedisService.replaceData("CHECK_IN_OPEN_TIME", JsonUtil.objectToJson(resp), 0);
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询出错!");
        }
        return resp;

    }


    /**电子登机数据
     *
     * @param boardingPassRequest
     * @param request
     * @return
     */
    @RequestMapping(value = "/getBoardingPass",method = RequestMethod.POST)
    @ApiOperation(value = "电子登机数据", notes = "电子登机数据")
    public BoardingPassResponse getBoardingPass(@RequestBody BoardingPassRequest boardingPassRequest,  HttpServletRequest request){
        BoardingPassResponse response = new BoardingPassResponse();
        String logStr = JsonUtil.objectToJson(boardingPassRequest);
        log.debug("电子登机数据request:{}", logStr);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BoardingPassRequest>> violations = validator.validate(boardingPassRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String clientIp = this.getClientIP(request);

        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(boardingPassRequest.getFfpId(), boardingPassRequest.getLoginKeyInfo(), boardingPassRequest.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }

        //获取客票状态
        String ticketStatus = "";
        BaseRequestDTO<DetrRequestDTO> detrBaseRequest = new BaseRequestDTO<>();
        DetrRequestDTO detrRequest = new DetrRequestDTO();
        detrRequest.setCertNo(boardingPassRequest.getTktNum());
        detrRequest.setCertType("TN");
        //强制刷新
        detrRequest.setRefresh(true);
        detrBaseRequest.setRequest(detrRequest);
        detrBaseRequest.setIp(clientIp);
        detrBaseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        detrBaseRequest.setChannelCode(getChannelInfo(boardingPassRequest.getChannelCode(), "50"));
        detrBaseRequest.setUserNo(getChannelInfo(boardingPassRequest.getChannelCode(), "10"));
        HttpResult detrResult ;
        try {
            String logStr2 = JsonUtil.objectToJson(detrBaseRequest);
            log.debug("获取客票状态,request:{}", logStr2);
            detrResult = this.doPostClient(detrBaseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_DETR);
            BaseResultDTO<DetrResponseDTO> detrInfo  = JsonMapper.buildNonNullMapper().fromJson(detrResult.getResponse(), BaseResultDTO.class, DetrResponseDTO.class);
            log.debug("获取客票状态,response:{}", detrResult);
            if (null != detrInfo && detrInfo.getResultCode().equals("1001")) {
                for (DetrTicketsDTO detrTicket : detrInfo.getResult().getDetrTickets()) {
                    if (detrTicket.getEtCode().equals(boardingPassRequest.getTktNum())) {
                        for (DetrTicketFlightDTO detrTicketFlight : detrTicket.getDetrTicketFlights()) {
                            if ((detrTicketFlight.getAirlineCode()+detrTicketFlight.getFlightNo()).equals(boardingPassRequest.getHostFlightNo()) &&
                                    detrTicketFlight.getFlightDate().equals(boardingPassRequest.getFlightDate()) &&
                                    detrTicketFlight.getDepAirportCode().equals(boardingPassRequest.getDeptCode()) &&
                                    detrTicketFlight.getArrAirportCode().equals(boardingPassRequest.getDestCode())) {
                                ticketStatus = detrTicketFlight.getTicketStatus();
                            }
                        }
                    }
                }
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo(detrInfo.getErrorMsg());
                return response;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("查询DETR信息出错");
            return response;
        }

        BaseRequestDTO<GetBoardingPassRequestDTO> baseRequest = new BaseRequestDTO<>();
        GetBoardingPassRequestDTO getBoardingPassRequest = new GetBoardingPassRequestDTO();
        BeanUtils.copyNotNullProperties(boardingPassRequest, getBoardingPassRequest);
        getBoardingPassRequest.setTicketStatus(ticketStatus);
        baseRequest.setRequest(getBoardingPassRequest);
        baseRequest.setIp(clientIp);
        baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequest.setChannelCode(getChannelInfo(boardingPassRequest.getChannelCode(), "50"));
        baseRequest.setUserNo(getChannelInfo(boardingPassRequest.getChannelCode(), "10"));
        HttpResult result;
        try {
            String logStr2 = JsonUtil.objectToJson(baseRequest);
            log.debug("获取电子登机数据request:{}", logStr2);
            result = this.doPostClient(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.GET_BOARDING_PASS);
            log.debug("获取电子登机数据response:{}", result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("查询登机牌出错");
            return response;
        }
        BaseResultDTO<GetBoardingPassResponseDTO> res  = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, GetBoardingPassResponseDTO.class);
        if (null != res && ("1001").equals(res.getResultCode())) {
            BeanUtils.copyNotNullProperties(res.getResult(), response);
            UMECheckInTravelInfo travelInfo = response.getCheckinTravelInfo();
            //过滤机场显示
            String regex = "[国际]*机场";
            travelInfo.setDeptAirportName(travelInfo.getDeptAirportName().replaceAll(regex,""));
            travelInfo.setDestAirportName(travelInfo.getDestAirportName().replaceAll(regex,""));
            response.setCheckinTravelInfo(travelInfo);
            //redis取最新一次检验章状态
            String statusImgUrl = apiRedisService.getData(LAST_UPDATE_STATUS_IMG_URL+":"+boardingPassRequest.getTktNum()+boardingPassRequest.getDeptCode()+boardingPassRequest.getDestCode());
            response.setLastUpdatePassStatusUrl(statusImgUrl);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return response;
        } else {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            String errorInfo = null == res || StringUtils.isBlank(res.getErrorMsg()) || !res.getErrorMsg().contains("对不起，该行程不支持电子登机牌。") ? "获取电子登机数据失败" : "对不起，该行程不支持电子登机牌。";
            response.setErrorInfo(errorInfo);
            return response;
        }

    }
    /**
     *  电子登机验讫
     */
    @RequestMapping(value = "/getBoardingPassStatus",method = RequestMethod.POST)
    @ApiOperation(value = "电子登机验讫", notes = "电子登机验讫")
    public BoardingPassStatusResponse getBoardingPassStatus(@RequestBody BoardingPassStatusRequest boardingPassStatusRequest,  HttpServletRequest request){
        BoardingPassStatusResponse response = new BoardingPassStatusResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BoardingPassStatusRequest>> violations = validator.validate(boardingPassStatusRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String clientIp = this.getClientIP(request);

        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(boardingPassStatusRequest.getFfpId(), boardingPassStatusRequest.getLoginKeyInfo(), boardingPassStatusRequest.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultCode());
            return response;
        }

        BaseRequestDTO<GetBoardingPassStatusRequestDTO> baseRequest = new BaseRequestDTO<>();
        GetBoardingPassStatusRequestDTO getBoardingPassStatusRequest = new GetBoardingPassStatusRequestDTO();
        BeanUtils.copyNotNullProperties(boardingPassStatusRequest,getBoardingPassStatusRequest);
        baseRequest.setRequest(getBoardingPassStatusRequest);
        baseRequest.setIp(clientIp);
        baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequest.setChannelCode(getChannelInfo(boardingPassStatusRequest.getChannelCode(), "50"));
        baseRequest.setUserNo(getChannelInfo(boardingPassStatusRequest.getChannelCode(), "10"));
        HttpResult result;
        try {
            result = this.doPostClient(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.GET_BOARDING_PASS_STATUS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return response;
        }
        BaseResultDTO<GetBoardingPassStatusResponseDTO> res  = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, GetBoardingPassStatusResponseDTO.class);
        if (null != res && ("1001").equals(res.getResultCode())) {
            //最新一次检验章地址写入缓存
            apiRedisService.replaceData(LAST_UPDATE_STATUS_IMG_URL+":"+boardingPassStatusRequest.getTktNum()+boardingPassStatusRequest.getDeptCode()+boardingPassStatusRequest.getDestCode(),res.getResult().getStatusImgUrl(),24*60*60L);
            BeanUtils.copyNotNullProperties(res.getResult(),response);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return response;
        } else {
            response.setResultCode(WSEnum.ERROR_CON_CHECK_NULL.getResultCode());
            if (null != res) {
                response.setErrorInfo(res.getErrorMsg());
            }
            return response;
        }

    }


    /**
     * 对返回的SYPRSegInfo进行排序处理
     */
    private Comparator comparatorSYPRSegInfo() {
        //实现一个比较器对list排序处理
        Comparator<CheckInInfoDetail> comparator = (s1, s2) -> {if (!s1.getCheckInStatus().equals(s2.getCheckInStatus())) {
            return s1.getCheckInStatus().compareTo(s2.getCheckInStatus());
        } else { //状态一致的情况下时间倒序
            return s1.getFlightDate().compareTo(s2.getFlightDate());
        }};
        return comparator;
    }

    /**
     * 比较当前时间是否可以值机
     * @param channelCode
     * @param depAirport
     * @param depTime
     * @param clientIp
     * @return
     */
    private String returnCheckInOpenTime(String channelCode,String depAirport, String depTime,String clientIp) {
        String statusNa = HandlerConstants.CHECKIN_STATUS_INVALID;
        String statusIn = HandlerConstants.CHECKIN_STATUS_IN;
        AirportCheckInTimeDTO checkInTime = new AirportCheckInTimeDTO();
        String checkInOpenTime = apiRedisService.getData(REDIS_CHECK_IN_OPEN_TIME);
        if (StringUtil.isNullOrEmpty(checkInOpenTime)) {
            BaseRequestDTO<AirportForCheckInTimeRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
            AirportForCheckInTimeRequestDTO checkInTimeRequestDTO = new AirportForCheckInTimeRequestDTO();
            checkInTimeRequestDTO.setAll(false);
            List<String> airCodes = new ArrayList<>();
            airCodes.add(depAirport);
            checkInTimeRequestDTO.setAirportCodeList(airCodes);
            baseRequestDTO.setIp(clientIp);
            baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
            //渠道编码
            baseRequestDTO.setChannelCode(getChannelInfo(channelCode, "50"));
            baseRequestDTO.setUserNo(getChannelInfo(channelCode, "10"));
            baseRequestDTO.setRequest(checkInTimeRequestDTO);
            HttpResult result;
            try {
                result = this.doPostClient(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_QUERY_CHECKIN_OPEN_TIME);
                BaseResultDTO<AirportCheckInTimeResponseDTO> rs = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, AirportCheckInTimeResponseDTO.class);
                if (("1001").equals(rs.getResultCode())) {
                    AirportCheckInTimeResponseDTO checkInTimeRes = rs.getResult();
                    checkInTime = checkInTimeRes.getAirportCheckInTimes().get(0);
                    apiRedisService.replaceData(REDIS_CHECK_IN_OPEN_TIME, JsonUtil.objectToJson(checkInTime), 3600L * 24);//缓存一天的值机开放信息
                }
            } catch (Exception e) {
                log.error("查询航班值机开放时间出错，" + e.getMessage(), e);
                return statusNa;
            }
        }else{
            checkInTime = (AirportCheckInTimeDTO)JsonUtil.jsonToBean(checkInOpenTime,AirportCheckInTimeDTO.class);
        }
        String openFre = checkInTime.getOpenFre();//1:16:00代表起飞前一天16：00  ； 36代表起飞前36小时
        String openEnd = checkInTime.getOpenEnd();
        if(StringUtil.isNullOrEmpty(openEnd) || StringUtil.isNullOrEmpty(openFre)){
            return statusNa;
        }
        //时间处理
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date checkInDateStart = null;
        Date checkInDatEnd = null;
        Calendar calendar = Calendar.getInstance();
        Date depDate = DateUtils.toDate(depTime, "yyyy/MM/dd HH:mm:ss");
        calendar.setTime(depDate);
        try {
            //处理开始值机时间
            if (openFre.contains(":")) {
                //1:16:00
                String[] openFres = openFre.split(":");
                calendar.add(Calendar.DATE, -1);
                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(openFres[1]));
                calendar.set(Calendar.MINUTE, Integer.parseInt(openFres[2]));
                checkInDateStart = sdf.parse(sdf.format(calendar.getTime()));
            } else {
                long dateLong = calendar.getTimeInMillis() - (long) Integer.parseInt(openFre) * 3600 * 1000;
                checkInDateStart = sdf.parse(sdf.format(dateLong));
            }
            //处理截至时间
            calendar.setTime(depDate);
            long dateLong = calendar.getTimeInMillis() - (long) Integer.parseInt(openEnd) * 60 * 1000;
            checkInDatEnd = sdf.parse(sdf.format(dateLong));
        }catch (Exception e){
            log.error("查询航班值机开放时间转换时间出错，" + e.getMessage(), e);
            return statusNa;
        }
        if (now.after(checkInDateStart) && now.before(checkInDatEnd)) {
            return statusIn;
        }
        return statusNa;
    }

    private CheckInInfoDetail createSYPRSegInfo(CheckInTicket checkInTicket) {
        if (checkInTicket != null) {
            CheckInInfoDetail info = new CheckInInfoDetail();
            AirPortInfoDto deptAirportInfo =  localCacheService.getLocalAirport(checkInTicket.getDepAirport(),DateUtils.toDate(checkInTicket.getDepDateTime(),"yyyy/MM/dd HH:mm:ss"));
            AirPortInfoDto arrAirportInfo =  localCacheService.getLocalAirport(checkInTicket.getArrAirport(),checkInTicket.getDepDateTime());
            info.setEtCode(checkInTicket.getTicketNo());
            info.setDepAirport(deptAirportInfo.getAirPortCode());
            info.setArrAirport(arrAirportInfo.getAirPortCode());
            info.setDepAirportName(deptAirportInfo.getAirPortName());
            info.setArrAirportName(arrAirportInfo.getAirPortName());
            info.setDepCityName(deptAirportInfo.getCityName());
            info.setArrCityName(arrAirportInfo.getCityName());
            //处理时间格式2016-10-22 22:00
            if (!StringUtil.isNullOrEmpty(checkInTicket.getDepDateTime())) {
                //此方式漏洞，不标准的时间处理不好如2016/1/22 22:00
                info.setFlightDate(DateUtils.convertDateToString(DateUtils.toDate(checkInTicket.getDepDateTime(), "yyyy/MM/dd HH:mm:ss"), "yyyy-MM-dd HH:mm"));
            }
            info.setPsrName(checkInTicket.getPassengerName());
            info.setFlightNo(checkInTicket.getFlightNo());
            return info;
        } else {
            return null;
        }
    }
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private IBasicService basicService;
}
