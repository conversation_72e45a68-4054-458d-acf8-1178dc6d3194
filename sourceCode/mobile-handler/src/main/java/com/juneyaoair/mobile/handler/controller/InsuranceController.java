package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.geetest.geeguard.sdk.GeetestLib;
import com.geetest.geeguard.sdk.enums.DigestmodEnum;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FlightQueryTypeEnum;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.insurance.InsuranceStatEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.InsureQueryRule;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.newcoupon.req.protocol.OrderCouponDto;
import com.juneyaoair.baseclass.request.booking.InsuranceFlightInfo;
import com.juneyaoair.baseclass.request.booking.InsuranceInfoNew;
import com.juneyaoair.baseclass.request.booking.TicketInfo;
import com.juneyaoair.baseclass.request.booking.TicketInfoCache;
import com.juneyaoair.baseclass.request.insure.InsureSegment;
import com.juneyaoair.baseclass.request.insure.QueryFlightInsureReq;
import com.juneyaoair.baseclass.response.insure.InsureInfo;
import com.juneyaoair.baseclass.ticketInfo.IBETicketInfo;
import com.juneyaoair.baseclass.ticketInfo.IdentityInfo;
import com.juneyaoair.baseclass.ticketInfo.SegmentInfo;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.service.cityandairport.ICityAirportService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.InsuranceService;
import com.juneyaoair.mobile.handler.controller.util.IdentityInfoUtil;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.GeetestService;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IThemeCardService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.AESTool;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MetricLogUtil;
import com.juneyaoair.utils.util.SensitiveInfoHider;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.juneyaoair.utils.StringUtil.doStringListFilter;

/**
 * <AUTHOR>
 * @description
 * @date 2019/8/26  9:22.
 */
@RequestMapping("insurance")
@RestController
@Api(value = "保险服务", description = "保险服务")
@Slf4j
public class InsuranceController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private ICityAirportService airportInfoService;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private LocalCacheService localCacheService;

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    @Autowired
    private GeetestService geetestService;
    @Autowired
    private OrderManage orderManage;

    @Autowired
    private IThemeCardService themeCardService;

    private static final String TICKETNO = "TicketNo";
    private static final String VERSION = "Version";
    private static final String USERNO = "UserNo";
    private static final String SUCCESS_CODE = "1001";
    private static final String LOG_ERROR = "【单独购保】服务异常{}";

    @ApiOperation(value = "保险列表介绍", notes = "保险列表介绍")
    @RequestMapping(value = "/queryInsureList", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryInsureList(@RequestBody BaseReq req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        Map<String, Object> insureMap = new HashMap();
        List<InsureInfo> domesticInsuranceList = new ArrayList<>();
        List<InsureInfo> internationalInsuranceList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(handConfig.getInsureQueryRules())) {
            List<InsureQueryRule> insureQueryRules = (List<InsureQueryRule>)
                    JsonUtil.jsonToBean(handConfig.getInsureQueryRules(), new TypeToken<List<InsureQueryRule>>() {
                    }.getType());
            InsureQueryRule insureQueryRuleD = insureQueryRules.stream().filter(insureQueryRule -> insureQueryRule.getTripTypes().contains(HandlerConstants.TRIP_TYPE_D)).findFirst().orElse(null);
            if (insureQueryRuleD != null) {
                domesticInsuranceList = insureQueryRuleD.getInsureList().stream().filter(insureInfo -> DateUtils.compareDate(DateUtils.toTotalTime(insureInfo.getEffectiveTime()), DateUtils.toTotalTime(insureInfo.getExpireTime()), DateUtils.toTotalTime(DateUtils.getCurrentDateTimeAllStr()))).filter(insureInfo -> "Y".equals(insureInfo.getIsAvailable())).collect(Collectors.toList());
            }
            InsureQueryRule insureQueryRuleI = insureQueryRules.stream().filter(insureQueryRule -> insureQueryRule.getTripTypes().contains(HandlerConstants.TRIP_TYPE_I)).findFirst().orElse(null);
            if (insureQueryRuleI != null) {
                internationalInsuranceList = insureQueryRuleI.getInsureList().stream().filter(insureInfo -> DateUtils.compareDate(DateUtils.toTotalTime(insureInfo.getEffectiveTime()), DateUtils.toTotalTime(insureInfo.getExpireTime()), DateUtils.toTotalTime(DateUtils.getCurrentDateTimeAllStr()))).filter(insureInfo -> "Y".equals(insureInfo.getIsAvailable())).collect(Collectors.toList());
            }
        }
        insureMap.put("domesticInsuranceList", domesticInsuranceList);
        insureMap.put("internationalInsuranceList", internationalInsuranceList);
        resp.setObjData(insureMap);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "航班信息查询", notes = "通过票号姓名查询航班信息，返回包含已购保险信息")
    @RequestMapping(value = "/queryInsuranceFlightInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<IBETicketInfo> queryInsuranceFlightInfo(@RequestBody BaseReq<TicketInfo> req, BindingResult bindingResult, HttpServletRequest request) {
        //响应
        BaseResp<IBETicketInfo> resp = new BaseResp<>();
        //校验数据
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //请求数据
        TicketInfo reqData = req.getRequest();
        String ticketNo = reqData.getTicketNo().replace("-", "");
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String ip = IPUtil.getIpAddr(request);
        GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
        HashMap<String, String> param = new HashMap<>();
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        param.put("digestmod", digestmodEnum.getName());
        param.put("user_id", ip); //网站用户id  设备号
        param.put("client_type", req.getRequest().getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ip); //传输用户请求验证时所携带的IP
        geetestService.validate(gtSdk, reqData, param);
        //度量记录
        JSONObject tags = new JSONObject();
        tags.put("IP", ip);
        tags.put("FfpCardNo", reqData.getFfpCardNo());
        tags.put("ChannelCode", headChannelCode);
        MetricLogUtil.saveMetricLog("保险-客票提取", tags, new BigDecimal(1));
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        InsuranceFlightInfo insuranceFlightInfo = orderManage.getInsuranceFlightInfo(req.getChannelCode(), ticketNo, reqData.getPassengerName(), headMap);
        IBETicketInfo ibeTicketInfo = insuranceFlightInfo.getIBETicketInfoList().get(0);
        //校验客票有效性
        ibeTicketInfo.getSegmentInfoList().forEach(segmentInfo -> {
                    if (!HandlerConstants.OPEN_FOR_USE.equalsIgnoreCase(segmentInfo.getTicketStatus()) && !HandlerConstants.CHECKED_IN.equalsIgnoreCase(segmentInfo.getTicketStatus())) {
                        resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    }
                }
        );
        if (WSEnum.ERROR_CHK_ERROR.getResultCode().equals(resp.getResultCode())) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.INSURANCE_TICKET_ERROR.getResultInfo());
            return resp;
        }
        //校验票号姓名
        String passName = reqData.getPassengerName().toUpperCase();
        //正则表达式
        String patternStr = passName + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*(INF.+)?\\s*)";
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(ibeTicketInfo.getPassengerName());
        if (!matcher.matches()) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("客票信息与乘客姓名不符");
            return resp;
        }
        AtomicReference<String> depCity= new AtomicReference<>("");
        AtomicReference<String> arrCity= new AtomicReference<>("");
        boolean check = ibeTicketInfo.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> {
            if (reqData.getFlightNo().equals(ptSegmentInfo.getFlightNo())) {
                AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode());
                AirPortInfoDto depAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode());
                depCity.set(depAirPort.getCityCode());
                arrCity.set(arrAirPort.getCityCode());
                return true;
            } else {
                return false;
            }
        });
        if (!check) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("未查询到符合条件的客票");
            return resp;
        }

        //暂时限制联程和SPA的保险购买
        if (StringUtils.isNotBlank(ibeTicketInfo.getFollowTicketNo())) {
            resp.setResultCode(WSEnum.INSURANCE_COMBINED_FLIGHT.getResultCode());
            resp.setResultInfo(WSEnum.INSURANCE_COMBINED_FLIGHT.getResultInfo());
            return resp;
        }
        if (ibeTicketInfo.getOrgCity().equals(ibeTicketInfo.getDstCity())) {
            if (ibeTicketInfo.getSegmentInfoList().size() > 2) {
                resp.setResultCode(WSEnum.INSURANCE_COMBINED_FLIGHT.getResultCode());
                resp.setResultInfo(WSEnum.INSURANCE_COMBINED_FLIGHT.getResultInfo());
                return resp;
            }
        } else {
            if (ibeTicketInfo.getSegmentInfoList().size() > 1) {
                resp.setResultCode(WSEnum.INSURANCE_COMBINED_FLIGHT.getResultCode());
                resp.setResultInfo(WSEnum.INSURANCE_COMBINED_FLIGHT.getResultInfo());
                return resp;
            }
        }
        //起飞城市为国外不可单独购保
        if (!localCacheService.getLocalAirport(ibeTicketInfo.getSegmentInfoList().get(0).getDepAirportCode()).getIsInternational().equals(HandlerConstants.TRIP_TYPE_D)) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo(WSEnum.INSURANCE_TICKET_ERROR.getResultInfo());
            return resp;
        }
        // 婴儿乘客需要处理姓名
        if (CommonBaseConstants.IBE_PASSENGER_TYPE_INF.equals(ibeTicketInfo.getPassengerType()) && ibeTicketInfo.getPassengerName().contains("INF")) {
            ibeTicketInfo.setPassengerName(passName);
        }

        //设置航班类型
        // 单程：OW；
        // 往返：RT 不仅仅判断起始与目的地城市是否一致，还需判断航段数是否满足往返条件
        if (ibeTicketInfo.getOrgCity().equals(ibeTicketInfo.getDstCity())) {
            if (ibeTicketInfo.getSegmentInfoList().size() % 2 == 0) {
                ibeTicketInfo.setFlightType(HandlerConstants.ROUTE_TYPE_RT);
            } else {
                ibeTicketInfo.setFlightType(HandlerConstants.ROUTE_TYPE_OW);
            }
        } else {
            ibeTicketInfo.setFlightType(HandlerConstants.ROUTE_TYPE_OW);
        }
        //处理保险名称
        List<InsureQueryRule> insureQueryRules = insuranceService.getInsuranceList();
        Set<InsureInfo> insureInfoList = new HashSet<>();
        //过滤符合条件的保险
        insureQueryRules.stream().filter(insureQueryRule -> doStringListFilter(insureQueryRule.getTripTypes(), ibeTicketInfo.getInterFlag()))
                .filter(insureQueryRule -> doStringListFilter(insureQueryRule.getFlightTypes(), ibeTicketInfo.getFlightType()))
                .forEach(insureQueryRule ->
                        insureQueryRule.getInsureList().stream()
                                .filter(insureInfo -> org.apache.commons.lang3.StringUtils.isNotBlank(insureInfo.getInsId()))
                                .forEach(insureInfoList::add));
        //不可退保id集合
        HashSet<String> insCdSet = new HashSet<>();
        //处理航段中的保险名称
        ibeTicketInfo.getSegmentInfoList().stream()
                .filter(segmentInfo -> CollectionUtils.isNotEmpty(segmentInfo.getInsuranceList()))
                .forEach(segmentInfo -> segmentInfo.getInsuranceList()
                        .forEach(insuranceInfoNew -> {
                            insureInfoList.stream().
                                    filter(insureInfo -> insureInfo.getInsId().equals(insuranceInfoNew.getInsuranceCode()))
                                    .forEach(insureInfo -> {
                                        insuranceInfoNew.setInsuranceName(insureInfo.getInsNm());
                                        insuranceInfoNew.setInsurancePdfURL(insureInfo.getInsDescPdfURL());
                                        //已购保险id集合 成功或未支付时不可重复购保
                                        boolean successFlag = InsuranceStatEnum.SUCCESS.getInsStat().equalsIgnoreCase(insuranceInfoNew.getInsuranceState());
                                        boolean applyFlay = InsuranceStatEnum.APPLY.getInsStat().equalsIgnoreCase(insuranceInfoNew.getInsuranceState());
                                        if (successFlag || applyFlay) {
                                            insCdSet.add(insureInfo.getInsCd());
                                        }
                                    });
                        }));
        //只显示success保险
        List<SegmentInfo> segmentInfoList = ibeTicketInfo.getSegmentInfoList();
        segmentInfoList.stream()
                .filter(segmentInfo -> CollectionUtils.isNotEmpty(segmentInfo.getInsuranceList()))
                .forEach(segmentInfo -> {
                    List<InsuranceInfoNew> insuranceList = segmentInfo.getInsuranceList();
                    ArrayList<InsuranceInfoNew> insuranceInfoNews = new ArrayList<>();
                    final int insListSize = insuranceList.size();
                    for (int i = 0; i < insListSize; i++) {
                        if (InsuranceStatEnum.SUCCESS.getInsStat().equalsIgnoreCase(insuranceList.get(i).getInsuranceState())) {
                            insuranceInfoNews.add(insuranceList.get(i));
                        }
                    }
                    segmentInfo.setInsuranceList(insuranceInfoNews);
                });
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf3 = new SimpleDateFormat("HH:mm");
        ibeTicketInfo.getSegmentInfoList().forEach(segmentInfo -> {
            //设置出发到达机场和城市数据
            String depAirportCode = segmentInfo.getDepAirportCode();
            String arrAirportCode = segmentInfo.getArrAirportCode();
            AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(depAirportCode, segmentInfo.getDepDate());
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(arrAirportCode, segmentInfo.getDepDate());
            segmentInfo.setDepCityName(depAirportInfo.getCityName());
            segmentInfo.setDepAirportName(depAirportInfo.getAirPortName());
            segmentInfo.setArrCityName(arrAirportInfo.getCityName());
            segmentInfo.setArrAirportName(arrAirportInfo.getAirPortName());
            //处理时间
            try {
                segmentInfo.setDepDate(sdf2.format(sdf.parse(segmentInfo.getDepTime())));
                segmentInfo.setArrDate(sdf2.format(sdf.parse(segmentInfo.getArrTime())));
                segmentInfo.setDepHm(sdf3.format(sdf.parse(segmentInfo.getDepTime())));
                segmentInfo.setArrHm(sdf3.format(sdf.parse(segmentInfo.getArrTime())));
                //周几
                String weekStr = DateUtils.getWeekStr(sdf.parse(segmentInfo.getDepTime()));
                segmentInfo.setWeekDay(weekStr);
            } catch (ParseException e) {
                log.error(LOG_ERROR, e);
            }
        });
        //设置乘客类型代码
        String passengerType = ibeTicketInfo.getPassengerType();
        switch (passengerType) {
            case "PASSENGER_ADULT":
                ibeTicketInfo.setPassengerTypeCode(PassengerTypeEnum.ADT.getPassType());
                break;
            case "PASSENGER_CHILD":
                ibeTicketInfo.setPassengerTypeCode(PassengerTypeEnum.CHD.getPassType());
                break;
            case "PASSENGER_INFANT":
                ibeTicketInfo.setPassengerTypeCode(PassengerTypeEnum.INF.getPassType());
                break;
            case "PASSENGER_CHILD_UNACCOMPANIED":
                ibeTicketInfo.setPassengerTypeCode(PassengerTypeEnum.CHD.getPassType());
                break;
            default:
                ibeTicketInfo.setPassengerTypeCode(PassengerTypeEnum.ADT.getPassType());
                break;
        }
        //处理城市代码
        for (SegmentInfo segmentInfo : ibeTicketInfo.getSegmentInfoList()) {
            String depAirportCode = segmentInfo.getDepAirportCode();
            String arrAirportCode = segmentInfo.getArrAirportCode();
            AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(depAirportCode, segmentInfo.getDepDate());
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(arrAirportCode, segmentInfo.getDepDate());
            segmentInfo.setDepCityCode(depAirportInfo.getCityCode());
            segmentInfo.setArrCityCode(arrAirportInfo.getCityCode());
        }
        //查询可购保险
        QueryFlightInsureReq queryFlightInsureReq = new QueryFlightInsureReq();
        queryFlightInsureReq.setFlightType(ibeTicketInfo.getFlightType());
        queryFlightInsureReq.setTripType(ibeTicketInfo.getInterFlag());
        queryFlightInsureReq.setIsSingleBuy("Y");
        queryFlightInsureReq.setFareType("Simple");
        List<InsureSegment> insureSegList = new ArrayList<>();
        ibeTicketInfo.getSegmentInfoList().stream().forEach(segmentInfo -> {
            InsureSegment insureSegment = new InsureSegment();
            insureSegment.setSegNO(segmentInfo.getSegmentIndex());
            insureSegment.setTicketStatus(segmentInfo.getTicketStatus());
            insureSegment.setFlightDate(segmentInfo.getDepDate());
            insureSegment.setDepDateTime(segmentInfo.getDepTime());
            insureSegment.setCabin(segmentInfo.getCabin());
            insureSegment.setDepAirport(segmentInfo.getDepAirportCode());
            insureSegment.setArrAirport(segmentInfo.getArrAirportCode());
            insureSegList.add(insureSegment);
        });
        queryFlightInsureReq.setInsureSegList(insureSegList);

        ibeTicketInfo.setNewProcessInsurance("Y".equalsIgnoreCase(handConfig.getIsNewProcessInsurance()));

        //临期次卡保险需要屏蔽国内和国际退票险
        List<OrderCouponDto> orderCouponDtoList = themeCardService.queryTicketRedeem(ticketNo, depCity.get(),arrCity.get());
        if (CollectionUtils.isNotEmpty(orderCouponDtoList)) {
            //临期次卡
            List<OrderCouponDto> expiringCard = orderCouponDtoList.stream().filter(orderCouponDto -> VoucherTypesEnum.EXPIRING_CARD.getCode().equals(orderCouponDto.getCouponSource())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(expiringCard)) {
                queryFlightInsureReq.setQueryType(FlightQueryTypeEnum.OPEN_FREE_TICKET.getType());
            }
        }

        //查询可购保险列表
        List<InsureInfo> insureInfos = insuranceService.queryOptionalInsurabce(queryFlightInsureReq, getClientIP(request));
        //去除已购保险
        insureInfos = insureInfos.stream().filter(insureInfo -> !insCdSet.contains(insureInfo.getInsCd())).collect(Collectors.toList());
        //匹配适用于当前乘客类型的
        insureInfos = insureInfos.stream().filter(insureInfo -> {
            if (StringUtil.isNullOrEmpty(insureInfo.getSuitPassTypeList())) {
                return true;
            } else {
                return insureInfo.getSuitPassTypeList().contains(ibeTicketInfo.getPassengerTypeCode());
            }
        }).collect(Collectors.toList());
        //可购买保险列表
        if (CollectionUtils.isEmpty(insureInfos)) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo(WSEnum.INSURANCE_TICKET_ERROR.getResultInfo());
            return resp;
        }
        HashMap<String, List<InsureInfo>> insMap = new HashMap<>();
        //单规格多规格分组处理
        Map<String, List<InsureInfo>> collect = insureInfos.stream().collect(Collectors.groupingBy(InsureInfo::getIsMultSpec));
        insMap.put("singleSpec", collect.get("N"));
        insMap.put("multSpec", collect.get("Y"));
        ibeTicketInfo.setToBuyInsurance(insMap);
        // 过滤identityInfo.getIdType() 为CertificateTypeEnum 中showCode中的类型、和"ID"
        IdentityInfo identityInfo = IdentityInfoUtil.getIdentityInfoInsure(ibeTicketInfo.getIdentityInfoList());
        if (identityInfo == null) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("客票信息有误，未获取到证件信息，请联系客服95520");
            return resp;
        } else {
            List<IdentityInfo> list = new ArrayList();
            list.add(identityInfo);
            ibeTicketInfo.setIdentityInfoList(list);
        }
        //设置客票信息的缓存，便于单独购保下单时获取出生日期信息 缓存30分钟
        if (StringUtils.isNotEmpty(ibeTicketInfo.getPassengerName()) && StringUtils.isNotEmpty(ibeTicketInfo.getTicketNo())) {
            String redisKey = RedisKeyConfig.createInsuranceFlightInfoPassKey(ibeTicketInfo.getPassengerName(), ibeTicketInfo.getTicketNo());
            apiRedisService.replaceData(redisKey, JsonUtil.objectToJson(ibeTicketInfo), 1800L);
        }
        //客票敏感信息处理
        for (IdentityInfo info : ibeTicketInfo.getIdentityInfoList()) {
            info.setIdNo(SensitiveInfoHider.hideMiddleSensitiveInfo(info.getIdNo()));
        }
        resp.setObjData(ibeTicketInfo);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        try {
            String ticketSign = AESTool.encrypt(ticketNo, handConfig.getHoAesKey(), handConfig.getHoAesKey().substring(0, 16));
            ibeTicketInfo.setIbeTicketSign(Base64.getUrlEncoder().encodeToString(ticketSign.getBytes(StandardCharsets.UTF_8)));
            String insureRedisKey = RedisKeyConfig.createInsuranceFlightInfoKey(ticketNo);
            apiRedisService.replaceData(insureRedisKey, JsonUtil.objectToJson(resp), 30L);
        } catch (Exception e) {
            log.error("{}数据加解密异常:", ticketNo, e);
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "航班信息保险缓存查询", notes = "通过票号姓名查询航班信息，返回包含已购保险信息，缓存来源于queryInsuranceFlightInfo")
    @RequestMapping(value = "/queryInsuranceFlightInfoCache", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<IBETicketInfo> queryInsuranceFlightInfoCache(@RequestBody BaseReq<TicketInfoCache> req, BindingResult bindingResult, HttpServletRequest request) {
        //校验数据
        if (bindingResult.hasErrors()) {
            throw new IllegalArgumentException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        //请求数据
        TicketInfoCache reqData = req.getRequest();
        String ticketSign = reqData.getIbeTicketSign();
        String ticketNo = "";
        try {
            //解决前端编码问题
            ticketSign = new String(Base64.getUrlDecoder().decode(ticketSign), StandardCharsets.UTF_8);
            ticketNo = AESTool.decrypt(ticketSign, handConfig.getHoAesKey(), handConfig.getHoAesKey().substring(0, 16));
        } catch (Exception e) {
            log.error("{}数据解密异常:", ticketSign, e);
            throw new CommonException(WSEnum.ERROR.getResultCode(), "数据解析异常");
        }
        String insureRedisKey = RedisKeyConfig.createInsuranceFlightInfoKey(ticketNo);
        //存在此值时直接从缓存获取
        String data = apiRedisService.getData(insureRedisKey);
        if (StringUtils.isNotBlank(data)) {
            return JsonUtil.fromJson(data, new TypeToken<BaseResp<IBETicketInfo>>() {
            });
        } else {
            throw new CommonException(WSEnum.OPERATION_TIMEOUT_50016.getResultCode(), WSEnum.OPERATION_TIMEOUT_50016.getResultInfo());
        }
    }
}
