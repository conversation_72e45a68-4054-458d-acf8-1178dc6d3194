package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.activity.ActivityVIPExperienceCardEnum;
import com.juneyaoair.appenum.av.PackageTypeEnum;
import com.juneyaoair.appenum.insurance.InsuranceOrderStatEnum;
import com.juneyaoair.appenum.order.*;
import com.juneyaoair.appenum.rightcoupon.DeliveryTypeEnum;
import com.juneyaoair.appenum.rightcoupon.PhoneCardShipWayEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherStateEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.airbookstore.bean.SupportFlightInfo;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.cuss.request.SeatOrderParam;
import com.juneyaoair.baseclass.cuss.response.SeatOrderInfo;
import com.juneyaoair.baseclass.cuss.response.SeatOrderRemark;
import com.juneyaoair.baseclass.cuss.response.SeatOrderResult;
import com.juneyaoair.baseclass.cuss.response.SeatOrderSingle;
import com.juneyaoair.baseclass.flight.request.FlightInfoListReq;
import com.juneyaoair.baseclass.flight.response.FlightInfoListResp;
import com.juneyaoair.baseclass.gateupgrade.response.UpgCouponOrderFlightInfo;
import com.juneyaoair.baseclass.lounge.req.CommCouponOrderReq;
import com.juneyaoair.baseclass.lounge.req.CouponProductCancelReserveVoucherRequestDto;
import com.juneyaoair.baseclass.lounge.req.LoungeCancelReq;
import com.juneyaoair.baseclass.lounge.req.LoungeReq;
import com.juneyaoair.baseclass.lounge.res.CommCouponOrderRes;
import com.juneyaoair.baseclass.lounge.res.CouponProductCancelReserveVoucherResponseDto;
import com.juneyaoair.baseclass.lounge.res.VoucherInfo;
import com.juneyaoair.baseclass.newcoupon.bean.*;
import com.juneyaoair.baseclass.newcoupon.req.*;
import com.juneyaoair.baseclass.newcoupon.req.protocol.InsuranceDetailDto;
import com.juneyaoair.baseclass.newcoupon.req.protocol.InsuranceOrderDto;
import com.juneyaoair.baseclass.newcoupon.req.protocol.NewBasicCouponOrderDetail;
import com.juneyaoair.baseclass.newcoupon.req.protocol.OrderCouponDto;
import com.juneyaoair.baseclass.newcoupon.req.protocol.TicketInsuranceDto;
import com.juneyaoair.baseclass.newcoupon.resp.*;
import com.juneyaoair.baseclass.newcoupon.resp.protocol.ProtocolTrafficInfo;
import com.juneyaoair.baseclass.newcoupon.resp.protocol.ProtocolTrafficRemark;
import com.juneyaoair.baseclass.onBoardWifi.req.PackageWifiParamProduct;
import com.juneyaoair.baseclass.prepayment.common.*;
import com.juneyaoair.baseclass.prepayment.request.CancelBaggageReq;
import com.juneyaoair.baseclass.prepayment.request.RefundCouponInfoDto;
import com.juneyaoair.baseclass.prepayment.response.BaggageOrderInfoResp;
import com.juneyaoair.baseclass.prepayment.response.CancelBaggageResp;
import com.juneyaoair.baseclass.prepayment.response.CouponOrderInfoDto;
import com.juneyaoair.baseclass.prepayment.response.QueryOrderInfoResponse;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.premium.RefundPremiumProduct;
import com.juneyaoair.baseclass.response.order.comm.BoardingPass;
import com.juneyaoair.baseclass.response.order.query.SaleCouponGetResponse;
import com.juneyaoair.baseclass.response.payment.PaymentResp;
import com.juneyaoair.baseclass.salecoupon.bean.*;
import com.juneyaoair.baseclass.salecoupon.response.CouponProduct;
import com.juneyaoair.baseclass.salecoupon.response.CouponShow;
import com.juneyaoair.baseclass.salecoupon.response.SaleCouponGet;
import com.juneyaoair.baseclass.unlimit.UnlimitedCard2Config;
import com.juneyaoair.baseclass.unlimit.UpgradeCardV2Config;
import com.juneyaoair.baseclass.visa.req.CouponProductVisaDictionariesRequestDto;
import com.juneyaoair.baseclass.visa.resp.CouponProductVisaDictionariesResponseDto;
import com.juneyaoair.cuss.dto.booking.ENUM_RESPONSE_STATUS;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.flight.FlightInfoResult;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.FlightService;
import com.juneyaoair.mobile.handler.controller.service.ThemeCardService;
import com.juneyaoair.mobile.handler.controller.util.RightCouponObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.VirtualPaymentConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.controller.v2.util.RightCouponConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.manage.PdiManage;
import com.juneyaoair.mobile.handler.service.CheckInSeatService;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IOrderService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.handler.util.OrderUtil;
import com.juneyaoair.mobile.mapstruct.CouponOrderReqMapping;
import com.juneyaoair.mobile.mongo.entity.ApiErrorLogs;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.mobile.mongo.service.apiErrorLogs.IApiErrorLogsService;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.boardingpass.common.BaseAirport;
import com.juneyaoair.thirdentity.boardingpass.common.BaseCouponOrderIdentity;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.request.PtorRequest;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.comm.response.PtorResponse;
import com.juneyaoair.thirdentity.pdi.PdiCancelOrder;
import com.juneyaoair.thirdentity.pdi.PdiCancelResult;
import com.juneyaoair.thirdentity.pdi.PdiResult;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponOrderGetResponse;
import com.juneyaoair.thirdentity.salecoupon.v2.common.Product;
import com.juneyaoair.util.ControllerUtil;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.ObjCheckUtil;
import com.juneyaoair.utils.util.VersionNoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


/**
 * <AUTHOR> by jiangmingming
 * @date 2019/2/13 14:15
 */
@RequestMapping("/newinterest")
@RestController
@Api(value = "权益服务管理", tags = "权益服务管理")
public class NewInterestVoucherController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private FlightService flightService;
    @Autowired
    private CheckInSeatService checkInSeatService;
    @Autowired
    private OrderManage orderManage;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private PdiManage pdiManage;

    @Autowired
    private ThemeCardService themeCardService;

    @Resource(name = "apiErrorLogsService")
    private IApiErrorLogsService apiErrorLogsServiceImpl;

    private static final int READ_TIMEOUT = 40000;
    private static final int CONNECT_TIMEOUT = 40000;

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_FORMAT_TWO = "yyyy-MM-dd HH:mm";
    private static final String DATE_FORMAT_THREE = "yyyy-MM-dd";
    private static final String LOG_ERROR = "请求号:{}，IP地址:{}，服务端异常：{}";
    private static final String LOG_ERROR_TWO = "数据异常！";
    private static final String LOG_RESP_ONE = "网络请求异常！";
    private static final String LOG_RESP_TWO = "请求号:{}，IP地址:{}，服务端响应：{}";
    private static final String SERVICE_NAME = "权益/服务订单服务";

    @ApiOperation(value = "权益券订单列表和详情", notes = "权益券订单列表和详情")
    @RequestMapping(value = "/queryCouponOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp queryCouponOrder(@RequestBody BaseReq<CouponOrderReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            //检验登录
            CouponOrderReq couponOrderReq = req.getRequest();
            String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
            String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
            /*boolean flag = this.checkKeyInfo(couponOrderReq.getFfpId(), couponOrderReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String respJson = JsonUtil.objectToJson(resp);
                log.info(LOG_RESP_TWO, reqId, ip, respJson);
                return resp;
            }*/
            if (couponOrderReq.getPageNo() <= 0) {
                couponOrderReq.setPageNo(1);
            }
            if (couponOrderReq.getPageSize() <= 0) {
                couponOrderReq.setPageSize(20);
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            //判断有无订单号和渠道订单编号
            //没有调用综合订单查询接口
            //有就走详情接口
            String orderNo = couponOrderReq.getOrderNo();
            String channelOrderNo = couponOrderReq.getChannelOrderNo();
            Map<String, Object> pageMap;
            //订单列表
            if (!StringUtil.isNullOrEmpty(orderNo) || !StringUtil.isNullOrEmpty(channelOrderNo)) {
                //订单详情详情接口
                pageMap = queryOrderDetail(channelCode, userNo, couponOrderReq, request);
            } else {
                //订单列表接口
                List<String> voucherTypes = VoucherTypesEnum.getShowVoucherTypeCodes();
                //主题卡类型动态Apollo的配置
                voucherTypes.addAll(handConfig.getThemeCouponList());
                // VIP体验卡
                voucherTypes.addAll(ActivityVIPExperienceCardEnum.getVipExperienceCardTypeList());
                String queryUrl = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_COUPON_ORDER_LIST;
                if (ChannelCodeEnum.MOBILE.getChannelCode().equals(realChannelCode)) {
                    if (VersionNoUtil.toMVerInt(versionCode) < 62400) {
                        voucherTypes.remove(VoucherTypesEnum.ONBOARDPRODUCT.getCode());
                        voucherTypes.remove(VoucherTypesEnum.BRANDMEALS.getCode());
                    }
                }
                if (ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode) || ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(realChannelCode)) {
                    voucherTypes.remove(VoucherTypesEnum.GUIDESERVICE.getCode());
                    voucherTypes.remove(VoucherTypesEnum.ONBOARDPRODUCT.getCode());
                    voucherTypes.remove(VoucherTypesEnum.BRANDMEALS.getCode());
                }
                pageMap = queryOrderList(queryUrl, channelCode, userNo, couponOrderReq, request, voucherTypes);
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(pageMap);
            return resp;
        } catch (ServiceException se) {
            log.error("权益券订单列表查询异常", se);
            resp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
            resp.setResultInfo(se.getMessage());
            return resp;
        } catch (Exception e) {
            log.error("权益券订单列表查询异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            return resp;
        }
    }

    @ApiOperation(value = "新订单权益券订单详情", notes = "新订单权益券订单详情（/Order/BasicGetCouponOrder）")
    @RequestMapping(value = "/queryCouponOrderDetail/{couponSource}", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<Object> queryCouponOrderDetail(@RequestBody BaseReq<NewCouponOrderDetailReq> req, @PathVariable(value = "couponSource") String couponSource, HttpServletRequest request) {
        BaseResp<Object> resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String realChannelCode = request.getHeader("channelCode");
        String ip = getClientIP(request);
        try {
            // 参数校验
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<NewCouponOrderDetailReq>>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }

            NewCouponOrderDetailReq couponOrderDetail = req.getRequest();
            // 获取公共参数
            String channelCode = req.getChannelCode();
            //TODO 渠道临时转换，待产品平台排查时效慢的问题
            if (StringUtils.isNotEmpty(realChannelCode) && ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode) && ChannelCodeEnum.WEIXIN.getChannelCode().equals(channelCode)) {
                channelCode = ChannelCodeEnum.WXAPP.getChannelCode();
            }
            String userNo = getChannelInfo(channelCode, "10");
            // 使用新订单详情的类型清单
            String[] split = HandlerConstants.NEW_ORDER_COUPON_SOURCE.split(",");
            // 不在清单中调用老接口
            if (!ArrayUtils.contains(split, couponSource)) {
                CouponOrderReq couponOrderReq = new CouponOrderReq();
                couponOrderReq.setOrderNo(couponOrderDetail.getOrderNo());
                couponOrderReq.setChannelOrderNo(couponOrderDetail.getChannelOrderNo());
                // 老订单详情详情接口
                Map<String, Object> pageMap = queryOrderDetail(channelCode, userNo, couponOrderReq, request);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(pageMap);
                String respJson = JsonMapper.buildNormalMapper().toJson(resp);
                log.info(LOG_RESP_TWO, reqId, ip, respJson);
                return resp;
            }

            couponOrderDetail.setRequestIp(ip);
            couponOrderDetail.setChannelCode(channelCode);
            couponOrderDetail.setUserNo(userNo);
            couponOrderDetail.setVersion(HandlerConstants.VERSION);
            Object objData = null;
            switch (couponSource) {
                case "PaySeat":
                    // 设置付费选座
                    objData = queryPaySeatOrderDetail(couponOrderDetail);
                    break;
                case "INNNCustomServe":
                    // 日本接送机
                    objData = queryINNNCustomDetail(couponOrderDetail, couponSource);
                    break;
                case "ExtraBaggage":
                    //预付费行李
                    try {
                        objData = queryPrepaymentBaggageOrderInfo(req, request);
                    } catch (BaggageException e) {
                        resp.setResultCode(e.getErrorCode());
                        resp.setResultInfo(e.getErrorMessage());
                        return resp;
                    }
                    break;
                case "ServiceButler":
                    // 吉祥服务管家
                    objData = queryServiceButlerDetail(couponOrderDetail);
                    break;
                default:
                    resp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
                    resp.setResultInfo("不支持的权益券类型");
                    return resp;
            }

            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(objData);
            String respJson = JsonMapper.buildNormalMapper().toJson(resp);
            log.info(LOG_RESP_TWO, reqId, ip, respJson);
            return resp;
        } catch (ServiceException se) {
            log.error("权益券订单列表查询异常", se);
            resp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
            resp.setResultInfo(se.getMessage());
            return resp;
        } catch (Exception e) {
            log.error("权益券订单列表查询异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            return resp;
        }
    }

    /**
     * 查询吉祥服务管家订单详情
     */
    private ServiceButlerOrderDetail queryServiceButlerDetail(NewCouponOrderDetailReq couponOrderDetail) {
        // 调用订单详情接口获取数据
        NewBasicOrderDetailResponse orderDetailResponse = orderManage.queryCouponOrderDetail(couponOrderDetail);

        ServiceButlerOrderDetail serviceButlerOrderDetail = new ServiceButlerOrderDetail();
        // 拷贝主订单信息
        serviceButlerOrderDetail.setOrderNo(orderDetailResponse.getOrderNo());
        serviceButlerOrderDetail.setChannelOrderNo(orderDetailResponse.getChannelOrderNo());
        serviceButlerOrderDetail.setCustomerName(orderDetailResponse.getCustomerName());
        serviceButlerOrderDetail.setContactTelphone(orderDetailResponse.getContactTelphone());
        serviceButlerOrderDetail.setPayDatetimeLimit(orderDetailResponse.getPayDatetimeLimit());
        serviceButlerOrderDetail.setIsSelf(orderDetailResponse.getIsSelf());
        serviceButlerOrderDetail.setCanRefundFlag(orderDetailResponse.getCanRefundFlag());

        //
        serviceButlerOrderDetail.setOrderTotalAmount(orderDetailResponse.getOrderTotalAmount());
        serviceButlerOrderDetail.setOrderState(orderDetailResponse.getOrderState());
        serviceButlerOrderDetail.setOrderCreateDatetime(orderDetailResponse.getOrderCreateDatetime());
        serviceButlerOrderDetail.setPayState(orderDetailResponse.getPayState());

        ProductOrderEnum productOrderEnum = ProductOrderEnum.checkEnum(orderDetailResponse.getOrderState());
        serviceButlerOrderDetail.setOrderStateName(productOrderEnum.getMessage());


        OrderPayStateNewEnum orderPayStateNewEnum = OrderUtil.convertOrderState(orderDetailResponse.getOrderState(), orderDetailResponse.getPayState());
        // 只有订单是待支付时，才会展示倒计时
        if (orderPayStateNewEnum.equals(OrderPayStateNewEnum.Booking)) {
            // 存在支付超时时间  计算超时时间
            if (null != orderDetailResponse.getPayDatetimeLimit() && orderDetailResponse.getPayDatetimeLimit() > 0) {
                // 计算订单还剩余多少时间
                Date bookingTime = DateUtils.toTotalDate(orderDetailResponse.getOrderCreateDatetime());
                Date currentTime = new Date();
                long remainTime = orderDetailResponse.getPayDatetimeLimit() * 60 * 1000 - (currentTime.getTime() - bookingTime.getTime());
                if (remainTime <= 0) {
                    remainTime = 0;
                }
                serviceButlerOrderDetail.setOrderRemainTime(remainTime);
            }
        }


        String refundRule;
        ;
        if (CollectionUtils.isNotEmpty(orderDetailResponse.getBasicCouponOrderDtoList())) {
            NewBasicCouponOrderDetail orderDetail = orderDetailResponse.getBasicCouponOrderDtoList().get(0);

            // 设置航班信息
            String flightDate = orderDetail.getFlightDate().substring(0, 10);
            ServiceButlerOrderDetail.BasicCouponOrderDto basicCouponOrderDto = new ServiceButlerOrderDetail.BasicCouponOrderDto();
            //
            basicCouponOrderDto.setFlightDate(flightDate);
            //
            basicCouponOrderDto.setOrderNo(orderDetail.getOrderNo());
            basicCouponOrderDto.setPassengerName(orderDetail.getPassengerName());
            basicCouponOrderDto.setFlightNo(orderDetail.getFlightNo());
            basicCouponOrderDto.setAmount(orderDetail.getAmount());
            basicCouponOrderDto.setCouponSaleAmount(orderDetail.getCouponSaleAmount());
            basicCouponOrderDto.setCouponValueAmount(orderDetail.getCouponValueAmount());
            basicCouponOrderDto.setCouponCode(orderDetail.getCouponCode());
            basicCouponOrderDto.setCouponState(orderDetail.getCouponState());
            basicCouponOrderDto.setBoughtScore(orderDetail.getBoughtScore());
            basicCouponOrderDto.setCouponActivityNo(orderDetail.getCouponActivityNo());
            basicCouponOrderDto.setCouponActivityName(orderDetail.getCouponActivityName());
            basicCouponOrderDto.setCouponSource(orderDetail.getCouponSource());
            basicCouponOrderDto.setIsSingleOrder(orderDetail.getIsSingleOrder());
            basicCouponOrderDto.setChannelCustomerNo(orderDetail.getChannelCustomerNo());
            basicCouponOrderDto.setFfCardNo(orderDetail.getFfCardNo());
            basicCouponOrderDto.setFfpUseScore(orderDetail.getFfpUseScore());
            basicCouponOrderDto.setPayState(orderDetail.getPayState());
            basicCouponOrderDto.setOrderIdFk(orderDetail.getOrderIdFk());
            basicCouponOrderDto.setChannelNo(orderDetail.getChannelNo());
            basicCouponOrderDto.setCreatorId(orderDetail.getCreatorId());
            basicCouponOrderDto.setCreatorName(orderDetail.getCreatorName());
            basicCouponOrderDto.setCreateDatetime(orderDetail.getCreateDatetime());
            //
            //basicCouponOrderDto.setIsRemoved();
            basicCouponOrderDto.setTicketNo(orderDetail.getTicketNo());
            basicCouponOrderDto.setDepAirport(orderDetail.getDepAirport());
            basicCouponOrderDto.setArrAirport(orderDetail.getArrAirport());
            refundRule = orderDetail.getRefundRule();


            // 获取全部机场信息
            FlightInfoResult flightInfoResult = flightService.getFlightInfo(flightDate, orderDetail.getFlightNo(),
                    orderDetail.getDepAirport(), orderDetail.getArrAirport(), null);


            // 订单航班基础服务参数
            serviceButlerOrderDetail.setDepCityName(flightInfoResult.getDepCityName());
            serviceButlerOrderDetail.setDepAirportName(flightInfoResult.getDepAirportName());
            serviceButlerOrderDetail.setDepAirportTerminal(flightInfoResult.getDepAirportTerminal());
            serviceButlerOrderDetail.setArrCityName(flightInfoResult.getArrCityName());
            serviceButlerOrderDetail.setArrAirportName(flightInfoResult.getArrAirportName());
            serviceButlerOrderDetail.setArrAirportTerminal(flightInfoResult.getArrAirportTerminal());
            serviceButlerOrderDetail.setDepTime(flightInfoResult.getDepDateTime());
            serviceButlerOrderDetail.setArrTime(flightInfoResult.getArrDateTime());

            // 设置服务管家特有参数
            if (orderDetail.getServiceButlerParam() != null) {
                ServiceButlerOrderDetail.ServiceButlerParam serviceButlerParam = getServiceButlerParam(orderDetail);
                basicCouponOrderDto.setServiceButlerParam(serviceButlerParam);
            }
            //
            serviceButlerOrderDetail.setBasicCouponOrderDtoList(Arrays.asList(basicCouponOrderDto));
            serviceButlerOrderDetail.setRefundRule(refundRule);


        }
        return serviceButlerOrderDetail;
    }

    @NotNull
    private static ServiceButlerOrderDetail.ServiceButlerParam getServiceButlerParam(NewBasicCouponOrderDetail orderDetail) {
        ServiceButlerOrderDetail.ServiceButlerParam serviceButlerParam = new ServiceButlerOrderDetail.ServiceButlerParam();
        serviceButlerParam.setSeatPreference(orderDetail.getServiceButlerParam().getSeatPreference());
        serviceButlerParam.setHasCheckedBaggage(orderDetail.getServiceButlerParam().getHasCheckedBaggage());
        serviceButlerParam.setArrivalTransport(orderDetail.getServiceButlerParam().getArrivalTransport());
        serviceButlerParam.setArrivalTime(orderDetail.getServiceButlerParam().getArrivalTime());
        serviceButlerParam.setPhoneNo(orderDetail.getServiceButlerParam().getPhoneNo());
        return serviceButlerParam;
    }

    /**
     * 查询行李券产品订单详情
     *
     * @param req
     * @param request
     * @return
     * @throws BaggageException
     */
    private BaggageOrderInfoResp queryPrepaymentBaggageOrderInfo(BaseReq<NewCouponOrderDetailReq> req, HttpServletRequest request) throws BaggageException {
        String ip = this.getClientIP(request);
        NewCouponOrderDetailReq orderInfoReq = req.getRequest();

        String userNo = getChannelInfo(req.getChannelCode(), "10");

        BaseRequestDTO<String> baseRequestDTO = new BaseRequestDTO<>();
        baseRequestDTO.setChannelCode(req.getChannelCode());
        baseRequestDTO.setFfpId(orderInfoReq.getFfpId());
        baseRequestDTO.setFfpCardNo(orderInfoReq.getFfpCardNo());
        baseRequestDTO.setUserNo(userNo);
        baseRequestDTO.setRequest(orderInfoReq.getOrderNo());

        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_COUPON_API + HandlerConstants.PRODUCT_PREPAYBAGGAGE_ORDERINFO;
        HttpResult httpResult = this.doPostClient(baseRequestDTO, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());

        QueryOrderInfoResponse orderInfoResponse;
        if (null != httpResult && httpResult.isResult()) {
            com.juneyaoair.baseclass.prepayment.common.BaseResultDTO<QueryOrderInfoResponse> baseResultDTO = (com.juneyaoair.baseclass.prepayment.common.BaseResultDTO<QueryOrderInfoResponse>) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<com.juneyaoair.baseclass.prepayment.common.BaseResultDTO<QueryOrderInfoResponse>>() {
            }.getType());
            if (!baseResultDTO.getResultCode().equals("10001")) {
                throw new BaggageException(WSEnum.ERROR.getResultCode(), "查询订单错误");
            }
            orderInfoResponse = baseResultDTO.getResult();
        } else {
            throw new BaggageException(WSEnum.NO_DATA.getResultCode(), "远程服务异常");
        }
        // 处理详情
        BaggageOrderInfoResp baggageOrderInfoResp = buildBaggageOrderInfoResp(orderInfoResponse);
        return baggageOrderInfoResp;
    }

    /**
     * 获取付费座位订单详情
     *
     * @param couponOrderDetail
     * @return
     */


    private NewBasicOrderDetailResponse queryINNNCustomDetail(NewCouponOrderDetailReq couponOrderDetail, String couponSource) {
        NewCouponOrderDetailReq couponOrderDetailReq = new NewCouponOrderDetailReq();
        couponOrderDetailReq.setChannelCode(couponOrderDetail.getChannelCode());
        couponOrderDetailReq.setVersion(HandlerConstants.VERSION);
        couponOrderDetailReq.setUserNo(ControllerUtil.getChannelInfo(couponOrderDetail.getChannelCode(), "10"));
        couponOrderDetailReq.setOrderNo(couponOrderDetail.getOrderNo());
        couponOrderDetailReq.setChannelOrderNo(couponOrderDetail.getChannelOrderNo());
        NewBasicOrderDetailResponse newBasicOrderDetailResponse = orderManage.queryCouponOrderDetail(couponOrderDetailReq);
        if (CollectionUtils.isNotEmpty(newBasicOrderDetailResponse.getInnnTransOrderList())) {
            InnnTransOrderDetail innnTransOrderDetail = newBasicOrderDetailResponse.getInnnTransOrderList().get(0);
            newBasicOrderDetailResponse.setOrderNo(innnTransOrderDetail.getOrderNo());
            newBasicOrderDetailResponse.setOrderCreateDatetime(innnTransOrderDetail.getCreateDatetime());
            newBasicOrderDetailResponse.setCustomerName(innnTransOrderDetail.getPsgName());
            newBasicOrderDetailResponse.setContactTelphone(innnTransOrderDetail.getPsgPhone());
            NewBasicCouponOrderDetail basicCouponOrderDetail = newBasicOrderDetailResponse.getBasicCouponOrderDtoList().get(0);
            newBasicOrderDetailResponse.setPayState(basicCouponOrderDetail.getPayState());
            newBasicOrderDetailResponse.setPhoneCountryCode(basicCouponOrderDetail.getPhoneCountryCode());
            newBasicOrderDetailResponse.setOrderState(innnTransOrderDetail.getAirtransOrderStatus());
            newBasicOrderDetailResponse.setOrderStateDesc(innnTransOrderDetail.getAirtransOrderStatusName());
            newBasicOrderDetailResponse.setProductName(innnTransOrderDetail.getProductName());
            newBasicOrderDetailResponse.setCouponNo(innnTransOrderDetail.getCouponNo());
            newBasicOrderDetailResponse.setCouponSource(basicCouponOrderDetail.getCouponSource());
            newBasicOrderDetailResponse.setOrderTotalAmount(innnTransOrderDetail.getPayAmount());
            newBasicOrderDetailResponse.setApprovalCode(innnTransOrderDetail.getApprovalCode());
            newBasicOrderDetailResponse.setAmount(basicCouponOrderDetail.getAmount());
            newBasicOrderDetailResponse.setCanRefundFlag(newBasicOrderDetailResponse.getCanRefundFlag());
            newBasicOrderDetailResponse.setServiceType(innnTransOrderDetail.getServiceType());
            newBasicOrderDetailResponse.setToAddress(innnTransOrderDetail.getToAddress());
            newBasicOrderDetailResponse.setFromAddress(innnTransOrderDetail.getFromAddress());
            newBasicOrderDetailResponse.setCouponDiscount(innnTransOrderDetail.getCouponDiscount());
            newBasicOrderDetailResponse.setBookingTime(innnTransOrderDetail.getBookingTime());
            newBasicOrderDetailResponse.setTotalFee(innnTransOrderDetail.getTotalFee());
            // 只有订单是待支付时，才会展示倒计时
            if ("WaitPay".equals(newBasicOrderDetailResponse.getOrderState())) {
                // 存在支付超时时间  计算超时时间
                if (null != newBasicOrderDetailResponse.getPayDatetimeLimit() && newBasicOrderDetailResponse.getPayDatetimeLimit() > 0) {
                    // 计算订单还剩余多少时间
                    Date bookingTime = DateUtils.toTotalDate(newBasicOrderDetailResponse.getOrderCreateDatetime());
                    Date currentTime = new Date();
                    long remainTime = newBasicOrderDetailResponse.getPayDatetimeLimit() * 60 * 1000 - (currentTime.getTime() - bookingTime.getTime());
                    if (remainTime <= 0) {
                        remainTime = 0;
                    }
                    newBasicOrderDetailResponse.setOrderRemainTime(remainTime);
                }
            }
        }
        return newBasicOrderDetailResponse;
    }

    private PaySeatCouponOrderDetail queryPaySeatOrderDetail(NewCouponOrderDetailReq couponOrderDetail) {
        // 订单详情详情接口
        NewBasicOrderDetailResponse orderDetailResponse = orderManage.queryCouponOrderDetail(couponOrderDetail);
        PaySeatCouponOrderDetail paySeatCouponOrderDetail = new PaySeatCouponOrderDetail();
        paySeatCouponOrderDetail.setCouponSource("PaySeat");
        // 拷贝主订单信息
        BeanUtils.copyProperties(orderDetailResponse, paySeatCouponOrderDetail);
        // 设置航班信息
        List<PaySeatSubOrderDetail> subOrderList = new ArrayList<>();
        // 选座数据 按票号分组
        Map<String, SeatOrderSingle> selectSeatInfoMap = Maps.newHashMap();
        // 是否设置了航班基础信息
        boolean flightBase = false;
        // 循环处理子订单信息
        if (CollectionUtils.isNotEmpty(orderDetailResponse.getBasicCouponOrderDtoList())) {
            for (NewBasicCouponOrderDetail orderDetail : orderDetailResponse.getBasicCouponOrderDtoList()) {
                // 2023区分优选座位订单 和 积分购买订单  积分购买订单不处理
                if ("ScoreBuy".equals(orderDetail.getCouponSource())) {
                    continue;
                }
                if (StringUtils.isBlank(orderDetail.getTicketNo())) {
                    continue;
                }
                // 未设置航班基础信息 且 存在航班信息
                if (!flightBase) {
                    String flightDate = orderDetail.getFlightDate().substring(0, 10);
                    paySeatCouponOrderDetail.setFlightDate(flightDate);
                    // 获取全部机场信息
                    FlightInfoResult flightInfoResult = flightService.getFlightInfo(flightDate, orderDetail.getFlightNo(), orderDetail.getDepAirport(), orderDetail.getArrAirport(), null);
                    paySeatCouponOrderDetail.setDepCityName(flightInfoResult.getDepCityName());
                    paySeatCouponOrderDetail.setDepAirportName(flightInfoResult.getDepAirportName());
                    paySeatCouponOrderDetail.setArrCityName(flightInfoResult.getArrCityName());
                    paySeatCouponOrderDetail.setArrAirportName(flightInfoResult.getArrAirportName());
                    paySeatCouponOrderDetail.setFlightNo(orderDetail.getFlightNo());
                    paySeatCouponOrderDetail.setDepAirPortCode(orderDetail.getDepAirport());
                    paySeatCouponOrderDetail.setArrAirPortCode(orderDetail.getArrAirport());
                    paySeatCouponOrderDetail.setDepAirportTerminal(flightInfoResult.getDepAirportTerminal());
                    paySeatCouponOrderDetail.setArrAirportTerminal(flightInfoResult.getArrAirportTerminal());
                    paySeatCouponOrderDetail.setFlightTime(flightInfoResult.getFlyTimeLength());
                    paySeatCouponOrderDetail.setFlightDay(flightInfoResult.getCrossDay());
                    paySeatCouponOrderDetail.setFlightTypeName(flightInfoResult.getPlaneType());
                    paySeatCouponOrderDetail.setDepTime(flightInfoResult.getDepDateTime());
                    paySeatCouponOrderDetail.setArrTime(flightInfoResult.getArrDateTime());
                    // 查询本地订单信息
                    SeatOrderParam seatOrderParam = new SeatOrderParam();
                    seatOrderParam.setOrderNo(orderDetailResponse.getOrderNo());
                    seatOrderParam.setChannelOrderNo(orderDetailResponse.getChannelOrderNo());
                    SeatOrderResult seatOrderResult = checkInSeatService.getSeatOrder(seatOrderParam);
                    selectSeatInfoMap = seatOrderResult.getSeatOrderList().stream().collect(Collectors.toMap(SeatOrderSingle::getTicketNo, x -> x));
                    flightBase = true;
                }
                // 设置子订单信息
                PaySeatSubOrderDetail paySeatSubOrderDetail = new PaySeatSubOrderDetail();
                BeanUtils.copyProperties(orderDetail, paySeatSubOrderDetail, "boughtScore", "ffpUseScore", "cashTotalAmount");
                // 计算现金总金额
                BigDecimal couponSale = new BigDecimal(orderDetail.getCouponSaleAmount());
                BigDecimal ffpUseScore = new BigDecimal(orderDetail.getFfpUseScore());
                OrderCouponDto orderCouponDto = orderDetail.getOrderCouponDto();
                BigDecimal couponPrice = BigDecimal.ZERO;
                if (null != orderCouponDto) {
                    SeatOrderCoupon seatOrderCoupon = new SeatOrderCoupon();
                    seatOrderCoupon.setCouponNo(orderCouponDto.getCouponNo());
                    seatOrderCoupon.setCouponType(orderCouponDto.getCouponType());
                    seatOrderCoupon.setCouponPrice(orderCouponDto.getCouponPrice());
                    paySeatSubOrderDetail.setSeatOrderCoupon(seatOrderCoupon);
                    couponPrice = new BigDecimal(orderCouponDto.getCouponPrice());
                }
                paySeatSubOrderDetail.setFfpUseScore(ffpUseScore.stripTrailingZeros().toPlainString());
                BigDecimal cash = couponSale.subtract(ffpUseScore).subtract(couponPrice);
                paySeatSubOrderDetail.setCashTotalAmount(cash.stripTrailingZeros().toPlainString());
                if (null != selectSeatInfoMap.get(orderDetail.getTicketNo())) {
                    SeatOrderRemark seatOrderRemark = selectSeatInfoMap.get(orderDetail.getTicketNo()).getSeatOrderRemark();
                    if (null != seatOrderRemark) {
                        String cabinClassByCabinCode = CommonUtil.getCabinBySeatNo(seatOrderRemark.getSeatNo());
                        String cabinName = CommonUtil.showCabinClassName(cabinClassByCabinCode);
                        paySeatCouponOrderDetail.setCabinName(cabinName);
                        paySeatSubOrderDetail.setSeatNo(seatOrderRemark.getSeatNo());
                        paySeatSubOrderDetail.setAreaCode(seatOrderRemark.getAreaCode());
                        paySeatSubOrderDetail.setAreaName(seatOrderRemark.getAreaName());
                        paySeatSubOrderDetail.setMemberCode(seatOrderRemark.getMemberCode());
                        paySeatSubOrderDetail.setMemberDiscount(seatOrderRemark.getMemberDiscount());
                        paySeatSubOrderDetail.setTimeDiscount(seatOrderRemark.getTimeDiscount());
                        paySeatSubOrderDetail.setDiscountPrice(seatOrderRemark.getDiscountPrice());
                    }
                }
                // 子订单根据优惠券状态设置状态
                SubOrderDetailStateEnum subOrderDetailStateEnum = OrderUtil.convertSubOrderState(orderDetail.getPayState(), orderDetail.getIsVoluntaryRefund(), orderDetail.getCouponState(), orderDetail.getRebateState());
                paySeatSubOrderDetail.setSubOrderState(subOrderDetailStateEnum.getStateCode());
                paySeatSubOrderDetail.setSubOrderStateName(subOrderDetailStateEnum.getStateDesc());
                //设置按钮
                String buttonState = handleButtonState(orderDetail);
                paySeatSubOrderDetail.setDetailOrApply(buttonState);
                subOrderList.add(paySeatSubOrderDetail);
            }
        }
        paySeatCouponOrderDetail.setSubOrderList(subOrderList);

        OrderPayStateNewEnum orderPayStateNewEnum = OrderUtil.convertOrderState(paySeatCouponOrderDetail.getOrderState(), paySeatCouponOrderDetail.getPayState());
        paySeatCouponOrderDetail.setOrderStateName(orderPayStateNewEnum.getStateDesc());

        //设置订单详情的航班状态
        JSONObject orderRemark = getOrderRemark(orderDetailResponse);
        String flightStatus = (String) orderRemark.get("flightStatus");
        paySeatCouponOrderDetail.setFlightStatus(flightStatus);
        Integer payType = (Integer) orderRemark.get("payType");
        paySeatCouponOrderDetail.setPayType(payType);
        String payTypeList = orderRemark.getString("payTypeList");
        if (StringUtils.isNotBlank(payTypeList)) {
            paySeatCouponOrderDetail.setPayTypeList(JSON.parseArray(payTypeList, String.class));
        }

        // 设置0元支付
        setFreeTicket(paySeatCouponOrderDetail, orderDetailResponse);

        // 只有订单是待支付时，才会展示倒计时
        if (orderPayStateNewEnum.equals(OrderPayStateNewEnum.Booking)) {
            // 存在支付超时时间  计算超时时间
            if (null != paySeatCouponOrderDetail.getPayDatetimeLimit() && paySeatCouponOrderDetail.getPayDatetimeLimit() > 0) {
                // 计算订单还剩余多少时间
                Date bookingTime = DateUtils.toTotalDate(paySeatCouponOrderDetail.getOrderCreateDatetime());
                Date currentTime = new Date();
                long remainTime = paySeatCouponOrderDetail.getPayDatetimeLimit() * 60 * 1000 - (currentTime.getTime() - bookingTime.getTime());
                if (remainTime <= 0) {
                    remainTime = 0;
                }
                paySeatCouponOrderDetail.setOrderRemainTime(remainTime);
            }
        }

        Map<String, List<TicketInsuranceDto>> ticketInsuranceMap = Maps.newHashMap();
        // 处理保险信息
        List<InsuranceOrderDto> insuranceOrderList = orderDetailResponse.getInsuranceOrderDtoList();
        if (CollectionUtils.isNotEmpty(insuranceOrderList)) {
            for (InsuranceOrderDto insuranceOrder : insuranceOrderList) {
                String flightDate = insuranceOrder.getFlightDate().substring(0, 10);
                // 未设置航班基础信息 且 存在航班信息
                if (!flightBase) {
                    paySeatCouponOrderDetail.setFlightDate(flightDate);
                    // 获取全部机场信息
                    FlightInfoResult flightInfoResult = flightService.getFlightInfo(flightDate, insuranceOrder.getFlightNo(), insuranceOrder.getDepAirport(), insuranceOrder.getArrAirport(), null);
                    paySeatCouponOrderDetail.setDepCityName(flightInfoResult.getDepCityName());
                    paySeatCouponOrderDetail.setDepAirportName(flightInfoResult.getDepAirportName());
                    paySeatCouponOrderDetail.setArrCityName(flightInfoResult.getArrCityName());
                    paySeatCouponOrderDetail.setArrAirportName(flightInfoResult.getArrAirportName());
                    paySeatCouponOrderDetail.setFlightNo(insuranceOrder.getFlightNo());
                    paySeatCouponOrderDetail.setDepAirPortCode(insuranceOrder.getDepAirport());
                    paySeatCouponOrderDetail.setArrAirPortCode(insuranceOrder.getArrAirport());
                    paySeatCouponOrderDetail.setDepAirportTerminal(flightInfoResult.getDepAirportTerminal());
                    paySeatCouponOrderDetail.setArrAirportTerminal(flightInfoResult.getArrAirportTerminal());
                    paySeatCouponOrderDetail.setFlightTime(flightInfoResult.getFlyTimeLength());
                    paySeatCouponOrderDetail.setFlightDay(flightInfoResult.getCrossDay());
                    paySeatCouponOrderDetail.setFlightTypeName(flightInfoResult.getPlaneType());
                    paySeatCouponOrderDetail.setDepTime(flightInfoResult.getDepDateTime());
                    paySeatCouponOrderDetail.setArrTime(flightInfoResult.getArrDateTime());
                    flightBase = true;
                }
                String flightKey = flightDate + "_" + insuranceOrder.getFlightNo() + "_" + insuranceOrder.getDepAirport() + "_" + insuranceOrder.getArrAirport();
                List<TicketInsuranceDto> ticketInsuranceList = ticketInsuranceMap.get(flightKey);
                if (null == ticketInsuranceList) {
                    ticketInsuranceList = Lists.newArrayList();
                }
                if (CollectionUtils.isNotEmpty(insuranceOrder.getTicketInsuranceDtoList())) {
                    insuranceOrder.getTicketInsuranceDtoList().forEach(ticketInsurance -> ticketInsurance.setTicketOrderNo(insuranceOrder.getTicketOrderNo()));
                }
                ticketInsuranceList.addAll(insuranceOrder.getTicketInsuranceDtoList());
                ticketInsuranceMap.put(flightKey, ticketInsuranceList);
            }
        }
        String flightKey = paySeatCouponOrderDetail.getFlightDate() + "_" + paySeatCouponOrderDetail.getFlightNo() + "_" + paySeatCouponOrderDetail.getDepAirPortCode() + "_" + paySeatCouponOrderDetail.getArrAirPortCode();
        paySeatCouponOrderDetail.setTicketInsuranceList(ticketInsuranceMap.get(flightKey));
        paySeatCouponOrderDetail.setIsPushInsurance(orderDetailResponse.getIsPushInsurance());
        if (CollectionUtils.isNotEmpty(paySeatCouponOrderDetail.getTicketInsuranceList())) {
            // 保险状态清单
            Set<InsuranceOrderStatEnum> insuranceOrderStatEnums = Sets.newHashSet();
            for (TicketInsuranceDto ticketInsurance : paySeatCouponOrderDetail.getTicketInsuranceList()) {
                if (CollectionUtils.isEmpty(ticketInsurance.getInsuranceDetailDtoList())) {
                    continue;
                }
                for (InsuranceDetailDto insuranceDetail : ticketInsurance.getInsuranceDetailDtoList()) {
                    InsuranceOrderStatEnum insuranceOrderStatEnum = InsuranceOrderStatEnum.getEnum(insuranceDetail.getInsuranceState());
                    insuranceDetail.setInsuranceStateName(null == insuranceOrderStatEnum ? null : insuranceOrderStatEnum.getInsStatDesc());
                    if (null != insuranceOrderStatEnum) {
                        insuranceOrderStatEnums.add(insuranceOrderStatEnum);
                    }
                }
            }
            if (OrderPayStateNewEnum.Cancel.getStateCode().equals(paySeatCouponOrderDetail.getOrderState())) {
                paySeatCouponOrderDetail.setInsuranceState(InsuranceStateEnum.CANCEL.name());
                paySeatCouponOrderDetail.setInsuranceStateName(InsuranceStateEnum.CANCEL.getDesc());
            } else if (OrderPayStateNewEnum.Booking.getStateCode().equals(paySeatCouponOrderDetail.getOrderState())) {
                paySeatCouponOrderDetail.setInsuranceState(InsuranceStateEnum.BOOKING.name());
                paySeatCouponOrderDetail.setInsuranceStateName(InsuranceStateEnum.BOOKING.getDesc());
            } else {
                if (!insuranceOrderStatEnums.isEmpty()) {
                    InsuranceStateEnum insuranceStateEnum;
                    // 多个订单状态返回已支付可退保状态
                    if (insuranceOrderStatEnums.size() != 1) {
                        insuranceStateEnum = InsuranceStateEnum.PAY_DEL;
                    } else {
                        InsuranceOrderStatEnum insuranceOrderStatEnum = insuranceOrderStatEnums.iterator().next();
                        switch (insuranceOrderStatEnum) {
                            case Not:
                                insuranceStateEnum = InsuranceStateEnum.UN_PAY;
                                break;
                            case APPLY:
                                insuranceStateEnum = InsuranceStateEnum.PAY;
                                break;
                            case DELETE:
                                insuranceStateEnum = InsuranceStateEnum.REFUND;
                                break;
                            default:
                                insuranceStateEnum = InsuranceStateEnum.PAY_DEL;
                        }
                    }
                    paySeatCouponOrderDetail.setInsuranceState(insuranceStateEnum.name());
                    paySeatCouponOrderDetail.setInsuranceStateName(insuranceStateEnum.getDesc());
                }
            }
        }
        return paySeatCouponOrderDetail;
    }

    private void setFreeTicket(PaySeatCouponOrderDetail paySeatCouponOrderDetail, NewBasicOrderDetailResponse orderDetailResponse) {
        if (null == orderDetailResponse) {
            return;
        }

        //如果订单总金额为0，则返回空串
        if ("0".equals(orderDetailResponse.getOrderTotalAmount())) {
            paySeatCouponOrderDetail.setPayMethod("");
        }
    }

    /**
     * 从子订单中获取航班状态
     *
     * @param orderDetailResponse
     */
    private JSONObject getOrderRemark(NewBasicOrderDetailResponse orderDetailResponse) {
        if (null == orderDetailResponse) {
            return new JSONObject();
        }
        List<NewBasicCouponOrderDetail> subOrderList = orderDetailResponse.getBasicCouponOrderDtoList();
        if (CollectionUtils.isNotEmpty(subOrderList)) {
            Optional<NewBasicCouponOrderDetail> first = subOrderList.stream().filter(x -> null != x.getRemark()).findFirst();
            if (first.isPresent()) {
                NewBasicCouponOrderDetail newBasicCouponOrderDetail = first.get();
                return JSON.parseObject(newBasicCouponOrderDetail.getRemark());
            }
        }
        return new JSONObject();
    }

    /**
     * 构建预付费行李订单详情
     *
     * @param orderInfoResponse
     * @return
     */
    private BaggageOrderInfoResp buildBaggageOrderInfoResp(QueryOrderInfoResponse orderInfoResponse) {
        BaggageOrderInfoResp baggageOrderInfoResp = new BaggageOrderInfoResp();
        baggageOrderInfoResp.setOrderCreateDatetime(orderInfoResponse.getCreateDate());
        baggageOrderInfoResp.setOrderNo(orderInfoResponse.getOrderNo());
        baggageOrderInfoResp.setPhoneNo(orderInfoResponse.getPhoneNo());
        baggageOrderInfoResp.setOrderTotalAmount(orderInfoResponse.getOrderTotalAmount());
        //退款规则
        baggageOrderInfoResp.setRefundRule(orderInfoResponse.getRefundRule());
        List<CouponOrderInfoDto> couponOrderInfoDtoList = orderInfoResponse.getCouponOrderInfoDtoList();
        /**
         * 处理子订单详情
         */
        updateCouponOrderInfo(couponOrderInfoDtoList);

        //计算总积分
        int allScore = couponOrderInfoDtoList.stream()
                .mapToInt(obj -> Integer.parseInt(obj.getFfpUseScore()))
                .sum();

        baggageOrderInfoResp.setFfpUseTotalScore(allScore);
        //设置订单状态
        //判断是否可以退票
        baggageOrderInfoResp.setOrderState(orderInfoResponse.getOrderStatus());
        //是否非0元支付
        baggageOrderInfoResp.setIsZeroPay(false);
        if (new Double(orderInfoResponse.getOrderTotalAmount()).equals(0.0)) {
            baggageOrderInfoResp.setIsZeroPay(true);
        }

        //转换
        ProductOrderEnum productOrderEnum = ProductOrderEnum.checkEnum(orderInfoResponse.getOrderStatus());
        baggageOrderInfoResp.setCanRefund(false);
        if (productOrderEnum.equals(ProductOrderEnum.Finish) || productOrderEnum.equals(ProductOrderEnum.ApplyRefund)) {
            if (couponOrderInfoDtoList.stream()
                    .anyMatch(CouponOrderInfoDto::getCanRefund)) {
                baggageOrderInfoResp.setCanRefund(true);
            }
            //根据订单获取主订单状态显示
            ProductOrderEnum stateByCouponOrderInfo = getStateByCouponOrderInfo(couponOrderInfoDtoList);
            if (stateByCouponOrderInfo != null) {
                productOrderEnum = stateByCouponOrderInfo;
            }
        }



        if (orderInfoResponse.getPayState().equals(OrderPayStateEnum.Pay.getStateCode())
                && productOrderEnum.equals(ProductOrderEnum.Booking)) {
            //设置状态为已支付
            productOrderEnum = ProductOrderEnum.Finish;
        }
        //未支付才会设置支付状态
        if (orderInfoResponse.getPayState().equals(OrderPayStateEnum.UnPay.getStateCode())
                && productOrderEnum.equals(ProductOrderEnum.Booking)) {
            baggageOrderInfoResp.setPayState(orderInfoResponse.getPayState());
        }


        baggageOrderInfoResp.setOrderState(productOrderEnum.getCode());
        baggageOrderInfoResp.setOrderStateName(productOrderEnum.getMessage());


        orderInfoResponse.setCouponOrderInfoDtoList(couponOrderInfoDtoList);
        // 时间
        baggageOrderInfoResp.setPayDatetimeLimit(orderInfoResponse.getPayDatetimeLimit());
        // 存在支付超时时间  计算超时时间
        if (null != orderInfoResponse.getPayDatetimeLimit() && orderInfoResponse.getPayDatetimeLimit() > 0) {
            // 计算订单还剩余多少时间
            Date bookingTime = DateUtils.toDate(baggageOrderInfoResp.getOrderCreateDatetime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
            Date currentTime = new Date();
            long remainTime = orderInfoResponse.getPayDatetimeLimit() * 60 * 1000 - (currentTime.getTime() - bookingTime.getTime());
            if (remainTime <= 0) {
                remainTime = 0;
            }
            baggageOrderInfoResp.setOrderRemainTime(remainTime);
        }
        if (!productOrderEnum.equals(ProductOrderEnum.Booking)) {
            baggageOrderInfoResp.setOrderRemainTime(0);
        }


        //去程返程,国际国内
        baggageOrderInfoResp.setFlightType(orderInfoResponse.getFlightType());
        baggageOrderInfoResp.setInterFlag(orderInfoResponse.getInterFlag());
        baggageOrderInfoResp.setLinker(orderInfoResponse.getLinker());
        baggageOrderInfoResp.setPayMethod(orderInfoResponse.getPayMethod());
        baggageOrderInfoResp.setCouponSource(orderInfoResponse.getCouponSource());
        baggageOrderInfoResp.setChannelOrderNo(orderInfoResponse.getChannelOrderNo());
        //产品类型
        CouponOrderInfoDto couponOrderInfoDto = couponOrderInfoDtoList.get(0);
        baggageOrderInfoResp.setCouponSource(couponOrderInfoDto.getCouponSource());

        //设置支付用产品名称
        baggageOrderInfoResp.setProductDesc(VoucherTypesEnum.EXTRABAGGAGE.getName());

        baggageOrderInfoResp.setCouponOrderInfoDtoList(orderInfoResponse.getCouponOrderInfoDtoList());
        return baggageOrderInfoResp;
    }

    /**
     * 根据子订单状态获取主订单信息
     * 仅支持最多两个子订单
     *
     * @param couponOrderInfoDtoList
     * @return
     */
    private ProductOrderEnum getStateByCouponOrderInfo(List<CouponOrderInfoDto> couponOrderInfoDtoList) {
        //都为空,返回空
        if (couponOrderInfoDtoList.stream()
                .allMatch(orderInfo -> StringUtil.isNullOrEmpty(orderInfo.getRebateState()))) {
            return null;
        }
        if (couponOrderInfoDtoList.stream()
                .allMatch(orderInfo -> RefundOrderStateEnum.Success.getStateCode().equals(orderInfo.getRebateState()))) {
            return ProductOrderEnum.ApplyRefund;
        }
        if (couponOrderInfoDtoList.stream()
                .allMatch(orderInfo -> RefundOrderStateEnum.Problem.getStateCode().equals(orderInfo.getRebateState()))) {
            return ProductOrderEnum.Problem;
        }
        if (couponOrderInfoDtoList.stream()
                .anyMatch(orderInfo -> RefundOrderStateEnum.Problem.getStateCode().equals(orderInfo.getRebateState()))) {
            if (couponOrderInfoDtoList.stream()
                    .anyMatch(orderInfo -> RefundOrderStateEnum.Success.getStateCode().equals(orderInfo.getRebateState()))) {
                return ProductOrderEnum.Problem;
            }
            if (couponOrderInfoDtoList.stream()
                    .anyMatch(orderInfo -> StringUtil.isNullOrEmpty(orderInfo.getRebateState()))) {
                return ProductOrderEnum.Finish;
            }
        } else {
            return ProductOrderEnum.Rebates;
        }


        return null;
    }


    /**
     * 查询预付费订单详情,处理子订单信息
     *
     * @param couponOrderInfoDtoList
     */
    private void updateCouponOrderInfo(List<CouponOrderInfoDto> couponOrderInfoDtoList) {
        for (CouponOrderInfoDto couponOrderInfoDto : couponOrderInfoDtoList) {
            String cabinClass = CommonUtil.getCabinClassByCabinCode(couponOrderInfoDto.getCabin(), handConfig.getCabinClass());
            String cabinClassName = CommonUtil.showCabinClassName(cabinClass);
            couponOrderInfoDto.setCabinClass(cabinClass);
            couponOrderInfoDto.setCabinClassName(cabinClassName);
            couponOrderInfoDto.setArrAirportTerminal(couponOrderInfoDto.getArrAirportTerminal().equals("--") ? "" : couponOrderInfoDto.getArrAirportTerminal());
            couponOrderInfoDto.setDepAirportTerminal(couponOrderInfoDto.getDepAirportTerminal().equals("--") ? "" : couponOrderInfoDto.getDepAirportTerminal());

            String depDateTime = couponOrderInfoDto.getDepFlightDate() + " " + couponOrderInfoDto.getDepTime();
            String arrDateTime = couponOrderInfoDto.getArrFlightDate() + " " + couponOrderInfoDto.getArrTime();

            //是否退款
            couponOrderInfoDto.setCanRefund(false);
            BaggageEmdTicketStateEnum baggageEmdTicketStateEnum = BaggageEmdTicketStateEnum.checkCode(couponOrderInfoDto.getEmdTicketState());
            if (couponOrderInfoDto.getCouponState().equals(PrepaymentBaggageStatusEnum.Not.getStatusCode())
                    && (baggageEmdTicketStateEnum.equals(BaggageEmdTicketStateEnum.EMPTY) || baggageEmdTicketStateEnum.equals(BaggageEmdTicketStateEnum.ISSUED))) {
                //设置可以退款
                couponOrderInfoDto.setCanRefund(true);
            }

            // 私域行李券
            // 是否使用了券
            // 非 Not\Active 表示使用了私域行李券。使其不可退
            boolean isUseCoupon = false;
            if (couponOrderInfoDto.getOrderCoupon() != null) {
                isUseCoupon = !("Not".equals(couponOrderInfoDto.getOrderCoupon().getCouponState())
                        || ("Active").equals(couponOrderInfoDto.getOrderCoupon().getCouponState()));
            }
            if (couponOrderInfoDto.getCanRefund() && isUseCoupon) {
                couponOrderInfoDto.setCanRefund(false);
            }

            //判断退款描述,根据描述显示
            String rebateState = couponOrderInfoDto.getRebateState();
            if (!StringUtil.isNullOrEmpty(rebateState)) {
                //状态转换 rebateState: "Dealing" 转成 Success
                if (RefundOrderStateEnum.Dealing.getStateCode().equals(rebateState)) {
                    couponOrderInfoDto.setRebateState(RefundOrderStateEnum.Success.getStateCode());
                    rebateState = RefundOrderStateEnum.Success.getStateCode();
                }
                if (RefundOrderStateEnum.Success.getStateCode().equals(rebateState)) {
                    couponOrderInfoDto.setCouponStateDesc("已退款");
                }
                if (RefundOrderStateEnum.Problem.getStateCode().equals(rebateState)) {
                    couponOrderInfoDto.setCouponStateDesc("退款中");
                }
            } else {
                couponOrderInfoDto.setRebateState("");
            }

            AirPortInfoDto depAirPort = localCacheService.getLocalAirport(couponOrderInfoDto.getDepAirportCode(), DateUtils.toDate(depDateTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
            AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(couponOrderInfoDto.getArrAirportCode(), DateUtils.toDate(arrDateTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
            //设置名称
            couponOrderInfoDto.setDepCityName(depAirPort.getCityName());
            couponOrderInfoDto.setArrCityName(arrAirPort.getCityName());
            couponOrderInfoDto.setDepAirportName(depAirPort.getAirPortName());
            couponOrderInfoDto.setArrAirportName(arrAirPort.getAirPortName());
            //计算时间
            if (depAirPort != null && arrAirPort != null) {
                String depCityTimeZone = depAirPort.getCityTimeZone();
                String arrCityTimeZone = arrAirPort.getCityTimeZone();
                //飞行时长
                if ((!StringUtil.isNullOrEmpty(arrCityTimeZone)) &&
                        (!StringUtil.isNullOrEmpty(depCityTimeZone)) &&
                        (!StringUtil.isNullOrEmpty(depDateTime)) &&
                        (!StringUtil.isNullOrEmpty(arrDateTime))) {
                    //添加夏、冬令时处理
                    if (!depCityTimeZone.equals(arrCityTimeZone)) {
                        depCityTimeZone = FlightUtil.convertSummerOrWinterTime(depCityTimeZone, depDateTime, depAirPort);
                        arrCityTimeZone = FlightUtil.convertSummerOrWinterTime(arrCityTimeZone, arrDateTime, arrAirPort);
                    }
                    //飞行时长
                    long flightTime = DateUtils.calDuration(depDateTime, depCityTimeZone, arrDateTime, arrCityTimeZone);
                    couponOrderInfoDto.setFlightTime(formatTime(flightTime));
                    int day = DateUtils.diffDays(depDateTime.substring(0, 10), arrDateTime.substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
                    couponOrderInfoDto.setDurDay(day < 0 ? 0 : day);
                }
            }
        }
    }

    /**
     * 毫秒转化时分,英文
     */
    private String formatTime(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day + "d");
        }
        if (hour > 0) {
            sb.append(hour + "h");
        }
        if (minute > 0) {
            sb.append(minute + "m");
        }

        return sb.toString();
    }

    /**
     * 设置子订单按钮状态
     *
     * @param orderDetail
     * @return
     */
    private String handleButtonState(NewBasicCouponOrderDetail orderDetail) {
        //设置可以退款的情况
        if ((orderDetail.getPayState().equals(OrderDetailStateEnum.Pay.getStateCode()) && orderDetail.getCouponState().equals(OrderDetailStateEnum.Apply.getStateCode())) ||
                (orderDetail.getPayState().equals(OrderDetailStateEnum.Pay.getStateCode()) && orderDetail.getCouponState().equals(OrderDetailStateEnum.Not.getStateCode()))) {
            return DetailOrApplyEnum.Apply.getStateCode();
        } else if ("P".equals(orderDetail.getIsVoluntaryRefund()) ||
                (orderDetail.getPayState().equals(OrderDetailStateEnum.UnPay.getStateCode()) && orderDetail.getCouponState().equals(OrderDetailStateEnum.Apply.getStateCode())) ||
                (orderDetail.getPayState().equals(OrderDetailStateEnum.UnPay.getStateCode()) && orderDetail.getCouponState().equals(OrderDetailStateEnum.Cancel.getStateCode()))) {
            return null;
        } else if (OrderDetailStateEnum.WaitFirstCheck.getStateCode().equals(orderDetail.getCouponState()) || OrderDetailStateEnum.WaitSecondCheck.getStateCode().equals(orderDetail.getCouponState()) ||
                OrderDetailStateEnum.FirstCheckFail.getStateCode().equals(orderDetail.getCouponState()) || OrderDetailStateEnum.SecondCheckFail.getStateCode().equals(orderDetail.getCouponState()) ||
                OrderDetailStateEnum.ApplyRefund.getStateCode().equals(orderDetail.getCouponState()) || OrderDetailStateEnum.Refund.getStateCode().equals(orderDetail.getCouponState())) {
            return DetailOrApplyEnum.Detail.getStateCode();
        }
        return null;
    }

    @ApiOperation(value = "新查询订单退单详情", notes = "新查询订单退单详情（/Order/BasicGetCouponRefund）")
    @RequestMapping(value = "/queryCouponRefundOrderDetail/{couponSource}", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<Object> queryCouponRefundOrderDetail(@RequestBody BaseReq<NewCouponRefundOrderDetailReq> req, @PathVariable(value = "couponSource") String couponSource, HttpServletRequest request) {
        BaseResp<Object> resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        try {
            // 参数校验
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<NewCouponRefundOrderDetailReq>>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }

            NewCouponRefundOrderDetailReq couponOrderDetail = req.getRequest();
            // 获取公共参数
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            // 使用新订单退单详情
            couponOrderDetail.setRequestIp(ip);
            couponOrderDetail.setChannelCode(channelCode);
            couponOrderDetail.setUserNo(userNo);
            couponOrderDetail.setVersion(HandlerConstants.VERSION);
            Object objData;
            switch (couponSource) {
                case "PaySeat":
                    // 定义返回数据解析格式
                    TypeReference<NewOrderBaseResponse<PaySeatOrderRefundDetail>> typeReference = new TypeReference<NewOrderBaseResponse<PaySeatOrderRefundDetail>>() {
                    };
                    // 设置付费选座
                    PaySeatOrderRefundDetail refundDetailResponse = orderManage.queryCouponRefundOrderDetail(couponOrderDetail, typeReference);
                    if (null == refundDetailResponse) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("未获取到退单记录");
                        return resp;
                    }
                    if (StringUtils.isNotBlank(refundDetailResponse.getRemark())) {
                        Map<?, ?> map = JsonUtil.jsonToMap(refundDetailResponse.getRemark());
                        refundDetailResponse.setRemarkMap(map);
                    }
                    String flightDate = refundDetailResponse.getFlightDate().substring(0, 10);
                    refundDetailResponse.setFlightDate(flightDate);
                    // 获取全部机场信息
                    FlightInfoResult flightInfoResult = flightService.getFlightInfo(flightDate, refundDetailResponse.getFlightNo(), refundDetailResponse.getDepAirport(), refundDetailResponse.getArrAirport(), null);
                    refundDetailResponse.setDepCityName(flightInfoResult.getDepCityName());
                    refundDetailResponse.setDepAirportName(flightInfoResult.getDepAirportName());
                    refundDetailResponse.setArrCityName(flightInfoResult.getArrCityName());
                    refundDetailResponse.setArrAirportName(flightInfoResult.getArrAirportName());
                    refundDetailResponse.setFlightNo(flightInfoResult.getFlightNo());
                    refundDetailResponse.setDepAirportTerminal(flightInfoResult.getDepAirportTerminal());
                    refundDetailResponse.setArrAirportTerminal(flightInfoResult.getArrAirportTerminal());
                    OrderCouponDto orderCouponDto = refundDetailResponse.getOrderCouponDto();
                    BigDecimal couponPrice = BigDecimal.ZERO;
                    if (null != orderCouponDto) {
                        SeatOrderCoupon seatOrderCoupon = new SeatOrderCoupon();
                        seatOrderCoupon.setCouponNo(orderCouponDto.getCouponNo());
                        seatOrderCoupon.setCouponType(orderCouponDto.getCouponType());
                        seatOrderCoupon.setCouponPrice(orderCouponDto.getCouponPrice());
                        refundDetailResponse.setSeatOrderCoupon(seatOrderCoupon);
                        couponPrice = new BigDecimal(orderCouponDto.getCouponPrice());
                    }
                    // 子订单金额
                    BigDecimal couponSale = new BigDecimal(refundDetailResponse.getCouponSaleAmount());
                    BigDecimal useScore = new BigDecimal(refundDetailResponse.getFfpUseScore());
                    BigDecimal cash = couponSale.subtract(useScore).subtract(couponPrice);
                    refundDetailResponse.setFfpUseScore(refundDetailResponse.getFfpUseScore());
                    refundDetailResponse.setCashAmount(cash.stripTrailingZeros().toPlainString());
                    //设置退单状态名
                    RefundEnum refundEnum = OrderUtil.convertRefundOrderState(refundDetailResponse.getCouponState(), refundDetailResponse.getIsVoluntaryRefund(), refundDetailResponse.getRebateState());
                    refundDetailResponse.setRefundState(refundEnum.getStateCode());
                    refundDetailResponse.setOrderStateName(refundEnum.getStateDesc());
                    //设置描述信息
                    if (refundEnum.equals(RefundEnum.In) || refundEnum.equals(RefundEnum.Success)) {
                        refundDetailResponse.setTextDescription("实际退款金额以最终审核结果为准");
                    } else if (refundEnum.equals(RefundEnum.Fail)) {
                        refundDetailResponse.setTextDescription("您申请退款的座位不符合非自愿退订条件");
                    } else {
                        refundDetailResponse.setTextDescription("");
                    }
                    if (StringUtil.isNullOrEmpty(refundDetailResponse.getRebateNo())) {
                        refundDetailResponse.setRebateNo("");
                    }
                    refundDetailResponse.setOrderCouponDto(null);
                    objData = refundDetailResponse;
                    break;
                default:
                    resp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
                    resp.setResultInfo("不支持的权益券类型");
                    return resp;
            }

            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(objData);
            String respJson = JsonMapper.buildNormalMapper().toJson(resp);
            log.info(LOG_RESP_TWO, reqId, ip, respJson);
            return resp;
        } catch (ServiceException se) {
            log.error("权益券退单详情查询异常", se);
            resp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
            resp.setResultInfo(se.getMessage());
            return resp;
        } catch (Exception e) {
            log.error("权益券退单详情查询异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            return resp;
        }
    }

    @ApiOperation(value = "权益券订单列表", notes = "权益券订单列表")
    @RequestMapping(value = "/queryOrderList", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp queryOrderList(@RequestBody BaseReq<CouponOrderReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            CouponOrderReq couponOrderReq = req.getRequest();
            if (StringUtils.isBlank(couponOrderReq.getFfpId()) || StringUtils.isBlank(couponOrderReq.getFfpCardNo())) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //检验登录
            boolean flag = this.checkKeyInfo(couponOrderReq.getFfpId(), couponOrderReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            if (couponOrderReq.getPageNo() <= 0) {
                couponOrderReq.setPageNo(1);
            }
            if (couponOrderReq.getPageSize() <= 0) {
                couponOrderReq.setPageSize(20);
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
            Map<String, Object> pageMap;
            //订单列表接口
            String realChannelCode = request.getHeader("channelCode");
            List<String> voucherTypes = VoucherTypesEnum.getShowVoucherTypeCodes();
            //主题卡类型动态Apollo的配置
            voucherTypes.addAll(handConfig.getThemeCouponList());
            String queryUrl = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_COUPON_ORDER_LIST;
            pageMap = queryOrderList(queryUrl, channelCode, userNo, couponOrderReq, request, voucherTypes);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(pageMap);
            return resp;
        } catch (ServiceException se) {
            log.error("权益券订单列表查询异常", se);
            resp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
            resp.setResultInfo(se.getMessage());
            return resp;
        } catch (Exception e) {
            log.error("权益券订单列表查询异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            return resp;
        }
    }


    @ApiOperation(value = "权益券订单收银台详情", notes = "权益券订单收银台详情")
    @RequestMapping(value = "/queryCouponPayDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp queryCouponPayDetail(@RequestBody BaseReq<CouponOrderReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //检验登录
        CouponOrderReq couponOrderReq = req.getRequest();
        boolean flag = this.checkKeyInfo(couponOrderReq.getFfpId(), couponOrderReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String orderNo = couponOrderReq.getOrderNo();
        String channelOrderNo = couponOrderReq.getChannelOrderNo();
        ObjCheckUtil.notBlank(orderNo, "订单号不可为空");
        ObjCheckUtil.notBlank(channelOrderNo, "渠道订单号不可为空");
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        Map<String, Object> pageMap = queryOrderDetailPay(channelCode, userNo, couponOrderReq, request);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(pageMap);
        return resp;
    }

    //查询收银台需要的订单支付信息
    private Map<String, Object> queryOrderDetailPay(String channelCode, String userNo, CouponOrderReq couponOrderReq, HttpServletRequest request) {
        CouponProductOrderGetRequestDto requestDto = new CouponProductOrderGetRequestDto();
        List<SaleCouponGet> saleOrderList = new LinkedList<>();
        Map<String, Object> pageMap = new HashMap<>();
        BeanUtils.copyNotNullProperties(couponOrderReq, requestDto);
        requestDto.setUserNo(userNo);
        requestDto.setIsRemoved("0");
        requestDto.setChannelCode(channelCode);
        String ip = getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");

        PtSaleCouponOrderGetResponse ptSaleCouponOrderGetResponse = orderManage.queryCouponList(requestDto, headMap);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptSaleCouponOrderGetResponse.getResultCode()) && !StringUtil.isNullOrEmpty(ptSaleCouponOrderGetResponse.getCouponOrderList())) {
            List<PtSaleCouponOrderGetResponse.CouponOrder> couponOrderList = ptSaleCouponOrderGetResponse.getCouponOrderList();
            //订单处理
            //当前时间，单位为毫秒
            long curTime = System.currentTimeMillis();
            for (PtSaleCouponOrderGetResponse.CouponOrder couponOrder : couponOrderList) {
                SaleCouponGet saleCouponGet = new SaleCouponGet();
                org.springframework.beans.BeanUtils.copyProperties(couponOrder, saleCouponGet);
                //处理联系人，退款规则等公用属性
                String couponSource = couponOrder.getSaleCouponList().get(0).getCouponSource();
                saleCouponGet.setCouponSource(couponSource);
                //处理固包的特殊情况
                if (VoucherTypesEnum.PACKAGE.getCode().equals(couponSource)) {
                    int count = couponOrder.getSaleCouponList().size() / 2;
                    saleCouponGet.setSaleCouponCount(count);
                    saleCouponGet.setPrice((couponOrder.getOrderTotalAmount() + couponOrder.getFfpUseTotalScore()) / count);
                } else {
                    saleCouponGet.setSaleCouponCount(couponOrder.getSaleCouponList().size());
                }

                List<CouponShow> showList = new LinkedList<>();
                //固包券特殊处理
                //优惠劵处理  开始
                for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon : couponOrder.getSaleCouponList()) {
                    CouponShow couponShow = new CouponShow();
                    org.springframework.beans.BeanUtils.copyProperties(saleCoupon, couponShow);
                    //固包券处理购买来源
                    if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(couponShow.getVoucherType())) {
                        if (!StringUtil.isNullOrEmpty(saleCoupon.getCouponDynamicParam())) {
                            String couponDynamicParam = saleCoupon.getCouponDynamicParam();
                            PackageWifiParamProduct packageWifiParamProduct = (PackageWifiParamProduct) JsonUtil.jsonToBean(couponDynamicParam, PackageWifiParamProduct.class);
                            saleCouponGet.setBuyProductType((packageWifiParamProduct.getBuyCouponType() != null && packageWifiParamProduct.getBuyCouponType()));
                        } else {
                            saleCouponGet.setBuyProductType(false);
                        }
                    }
                    //优惠券礼包券处理
                    dealCouponInfo(couponShow, saleCoupon, couponOrder.getIsBundleSale());
                    couponShow.setActivityName(showOrderNameBySource(couponShow.getCouponSource(), couponShow.getCouponPrice(), couponShow.getActivityName()));
                    OrderCouponStateEnum orderState = OrderCouponStateEnum.getEnum(couponShow.getCouponState());
                    couponShow.setCouponStateName(orderState == null ? "" : orderState.getDesc());
                    showList.add(couponShow);
                }
                //优惠劵处理  结束
                List<CouponShow> couponShows = new LinkedList<>();
                //处理固包券的特殊情况，将行李券和固包券两两组合
                if (VoucherTypesEnum.PACKAGE.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
                    List<CouponShow> finalCouponShows = couponShows;
                    showList.forEach(s -> {
                        if (VoucherTypesEnum.BAGGAGE.getCode().equals(s.getVoucherType())) {
                            //该券为行李券，查询与之匹配的固包子券
                            List<CouponShow> list = new ArrayList<>();
                            showList.forEach(sh -> {
                                if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(sh.getVoucherType()) && s.getMainVoucherNo().equals(sh.getMainVoucherNo())) {
                                    s.setDepTime(sh.getDepTime());
                                    s.setFlightNo(sh.getFlightNo());
                                    list.add(sh);
                                }
                            });
                            s.setVouchers(list);
                            finalCouponShows.add(s);
                        }
                    });
                    couponShows = finalCouponShows;
                } else {
                    couponShows = showList;
                }
                //支付时限
                String bookDateStr = saleCouponGet.getOrderDatetime();
                Date bookDate = DateUtils.toDate(bookDateStr, DATE_FORMAT);
                //下单时间
                long bookTime = bookDate.getTime();
                //支付时限  小于0代表已过期
                long endTime;
                if (VoucherTypesEnum.DELIVERY.getCode().equals(couponSource)) {
                    endTime = DateUtils.dateAddOrLessSecond(bookDate, 5 * 60).getTime();
                } else {
                    endTime = DateUtils.dateAddOrLessSecond(bookDate, handConfig.getTimeLimit() * 60).getTime();
                }
                saleCouponGet.setCouponList(couponShows);
                //处理订单基本信息，退款状态
                dealSaleCouponGet(saleCouponGet, couponOrder.getOrderState(), couponOrder.getOrderStateName(), couponOrder.getPayState(), bookTime, endTime, curTime);
                //订单详情页下单时间格式
                saleCouponGet.setOrderFormatDatetime(bookDateStr.substring(0, bookDateStr.length() - 3));
                //支付页使用的下单时间格式
                saleCouponGet.setOrderDatetime(bookDateStr);
                //支付方式，支付时间
                saleCouponGet.setPayMethodName(couponOrder.getPayMethod() == null ? "" : couponOrder.getPayMethod());
                saleCouponGet.setPaidDatetime(couponOrder.getPaidDateTime() == null ? "" : couponOrder.getPaidDateTime());
                //优惠券礼包采用固包的展示方式
                dealCouponBagOrder(saleCouponGet, couponOrder);
                saleOrderList.add(saleCouponGet);
            }
        }
        pageMap.put("orderList", saleOrderList);
        return pageMap;
    }

    @ApiOperation(value = "取消权益券订单", notes = "取消权益券订单")
    @RequestMapping(value = "/cancelCouponOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp cancelCouponOrder(@RequestBody BaseReq<CancelOrderReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        String reqJson = JsonUtil.objectToJson(req);
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            //检验登录
            CancelOrderReq cancelOrderReq = req.getRequest();
            boolean flag = this.checkKeyInfo(cancelOrderReq.getFfpId(), cancelOrderReq.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //预留登机牌的单独调用取消
            if (VoucherTypesEnum.CHECKINSUBSTITUTION.getCode().equals(cancelOrderReq.getCouponSource())) {
                cancelBoardingPass(cancelOrderReq, channelCode, userNo, ip, resp);
                return resp;
            } else if (VoucherTypesEnum.PAY_SEAT.getCode().equals(cancelOrderReq.getCouponSource())) {
                cancelEmdSeatOrder(cancelOrderReq, channelCode, userNo, ip, resp);
                return resp;
            } else if (VoucherTypesEnum.EXTRABAGGAGE.getCode().equals(cancelOrderReq.getCouponSource())) {
                try {
                    req.getRequest().setCancelReason("预付费行李取消");
                    resp = cancelPrepaymentBaggage(req, request);
                } catch (BaggageException e) {
                    resp.setResultCode(e.getErrorCode());
                    resp.setResultInfo(e.getErrorMessage());
                    return resp;
                }
                return resp;
            } else if (VoucherTypesEnum.INNNCUSTOMSERVE.getCode().equals(cancelOrderReq.getCouponSource())
                    || VoucherTypesEnum.INNN.getCode().equals(cancelOrderReq.getCouponSource())) {
                try {
                    req.getRequest().setCancelReason("日本接送机预约服务取消");
                    resp = cancelPrepaymentBaggage(req, request);
                } catch (BaggageException e) {
                    resp.setResultCode(e.getErrorCode());
                    resp.setResultInfo(e.getErrorMessage());
                    return resp;
                }
                return resp;
            } else {
                cancelOtherOrder(cancelOrderReq, channelCode, ip, resp);
                return resp;
            }

        } catch (Exception e) {
            saveError(SERVICE_NAME, reqId, ip, reqJson, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            return resp;
        }
    }

    /**
     * 取消预付费行李订单
     *
     * @param req
     * @param request
     * @return
     * @throws BaggageException
     */
    private BaseResp cancelPrepaymentBaggage(@RequestBody BaseReq<CancelOrderReq> req, HttpServletRequest request) throws BaggageException {
        BaseResp<BaggageOrderInfoResp> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        CancelOrderReq cancelPrepaymentBaggageDTO = req.getRequest();
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        CancelBaggageReq cancelBaggageReq = new CancelBaggageReq();
        cancelBaggageReq.setChannelCode(req.getChannelCode());
        cancelBaggageReq.setOrderNo(cancelPrepaymentBaggageDTO.getOrderNo());
        cancelBaggageReq.setChannelOrderNo(cancelPrepaymentBaggageDTO.getChannelOrderNo());
        cancelBaggageReq.setRequestIp(ip);
        cancelBaggageReq.setVersion("10");
        cancelBaggageReq.setReason(cancelPrepaymentBaggageDTO.getCancelReason());

        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.ORDER_CANCEL_COUPONORDER;
        HttpResult httpResult = this.doPostClient(cancelBaggageReq, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (null != httpResult && httpResult.isResult()) {
            CancelBaggageResp cancelBaggageResp = (CancelBaggageResp) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<CancelBaggageResp>() {
            }.getType());
            if (!cancelBaggageResp.getResultCode().equals("1001")) {
                throw new BaggageException(WSEnum.ERROR.getResultCode(), "取消权益服务异常");
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            throw new BaggageException(WSEnum.ERROR.getResultCode(), WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }


    /**
     * 取消EMD选座订单
     *
     * @param cancelOrderReq
     * @param channelCode
     * @param userNo
     * @param ip
     * @param resp
     */
    private void cancelEmdSeatOrder(CancelOrderReq cancelOrderReq, String channelCode, String userNo, String ip, BaseResp resp) {
        SeatOrderInfo seatOrderInfo = checkInSeatService.getSeatOrderInfo(channelCode, ip, cancelOrderReq.getOrderNo(), cancelOrderReq.getChannelOrderNo(), null);
        if (!"Y".equals(seatOrderInfo.getIsSelf())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("外部渠道订单，请去原渠道取消！");
            return;
        }
        // 封裝请求参数
        com.juneyaoair.baseclass.common.request.BaseRequestDTO<CancelOrderReq> requestBaseRequest = new com.juneyaoair.baseclass.common.request.BaseRequestDTO<>();
        requestBaseRequest.setRequest(cancelOrderReq);
        requestBaseRequest.setIp(ip);
        requestBaseRequest.setVersion("2.0");
        requestBaseRequest.setChannelCode(ControllerUtil.getChannelInfo(channelCode, "50"));
        requestBaseRequest.setUserNo(userNo);
        // 调用预订接口
        HttpResult serviceResult = HttpUtil.doPostClient(requestBaseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_EMD_CANCEL_SEAT_ORDER, READ_TIMEOUT, CONNECT_TIMEOUT);
        // 没有响应结果
        if (!serviceResult.isResult()) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("网络出错了，请重新提交");
            return;
        }
        // 存在响应解析数据
        Type type = new TypeToken<BaseResultDTO>() {
        }.getType();
        BaseResultDTO resultBaseResult = (BaseResultDTO) JsonUtil.jsonToBean(serviceResult.getResponse(), type);
        // 返回失败
        if (!ENUM_RESPONSE_STATUS.SUCC.code().equals(resultBaseResult.getResultCode())) {
            resp.setResultCode(resultBaseResult.getResultCode());
            resp.setErrorInfo(resultBaseResult.getErrorMsg());
        } else {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        }
    }

    /**
     * 除预留登机牌之外权益订单取消
     *
     * @param cancelOrderReq
     * @param channelCode
     * @param ip
     * @param resp
     */
    private void cancelOtherOrder(CancelOrderReq cancelOrderReq, String channelCode, String ip, BaseResp resp) {
        //构建请求类
        CouponProductOrderCancelRequestDto requestDto = new CouponProductOrderCancelRequestDto();
        BeanUtils.copyNotNullProperties(cancelOrderReq, requestDto);
        requestDto.setChannelCode(channelCode);
        requestDto.setVersion(HandlerConstants.VERSION);
        requestDto.setUserNo(getChannelInfo(channelCode, "10"));
        //发起请求
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.CANCEL_COUPON_ORDER_PRODUCT, headMap);
        if (result.isResult()) {
            CouponProductOrderCancelResponseDto responseDto = (CouponProductOrderCancelResponseDto) JsonUtil.jsonToBean(result.getResponse(), CouponProductOrderCancelResponseDto.class);
            if ("1001".equals(responseDto.getResultCode())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("取消订单失败");
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_RESP_ONE);
        }
    }

    /**
     * 预留登机牌订单取消
     *
     * @param cancelOrderReq
     * @param channelCode
     * @param ip
     * @param resp
     */
    private void cancelBoardingPass(CancelOrderReq cancelOrderReq, String channelCode, String userNo, String ip, BaseResp resp) {
        PtRequest ptRequest = RightCouponObjectConvert.createBaseRequest(channelCode, cancelOrderReq.getFfpId(), cancelOrderReq.getFfpCardNo(), userNo, ip);
        BaseCouponOrderIdentity couponOrderIdentity = new BaseCouponOrderIdentity();
        couponOrderIdentity.setOrderNo(cancelOrderReq.getOrderNo());
        couponOrderIdentity.setOrderChannelOrderNo(cancelOrderReq.getChannelOrderNo());
        ptRequest.setRequest(couponOrderIdentity);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = this.doPostClient(ptRequest, HandlerConstants.URL_FARE_API + HandlerConstants.BOARDING_PRODUCT_CANCEL, headMap);
        if (httpResult.isResult()) {
            PtResponse ptResponse = (PtResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtResponse.class);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(httpResult.getResponse());
        }
    }

    @ApiOperation(value = "权益券订单列表(按类型)", notes = "权益券订单列表(按类型)")
    @RequestMapping(value = "/queryPackageCouponOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryPackageCouponOrder(@RequestBody BaseReq<CouponOrderReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //检验登录
            CouponOrderReq couponOrderReq = req.getRequest();
            boolean flag = this.checkKeyInfo(couponOrderReq.getFfpId(), couponOrderReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String respJson = JsonUtil.objectToJson(resp);
                log.info(LOG_RESP_TWO, reqId, ip, respJson);
                return resp;
            }
            if (couponOrderReq.getPageNo() <= 0) {
                couponOrderReq.setPageNo(1);
            }
            if (couponOrderReq.getPageSize() <= 0) {
                couponOrderReq.setPageSize(20);
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            //订单列表
            Map<String, Object> pageMap = queryPackageOrderList(channelCode, userNo, couponOrderReq, request);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(pageMap);
            String respJson = JsonUtil.objectToJson(resp);
            log.info(LOG_RESP_TWO, reqId, ip, respJson);
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            log.error(LOG_ERROR, reqId, ip, e);
            return resp;
        }
    }

    @ApiOperation(value = "创建权益券订单", notes = "创建权益券订单")
    @RequestMapping(value = "/createOrderInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public CouponProductBuyResponse createOrderInfo(@RequestBody BaseReq<ComCounponReq> baseReq, HttpServletRequest request) {
        CouponProductBuyResponse couponProductBuyResponse = new CouponProductBuyResponse();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq<ComCounponReq>>> violations = validator.validate(baseReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            couponProductBuyResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            couponProductBuyResponse.setErrorInfo(violations.iterator().next().getMessage());
            return couponProductBuyResponse;
        }
        //判断 invitationCode  长度不能大于50，并且校验输入格式数字和字母
        String invitationCode = baseReq.getRequest().getInvitationCode();
        if (!StringUtil.isNullOrEmpty(invitationCode)) {
            String regex = "^[a-z0-9A-Z]+$";
            boolean matches = invitationCode.matches(regex);
            if (!matches) {
                couponProductBuyResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                couponProductBuyResponse.setErrorInfo("输入格式不正确");
                return couponProductBuyResponse;
            }
            if (invitationCode.length() > 50) {
                couponProductBuyResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                couponProductBuyResponse.setErrorInfo("长度超过最大限制");
                return couponProductBuyResponse;
            }

        }
        ComCounponReq req = baseReq.getRequest();
        String channelCode = baseReq.getChannelCode();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), baseReq.getChannelCode());
        if (!flag) {
            couponProductBuyResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            couponProductBuyResponse.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return couponProductBuyResponse;
        }
        if (null != req.getScore()) {
            BaseResp resp = orderService.checkFreeScoreLimit(req.getFfpCardNo(), req.getFfpId(), baseReq.getChannelCode(), getChannelInfo(baseReq.getChannelCode(), "40"), req.getScore(), req.getScorePwd(), request);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                couponProductBuyResponse.setResultCode(resp.getResultCode());
                couponProductBuyResponse.setErrorInfo(resp.getResultInfo());
                return couponProductBuyResponse;
            }
        }
        //生成统一订单数据
        CouponProductBuyRequest couponProductBuyRequest = new CouponProductBuyRequest();
        couponProductBuyRequest.setVersion("10");
        couponProductBuyRequest.setFfpId(req.getFfpId());
        couponProductBuyRequest.setFfpCardNo(req.getFfpCardNo());
        //微信渠道暂时无产品查询时，查询时变换为MOBILE
        if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(baseReq.getChannelCode())) {
            channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
        }
        String userNo = getChannelInfo(channelCode, "10");
        couponProductBuyRequest.setChannelCode(channelCode);
        couponProductBuyRequest.setUserNo(userNo);
        couponProductBuyRequest.setChannelOrderNo(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + (int) (Math.random() * 1000));
        couponProductBuyRequest.setIdNbr(req.getIdNo());
        BookProductInfo bookProductInfo = new BookProductInfo();
        bookProductInfo.setProductId(req.getProductId());
        bookProductInfo.setProductName(req.getProductName());
        bookProductInfo.setProductType(req.getProductType());
        //生成产品数据
        BookResourceInfo[] bookResourceInfos = new BookResourceInfo[1];
        BookResourceInfo bookResourceInfo = new BookResourceInfo();
        bookResourceInfo.setResourceId(req.getResourceId());
        bookResourceInfo.setResourceType(req.getResourceType());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, 10);
        Date now = calendar.getTime();
        bookResourceInfo.setUseStartDate(new SimpleDateFormat(DATE_FORMAT).format(now));
        //价格明细
        ResourcePriceDetail[] resourcePriceDetails = new ResourcePriceDetail[1];
        ResourcePriceDetail resourcePriceDetail = new ResourcePriceDetail();
        resourcePriceDetail.setPriceType(req.getPriceType());
        resourcePriceDetail.setPriceUnit(req.getPriceUnit());
        resourcePriceDetail.setBookingCount(req.getCount());
        resourcePriceDetail.setUnitNumber(1.0);
        resourcePriceDetail.setSalePrice(req.getPrice());
        resourcePriceDetails[0] = resourcePriceDetail;
        bookResourceInfo.setResourcePriceDetails(resourcePriceDetails);
        bookResourceInfos[0] = bookResourceInfo;
        bookProductInfo.setResources(bookResourceInfos);
        List<BookProductInfo> bookProductInfoList = new ArrayList<>();
        bookProductInfoList.add(bookProductInfo);
        couponProductBuyRequest.setProducts(bookProductInfoList);
        //产品总价
        BigDecimal totalPrice = BigDecimal.valueOf(req.getPrice()).multiply(BigDecimal.valueOf(req.getCount()));
        couponProductBuyRequest.setTotalPrice(totalPrice);
        //订单实际支付金额
        if (null != req.getScore()) {
            totalPrice = totalPrice.subtract(BigDecimal.valueOf(req.getScore()));
        }
        if (BigDecimal.valueOf(req.getTotalPrice()).compareTo(totalPrice) != 0) {
            couponProductBuyResponse.setResultCode(WSEnum.ERROR.getResultCode());
            couponProductBuyResponse.setErrorInfo("总价异常！");
            return couponProductBuyResponse;
        }
        couponProductBuyRequest.setPayAmount(totalPrice);
        couponProductBuyRequest.setUseScore(req.getScore());
        couponProductBuyRequest.setLinker(req.getLinker() == null ? "" : req.getLinker());
        couponProductBuyRequest.setLinkerMobile(req.getLinkerMobile());
        //优惠券邀请码
        couponProductBuyRequest.setInvitationCode(req.getInvitationCode());
        //发起请求
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_BUY_PRODUCT_V2;
        try {
            HttpResult result = this.doPostClient(couponProductBuyRequest, url, headMap);
            if (result.isResult()) {
                couponProductBuyResponse = (CouponProductBuyResponse) JsonUtil.jsonToBean(result.getResponse(), CouponProductBuyResponse.class);
                if ("1001".equals(couponProductBuyResponse.getResultCode())) {
                    couponProductBuyResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                    couponProductBuyResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    //0元支付
                    if (null != req.getScore() && req.getScore() > 0 && couponProductBuyRequest.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                        String key = getChannelInfo(baseReq.getChannelCode(), "20");
                        String postUrl = HandlerConstants.URL_PAY;
                        Map<String, String> parametersMap = VirtualPaymentConvert.payment0(baseReq.getChannelCode(),
                                couponProductBuyResponse.getOrderNo(), couponProductBuyResponse.getChannelOrderNo(), key,
                                "CouponType", "", "O");
                        parametersMap.put("UseScore", String.valueOf(req.getScore()));
                        HttpResult payResult = doPayPost(postUrl, parametersMap);
                        PaymentResp paymentResp;
                        if (payResult.isResult()) {
                            String paymentInfo = payResult.getResponse().trim();
                            String json = JsonUtil.objectToJson(paymentInfo);
                            log.info("请求号:{}，IP地址:{}，0元支付结果：{}", reqId, ip, json);
                            paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                            //虚拟支付成功
                            if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                                couponProductBuyResponse.setResultCode("P10001");
                                couponProductBuyResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                            } else {
                                couponProductBuyResponse.setResultCode(WSEnum.ERROR.getResultCode());
                                couponProductBuyResponse.setErrorInfo("权益券购买失败！");
                            }
                            String respJson = JsonUtil.objectToJson(couponProductBuyResponse);
                            log.info(LOG_RESP_TWO, reqId, ip, respJson);
                            return couponProductBuyResponse;
                        } else {
                            couponProductBuyResponse.setResultCode(WSEnum.ERROR.getResultCode());
                            couponProductBuyResponse.setErrorInfo("支付请求出错");
                            String respJson = JsonUtil.objectToJson(couponProductBuyResponse);
                            log.info(LOG_RESP_TWO, reqId, ip, respJson);
                            return couponProductBuyResponse;
                        }
                    }
                }
                return couponProductBuyResponse;
            } else {
                couponProductBuyResponse.setResultCode(WSEnum.ERROR.getResultCode());
                couponProductBuyResponse.setErrorInfo("订单创建失败");
            }
        } catch (Exception e) {
            log.error(LOG_ERROR, reqId, ip, e);
            couponProductBuyResponse.setResultCode("999999");
            couponProductBuyResponse.setErrorInfo("订单创建失败");
        }
        return couponProductBuyResponse;
    }

    @ApiOperation(value = "权益券申请退款", notes = "权益券申请退款")
    @RequestMapping(value = "/applyRefundProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp applyRefundProduct(@RequestBody @Validated BaseReq<RefundProductReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = MdcUtils.getRequestId();
        String ip = getClientIP(request);
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            //检验登录
            RefundProductReq refundProductReq = req.getRequest();
            boolean flag = this.checkKeyInfo(refundProductReq.getFfpId(), refundProductReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //pdi订单需要去第三方取消
            if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(refundProductReq.getCouponSource())) {
                if (!cancelPdiOrder(refundProductReq)) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("订单取消异常");
                    return resp;
                }
            }
            //主题卡退款需要判断存在关联机票订单
            if (StringUtils.isNotBlank(req.getRequest().getCouponSource())
                    && req.getRequest().getCouponSource().matches(PatternCommon.TOTALTYPE)
                    && CollectionUtils.isNotEmpty(req.getRequest().getVoucherNos())) {
                PtorRequest ptorRequest = new PtorRequest();
                ptorRequest.setChannelNo(channelCode);
                ptorRequest.setVersion(HandlerConstants.VERSION);
                ptorRequest.setUserNo(userNo);
                ptorRequest.setCouponCode(req.getRequest().getVoucherNos().get(0));
                PtorResponse ptorResponse = orderManage.QueryOrderPayStateByCouponCode(ptorRequest);
                if (CollectionUtils.isNotEmpty(ptorResponse.getOrderInfo())) {
                    resp.setResultCode(WSEnum.ALERT_ERROR_ORDER_MESSAGE.getResultCode());
                    resp.setResultInfo(WSEnum.ALERT_ERROR_ORDER_MESSAGE.getResultInfo());
                    return resp;
                }
                if (StringUtils.isNotEmpty(ptorResponse.getNoShowFlag()) && "Y".equals(ptorResponse.getNoShowFlag())) {
                    resp.setResultCode(WSEnum.ALERT_ERROR_NOSHOW_MESSAGE.getResultCode());
                    resp.setResultInfo(WSEnum.ALERT_ERROR_NOSHOW_MESSAGE.getResultInfo());
                    return resp;
                }
            }

            List<CouponShow> couponList = null;
            CouponOrderReq couponOrderReq = new CouponOrderReq();
            couponOrderReq.setPageNo(1);
            couponOrderReq.setPageSize(20);
            BeanUtils.copyNotNullProperties(req.getRequest(), couponOrderReq);
            Map<String, Object> stringObjectMap = queryOrderDetail(req.getChannelCode(), userNo, couponOrderReq, request);
            Object list = stringObjectMap.get("orderList");
            List<SaleCouponGet> list1 = castList(list, SaleCouponGet.class);
            SaleCouponGet saleCouponGet1 = new SaleCouponGet();
            for (SaleCouponGet saleCouponGet : list1) {
                if (PayEnum.Pay.getStateCode().equals(saleCouponGet.getPayState())) {
                    BeanUtils.copyNotNullProperties(saleCouponGet, saleCouponGet1);
                    couponList = saleCouponGet.getCouponList();
                } else {
                    resp.setResultInfo("该订单还未支付，暂不能退款");
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    return resp;
                }
            }

            if (OrderCouponStateEnum.Refund.getStateCode().equals(saleCouponGet1.getOrderState())) {
                resp.setResultInfo("该权益订单已经退款");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                return resp;
            }
            if (VoucherTypesEnum.PACKAGE.getCode().equals(saleCouponGet1.getCouponSource())) {
                for (CouponShow couponShow : couponList) {
                    if (!couponShow.getCouponState().equals(OrderCouponStateEnum.Not.getStateCode())) {
                        resp.setResultInfo("该礼包中的有些优惠劵已被使用");
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        return resp;
                    }
                }
            }
            if ("INNN".equals(refundProductReq.getCouponSource())
                    || "INNNCustomServe".equals(refundProductReq.getCouponSource())
                    || "ServiceButler".equals(refundProductReq.getCouponSource())) {
                //构建请求参数
                CouponProductRefundRequestDto couponProductRefundRequestDto = new CouponProductRefundRequestDto();
                BeanUtils.copyNotNullProperties(refundProductReq, couponProductRefundRequestDto);
                couponProductRefundRequestDto.setVersion(HandlerConstants.VERSION);
                couponProductRefundRequestDto.setUserNo(userNo);
                couponProductRefundRequestDto.setChannelCode(req.getChannelCode());
                couponProductRefundRequestDto.setCouponSource(refundProductReq.getCouponSource());
                //
                List<RefundCouponInfoDto> basicRefundCouponInfoDto = new ArrayList<>();
                RefundCouponInfoDto refundCouponInfoDto = new RefundCouponInfoDto();
                // 查询新详情接口，匹配券号，用新详情接口中的CouponSaleAmount  和    FfpUseScore处理金额和积分
                NewCouponOrderDetailReq couponOrderDetail = new NewCouponOrderDetailReq();
                couponOrderDetail.setOrderNo(refundProductReq.getOrderNo());
                couponOrderDetail.setChannelCode(req.getChannelCode());
                couponOrderDetail.setChannelOrderNo(refundProductReq.getChannelOrderNo());
                NewBasicOrderDetailResponse orderDetailResponse = orderManage.queryCouponOrderDetail(couponOrderDetail);
                // 重新处理basicRefundCouponInfoDto
                // 匹配orderDetailResponse.getBasicCouponOrderDtoList() 中的 CouponCode券号
                CreateCouponProductRefundRequestDto(refundProductReq, orderDetailResponse, refundCouponInfoDto, basicRefundCouponInfoDto, couponProductRefundRequestDto);
                couponProductRefundRequestDto.setBasicRefundCouponInfoDto(basicRefundCouponInfoDto);
                Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                HttpResult result = doPostClient(couponProductRefundRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.BASIC_REFUND_COUPON_ORDER, headMap);
                if (result.isResult()) {
                    CouponProductRefundResponseDto couponProductRefundResponseDto = (CouponProductRefundResponseDto) JsonUtil.jsonToBean(result.getResponse(), CouponProductRefundResponseDto.class);
                    if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(couponProductRefundResponseDto.getResultCode())) {
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(couponProductRefundResponseDto.getErrorInfo());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_RESP_ONE);
                }
                return resp;
            } else {
                //构建请求参数
                CouponProductRefundRequestDto couponProductRefundRequestDto = new CouponProductRefundRequestDto();
                BeanUtils.copyNotNullProperties(refundProductReq, couponProductRefundRequestDto);
                couponProductRefundRequestDto.setVersion(HandlerConstants.VERSION);
                couponProductRefundRequestDto.setUserNo(userNo);
                couponProductRefundRequestDto.setChannelCode(req.getChannelCode());
                Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                HttpResult result = doPostClient(couponProductRefundRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.REFUND_PRIDUCT_APPLY, headMap);
                if (result.isResult()) {
                    CouponProductRefundResponseDto couponProductRefundResponseDto = (CouponProductRefundResponseDto) JsonUtil.jsonToBean(result.getResponse(), CouponProductRefundResponseDto.class);
                    if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(couponProductRefundResponseDto.getResultCode())) {
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(couponProductRefundResponseDto.getErrorInfo());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_RESP_ONE);
                }
                return resp;
            }


        } catch (Exception e) {
            log.error("请求ID:{},{}申请权益退款异常", JsonUtil.objectToJson(req), e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            return resp;
        }
    }

    private void CreateCouponProductRefundRequestDto(RefundProductReq refundProductReq, NewBasicOrderDetailResponse orderDetailResponse, RefundCouponInfoDto refundCouponInfoDto, List<RefundCouponInfoDto> basicRefundCouponInfoDto, CouponProductRefundRequestDto couponProductRefundRequestDto) {
        List<String> voucherNos = refundProductReq.getVoucherNos().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(voucherNos)) {
            voucherNos.forEach(
                    voucherNo -> orderDetailResponse.getBasicCouponOrderDtoList()
                            .forEach(basicCouponOrderDto -> {
                                if (basicCouponOrderDto.getCouponCode().equals(voucherNo)) {
                                    // default
                                    refundCouponInfoDto.setReason("");
                                    refundCouponInfoDto.setIsVoluntaryRefund("Y");
                                    //
                                    refundCouponInfoDto.setCouponCode(voucherNo);
                                    refundCouponInfoDto.setAmount(new BigDecimal(basicCouponOrderDto.getCouponSaleAmount()));
                                    if (StringUtils.isNotBlank(basicCouponOrderDto.getFfpUseScore())) {
                                        refundCouponInfoDto.setScore(Integer.valueOf(basicCouponOrderDto.getFfpUseScore()));
                                    } else {
                                        refundCouponInfoDto.setScore(0);
                                    }
                                    basicRefundCouponInfoDto.add(refundCouponInfoDto);
                                }
                            }));
        } else {
            orderDetailResponse.getBasicCouponOrderDtoList()
                    .forEach(basicCouponOrderDto -> {
                        // default
                        refundCouponInfoDto.setReason("");
                        refundCouponInfoDto.setIsVoluntaryRefund("Y");
                        refundCouponInfoDto.setAmount(new BigDecimal(basicCouponOrderDto.getCouponSaleAmount()));
                        if (StringUtils.isNotBlank(basicCouponOrderDto.getFfpUseScore())) {
                            refundCouponInfoDto.setScore(Integer.valueOf(basicCouponOrderDto.getFfpUseScore()));
                        } else {
                            refundCouponInfoDto.setScore(0);
                        }
                        basicRefundCouponInfoDto.add(refundCouponInfoDto);
                    });
            couponProductRefundRequestDto.setVoucherNos(new ArrayList<>());
        }
    }

    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<T>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }

    private boolean cancelPdiOrder(RefundProductReq refundProductReq) {
        PdiResult<String> tokenResult = pdiManage.getToken();
        log.info("{}获取PDITOKEN结果{}", JsonUtil.objectToJson(refundProductReq), JsonUtil.objectToJson(tokenResult));
        if (tokenResult.isSuccess()) {
            PdiCancelOrder pdiCancelOrder = new PdiCancelOrder();
            pdiCancelOrder.setCancelType("1");
            pdiCancelOrder.setCancelReason("用户自行取消");
            pdiCancelOrder.setAirlineOrderNo(refundProductReq.getOrderNo());
            PdiResult<PdiCancelResult> pdiCancelResultPdiResult = pdiManage.cancleOrder(tokenResult.getData(), pdiCancelOrder);
            log.info("{}取消PDI订单结果:{}", JsonUtil.objectToJson(pdiCancelOrder), JsonUtil.objectToJson(pdiCancelResultPdiResult));
            if (!pdiCancelResultPdiResult.isSuccess()) {
                ApiErrorLogs apiErrorLogs = new ApiErrorLogs();
                apiErrorLogs.setFfpId(refundProductReq.getFfpId());
                apiErrorLogs.setFfpCard(refundProductReq.getFfpCardNo());
                apiErrorLogs.setPath(HandlerConstants.PDI_URL + HandlerConstants.PDI_CANCEL_ORDER);
                apiErrorLogs.setRequest(JsonUtil.objectToJson(pdiCancelOrder));
                apiErrorLogs.setResponse(JsonUtil.objectToJson(pdiCancelResultPdiResult));
                apiErrorLogsServiceImpl.saveApiErrorLogs(apiErrorLogs);
                return false;
            } else {
                PdiCancelResult pdiCancelResult = pdiCancelResultPdiResult.getData();
                if ("6".equals(pdiCancelResult.getOrderStatus())) {
                    return true;
                } else {
                    throw new ServiceException(pdiCancelResult.getMsg());
                }
            }
        } else {
            throw new ServiceException("订单取消验证异常");
        }
    }

    @ApiOperation(value = "权益券申请退款", notes = "权益券申请退款")
    @RequestMapping(value = "/refundPremiumProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp refundPremiumProduct(@RequestBody BaseReq<RefundPremiumProduct> req, HttpServletRequest request) {
        //1.数据校验
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_applyRefundProduct";
        String ip = getClientIP(request);
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //检验登录
            RefundPremiumProduct refundPremiumProduct = req.getRequest();
            boolean flag = this.checkKeyInfo(refundPremiumProduct.getFfpId(), refundPremiumProduct.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String respJson = JsonUtil.objectToJson(resp);
                log.info(LOG_RESP_TWO, reqId, ip, respJson);
                return resp;
            }
            if ("INNN".equals(refundPremiumProduct.getCouponSource()) || "INNNCustomServe".equals(refundPremiumProduct.getCouponSource())) {
                //构建请求参数
                CouponProductRefundRequestDto couponProductRefundRequestDto = new CouponProductRefundRequestDto();
                BeanUtils.copyNotNullProperties(refundPremiumProduct, couponProductRefundRequestDto);
                couponProductRefundRequestDto.setVersion(HandlerConstants.VERSION);
                couponProductRefundRequestDto.setUserNo(userNo);
                couponProductRefundRequestDto.setChannelCode(req.getChannelCode());
                couponProductRefundRequestDto.setCouponSource(refundPremiumProduct.getCouponSource());
                List<RefundCouponInfoDto> basicRefundCouponInfoDto = new ArrayList<>();
                RefundCouponInfoDto refundCouponInfoDto = new RefundCouponInfoDto();
                refundCouponInfoDto.setReason("");
                refundCouponInfoDto.setIsVoluntaryRefund("Y");
                refundCouponInfoDto.setAmount(BigDecimal.ZERO);
                refundCouponInfoDto.setScore(0);
                refundCouponInfoDto.setCouponCode(req.getRequest().getOrderProductIds().get(0));
                basicRefundCouponInfoDto.add(refundCouponInfoDto);
                couponProductRefundRequestDto.setBasicRefundCouponInfoDto(basicRefundCouponInfoDto);
                Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                HttpResult result = doPostClient(couponProductRefundRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.BASIC_REFUND_COUPON_ORDER, headMap);
                if (result.isResult()) {
                    CouponProductRefundResponseDto couponProductRefundResponseDto = (CouponProductRefundResponseDto) JsonUtil.jsonToBean(result.getResponse(), CouponProductRefundResponseDto.class);
                    if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(couponProductRefundResponseDto.getResultCode())) {
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(couponProductRefundResponseDto.getErrorInfo());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_RESP_ONE);
                }
                return resp;
            } else {
                //2.封装远程参数
                CouponProductRefundRequestDto couponProductRefundRequestDto = new CouponProductRefundRequestDto();
                BeanUtils.copyNotNullProperties(refundPremiumProduct, couponProductRefundRequestDto);
                couponProductRefundRequestDto.setVersion(HandlerConstants.VERSION);
                couponProductRefundRequestDto.setUserNo(userNo);
                couponProductRefundRequestDto.setChannelCode(req.getChannelCode());
                couponProductRefundRequestDto.setVoucherNos(refundPremiumProduct.getOrderProductIds());
                //发送请求
                Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                HttpResult result = doPostClient(couponProductRefundRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.REFUND_PRIDUCT_APPLY, headMap);
                //3.解析响应
                if (result.isResult()) {
                    CouponProductRefundResponseDto couponProductRefundResponseDto = (CouponProductRefundResponseDto) JsonUtil.jsonToBean(result.getResponse(), CouponProductRefundResponseDto.class);
                    if ("1001".equals(couponProductRefundResponseDto.getResultCode())) {
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        log.info(LOG_RESP_TWO, reqId, ip, resp);
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(couponProductRefundResponseDto.getErrorInfo());
                        log.info(LOG_RESP_TWO, reqId, ip, resp);
                    }
                    return resp;
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_RESP_ONE);
                    log.info(LOG_RESP_TWO, reqId, ip, resp);
                    return resp;
                }
            }


        } catch (Exception e) {
            log.error(LOG_ERROR, reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR_TWO);
            return resp;
        }
    }

    @ApiOperation(value = "预约贵宾休息室", notes = "预约贵宾休息室")
    @RequestMapping(value = "/couponOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<VoucherInfo> orderLoungeCoupon(@RequestBody BaseReq<LoungeReq> baseReq, HttpServletRequest request) {
        BaseResp<VoucherInfo> baseResp = new BaseResp<>();
        String reqId = "_orderLoungeCoupon";
        if (StringUtils.isBlank(baseReq.getChannelCode())) {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("渠道号不能为空");
            return baseResp;
        }
        LoungeReq req = baseReq.getRequest();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<LoungeReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setErrorInfo(violations.iterator().next().getMessage());
            return baseResp;
        }
        if (CollectionUtils.isEmpty(req.getVoucherNo())) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setErrorInfo("权益券凭证号不能为空！");
            return baseResp;
        }
        CommCouponOrderReq commCouponOrderReq = new CommCouponOrderReq();
        commCouponOrderReq.setVersion("10");
        commCouponOrderReq.setChannelCode(baseReq.getChannelCode());
        commCouponOrderReq.setFfpId(req.getFfpId());
        commCouponOrderReq.setFfpCardNo(req.getFfpCardNo());
        commCouponOrderReq.setVoucherNos(req.getVoucherNo());
        commCouponOrderReq.setLinkerMobile(req.getLinkerMobile());

        //升舱、逾重行李、预留登机牌多的参数
        if (req.getResourceType().equalsIgnoreCase("Upgrade")
                || req.getResourceType().equalsIgnoreCase("Baggage")
                || req.getResourceType().equalsIgnoreCase("CheckinSubstitution")) {
            List<SegmentInfo> list = new ArrayList<>();
            for (int i = 0; i < req.getVoucherNo().size(); i++) {
                SegmentInfo segmentInfo = new SegmentInfo();
                segmentInfo.setSegNo(0);
                segmentInfo.setFlightDirection("G");
                segmentInfo.setFlightNo(req.getFlightNo());
                segmentInfo.setDepAirport(req.getDepAirport());
                segmentInfo.setArrAirport(req.getArrAirport());
                segmentInfo.setArrDateTime(req.getArrDateTime());
                segmentInfo.setDepDateTime(req.getDepDateTime());
                segmentInfo.setCabin(req.getCabin());
                list.add(segmentInfo);
            }
            commCouponOrderReq.setSegmentInfoList(list);
        } else if (req.getResourceType().equalsIgnoreCase("Lounge")) {
            List<SegmentInfo> list = new ArrayList<>();
            for (int i = 0; i < req.getVoucherNo().size(); i++) {
                SegmentInfo segmentInfo = new SegmentInfo();
                segmentInfo.setSegNo(0);
                segmentInfo.setFlightDirection("G");
                segmentInfo.setFlightNo(req.getFlightNo());
                segmentInfo.setDepAirport(req.getDepAirport());
                segmentInfo.setArrAirport(req.getArrAirport());
                try {
                    segmentInfo.setDepDateTime(req.getUseDate() + " 00:00:00");
                } catch (Exception e) {
                    log.error(LOG_ERROR, reqId, getClientIP(request), e);
                }
                segmentInfo.setArrDateTime(req.getArrDateTime() + ":00");
                list.add(segmentInfo);
            }
            commCouponOrderReq.setSegmentInfoList(list);
        }
        String url = HandlerConstants.URL_FARE_API + "/Order/ReserveVoucherCouponProduct";
        //发起请求
        String ip = getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        try {
            HttpResult result = this.doPostClient(commCouponOrderReq, url, headMap);
            CommCouponOrderRes commCouponOrderRes = (CommCouponOrderRes) JsonUtil.jsonToBean(result.getResponse(), CommCouponOrderRes.class);
            if (commCouponOrderRes.getResultCode().equals("1001")) {
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setObjData(commCouponOrderRes.getVoucherInfo());
            } else {
                baseResp.setResultCode(commCouponOrderRes.getResultCode());
            }
            baseResp.setResultInfo(commCouponOrderRes.getErrorInfo());
            return baseResp;
        } catch (Exception e) {
            log.error("预约贵宾休息室出现异常，IP地址:{}，服务端异常", ip, e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo(LOG_ERROR_TWO);
            return baseResp;
        }
    }

    @ApiOperation(value = "取消预约贵宾休息室", notes = "取消预约贵宾休息室")
    @RequestMapping(value = "/cancelOrderLoungeCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<VoucherInfo> cancelOrderLoungeCoupon(@RequestBody BaseReq<LoungeCancelReq> baseReq, HttpServletRequest request) {
        BaseResp<VoucherInfo> baseResp = new BaseResp<>();
        LoungeCancelReq req = baseReq.getRequest();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<LoungeCancelReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setErrorInfo(violations.iterator().next().getMessage());
            return baseResp;
        }
        CouponProductCancelReserveVoucherRequestDto voucherRequestDto = new CouponProductCancelReserveVoucherRequestDto();
        voucherRequestDto.setVersion("10");
        voucherRequestDto.setChannelCode(baseReq.getChannelCode());
        voucherRequestDto.setFfpId(req.getFfpId());
        voucherRequestDto.setFfpCardNo(req.getFfpCardNo());
        String userNo = getChannelInfo(baseReq.getChannelCode(), "10");
        voucherRequestDto.setUserNo(userNo);
        List<String> voucherNos = Lists.newArrayList();
        voucherNos.add(req.getCouponNo());
        voucherRequestDto.setVoucherNos(voucherNos);

        String url = HandlerConstants.URL_FARE_API + "/Order/CancelReserveVoucherCouponProduct";
        //发起请求
        String ip = getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        try {
            HttpResult result = this.doPostClient(voucherRequestDto, url, headMap);
            CouponProductCancelReserveVoucherResponseDto voucherResponseDto = (CouponProductCancelReserveVoucherResponseDto) JsonUtil.jsonToBean(result.getResponse(), CouponProductCancelReserveVoucherResponseDto.class);
            if (voucherResponseDto.getResultCode().equals("1001")) {
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setErrorInfo(voucherResponseDto.getErrorInfo());
            }
            return baseResp;
        } catch (Exception e) {
            log.error("取消预约贵宾休息室出现异常，IP地址:{}，服务端异常", ip, e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo(LOG_ERROR_TWO);
            return baseResp;
        }
    }

    @ApiOperation(value = "根据机场、日期查询航班", notes = "根据机场、日期查询航班")
    @RequestMapping(value = "queryFlightInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<FlightInfoListResp>> queryFlightInfo(@RequestBody FlightInfoListReq flightInfoListReq, HttpServletRequest request) {
        String ip = this.getClientIP(request);
        BaseResp<List<FlightInfoListResp>> baseResp = new BaseResp<>();
        if (StringUtils.isBlank(flightInfoListReq.getDepAirport())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo("出发机场不能为空！");
            return baseResp;
        }
        if (StringUtils.isBlank(flightInfoListReq.getFlightDate())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo("出发日期不能为空！");
            return baseResp;
        }
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setDepAirport(flightInfoListReq.getDepAirport());
        flightInfo.setFlightDate(flightInfoListReq.getFlightDate());
        List<FlightInfo> list = basicService.queryFlightInfo(flightInfo);
        if (CollectionUtils.isEmpty(list)) {
            baseResp.setResultCode(WSEnum.NO_DATA.getResultCode());
            baseResp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
        } else {
            List<FlightInfoListResp> result = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            SimpleDateFormat sdf2 = new SimpleDateFormat(DATE_FORMAT_TWO);
            for (FlightInfo flightInfo1 : list) {
                try {
                    FlightInfoListResp flightInfoListResp = new FlightInfoListResp();
                    flightInfoListResp.setArrAirport(flightInfo1.getArrAirport());
                    flightInfoListResp.setDepAirport(flightInfo1.getDepAirport());
                    AirPortInfoDto airPortInfo = localCacheService.getLocalAirport(flightInfo1.getDepAirport(), flightInfo1.getFlightDate());
                    flightInfoListResp.setDepAirportName(airPortInfo.getAirPortName());
                    flightInfoListResp.setDepCityName(airPortInfo.getCityName());
                    airPortInfo = localCacheService.getLocalAirport(flightInfo1.getArrAirport(), flightInfo1.getFlightDate());
                    flightInfoListResp.setArrAirportName(airPortInfo.getAirPortName());
                    flightInfoListResp.setArrCityName(airPortInfo.getCityName());
                    flightInfoListResp.setFlightDate(flightInfo1.getFlightDate());
                    flightInfoListResp.setFlightNo(flightInfo1.getFlightNo());
                    flightInfoListResp.setDepDateTime(flightInfo1.getFlightDate() + " " + flightInfo1.getDepDateTime().substring(0, 2) + ":" + flightInfo1.getDepDateTime().substring(2));
                    flightInfoListResp.setArrDateTime(flightInfo1.getFlightArrDate() + " " + flightInfo1.getArrDateTime().substring(0, 2) + ":" + flightInfo1.getArrDateTime().substring(2));
                    try {
                        flightInfoListResp.setPlaneDate(sdf.format(sdf2.parse(flightInfo1.getDepDateChinaTime())));
                    } catch (Exception e) {
                        flightInfoListResp.setPlaneDate(flightInfo1.getDepDateChinaTime());
                        log.error("根据机场、日期查询航班出现异常", e);
                    }
                    result.add(flightInfoListResp);
                } catch (Exception e) {
                    log.error("根据机场、日期查询航班出现异常", e);
                }
            }
            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
            baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            baseResp.setObjData(result);
        }
        return baseResp;
    }

    /**
     * 权益券订单列表
     *
     * @param channelCode
     * @param userNo
     * @param couponOrderReq
     * @param request
     * @return
     */
    private Map<String, Object> queryOrderList(String queryUrl, String channelCode, String userNo, CouponOrderReq couponOrderReq,
                                               HttpServletRequest request, List<String> VoucherTypes) {
        List<SaleCouponGet> saleOrderList = new LinkedList<>();
        Map<String, Object> pageMap = new HashMap<>();
        CouponProductOrderGetRequestDto requestDto = CouponOrderReqMapping.MAPPER.toCouponProductOrderGetRequestDto(couponOrderReq);
        requestDto.setUserNo(userNo);
        requestDto.setIsRemoved("0");
        requestDto.setChannelCode(channelCode);
        requestDto.setSearchType(1);
        // 查询代付款状态订单
        if (OrderStateEnum.Booking.getStateCode().equals(requestDto.getOrderState())) {
            requestDto.setPayState(OrderPayStateEnum.UnPay.getStateCode());
        }
        if (StringUtils.isNotBlank(couponOrderReq.getVoucherType())) {
            requestDto.setVoucherType(Collections.singletonList(couponOrderReq.getVoucherType()));
        } else {
            requestDto.setVoucherType(VoucherTypes);
        }
        //发起请求
        String ip = getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = doPostClient(requestDto, queryUrl, headMap);
        if (result.isResult()) {
            SaleCouponGetResponse resp = (SaleCouponGetResponse) JsonUtil.jsonToBean(result.getResponse(), SaleCouponGetResponse.class);
            if ("1001".equals(resp.getResultCode())) {
                //为保持与原有数据结构保持一致
                saleOrderList = orderToSaleOrder(resp.getCouponOrderList(), ip);
                pageMap.put("pageCount", resp.getPageCount());
            }
        }
        if (OrderStateEnum.Booking.getStateCode().equals(requestDto.getOrderState())) {
            saleOrderList = saleOrderList.stream().filter(order -> PayEnum.UnPay.getStateCode().equals(order.getOrderState())).collect(Collectors.toList());
        }
        pageMap.put("pageNo", couponOrderReq.getPageNo());
        pageMap.put("pageSize", couponOrderReq.getPageSize());
        pageMap.put("orderList", saleOrderList);
        return pageMap;
    }

    /**
     * 固包权益券订单列表
     */
    private Map<String, Object> queryPackageOrderList(String channelCode, String userNo, CouponOrderReq couponOrderReq, HttpServletRequest request) {
        List<SaleCouponGet> saleOrderList = new LinkedList<>();
        Map<String, Object> pageMap = new HashMap<>();
        //构建请求类
        CouponProductOrderGetRequestDto requestDto = new CouponProductOrderGetRequestDto();
        BeanUtils.copyNotNullProperties(couponOrderReq, requestDto);
        requestDto.setUserNo(userNo);
        requestDto.setIsRemoved("0");
        requestDto.setChannelCode(channelCode);
        requestDto.setVoucherType(Collections.singletonList(couponOrderReq.getVoucherType()));
        //发起请求
        String ip = getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_ORDER_PRODUCT, headMap);
        if (result.isResult()) {
            SaleCouponGetResponse resp = (SaleCouponGetResponse) JsonUtil.jsonToBean(result.getResponse(), SaleCouponGetResponse.class);
            if ("1001".equals(resp.getResultCode())) {
                //为保持与原有数据结构保持一致
                List<PtSaleCouponOrderGetResponse.CouponOrder> couponOrderList = resp.getCouponOrderList();
                saleOrderList = orderToSaleOrder(couponOrderList, ip);
                pageMap.put("pageCount", resp.getPageCount());
            }
        }
        pageMap.put("pageNo", couponOrderReq.getPageNo());
        pageMap.put("pageSize", couponOrderReq.getPageSize());
        pageMap.put("orderList", saleOrderList);
        return pageMap;
    }

    /**
     * 权益券订单详情
     *
     * @param channelCode
     * @param userNo
     * @param couponOrderReq
     * @param request
     * @return
     */
    private Map<String, Object> queryOrderDetail(String channelCode, String userNo, CouponOrderReq couponOrderReq, HttpServletRequest request) {
        CouponProductOrderGetRequestDto requestDto = new CouponProductOrderGetRequestDto();
        List<SaleCouponGet> saleOrderList = new LinkedList<>();
        Map<String, Object> pageMap = new HashMap<>();
        Date date = new Date();
        BeanUtils.copyNotNullProperties(couponOrderReq, requestDto);
        //主题卡权益卷订单列表进入详情页传参加入simpleFlag
        if (StringUtils.isNotEmpty(couponOrderReq.getCouponSource()) && handConfig.getThemeCouponList().contains(couponOrderReq.getCouponSource())) {
            requestDto.setSimpleOrderFlag("Y");
        }
        requestDto.setUserNo(userNo);
        requestDto.setIsRemoved("0");
        requestDto.setChannelCode(channelCode);
        String ip = getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        List<SaleCouponGet> saleOrderList1 = new LinkedList<>();
        List<CouponProduct> couponProducts = new ArrayList<>();
        SaleCouponGet saleCouponGet = new SaleCouponGet();
        PtSaleCouponOrderGetResponse ptSaleCouponOrderGetResponse = orderManage.queryCouponList(requestDto, headMap);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptSaleCouponOrderGetResponse.getResultCode()) &&
                !StringUtil.isNullOrEmpty(ptSaleCouponOrderGetResponse.getCouponOrderList())) {
            List<PtSaleCouponOrderGetResponse.CouponOrder> couponOrderList = ptSaleCouponOrderGetResponse.getCouponOrderList();
            //订单处理
            //当前时间，单位为毫秒
            long curTime = System.currentTimeMillis();
            for (PtSaleCouponOrderGetResponse.CouponOrder couponOrder : couponOrderList) {
                org.springframework.beans.BeanUtils.copyProperties(couponOrder, saleCouponGet);
                //处理联系人，退款规则等公用属性
                String couponSource = couponOrder.getSaleCouponList().get(0).getCouponSource();
                saleCouponGet.setCouponSource(couponSource);
                setSaleCouponGet(couponOrder, saleCouponGet);
                //默认订单详情中不显示查询权益按钮
                saleCouponGet.setIsShowViewBtn(false);
                Boolean wifiPhoneFlag = true;
                List<CouponShow> showList = new LinkedList<>();
                PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCouponV = couponOrder.getSaleCouponList().get(0);
                String couponDynamicParamV = saleCouponV.getCouponDynamicParam();
                if (StringUtils.isNotBlank(couponDynamicParamV)) {
                    Map<String, String> map = (Map<String, String>) JsonUtil.jsonToMap(couponDynamicParamV, Map.class);
                    String value = map.get("MerchantPayment");
                    saleCouponGet.setMerchantPayment(value);
                }
                //付费保级产品数量处理
                if (VoucherTypesEnum.MEMBERRENEWAL.getCode().equals(couponSource)
                        || VoucherTypesEnum.MEMBERUPGRADE.getCode().equals(couponSource)) {
                    saleCouponGet.setSaleCouponCount(couponOrder.getSaleCouponList().stream().mapToInt(PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon::getAmount).sum());
                }
                //固包券特殊处理
                //优惠劵处理  开始
                //为保持与原有数据结构保持一致
                double totalCouponPrice = 0.0;
                for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon : couponOrder.getSaleCouponList()) {
                    CouponShow couponShow = new CouponShow();
                    org.springframework.beans.BeanUtils.copyProperties(saleCoupon, couponShow);
                    couponShow.setCouponState(couponShow.getVoucherState());
                    couponShow.setUseDays(saleCoupon.getUseDays());
                    couponShow.setCouponStateName(VoucherStateEnum.queryVoucherState(couponShow.getVoucherState()).getName());
                    couponShow.setCommentState(StringUtils.isBlank(saleCoupon.getCommentState()) ? "" : saleCoupon.getCommentState());
                    totalCouponPrice += couponShow.getCouponPrice();
                    //默认情况下未使用可以退
                    couponShow.setRefundBtn(VoucherStateEnum.NOT.getCode().equals(couponShow.getCouponState()));
                    //吉祥镖局特有属性
                    if (VoucherTypesEnum.DELIVERY.getCode().equals(saleCoupon.getCouponSource()) && !StringUtil.isNullOrEmpty(saleCoupon.getFlightNumber())) {
                        long flightTime = DateUtils.toDate(saleCoupon.getEndDate(), DATE_FORMAT).getTime() + 40 * 60 * 1000;
                        String flightDate = DateUtils.timeStampToDateStr(flightTime, DATE_FORMAT_THREE);
                        SimpleFlightInfo simpleFlightInfo = queryFlightInfo(flightDate, saleCoupon.getFlightNumber(), saleCoupon.getDepAirport(), saleCoupon.getArrAirport());
                        if (simpleFlightInfo != null) {
                            couponShow.setSimpleFlightInfo(simpleFlightInfo);
                        }
                    }
                    //值机选座特有属性
                    if (VoucherTypesEnum.SEAT.getCode().equalsIgnoreCase(saleCoupon.getCouponSource())) {
                        checkINSeat(couponShow, saleCouponGet, saleCoupon);
                    }
                    //接送机特有属性
                    if (VoucherTypesEnum.TRAFFIC.getCode().equals(couponShow.getCouponSource())) {
                        Traffic(couponShow, saleCoupon, channelCode, headMap);
                    }
                    //邮寄行程单特有属性
                    if (VoucherTypesEnum.MAILTRAVEL.getCode().equals(couponShow.getCouponSource())) {
                        MailTravel(couponShow, saleCouponGet, saleCoupon);
                    }
                    //判断wifi电话卡是否要展示退款按钮
                    if (VoucherTypesEnum.WIFI.getCode().equals(couponShow.getVoucherType()) || VoucherTypesEnum.PHONECARD.getCode().equals(couponShow.getVoucherType())) {

                        if (!VoucherStateEnum.NOT.getCode().equals(couponShow.getCouponState())) {
                            wifiPhoneFlag = false;
                        }
                        //属于订单退款
                        couponShow.setRefundBtn(false);
                    }
                    //固包券处理购买来源
                    if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(couponShow.getVoucherType())) {
                        if (!StringUtil.isNullOrEmpty(saleCoupon.getCouponDynamicParam())) {
                            String couponDynamicParam = saleCoupon.getCouponDynamicParam();
                            PackageWifiParamProduct packageWifiParamProduct = (PackageWifiParamProduct) JsonUtil.jsonToBean(couponDynamicParam, PackageWifiParamProduct.class);
                            saleCouponGet.setBuyProductType((packageWifiParamProduct.getBuyCouponType() != null && packageWifiParamProduct.getBuyCouponType()));
                        } else {
                            saleCouponGet.setBuyProductType(false);
                        }
                    }
                    //签证特有属性
                    if (VoucherTypesEnum.VISA.getCode().equals(couponShow.getCouponSource())) {
                        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
                        VisaInfo visaInfo = (VisaInfo) JsonUtil.jsonToBean(couponDynamicParam, VisaInfo.class);
                        couponShow.setVisaInfo(visaInfo);
                        saleCouponGet.setLinker(visaInfo.getLinkerName());
                        saleCouponGet.setAddress(visaInfo.getLinkerAddress());
                    }
                    // 礼宾接送机
                    if (VoucherTypesEnum.GUIDESERVICE.getCode().equals(couponShow.getCouponSource())) {
                        GuideService(couponShow, saleCouponGet, saleCoupon);
                    }
                    //付费保级不可申请退款
                    if (VoucherTypesEnum.MEMBERRENEWAL.getCode().equals(couponShow.getCouponSource()) ||
                            VoucherTypesEnum.MEMBERUPGRADE.getCode().equals(couponShow.getCouponSource())) {
                        couponShow.setRefundBtn(false);
                    }
                    if (VoucherTypesEnum.UPGRADE.getCode().equals(couponShow.getCouponSource())) {
                        couponShow.setRefundBtn(OrderCouponStateEnum.Not.getStateCode().equals(couponShow.getCouponState()));
                    } else if (VoucherTypesEnum.UPGRADEUNLIMITED.getCode().equals(couponShow.getCouponSource())) {
                        couponShow.setRefundBtn(OrderCouponStateEnum.Not.getStateCode().equals(couponShow.getCouponState()));
                        //无限升舱卡已过期状态可以退款
                        UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                        Date saleTimeBegin = DateUtils.toDate(upgradeCardV2Config.getCardSaleTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                        Date orderDate = DateUtils.toDate(saleCouponGet.getOrderDatetime(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                        if (null != saleTimeBegin && orderDate.getTime() >= saleTimeBegin.getTime()) {
                            //设置无限升舱卡15周年版订单详情页可用日期，不可用日期
                            couponShow.setAvailDate(upgradeCardV2Config.getYearCardFlightTimeBegin() + "至" + upgradeCardV2Config.getYearCardFlightTimeEnd() + "?(不含" + upgradeCardV2Config.getUnusableTimeBegin().substring(0, 10) + "至" + upgradeCardV2Config.getUnusableTimeEnd().substring(0, 10) + ")");
                        }
                    } else if (VoucherTypesEnum.UnlimitUpgradeYear.getCode().equalsIgnoreCase(couponShow.getCouponSource())) {
                        //退款按钮显示
                        couponShow.setRefundBtn(OrderCouponStateEnum.Not.getStateCode().equals(couponShow.getCouponState())
                                || OrderCouponStateEnum.Giving.getStateCode().equals(couponShow.getCouponState())
                                || OrderCouponStateEnum.GiveAway.getStateCode().equals(couponShow.getCouponState()));
                        //判断是否存在兑换成功记录，如果存在不可退
                        if (saleCoupon.getOutTimes() != null && saleCoupon.getOutTimes() > 0) {//存在兑换成功记录的视为已使用，不可退款
                            couponShow.setRefundBtn(false);
                        }
                        UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                        //设置升舱卡2.0订单详情页可用日期，不可用日期
                        couponShow.setAvailDate(upgradeCardV2Config.getYearCardFlightTimeBegin() + "至" + upgradeCardV2Config.getYearCardFlightTimeEnd() + "?(不含" + upgradeCardV2Config.getUnusableTimeBegin().substring(0, 10) + "至" + upgradeCardV2Config.getUnusableTimeEnd().substring(0, 10) + ")");
                    }
                    //儿童畅飞卡，吉祥畅飞卡 未使用以及赠送中允许退款
                    if (VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode().equals(couponSource)
                            || VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode().equals(couponSource)
                            || VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(couponSource)
                            || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(couponSource)
                            || couponSource.matches(PatternCommon.TOTALTYPE)
                            || couponSource.matches(PatternCommon.THEME_MEC)) {

                        couponShow.setRefundBtn(OrderCouponStateEnum.Not.getStateCode().equals(couponShow.getCouponState())
                                || OrderCouponStateEnum.Giving.getStateCode().equals(couponShow.getCouponState())
                                || OrderCouponStateEnum.GiveAway.getStateCode().equals(couponShow.getCouponState()));

                        if (couponSource.matches(PatternCommon.TOTALTYPE)) {
                            PtRequest ptRequest = new PtRequest();
                            ptRequest.setRequest(couponShow.getCouponCode());
                            ptRequest.setChannelCode(channelCode);
                            BeanUtils.copyNotNullProperties(couponOrderReq, ptRequest);
                            PtResponse ptResponse = queryUsedSegByCode(ptRequest);
                            if (ptResponse != null && !Boolean.parseBoolean(ptResponse.getResult().toString()) &&
                                    !VoucherStateEnum.Refund.getCode().equals(couponShow.getCouponState())) {//未出行前，可以退款
                                couponShow.setRefundBtn(true);
                            }
                        }
                        UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
                        //设置畅飞卡2.0订单详情页可用日期，不可用日期
                        if (VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(couponSource)) {
                            couponShow.setAvailDate(unlimitedCard2Config.getAvailDateFlySF());
                        } else if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(couponSource)) {
                            couponShow.setAvailDate(unlimitedCard2Config.getAvailDateFly());
                        }
                    }

                    //机上购物
                    if (VoucherTypesEnum.ONBOARDPRODUCT.getCode().equals(couponSource)
                            || VoucherTypesEnum.BRANDMEALS.getCode().equals(couponSource)) {
                        OnboardProduct(couponShow, saleCoupon);
                    }
                    if (VoucherTypesEnum.EasyLiveHotel.getCode().equals(couponSource)) {
                        Date refundDateLimit = DateUtils.toDate(handConfig.getHuazhuHotelRefundTimeLimit(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                        couponShow.setRefundBtn(couponShow.isRefundBtn() && refundDateLimit != null && refundDateLimit.after(new Date()));
                        // 畅住卡订单只会有一张券
                        saleCouponGet.setRefundBtn(couponShow.isRefundBtn());
                    }
                    //会员延期不支持退款
                    if (VoucherTypesEnum.MemberLevelDelay.getCode().equals(couponSource)) {
                        couponShow.setRefundBtn(false);
                        couponShow.setCouponCode(null);
                        couponShow.setStartDate(null);
                        couponShow.setEndDate(null);
                        couponShow.setActivityName(VoucherTypesEnum.MemberLevelDelay.getName());
                        saleCouponGet.setRefundBtn(false);
                    }
                    // vip体验卡根据订单是否可退字段 控制前端refundBtn
                    if (couponSource.matches(PatternCommon.THEME_MEC)) {
                        couponShow.setRefundBtn("Y".equals(couponShow.getIsRefunded()));
                        saleCouponGet.setRefundBtn("Y".equals(couponShow.getIsRefunded()));
                    }
                    //优惠券礼包券处理
                    dealCouponInfo(couponShow, saleCoupon, couponOrder.getIsBundleSale());
                    couponShow.setActivityName(showOrderNameBySource(couponShow.getCouponSource(), couponShow.getCouponPrice(), couponShow.getActivityName()));
                    OrderCouponStateEnum orderState = OrderCouponStateEnum.getEnum(couponShow.getCouponState());
                    couponShow.setCouponStateName(orderState == null ? "" : orderState.getDesc());
                    // 非自营渠道不展示退款按钮
                    couponShow.setRefundBtn(couponShow.isRefundBtn() && handConfig.getVoucherRefundChannelCodes().contains(couponOrder.getChannelNo()));
                    //判断是否过期
                    if (StringUtils.isNotBlank(saleCoupon.getEndDate())) {
                        if (date.after(DateUtils.toDate(saleCoupon.getEndDate()))) {
                            //过期是否可退
                            couponShow.setRefundBtn(saleCoupon.getRefundExpire() == 1);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(saleCoupon.getGuestInfoList()) && saleCoupon.isIsPreBundle()) {
                        saleCoupon.getGuestInfoList().stream().forEach(guest -> {
                            CouponShow couponShowCopy = new CouponShow();
                            BeanUtils.copyProperties(couponShow, couponShowCopy);
                            couponShowCopy.setCustomerName(guest.getPassengerName());
                            couponShowCopy.setTicketType(guest.getPassengerType());
                            couponShowCopy.setCertType(guest.getCertType());
                            couponShowCopy.setCertNo(guest.getCertNo());
                            couponShowCopy.setContactTelphone(guest.getPhoneNO());
                            couponShowCopy.setVisitDate(guest.getVisitDate());
                            couponShowCopy.setEnableChange(guest.isEnableChange());
                            couponShowCopy.setIsPreBundle(saleCoupon.isIsPreBundle());
                            showList.add(couponShowCopy);
                        });
                    } else {
                        showList.add(couponShow);
                    }

                }
                //优惠劵处理  结束
                //WIFI处理
                if (VoucherTypesEnum.WIFI.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
                    saleCouponGet.setRefundBtn(false);
                    String couponDynamicParam = couponOrder.getSaleCouponList().get(0).getCouponDynamicParam();
                    WifiProduct wifiProduct = (WifiProduct) JsonUtil.jsonToBean(couponDynamicParam, WifiProduct.class);
                    saleCouponGet.setWifiProduct(wifiProduct);
                    Date curDate = DateUtils.toDate(DateUtils.getCurrentDateStr(), DATE_FORMAT_THREE);
                    //当前时间在领取时间之前
                    if (curDate.before(DateUtils.toDate(wifiProduct.getBorrowDate(), DATE_FORMAT_THREE)) && wifiPhoneFlag) {
                        //权益券状态为not时
                        saleCouponGet.setRefundBtn(true);
                    }
                }
                //电话卡处理
                if (VoucherTypesEnum.PHONECARD.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
                    saleCouponGet.setRefundBtn(false);
                    String couponDynamicParam = couponOrder.getSaleCouponList().get(0).getCouponDynamicParam();
                    PhoneCardProduct phoneCardProduct = (PhoneCardProduct) JsonUtil.jsonToBean(couponDynamicParam, PhoneCardProduct.class);
                    PhoneCardShipWayEnum shipWayEnum = PhoneCardShipWayEnum.queryPhoneCardDescEnum(phoneCardProduct.getShipWay());
                    phoneCardProduct.setShipWayName(shipWayEnum == null ? "" : shipWayEnum.getDesc());
                    saleCouponGet.setPhoneCardProduct(phoneCardProduct);
                    Date curDate = DateUtils.toDate(DateUtils.getCurrentDateStr(), DATE_FORMAT_THREE);
                    //电话卡为自取并且当前时间在领取时间之前
                    if ("2".equals(phoneCardProduct.getShipWay()) && curDate.before(DateUtils.toDate(phoneCardProduct.getGetCardDate(), DATE_FORMAT_THREE)) && wifiPhoneFlag) {
                        //权益券状态为not时
                        saleCouponGet.setRefundBtn(true);
                    }
                }
                List<CouponShow> couponShows = new LinkedList<>();
                //处理固包券的特殊情况，将行李券和固包券两两组合
                if (VoucherTypesEnum.PACKAGE.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
                    List<CouponShow> finalCouponShows = couponShows;
                    showList.forEach(s -> {
                        if (VoucherTypesEnum.BAGGAGE.getCode().equals(s.getVoucherType())) {
                            //该券为行李券，查询与之匹配的固包子券
                            List<CouponShow> list = new ArrayList<>();
                            showList.forEach(sh -> {
                                if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(sh.getVoucherType()) && s.getMainVoucherNo().equals(sh.getMainVoucherNo())) {
                                    s.setDepTime(sh.getDepTime());
                                    s.setFlightNo(sh.getFlightNo());
                                    list.add(sh);
                                }
                            });
                            s.setVouchers(list);
                            finalCouponShows.add(s);
                        }
                    });
                    couponShows = finalCouponShows;
                } else if (VoucherTypesEnum.ONBOARDPRODUCT.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())
                        || VoucherTypesEnum.BRANDMEALS.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
                    showList.forEach(i -> i.setBookingCount(1));
                    Map<String, List<CouponShow>> collect = showList.stream().collect(Collectors.groupingBy(CouponShow::getActivityNo));
                    List<CouponShow> finalCouponShows1 = couponShows;
                    collect.forEach((k, v) -> {
                        finalCouponShows1.add(v.get(0));
                        if (v.size() > 1) {
                            v.get(0).setBookingCount(v.size());
                        }
                    });
                    couponShows = finalCouponShows1;
                } else {
                    couponShows = showList;
                }
                //支付时限
                String bookDateStr = saleCouponGet.getOrderDatetime();
                Date bookDate = DateUtils.toDate(bookDateStr, DATE_FORMAT);
                //下单时间
                long bookTime = bookDate.getTime();
                //支付时限  小于0代表已过期
                long endTime;
                if (VoucherTypesEnum.DELIVERY.getCode().equals(couponSource)) {
                    endTime = DateUtils.dateAddOrLessSecond(bookDate, 5 * 60).getTime();
                } else {
                    endTime = DateUtils.dateAddOrLessSecond(bookDate, handConfig.getTimeLimit() * 60).getTime();
                }
                saleCouponGet.setCouponList(couponShows);
                UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
                //设置畅飞卡2.0订单详情页可用日期，不可用日期
                if (VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(couponSource)) {
                    saleCouponGet.setAvailDate(unlimitedCard2Config.getAvailDateFlySF());
                } else if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(couponSource)) {
                    saleCouponGet.setAvailDate(unlimitedCard2Config.getAvailDateFly());
                } else if (VoucherTypesEnum.UPGRADEUNLIMITED.getCode().equals(couponSource)) {
                    UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                    Date saleTimeBegin = DateUtils.toDate(upgradeCardV2Config.getCardSaleTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                    Date orderDate = DateUtils.toDate(saleCouponGet.getOrderDatetime(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                    if (null != saleTimeBegin && orderDate.getTime() >= saleTimeBegin.getTime()) {
                        //设置无限升舱卡15周年版订单详情页可用日期，不可用日期
                        saleCouponGet.setAvailDate(upgradeCardV2Config.getYearCardFlightTimeBegin() + "至" + upgradeCardV2Config.getYearCardFlightTimeEnd() + "?(不含" + upgradeCardV2Config.getUnusableTimeBegin().substring(0, 10) + "至" + upgradeCardV2Config.getUnusableTimeEnd().substring(0, 10) + ")");
                    }
                }
                //处理订单基本信息，退款状态
                dealSaleCouponGet(saleCouponGet, couponOrder.getOrderState(), couponOrder.getOrderStateName(), couponOrder.getPayState(), bookTime, endTime, curTime);
                //订单详情页下单时间格式
                saleCouponGet.setOrderFormatDatetime(bookDateStr.substring(0, bookDateStr.length() - 3));
                //支付页使用的下单时间格式
                saleCouponGet.setOrderDatetime(bookDateStr);
                //支付方式，支付时间
                saleCouponGet.setPayMethodName(couponOrder.getPayMethod() == null ? "" : couponOrder.getPayMethod());
                saleCouponGet.setPaidDatetime(couponOrder.getPaidDateTime() == null ? "" : couponOrder.getPaidDateTime());
                //2021-08-19 处理订单商品名称
                //打包产品名称处理
                saleCouponGet.setProductDesc("其他");
                saleCouponGet.setTotalCouponPrice(totalCouponPrice);
                if ("Y".equals(couponOrder.getIsBundleSale())) {
                    CouponShow show = couponShows.stream().filter(couponShow -> VoucherTypesEnum.COUPON.getCode().equals(couponShow.getCouponSource())).findFirst().orElse(null);
                    if (show != null) {
                        saleCouponGet.setProductDesc("机票优惠券");
                    } else {
                        saleCouponGet.setProductDesc("权益券");
                    }
                } else {
                    VoucherTypesEnum type = VoucherTypesEnum.getTypeByCode(saleCouponGet.getCouponSource());
                    if (type != null) {
                        saleCouponGet.setProductDesc(type.getName());
                    }
                }
                //优惠券礼包采用固包的展示方式
                dealCouponBagOrder(saleCouponGet, couponOrder);
                // 非自营渠道不展示退款按钮
                saleCouponGet.setRefundBtn(saleCouponGet.isRefundBtn() && handConfig.getVoucherRefundChannelCodes().contains(couponOrder.getChannelNo()));
                //根据权益券类型和状态，判断是否显示查看权益
                saleCouponGet.getCouponList().forEach(coupon -> {
                    //判断状态是否是未使用
                    if (VoucherStateEnum.NOT.getCode().equals(coupon.getCouponState()) ||
                            VoucherStateEnum.APPOINTMENT.getCode().equals(coupon.getCouponState())) {
                        if (VoucherTypesEnum.LOUNGE.getCode().equals(coupon.getCouponSource()) ||
                                VoucherTypesEnum.UPGRADE.getCode().equals(coupon.getCouponSource()) ||
                                VoucherTypesEnum.RESCHEDULECOUPON.getCode().equals(coupon.getCouponSource()) ||
                                VoucherTypesEnum.RESCHEDULE.getCode().equals(coupon.getCouponSource()) ||
                                VoucherTypesEnum.UPGRADECOUPON.getCode().equals(coupon.getCouponSource()) ||
                                VoucherTypesEnum.LOUNGECOUPON.getCode().equals(coupon.getCouponSource()) ||
                                VoucherTypesEnum.BAGGAGECOUPON.getCode().equals(coupon.getCouponSource())
                        ) {
                            //如果权益券状态是未使用或者预约，类型是休息室、升舱券、改期券、行李券，前端显示查看权益按钮
                            saleCouponGet.setIsShowViewBtn(true);
                        }
                    }
                });
                saleOrderList.add(saleCouponGet);
            }
        }
        if ("Package".equals(saleCouponGet.getCouponSource())) {
            for (SaleCouponGet saleCouponGet1 : saleOrderList) {
                List<CouponProduct> couponProductList = saleCouponGet1.getCouponProductList();
                for (CouponProduct couponShow : couponProductList) {
                    couponShow.setCount(saleCouponGet1.getCouponList().size() / saleCouponGet1.getCouponProductList().size());
                    couponProducts.add(couponShow);
                }
                saleCouponGet1.setCouponProductList(couponProducts);
                saleOrderList1.add(saleCouponGet1);
            }
            saleOrderListProcess(saleOrderList1);
            pageMap.put("pageNo", couponOrderReq.getPageNo());
            pageMap.put("pageSize", couponOrderReq.getPageSize());
            pageMap.put("orderList", saleOrderList1);
            return pageMap;
        }
        saleOrderListProcess(saleOrderList);
        pageMap.put("pageNo", couponOrderReq.getPageNo());
        pageMap.put("pageSize", couponOrderReq.getPageSize());
        pageMap.put("orderList", saleOrderList);
        return pageMap;
    }


    public PtResponse queryUsedSegByCode(PtRequest ptRequest) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_USED_SEGBY_CODE;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
            } else {
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), PtResponse.class);
                return ptResponse;
            }
        } else {
            log.info("地址：{},状态：{}", url, result.getResponse());
            throw new NetworkException(WSEnum.NETWORK_ERROR.getResultInfo());
        }
    }


    //优惠券礼包券处理
    private void dealCouponInfo(CouponShow couponShow, PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon, String isBundleSale) {
        if (VoucherTypesEnum.COUPONBAG.getCode().equals(couponShow.getCouponSource()) ||
                "Y".equals(isBundleSale)) {
            JsonObject obj = parseCouponInfo(saleCoupon.getCouponDynamicParam());
            if (obj != null) {
                couponShow.setStartDate(obj.get("validStartTime") == null ? "" : obj.get("validStartTime").getAsString());
                couponShow.setEndDate(obj.get("validEndTime") == null ? "" : obj.get("validEndTime").getAsString());
            }
        }
    }

    /**
     * 特殊处理优惠券礼包的数据
     *
     * @param saleCouponGet 前端封装的订单信息
     * @param couponOrder   统一订单的信息
     */
    private void dealCouponBagOrder(SaleCouponGet saleCouponGet, PtSaleCouponOrderGetResponse.CouponOrder couponOrder) {
        List<CouponShow> couponShows = saleCouponGet.getCouponList();
        //优惠券礼包1.0
        if (VoucherTypesEnum.COUPONBAG.getCode().equals(saleCouponGet.getCouponSource())) {
            saleCouponGet.setOrderName(couponOrder.getBundleName());
            //单价处理
            String dynamicParam = couponOrder.getSaleCouponList().get(0).getCouponDynamicParam();
            JsonObject obj = parseCouponInfo(dynamicParam);
            if (obj != null) {
                saleCouponGet.setSaleCouponCount(obj.get("couponAmount").getAsInt());
                saleCouponGet.setPrice((couponOrder.getOrderTotalAmount() + couponOrder.getFfpUseTotalScore()) / saleCouponGet.getSaleCouponCount());
            }
            formatSaleCouponGet(saleCouponGet, couponShows);
        }
        //优惠券礼包2.0
        if ("Y".equals(couponOrder.getIsBundleSale())) {
            saleCouponGet.setOrderName(couponOrder.getBundleName());
            formatSaleCouponGet(saleCouponGet, couponShows);
            saleCouponGet.setSaleCouponCount(couponOrder.getBundleCount());
            saleCouponGet.setPrice((couponOrder.getOrderTotalAmount() + couponOrder.getFfpUseTotalScore()) / saleCouponGet.getSaleCouponCount());
            if (!StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList())) {
                List<String> activityIdList = new ArrayList<>();
                List<CouponProduct> couponProductList = new ArrayList<>();
                for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon : couponOrder.getSaleCouponList()) {
                    if (!activityIdList.contains(saleCoupon.getActivityNo())) {
                        activityIdList.add(saleCoupon.getActivityNo());
                        CouponProduct couponProduct = new CouponProduct();
                        couponProduct.setActivityName(saleCoupon.getActivityName());
                        couponProduct.setPrice(saleCoupon.getPrice());
                        couponProduct.setCount(couponOrder.getBundleCount());
                        couponProductList.add(couponProduct);
                    }
                }
                saleCouponGet.setCouponProductList(couponProductList);
            }
        }
    }


    //封装礼包的特殊信息
    private void formatSaleCouponGet(SaleCouponGet saleCouponGet, List<CouponShow> couponShows) {
        saleCouponGet.setCouponSource(VoucherTypesEnum.PACKAGE.getCode());
        if (PayEnum.Pay.getStateCode().equals(saleCouponGet.getOrderState())) {
            saleCouponGet.setRefundBtn(true);
        } else {
            saleCouponGet.setRefundBtn(false);
        }
        saleCouponGet.setRefundRules("优惠券(礼包)购买单张或多张优惠券，" +
                "不可单独申请,优惠券(礼包)内单张优惠券退款，" +
                "仅在优惠券(礼包),内所包含的单张或多张优惠券均未使用的前提前可申请线上退款。");
        for (CouponShow couponShow : couponShows) {
            couponShow.setVouchers(new ArrayList<>());
            couponShow.setRefundRules("不支持退");
            couponShow.setCouponSource(VoucherTypesEnum.PACKAGE.getCode());
            if (!couponShow.getCouponState().equals(OrderCouponStateEnum.Not.getStateCode()) || "N".equals(couponShow.getIsRefunded())) {
                saleCouponGet.setRefundBtn(false);
            }
        }
    }

    private JsonObject parseCouponInfo(String dynamicParam) {
        if (!StringUtil.isNullOrEmpty(dynamicParam)) {
            try {
                //创建一个JsonParser
                JsonParser parser = new JsonParser();
                //通过JsonParser对象可以把json格式的字符串解析成一个JsonElement对象
                JsonElement el = parser.parse(dynamicParam);
                if (el.isJsonArray()) {
                    JsonArray jarray = parser.parse(dynamicParam).getAsJsonArray();
                    if (jarray != null && jarray.size() > 0) {
                        JsonElement couponInfo = jarray.get(0);
                        if (couponInfo.isJsonObject()) {
                            JsonObject obj = couponInfo.getAsJsonObject();
                            return obj;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("json对象转换异常：{}", dynamicParam);
                return null;
            }
        }
        return null;
    }

    /**
     * 综合订单转换为权益订单
     *
     * @param
     * @return
     */
    private List<SaleCouponGet> orderToSaleOrder(List<PtSaleCouponOrderGetResponse.CouponOrder> couponOrderList, String ip) {
        List<SaleCouponGet> saleOrderList = new LinkedList<>();
        if (StringUtil.isNullOrEmpty(couponOrderList)) {
            return saleOrderList;
        }
        //当前时间  单位是毫秒
        long curTime = System.currentTimeMillis();
        //机场信息
        Map<String, AirPortInfoDto> airportInfo = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
        //订单信息
        for (PtSaleCouponOrderGetResponse.CouponOrder couponOrder : couponOrderList) {
            SaleCouponGet saleCouponOrder = new SaleCouponGet();
            com.juneyaoair.utils.util.BeanUtils.copyProperties(couponOrder, saleCouponOrder);
            List<PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon> saleCouponList = Lists.newArrayList();
            // 不保留 选座-积分购买子订单类型数据
            for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon : couponOrder.getSaleCouponList()) {
                if (!VoucherTypesEnum.SCORE_BUY.getCode().equals(saleCoupon.getCouponSource())) {
                    saleCouponList.add(saleCoupon);
                }
            }
            //销售数量处理
            String couponSource = saleCouponList.get(0).getCouponSource();
            if (VoucherTypesEnum.PACKAGE.getCode().equals(couponSource)) {
                int count = saleCouponList.size() / 2;
                saleCouponOrder.setSaleCouponCount(count);
            } else if (VoucherTypesEnum.MEMBERRENEWAL.getCode().equals(couponSource)
                    || VoucherTypesEnum.MEMBERUPGRADE.getCode().equals(couponSource)) {
                saleCouponOrder.setSaleCouponCount(saleCouponList.stream().mapToInt(PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon::getAmount).sum());
            } else {
                saleCouponOrder.setSaleCouponCount(saleCouponList.size());
            }
            saleCouponOrder.setCouponSource(couponSource);
            saleCouponOrder.setOrderDatetime(couponOrder.getOrderDatetime());
            saleCouponOrder.setOrderTotalAmount(couponOrder.getOrderTotalAmount());
            saleCouponOrder.setFfpUseTotalScore(couponOrder.getFfpUseTotalScore());
            if (!StringUtil.isNullOrEmpty(saleCouponList.get(0).getSenderNum())) {
                saleCouponOrder.setLinkerHandphone(saleCouponList.get(0).getSenderNum());
            }
            if (!StringUtil.isNullOrEmpty(saleCouponList.get(0).getReceiver())) {
                saleCouponOrder.setReceiver(saleCouponList.get(0).getReceiver());
            }
            if (!StringUtil.isNullOrEmpty(saleCouponList.get(0).getReceiverNum())) {
                saleCouponOrder.setReceiverNum(saleCouponList.get(0).getReceiverNum());
            }
            //WIFI处理
            if (VoucherTypesEnum.WIFI.getCode().equals(couponSource)) {
                String couponDynamicParam = saleCouponList.get(0).getCouponDynamicParam();
                WifiProduct wifiProduct = (WifiProduct) JsonUtil.jsonToBean(couponDynamicParam, WifiProduct.class);
                saleCouponOrder.setWifiProduct(wifiProduct);
            }
            //电话卡处理
            if (VoucherTypesEnum.PHONECARD.getCode().equals(couponSource)) {
                String couponDynamicParam = saleCouponList.get(0).getCouponDynamicParam();
                PhoneCardProduct phoneCardProduct = (PhoneCardProduct) JsonUtil.jsonToBean(couponDynamicParam, PhoneCardProduct.class);
                PhoneCardShipWayEnum shipWayEnum = PhoneCardShipWayEnum.queryPhoneCardDescEnum(phoneCardProduct.getShipWay());
                phoneCardProduct.setShipWayName(shipWayEnum == null ? "" : shipWayEnum.getDesc());
                saleCouponOrder.setPhoneCardProduct(phoneCardProduct);
            }
            // 接送机处理
            if (VoucherTypesEnum.HOCAR.getCode().equals(couponSource)) {
                saleCouponOrder.setBookingTime(saleCouponList.get(0).getBookingTime());
                saleCouponOrder.setFromAddress(saleCouponList.get(0).getFromAddress());
                saleCouponOrder.setToAddress(saleCouponList.get(0).getToAddress());
            }
            if (VoucherTypesEnum.INNNCUSTOMSERVE.getCode().equals(couponSource) || VoucherTypesEnum.INNN.getCode().equals(couponSource)) {
                String couponDynamicParam = saleCouponList.get(0).getCouponDynamicParam();
                Map<String, String> map = (Map<String, String>) JsonUtil.jsonToMap(couponDynamicParam, Map.class);
                saleCouponOrder.setFromAddress(map.get("FromAddress"));
                saleCouponOrder.setToAddress(map.get("ToAddress"));
                saleCouponOrder.setBookingTime(map.get("ToDateTime"));
            }
            //预留登机牌
            dealBoardingInfo(saleCouponOrder, couponOrder, airportInfo);
            //优惠券处理 处理开始
            boolean flag = false;
            List<CouponShow> couponShowList = new LinkedList<>();
            for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon : saleCouponList) {
                if (saleCoupon != null) {
                    CouponShow couponShow = new CouponShow();
                    com.juneyaoair.utils.util.BeanUtils.copyProperties(saleCoupon, couponShow);
                    //吉祥镖局
                    if (VoucherTypesEnum.DELIVERY.getCode().equals(saleCoupon.getCouponSource()) && !StringUtil.isNullOrEmpty(saleCoupon.getFlightNumber())) {
                        long flightTime = DateUtils.toDate(saleCoupon.getEndDate(), DATE_FORMAT).getTime() + 40 * 60 * 1000;
                        String flightDate = DateUtils.timeStampToDateStr(flightTime, DATE_FORMAT_THREE);
                        SimpleFlightInfo simpleFlightInfo = queryFlightInfo(flightDate, saleCoupon.getFlightNumber(), saleCoupon.getDepAirport(), saleCoupon.getArrAirport());
                        if (simpleFlightInfo != null) {
                            couponShow.setSimpleFlightInfo(simpleFlightInfo);
                        }
                        couponShow.setActivityName(saleCoupon.getActivityName());
                    }
                    //值机选座
                    if (VoucherTypesEnum.SEAT.getCode().equals(saleCoupon.getCouponSource())) {
                        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
                        Map<String, String> map = (Map<String, String>) JsonUtil.jsonToMap(couponDynamicParam, Map.class);
                        String flightNo = map.get("flightNo");
                        String depAirportCode = map.get("depAirportCode");
                        String arrAirportCode = map.get("arrAirportCode");
                        String flightDate = map.get("flightDate");
                        SimpleFlightInfo simpleFlightInfo = queryFlightInfo(flightDate, flightNo, depAirportCode, arrAirportCode);
                        if (simpleFlightInfo != null) {
                            couponShow.setSimpleFlightInfo(simpleFlightInfo);
                            couponShow.setStartDate(simpleFlightInfo.getFlightDate());
                            couponShow.setEndDate(simpleFlightInfo.getFlightDate());
                            saleCouponOrder.setStartDate(simpleFlightInfo.getFlightDate());
                            saleCouponOrder.setEndDate(simpleFlightInfo.getFlightDate());
                        }
                        couponShow.setActivityName(saleCoupon.getActivityName());
                    }
                    //邮寄行程单
                    if (VoucherTypesEnum.MAILTRAVEL.getCode().equals(saleCoupon.getCouponSource()) && !StringUtil.isNullOrEmpty(saleCoupon.getExpressNo())) {
                        saleCouponOrder.setLogistics(saleCoupon.getExpressNo());
                    }
                    //签证特有属性
                    if (VoucherTypesEnum.VISA.getCode().equals(saleCoupon.getCouponSource())) {
                        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
                        VisaInfo visaInfo = (VisaInfo) JsonUtil.jsonToBean(couponDynamicParam, VisaInfo.class);
                        couponShow.setVisaInfo(visaInfo);
                    }
                    if (VoucherTypesEnum.TRAFFIC.getCode().equals(couponShow.getCouponSource())) {
                        SearchProductResponse.ProductInfo.RescourceInfo.TrafficExtInfo trafficExt = saleCoupon.getTrafficExt();
                        if (trafficExt != null) {
                            TrafficInfo trafficInfo = new TrafficInfo();
                            trafficInfo.setValidityDate("自购买之日起30天内有效");
                            couponShow.setTrafficInfo(trafficInfo);
                        }
                    }
                    // 登机口升舱
                    if (VoucherTypesEnum.GATEWAYUPGRADE.getCode().equalsIgnoreCase(saleCouponList.get(0).getCouponSource())) {
                        String couponDynamicParam = saleCouponList.get(0).getCouponDynamicParam();
                        UpgCouponOrderFlightInfo flightInfo = (UpgCouponOrderFlightInfo) JsonUtil.jsonToBean(couponDynamicParam, UpgCouponOrderFlightInfo.class);
                        SimpleFlightInfo simpleFlightInfo = queryFlightInfo(flightInfo.getFlightDate(), flightInfo.getFlightNo(),
                                flightInfo.getDepAirportCode(), flightInfo.getArrAirportCode());
                        if (simpleFlightInfo != null) {
                            couponShow.setSimpleFlightInfo(simpleFlightInfo);
                            couponShow.setStartDate(simpleFlightInfo.getFlightDate());
                            couponShow.setEndDate(simpleFlightInfo.getFlightDate());
                            saleCouponOrder.setStartDate(simpleFlightInfo.getFlightDate());
                            saleCouponOrder.setEndDate(simpleFlightInfo.getFlightDate());
                        }
                        couponShow.setActivityName(saleCoupon.getActivityName());
                    }
                    // 改期券数据处理
                    if (VoucherTypesEnum.RESCHEDULECOUPON.getCode().equalsIgnoreCase(saleCouponList.get(0).getCouponSource())) {
                        if (StringUtils.isNotBlank(saleCouponList.get(0).getDepCity())) {
                            couponShow.setDepCity(saleCouponList.get(0).getDepCity());
                            couponShow.setDepCityName(localCacheService.getLocalAirport(saleCouponList.get(0).getDepCity()).getCityName());
                        }
                        if (StringUtils.isNotBlank(saleCouponList.get(0).getArrCity())) {
                            couponShow.setArrCity(saleCouponList.get(0).getArrCity());
                            couponShow.setArrCityName(localCacheService.getLocalAirport(saleCouponList.get(0).getArrCity()).getCityName());
                        }
                    }
                    if (VoucherTypesEnum.GUIDESERVICE.getCode().equals(saleCouponList.get(0).getCouponSource())) {
                        couponShow.setEndDate("只限于指定航班当日使用");
                    }
                    // 机上购物
                    if (VoucherTypesEnum.ONBOARDPRODUCT.getCode().equals(saleCouponList.get(0).getCouponSource())
                            || VoucherTypesEnum.BRANDMEALS.getCode().equals(saleCouponList.get(0).getCouponSource())) {
                        //机上产品不支持自主退款
                        couponShow.setRefundBtn(false);
                        couponShow.setActivityName("吉食购");
                        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
                        if (StringUtils.isNotBlank(couponDynamicParam)) {
                            OnboardShoppingInfo onboardShoppingInfo = (OnboardShoppingInfo) JsonUtil.jsonToBean(couponDynamicParam, OnboardShoppingInfo.class);
                            couponShow.setOnboardShoppingInfo(onboardShoppingInfo);
                            couponShow.setFlightNo(onboardShoppingInfo.getFlightNo());
                            couponShow.setFlightDate(onboardShoppingInfo.getFlightDate());
                            String weekStr = DateUtils.getWeekStr(DateUtils.toDate(onboardShoppingInfo.getFlightDate()));
                            couponShow.setWeekDay(weekStr);
                            if (onboardShoppingInfo.getDeliveryInfo() != null) {
                                String deliveryType = onboardShoppingInfo.getDeliveryInfo().getDeliveryType();
                                DeliveryTypeEnum typeEnum = DeliveryTypeEnum.checkType(deliveryType);
                                onboardShoppingInfo.getDeliveryInfo().setDeliveryName(typeEnum == null ? "未知" : typeEnum.getName());
                                couponShow.setDeliveryType(deliveryType);
                                couponShow.setDeliveryName(onboardShoppingInfo.getDeliveryInfo().getDeliveryName());
                            }
                            if (onboardShoppingInfo.getDepInfo() != null) {
                                AirPortInfoDto depAirport = airportInfo.get(onboardShoppingInfo.getDepInfo().getAirportCode());
                                couponShow.setDepAirportCName(null == depAirport ? "" : depAirport.getAirPortName());
                                couponShow.setDepDateTime(onboardShoppingInfo.getDepInfo().getTime());
                                couponShow.setDepCityName(null == depAirport ? "" : depAirport.getCityName());
                                couponShow.setDepAirport(onboardShoppingInfo.getDepInfo().getAirportCode());
                                couponShow.setDepCity(null == depAirport ? "" : depAirport.getCityCode());
                                couponShow.setDepAirportTerminal(StringUtils.isNotBlank(onboardShoppingInfo.getDepInfo().getTerminal()) ? onboardShoppingInfo.getDepInfo().getTerminal() : "--");
                            }
                            if (onboardShoppingInfo.getArrInfo() != null) {
                                AirPortInfoDto arrAirport = airportInfo.get(onboardShoppingInfo.getArrInfo().getAirportCode());
                                couponShow.setArrAirportCName(null == arrAirport ? "" : arrAirport.getAirPortName());
                                couponShow.setArrDateTime(onboardShoppingInfo.getArrInfo().getTime());
                                couponShow.setArrCityName(null == arrAirport ? "" : arrAirport.getCityName());
                                couponShow.setArrAirport(onboardShoppingInfo.getArrInfo().getAirportCode());
                                couponShow.setArrCity(null == arrAirport ? "" : arrAirport.getCityCode());
                                couponShow.setArrAirportTerminal(StringUtils.isNotBlank(onboardShoppingInfo.getArrInfo().getTerminal()) ? onboardShoppingInfo.getArrInfo().getTerminal() : "--");
                            }
                        }
                    }
                    //优惠礼包优惠券处理
                    dealCouponInfo(couponShow, saleCoupon, couponOrder.getIsBundleSale());
                    couponShowList.add(couponShow);
                } else {
                    //错误的历史数据
                    flag = true;
                    break;
                }
            }

            //优惠券集合无值舍弃当前订单
            if (flag) {
                continue;
            }
            saleCouponOrder.setCouponList(couponShowList);
            //处理权益券评论标识 2021-03-18
            saleCouponOrder.setCommentFlag(false);
            List<CouponShow> collect = couponShowList.stream().filter(couponShow -> StringUtils.isNotBlank(couponShow.getCommentState()) && "COMMENTABLE".equals(couponShow.getCommentState())).collect(Collectors.toList());
            saleCouponOrder.setCommentFlag(CollectionUtils.isNotEmpty(collect));
            //支付状态判断
            String bookDateStr = saleCouponOrder.getOrderDatetime();
            Date bookDate = DateUtils.toDate(bookDateStr, DATE_FORMAT);
            //下单时间
            long bookTime = bookDate.getTime();
            //支付时限  小于0代表已过期
            long endTime;
            if (couponShowList.get(0).getCouponSource().equals("Delivery")) {
                endTime = DateUtils.dateAddOrLessSecond(bookDate, 5 * 60).getTime();
            } else {
                endTime = DateUtils.dateAddOrLessSecond(bookDate, handConfig.getTimeLimit() * 60).getTime();
            }
            //处理订单基本信息,退款状态
            dealSaleCouponGet(saleCouponOrder, couponOrder.getOrderState(), couponOrder.getOrderStateName(), couponOrder.getPayState(), bookTime, endTime, curTime);
            //机上WIFI
            formatAirWifi(saleCouponOrder, couponOrder, airportInfo, curTime);
            //礼包信息处理
            dealCouponBagOrder(saleCouponOrder, couponOrder);
            //处理预付费行李
            formatPrepaymentBaggage(saleCouponOrder, couponOrder);

            // 列表接口与订单详情接口的状态名需要保持一致，且标记是否包含已退客票
            handleListStatusAndRefundFlag(saleCouponOrder, couponOrder);

            saleOrderList.add(saleCouponOrder);
        }
        return saleOrderList;
    }

    private void handleListStatusAndRefundFlag(SaleCouponGet saleCouponOrder, PtSaleCouponOrderGetResponse.CouponOrder couponOrder) {
        if (null == saleCouponOrder || null == couponOrder) {
            return;
        }
        //只对优选座位做处理
        if ("PaySeat".equals(saleCouponOrder.getCouponSource())) {
            //从 couponOrder 中取原生的订单状态
            OrderPayStateNewEnum orderPayStateNewEnum = OrderUtil.convertOrderState(couponOrder.getOrderState(), couponOrder.getPayState());
            saleCouponOrder.setOrderStateName(orderPayStateNewEnum.getStateDesc());
            //是否包含已退航班
            List<PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon> saleCouponList = couponOrder.getSaleCouponList();
            if (CollectionUtils.isNotEmpty(saleCouponList)) {
                for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon salcoupon : saleCouponList
                ) {
                    /*if (sal)*/
                    if (OrderDetailStateEnum.WaitFirstCheck.getStateCode().equals(salcoupon.getCouponState()) ||
                            OrderDetailStateEnum.FirstCheckFail.getStateCode().equals(salcoupon.getCouponState()) ||
                            OrderDetailStateEnum.WaitSecondCheck.getStateCode().equals(salcoupon.getCouponState()) ||
                            OrderDetailStateEnum.SecondCheckFail.getStateCode().equals(salcoupon.getCouponState()) ||
                            OrderDetailStateEnum.ApplyRefund.getStateCode().equals(salcoupon.getCouponState()) ||
                            OrderDetailStateEnum.Refund.getStateCode().equals(salcoupon.getCouponState())
                    ) {
                        saleCouponOrder.setRefundFlag(true);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 处理订单列表 预付费行李信息
     *
     * @param saleCouponOrder
     * @param couponOrder
     */
    private void formatPrepaymentBaggage(SaleCouponGet saleCouponOrder, PtSaleCouponOrderGetResponse.CouponOrder couponOrder) {
        if (!saleCouponOrder.getCouponSource().equals(VoucherTypesEnum.EXTRABAGGAGE.getCode())) {
            return;
        }
        saleCouponOrder.setOrderName("额外行李");
        if (StringUtil.isNullOrEmpty(saleCouponOrder.getCouponList())) {
            return;
        }
        List<CouponOrderInfoDto> couponOrderInfoDtoList = new ArrayList<>();
        for (CouponShow couponShow : saleCouponOrder.getCouponList()) {
            String rebateState = couponShow.getRebateState();
            if (!StringUtil.isNullOrEmpty(rebateState)) {
                //状态转换 rebateState: "Dealing" 转成 Success
                if (RefundOrderStateEnum.Dealing.getStateCode().equals(rebateState)) {
                    couponShow.setRebateState(RefundOrderStateEnum.Success.getStateCode());
                }
            } else {
                couponShow.setRebateState("");
            }
            CouponOrderInfoDto couponOrderInfoDto = new CouponOrderInfoDto();
            couponOrderInfoDto.setRebateState(couponShow.getRebateState());
            couponOrderInfoDtoList.add(couponOrderInfoDto);
        }


        //根据订单获取主订单状态显示
        ProductOrderEnum stateByCouponOrderInfo = getStateByCouponOrderInfo(couponOrderInfoDtoList);
        if (stateByCouponOrderInfo != null) {
            if (stateByCouponOrderInfo.equals(ProductOrderEnum.ApplyRefund)) {
                saleCouponOrder.setOrderStateName("退款成功");
            } else {
                saleCouponOrder.setOrderStateName(stateByCouponOrderInfo.getMessage());
            }
        }

    }

    /**
     * 处理机上wifi的响应结构
     *
     * @param saleCouponGet
     * @param couponOrder
     */
    private void formatAirWifi(SaleCouponGet saleCouponGet, PtSaleCouponOrderGetResponse.CouponOrder couponOrder, Map<String, AirPortInfoDto> airPortInfoDtoMap, long curTime) {
        try {
            if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(saleCouponGet.getCouponSource()) && !StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList())) {
                String couponDynamicParam = couponOrder.getSaleCouponList().get(0).getCouponDynamicParam();
                AirWifiInfo airWifiInfo = JsonUtil.fromJson(couponDynamicParam, AirWifiInfo.class);
                AirPortInfo depAirInfo = airWifiInfo.getDepInfo();
                AirPortInfo arrAirInfo = airWifiInfo.getArrInfo();
                PassengerInfo passengerInfo = airWifiInfo.getPassengerInfo();
                ProductFlight productFlight = new ProductFlight();
                productFlight.setFlightNo(airWifiInfo.getFlightNo());
                productFlight.setFlightDate(airWifiInfo.getFlightDate());
                productFlight.setTktNo(passengerInfo.getTicketNo());
                productFlight.setDepTime(depAirInfo.getTime());
                productFlight.setArrTime(arrAirInfo.getTime());
                productFlight.setDepAirportCode(depAirInfo.getAirportCode());
                productFlight.setDepTerminal(depAirInfo.getTerminal());
                productFlight.setArrAirportCode(arrAirInfo.getAirportCode());
                productFlight.setArrTerminal(arrAirInfo.getTerminal());
                //注意机场信息为空的情况
                if (airPortInfoDtoMap != null) {
                    AirPortInfoDto depAirport = airPortInfoDtoMap.get(depAirInfo.getAirportCode());
                    AirPortInfoDto arrAirport = airPortInfoDtoMap.get(arrAirInfo.getAirportCode());
                    productFlight.setDepAirportName(depAirport != null ? depAirport.getAirPortName() : depAirInfo.getAirportCode());
                    productFlight.setDepCityCode(depAirport != null ? depAirport.getCityCode() : "");
                    productFlight.setDepCityName(depAirport != null ? depAirport.getCityName() : depAirInfo.getAirportCode());
                    productFlight.setArrAirportName(arrAirport != null ? arrAirport.getAirPortName() : arrAirInfo.getAirportCode());
                    productFlight.setArrCityCode(arrAirport != null ? arrAirport.getCityCode() : "");
                    productFlight.setArrCityName(arrAirport != null ? arrAirport.getCityName() : arrAirInfo.getAirportCode());
                    if (airWifiInfo.getFlightDate() != null && depAirInfo.getTime() != null && arrAirInfo.getTime() != null) {
                        //根据到达时间与出发时间判断是否跨天,到达时间小于出发时间代表可能跨天
                        Date depDate = DateUtils.toDate(airWifiInfo.getFlightDate() + " " + depAirInfo.getTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        Date arrDate = DateUtils.toDate(airWifiInfo.getFlightDate() + " " + arrAirInfo.getTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        if (arrDate.getTime() < depDate.getTime()) {
                            arrDate = DateUtils.addOrLessDay(arrDate, 1);
                        }
                        String flightDepDateTime = DateUtils.convertDate2Str(depDate, DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        String flightArrDateTime = DateUtils.convertDate2Str(arrDate, DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        //处理夏令时
                        if (depAirport != null && arrAirport != null) {
                            String depCityZone = FlightUtil.convertSummerOrWinterTime(depAirport.getCityTimeZone(), flightDepDateTime, depAirport);
                            String arrCityZone = FlightUtil.convertSummerOrWinterTime(arrAirport.getCityTimeZone(), flightArrDateTime, arrAirport);
                            //计算飞行时长
                            productFlight.setDuration(DateUtils.calDuration(flightDepDateTime, depCityZone, flightArrDateTime, arrCityZone));
                            //如果当前时间超过起飞时间则不展示退款按钮
                            if (curTime >= DateUtils.toTargetDate(flightDepDateTime, depCityZone, "8").getTime()) {
                                saleCouponGet.setRefundBtn(false);
                            } else {
                                if (PayEnum.Pay.getStateCode().equals(saleCouponGet.getOrderState())) {
                                    saleCouponGet.setRefundBtn(true);
                                }
                            }
                        }
                    }
                }
                saleCouponGet.setProductFlight(productFlight);
            }
        } catch (Exception e) {
            log.error("机上WIFI处理异常:{},处理参数:{},{}", e, JsonUtil.objectToJson(saleCouponGet), JsonUtil.objectToJson(couponOrder));
        }
    }

    private void dealBoardingInfo(SaleCouponGet saleCouponGet, PtSaleCouponOrderGetResponse.CouponOrder couponOrder, Map<String, AirPortInfoDto> airportInfo) {
        try {
            if (VoucherTypesEnum.CHECKINSUBSTITUTION.getCode().equals(saleCouponGet.getCouponSource()) && !StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList())) {
                String dynamicParam = couponOrder.getSaleCouponList().get(0).getCouponDynamicParam();
                BoardingInfo boardingInfo = (BoardingInfo) JsonUtil.jsonToBean(dynamicParam, BoardingInfo.class);
                BoardingPass boardingPass = new BoardingPass();
                boardingPass.setFlightNo(boardingInfo.getFlightNo());
                boardingPass.setFlightDate(boardingInfo.getFlightDate());
                BaseAirport depInfo = new BaseAirport();
                depInfo.setAirportCode(boardingInfo.getDepAirportCode());
                depInfo.setAirportName(airportInfo.get(boardingInfo.getDepAirportCode()) == null ? boardingInfo.getDepAirportCode() : airportInfo.get(boardingInfo.getDepAirportCode()).getAirPortName());
                depInfo.setCityName(airportInfo.get(boardingInfo.getDepAirportCode()) == null ? boardingInfo.getDepAirportCode() : airportInfo.get(boardingInfo.getDepAirportCode()).getCityName());
                depInfo.setTerminal(boardingInfo.getDepAirportTerminal());
                depInfo.setDate(boardingInfo.getFlightDate());
                depInfo.setTime(transfer(boardingInfo.getDepTime()));
                boardingPass.setDepInfo(depInfo);
                BaseAirport arrInfo = new BaseAirport();
                arrInfo.setAirportCode(boardingInfo.getArrAirportCode());
                arrInfo.setAirportName(airportInfo.get(boardingInfo.getArrAirportCode()) == null ? boardingInfo.getArrAirportCode() : airportInfo.get(boardingInfo.getArrAirportCode()).getCityName());
                arrInfo.setCityName(airportInfo.get(boardingInfo.getArrAirportCode()) == null ? boardingInfo.getArrAirportCode() : airportInfo.get(boardingInfo.getArrAirportCode()).getCityName());
                arrInfo.setTerminal(boardingInfo.getArrAirportTerminal());
                arrInfo.setTime(transfer(boardingInfo.getArrTime()));
                boardingPass.setArrInfo(arrInfo);
                saleCouponGet.setBoardingPass(boardingPass);
            }
        } catch (Exception e) {
            log.error("预留登机牌处理异常:{},处理参数:{},{}", e, JsonUtil.objectToJson(saleCouponGet), JsonUtil.objectToJson(couponOrder));
        }
    }

    private String transfer(String s) {
        String regex = "(.{2})";
        s = s.replaceAll(regex, "$1:");
        return s.substring(0, 5);
    }

    /**
     * 航班简要信息
     *
     * @param flightDate
     * @param flightNo
     * @param depAirportCode
     * @param arrAirportCode
     */
    private SimpleFlightInfo queryFlightInfo(String flightDate, String flightNo, String depAirportCode, String arrAirportCode) {
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setFlightDate(flightDate);
        flightInfo.setDepAirport(depAirportCode);
        flightInfo.setArrAirport(arrAirportCode);
        flightInfo.setFlightNo(flightNo);
        List<FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
        if (!StringUtil.isNullOrEmpty(flightInfoList)) {
            final FlightInfo[] info = {new FlightInfo()};
            flightInfoList.forEach((FlightInfo flight) -> {
                if (flight.getFlightNo().equals(flightNo)) {
                    info[0] = flight;
                }
            });
            AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(depAirportCode);
            SimpleFlightInfo simpleFlightInfo = new SimpleFlightInfo();
            if (info[0] != null) {
                if (info[0].getDepAirportTerminal() == null || "--".equals(info[0].getDepAirportTerminal())) {
                    info[0].setDepAirportTerminal("");
                }
                simpleFlightInfo.setDepAirportTerminal(info[0].getDepAirportTerminal());
                simpleFlightInfo.setDepAirportCName(depAirPortInfo.getCityName() + depAirPortInfo.getAirPortName() + info[0].getDepAirportTerminal());
                simpleFlightInfo.setDepDateTime(info[0].getFlightDate() + " " + transfer(info[0].getDepDateTime()));
                AirPortInfoDto arrAirPortInfo = localCacheService.getLocalAirport(arrAirportCode);
                if (info[0].getArrAirportTerminal() == null || "--".equals(info[0].getArrAirportTerminal())) {
                    info[0].setArrAirportTerminal("");
                }
                simpleFlightInfo.setArrAirportTerminal(info[0].getArrAirportTerminal());
                simpleFlightInfo.setArrAirportCName(arrAirPortInfo.getCityName() + arrAirPortInfo.getAirPortName() + info[0].getArrAirportTerminal());
                simpleFlightInfo.setArrDateTime(info[0].getFlightArrDate() + " " + transfer(info[0].getArrDateTime()));
                simpleFlightInfo.setFlightNo(flightNo);
                simpleFlightInfo.setDepAirportCode(depAirportCode);
                simpleFlightInfo.setArrAirportCode(arrAirportCode);
                simpleFlightInfo.setFlightDate(flightDate);
            }
            return simpleFlightInfo;
        }
        return null;
    }

    /**
     * 值机选座处理逻辑
     */


    /**
     * 航班综合信息
     * 全面
     */
    private SupportFlightInfo createFlightInfo(FlightInfo info, AirPortInfoDto depInfo, AirPortInfoDto arrInfo, SupportFlightInfo supportFlightInfo) {
        supportFlightInfo.setDepAirportCode(depInfo.getAirPortCode());
        supportFlightInfo.setDepAirportName(depInfo.getAirPortName());
        if (info.getDepAirportTerminal() == null || "--".equals(info.getDepAirportTerminal())) {
            info.setDepAirportTerminal("");
        }
        supportFlightInfo.setDepAirportTerminal(info.getDepAirportTerminal());
        supportFlightInfo.setArrAirportCode(arrInfo.getAirPortCode());
        supportFlightInfo.setArrAirportName(arrInfo.getAirPortName());
        if (info.getArrAirportTerminal() == null || "--".equals(info.getArrAirportTerminal())) {
            info.setArrAirportTerminal("");
        }
        supportFlightInfo.setArrAirportTerminal(info.getArrAirportTerminal());
        supportFlightInfo.setFlightDate(info.getFlightDate());
        supportFlightInfo.setFlightNo(info.getFlightNo());
        supportFlightInfo.setDepDateTime(transfer(info.getDepDateTime()));
        supportFlightInfo.setArrDateTime(transfer(info.getArrDateTime()));
        //处理航班时间
        String weekStr = DateUtils.getWeekStr(DateUtils.toDate(info.getFlightDate()));
        supportFlightInfo.setWeekDay(weekStr);
        int day = DateUtils.diffDays(info.getFlightDate() + " " + transfer(info.getDepDateTime()), depInfo.getCityTimeZone(), info.getFlightArrDate() + " " + transfer(info.getArrDateTime()), arrInfo.getCityTimeZone());
        supportFlightInfo.setDay(day);
        long time = DateUtils.toDate(info.getFlightArrDate() + " " + transfer(info.getArrDateTime()), DATE_FORMAT_TWO).getTime() - DateUtils.toDate(info.getFlightDate() + " " + transfer(info.getDepDateTime()), DATE_FORMAT_TWO).getTime();
        supportFlightInfo.setFlightTime(time);
        //处理机型转换
        Map<String, AircraftModel> stringAircraftModelMap = toAircraftModelMap(handConfig.getAircraftModel());
        if (stringAircraftModelMap != null) {
            AircraftModel aircraftModel = stringAircraftModelMap.get(info.getPlanType());
            supportFlightInfo.setAircraftCode(aircraftModel.getAircraftCode());
            supportFlightInfo.setRemark(aircraftModel.getRemark());
        }
        supportFlightInfo.setWifiFlag("789".equals(info.getPlanType()));
        return supportFlightInfo;
    }

    //机型JSON转对象
    public static Map<String, AircraftModel> toAircraftModelMap(String aircraftModelJson) {
        try {
            Type type = new TypeToken<Map<String, AircraftModel>>() {
            }.getType();
            return (Map<String, AircraftModel>) JsonUtil.jsonToMap(aircraftModelJson, type);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 可售券
     *
     * @param saleCouponGet
     * @param orderState
     * @param payState
     * @param bookTime
     * @param endTime
     * @param curTime       当前时间戳
     */
    private void dealSaleCouponGet(SaleCouponGet saleCouponGet, String orderState, String orderStateName, String payState, long bookTime, long endTime, long curTime) {
        List<CouponShow> couponList = saleCouponGet.getCouponList();
        Set<String> couponSourceSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(couponList)) {
            couponList.forEach(couponShow -> couponSourceSet.add(couponShow.getCouponSource()));
        }
        // 包含接送机
        if (couponSourceSet.contains(VoucherTypesEnum.HOCAR.getCode())) {
            saleCouponGet.setOrderState(orderState);
            saleCouponGet.setOrderStateName(orderStateName);
        } else {
            //预订
            if (OrderStateEnum.Init.getStateCode().equals(orderState) || OrderStateEnum.Booking.getStateCode().equals(orderState)) {
                if (PayEnum.UnPay.getStateCode().equals(payState)) {
                    if (curTime <= endTime) {
                        //支付时限
                        saleCouponGet.setTimeLimit(endTime - curTime);
                        saleCouponGet.setOrderState(PayEnum.UnPay.getStateCode());
                        saleCouponGet.setOrderStateName("待付款");
                    } else {
                        saleCouponGet.setOrderState(OrderStateEnum.Cancel.getStateCode());
                        saleCouponGet.setOrderStateName("已取消");
                    }
                } else if (PayEnum.Pay.getStateCode().equals(payState)) {
                    saleCouponGet.setOrderState(PayEnum.Pay.getStateCode());
                    saleCouponGet.setOrderStateName("已支付");
                }
            }
            //取消
            else if (OrderStateEnum.Cancel.getStateCode().equals(orderState)) {
                saleCouponGet.setOrderState(OrderStateEnum.Cancel.getStateCode());
                saleCouponGet.setOrderStateName("已取消");
            }
            //已完成
            else if (OrderStateEnum.Finish.getStateCode().equals(orderState) || "TickedOut".equals(orderState)) {
                saleCouponGet.setOrderState(PayEnum.Pay.getStateCode());
                saleCouponGet.setOrderStateName("已支付");
            } else {
                saleCouponGet.setOrderState(orderState);
                saleCouponGet.setOrderStateName("");
            }
            //已退款处理
            AtomicBoolean refundflag = new AtomicBoolean(true);
            saleCouponGet.getCouponList().forEach(s -> {
                if (!OrderCouponStateEnum.Refund.getStateCode().equals(s.getCouponState())) {
                    refundflag.set(false);
                }
            });
            if (refundflag.get()) {
                saleCouponGet.setOrderState(OrderPayStateEnum.Refund.getStateCode());
                saleCouponGet.setOrderStateName(OrderPayStateEnum.Refund.getStateDesc());
            }
            //已退款处理
            if (saleCouponGet.getCouponSource().equals(VoucherTypesEnum.EXTRABAGGAGE.getCode())) {
                if (saleCouponGet.getOrderState().equals(OrderPayStateEnum.ApplyRefund.getStateCode())) {
                    saleCouponGet.setOrderStateName("退款成功");
                }
            }
        }

        //名称处理
        if (!StringUtil.isNullOrEmpty(saleCouponGet.getCouponList())) {
            //产品名称都是一致的，取第一个即可
            CouponShow saleCoupon = saleCouponGet.getCouponList().get(0);
            saleCouponGet.setStartDate(saleCoupon.getStartDate() == null ? "" : saleCoupon.getStartDate());
            saleCouponGet.setEndDate(saleCoupon.getEndDate() == null ? "" : saleCoupon.getEndDate());
            if (!VoucherTypesEnum.PACKAGE.getCode().equals(saleCoupon.getCouponSource())) {
                saleCouponGet.setPrice(saleCoupon.getPrice());
            }
            saleCouponGet.setOrderName(saleCoupon.getActivityName());
        }
        if (VoucherTypesEnum.CHECKINSUBSTITUTION.getCode().equals(saleCouponGet.getCouponSource())) {
            saleCouponGet.setOrderName(VoucherTypesEnum.CHECKINSUBSTITUTION.getName());
        } else if (VoucherTypesEnum.PAY_SEAT.getCode().equals(saleCouponGet.getCouponSource())) {
            saleCouponGet.setOrderName(VoucherTypesEnum.PAY_SEAT.getName());
        } else if (VoucherTypesEnum.HOCAR.getCode().equals(saleCouponGet.getCouponSource())) {
            saleCouponGet.setOrderName(VoucherTypesEnum.HOCAR.getName());
        }
        //总金额
        saleCouponGet.setOrderAmoutPayable(saleCouponGet.getOrderTotalAmount() + saleCouponGet.getFfpUseTotalScore());
    }

    /**
     * 订单名称处理
     *
     * @param couponSource
     * @param couponPrice
     * @param actName      原有的活动名称
     * @return
     */
    private String showOrderNameBySource(String couponSource, double couponPrice, String actName) {
        String type;
        if (couponSource == null) {
            couponSource = "";
        }
        switch (couponSource) {
            case "HOBG": {
                type = "行李额度兑换券-" + (int) couponPrice + "KG";
                break;
            }
            case "HOLO": {
                type = "贵宾休息室券";
                break;
            }
            default: {
                type = actName;
                break;
            }
        }
        return type;
    }

    private void roductInformationsSortByCode(List<ProductInformation> productInformationList) {
        Collections.sort(productInformationList, Comparator.comparingInt(a -> Integer.parseInt(a.getCode())));
    }

    /**
     * 按照起飞时间排序,升序
     *
     * @param productInfos
     */
    private void sortByDepDateTime(List<FlightProductInfo> productInfos) {
        Collections.sort(productInfos, Comparator.comparing(a -> DateUtils.toDate(a.getDepDateTime(), DATE_FORMAT_TWO)));
    }

    //值机选座
    public void checkINSeat(CouponShow couponShow, SaleCouponGet saleCouponGet, PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon) {
        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
        Map<String, String> map = (Map<String, String>) JsonUtil.jsonToMap(couponDynamicParam, Map.class);
        String flightNo = map.get("flightNo");
        String depAirportCode = map.get("depAirportCode");
        String arrAirportCode = map.get("arrAirportCode");
        String flightDate = map.get("flightDate");
        String psgrName = map.get("psgrName");
        String seatNo = map.get("seatNo");
        String cabin = map.get("cabin");
        String pnrNo = map.get("pnrNo");
        String cabinClass = CommonUtil.getCabinClassByCabinCode(cabin, handConfig.getCabinClass());
        String cabinClassName = CommonUtil.showCabinClassName(cabinClass);
        SeatInfo seatInfo = new SeatInfo();
        seatInfo.setPsgrName(psgrName);
        seatInfo.setSeatNo(seatNo);
        seatInfo.setPnrNo(pnrNo);
        couponShow.setSeatInfo(seatInfo);
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setFlightDate(flightDate);
        flightInfo.setDepAirport(depAirportCode);
        flightInfo.setArrAirport(arrAirportCode);
        flightInfo.setFlightNo(flightNo);
        List<FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
        final FlightInfo[] info = {new FlightInfo()};
        flightInfoList.forEach((FlightInfo flight) -> {
            if (flight.getFlightNo().equals(flightNo)) {
                info[0] = flight;
            }
        });
        AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(depAirportCode);
        AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(arrAirportCode);
        SupportFlightInfo supportFlightInfo = new SupportFlightInfo();
        createFlightInfo(info[0], depAirportInfo, arrAirportInfo, supportFlightInfo);
        supportFlightInfo.setCabin(cabin);
        supportFlightInfo.setCabinClass(cabinClassName);
        couponShow.setSupportFlightInfo(supportFlightInfo);
        couponShow.setStartDate(supportFlightInfo.getFlightDate());
        couponShow.setEndDate(supportFlightInfo.getFlightDate());
        saleCouponGet.setStartDate(supportFlightInfo.getFlightDate());
        saleCouponGet.setEndDate(supportFlightInfo.getFlightDate());
    }

    //礼宾接送机 逻辑
    public void GuideService(CouponShow couponShow, SaleCouponGet saleCouponGet, PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon) {
        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
        ProtocolTrafficInfo protocolTrafficInfo = JsonUtil.fromJson(couponDynamicParam, ProtocolTrafficInfo.class);
        boolean showRefundBtn = false;
        if (null != protocolTrafficInfo) {
            couponShow.setFlightNo(protocolTrafficInfo.getFlightNo());
            String deptTime = protocolTrafficInfo.getDepInfo().getDate() + " " + protocolTrafficInfo.getDepInfo().getTime();
            String arrTime = protocolTrafficInfo.getArrInfo().getDate() + " " + protocolTrafficInfo.getArrInfo().getTime();
            if (protocolTrafficInfo.getDepInfo() != null) {
                AirPortInfoDto depAirport = localCacheService.getLocalAirport(protocolTrafficInfo.getDepInfo().getAirportCode());
                couponShow.setDepAirportCName(null == depAirport ? "" : depAirport.getAirPortName());
                couponShow.setDepDateTime(deptTime);
                couponShow.setDepCityName(null == depAirport ? "" : depAirport.getCityName());
            }
            if (protocolTrafficInfo.getArrInfo() != null) {
                AirPortInfoDto arrAirport = localCacheService.getLocalAirport(protocolTrafficInfo.getArrInfo().getAirportCode());
                couponShow.setArrAirportCName(null == arrAirport ? "" : arrAirport.getAirPortName());
                couponShow.setArrDateTime(arrTime);
                couponShow.setArrCityName(null == arrAirport ? "" : arrAirport.getCityName());
            }
            if (null != protocolTrafficInfo.getPayInfo()) {
                couponShow.setRefundRules(protocolTrafficInfo.getPayInfo().getRefundRule());
            }
            if (null != protocolTrafficInfo.getPassengerInfo()) {
                couponShow.setSender(protocolTrafficInfo.getPassengerInfo().getName());
                couponShow.setSenderNum(protocolTrafficInfo.getPassengerInfo().getContactPhone());
            }
            if (null != protocolTrafficInfo.getRemark()) {
                ProtocolTrafficRemark remark = JsonUtil.fromJson(protocolTrafficInfo.getRemark(), ProtocolTrafficRemark.class);
                couponShow.setServiceType(remark.getServiceType());
                if (OrderCouponStateEnum.Not.getStateCode().equals(couponShow.getCouponState())) {
                    // 送机
                    if ("C".equalsIgnoreCase(remark.getServiceType())) {
                        Date deptureTime = DateUtils.toDate(deptTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        // 距离出发时间两小时外可以取消
                        showRefundBtn = OrderCouponStateEnum.Not.getStateCode().equals(couponShow.getCouponState())
                                && deptureTime != null && DateUtils.millisecondDiff(new Date(), deptureTime) > 1000 * 60 * 60 * 2;
                    } else if ("P".equalsIgnoreCase(remark.getServiceType())) {
                        Date arriveTime = DateUtils.toDate(arrTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        // 距离到达时间两小时外可以取消
                        showRefundBtn = OrderCouponStateEnum.Not.getStateCode().equals(couponShow.getCouponState())
                                && arriveTime != null && DateUtils.millisecondDiff(new Date(), arriveTime) > 1000 * 60 * 60 * 2;
                    }
                }
            }
        }
        // 未使用状态显示退款按钮
        couponShow.setRefundBtn(showRefundBtn);
        couponShow.setEndDate("只限于指定航班当日适用");
        couponShow.setRemark(handConfig.getProtocolTrafficCustomerServicePhone());
        saleCouponGet.setRefundRules(couponShow.getRefundRules());
        saleCouponGet.setRemark(couponShow.getRemark());

    }

    //接送机逻辑处理
    public void Traffic(CouponShow couponShow, PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon, String channelCode, Map<String, String> headMap) {
        SearchProductResponse.ProductInfo.RescourceInfo.TrafficExtInfo trafficExt = saleCoupon.getTrafficExt();
        if (trafficExt != null) {
            TrafficInfo trafficInfo = new TrafficInfo();
            trafficInfo.setCityName(trafficExt.getCityName());
            //转换车型
            //调用字典值接口
            CouponProductVisaDictionariesRequestDto dicRequestDto = new CouponProductVisaDictionariesRequestDto();
            dicRequestDto.setChannelCode(channelCode);
            dicRequestDto.setUserNo("10001");
            dicRequestDto.setVersion(HandlerConstants.VERSION);
            String[] codes = {CouponProductTypeEnum.DICT_TRAFFIC_TYPE.getCode()};
            dicRequestDto.setDictCodes(codes);
            HttpResult typeResult = doPostClient(dicRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_VISA_DICT, headMap);
            Map<String, String> map = new HashMap<>();
            if (typeResult.isResult()) {
                CouponProductVisaDictionariesResponseDto responseDto = (CouponProductVisaDictionariesResponseDto) JsonUtil.jsonToBean(typeResult.getResponse(), CouponProductVisaDictionariesResponseDto.class);
                Map<String, List<CouponProductVisaDictionariesResponseDto.SimpleTreeNode>> data = responseDto.getData();
                Map<String, Map<String, String>> stringMapMap = RightCouponConvert.getStringMapMap(data);
                map = stringMapMap.get(CouponProductTypeEnum.DICT_TRAFFIC_TYPE.getCode());
            }
            trafficInfo.setCarType(map.get(trafficExt.getCarType()));
            trafficInfo.setValidityDate("自购买之日起30天内有效");
            couponShow.setTrafficInfo(trafficInfo);
        }
    }

    public void MailTravel(CouponShow couponShow, SaleCouponGet saleCouponGet, PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon) {
        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
        MailTravelProduct mailTravelProduct = (MailTravelProduct) JsonUtil.jsonToBean(couponDynamicParam, MailTravelProduct.class);
        saleCouponGet.setAddress(mailTravelProduct.getRecipientsAddress());
        saleCouponGet.setLinker(mailTravelProduct.getRecipientsName());
        saleCouponGet.setLinkerHandphone(mailTravelProduct.getRecipientsPhone());
        saleCouponGet.setLogistics(saleCoupon.getExpressNo());
        List<PassengerItinerary> passengerItineraryList = mailTravelProduct.getPassengerItineraryList();
        Map<String, List<PassengerItinerary>> collect = passengerItineraryList.stream().collect(Collectors.groupingBy(PassengerItinerary::getTicketNo));
        List<MailTravelInfo> mailTravelInfos = new ArrayList<>();
        collect.keySet().forEach(s -> {
            List<PassengerItinerary> passengerItineraries = collect.get(s);
            MailTravelInfo mailTravelInfo = new MailTravelInfo();
            mailTravelInfo.setTicketNo(s);
            List<SimpleFlightInfo> simpleFlightInfos = new ArrayList<>();
            passengerItineraries.forEach(p -> {
                mailTravelInfo.setPassengerName(p.getPassengerName());
                SimpleFlightInfo simpleFlightInfo = queryFlightInfo(p.getDepDateTime().substring(0, 10), p.getFlightNo(), p.getDeptAirport(), p.getArrAirport());
                if (simpleFlightInfo != null) {
                    simpleFlightInfos.add(simpleFlightInfo);
                }
            });
            mailTravelInfo.setFlightInfoList(simpleFlightInfos);
            mailTravelInfos.add(mailTravelInfo);
        });
        couponShow.setMailTravelInfos(mailTravelInfos);
    }

    //品牌餐食  机上购物
    private void OnboardProduct(CouponShow couponShow, PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon) {
        //机上产品不支持自主退款
        couponShow.setRefundBtn(false);
        couponShow.setActivityName("吉品祥购");
        String couponDynamicParam = saleCoupon.getCouponDynamicParam();
        if (StringUtils.isNotBlank(couponDynamicParam)) {
            OnboardShoppingInfo onboardShoppingInfo = (OnboardShoppingInfo) JsonUtil.jsonToBean(couponDynamicParam, OnboardShoppingInfo.class);
            OnboardShoppingInfo.ProductDetailBean productDetailBean = onboardShoppingInfo.getProductDetail();
            if (productDetailBean != null) {
                String productImg = productDetailBean.getProductImg();
                if (!StringUtil.isNullOrEmpty(productImg) && productImg.indexOf("|") > 0) {
                    String[] split = productImg.split("\\|");
                    productDetailBean.setProductImg(split[0]);
                }
            }
            couponShow.setOnboardShoppingInfo(onboardShoppingInfo);
            couponShow.setFlightNo(onboardShoppingInfo.getFlightNo());
            couponShow.setFlightDate(onboardShoppingInfo.getFlightDate());
            String weekStr = DateUtils.getWeekStr(DateUtils.toDate(onboardShoppingInfo.getFlightDate()));
            couponShow.setWeekDay(weekStr);
            if (onboardShoppingInfo.getDeliveryInfo() != null) {
                String deliveryType = onboardShoppingInfo.getDeliveryInfo().getDeliveryType();
                DeliveryTypeEnum typeEnum = DeliveryTypeEnum.checkType(deliveryType);
                onboardShoppingInfo.getDeliveryInfo().setDeliveryName(typeEnum == null ? "未知" : typeEnum.getName());
                couponShow.setDeliveryType(deliveryType);
                couponShow.setDeliveryName(onboardShoppingInfo.getDeliveryInfo().getDeliveryName());
            }
            if (onboardShoppingInfo.getDepInfo() != null) {
                AirPortInfoDto depAirport = localCacheService.getLocalAirport(onboardShoppingInfo.getDepInfo().getAirportCode());
                couponShow.setDepAirportCName(null == depAirport ? "" : depAirport.getAirPortName());
                couponShow.setDepDateTime(onboardShoppingInfo.getDepInfo().getTime());
                couponShow.setDepCityName(null == depAirport ? "" : depAirport.getCityName());
                couponShow.setDepAirport(onboardShoppingInfo.getDepInfo().getAirportCode());
                couponShow.setDepCity(null == depAirport ? "" : depAirport.getCityCode());
                couponShow.setDepAirportTerminal(StringUtils.isNotBlank(onboardShoppingInfo.getDepInfo().getTerminal()) ? onboardShoppingInfo.getDepInfo().getTerminal() : "--");
            }
            if (onboardShoppingInfo.getArrInfo() != null) {
                AirPortInfoDto arrAirport = localCacheService.getLocalAirport(onboardShoppingInfo.getArrInfo().getAirportCode());
                couponShow.setArrAirportCName(null == arrAirport ? "" : arrAirport.getAirPortName());
                couponShow.setArrDateTime(onboardShoppingInfo.getArrInfo().getTime());
                couponShow.setArrCityName(null == arrAirport ? "" : arrAirport.getCityName());
                couponShow.setArrAirport(onboardShoppingInfo.getArrInfo().getAirportCode());
                couponShow.setArrCity(null == arrAirport ? "" : arrAirport.getCityCode());
                couponShow.setArrAirportTerminal(StringUtils.isNotBlank(onboardShoppingInfo.getArrInfo().getTerminal()) ? onboardShoppingInfo.getArrInfo().getTerminal() : "--");
            }
        }
    }


    public void setSaleCouponGet(PtSaleCouponOrderGetResponse.CouponOrder couponOrder, SaleCouponGet saleCouponGet) {
        if (!StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList().get(0).getReceiver())) {
            saleCouponGet.setReceiver(couponOrder.getSaleCouponList().get(0).getReceiver());
        }
        if (!StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList().get(0).getReceiverNum())) {
            saleCouponGet.setReceiverNum(couponOrder.getSaleCouponList().get(0).getReceiverNum());
        }
        if (!StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList().get(0).getRefundRules())) {
            saleCouponGet.setRefundRules(couponOrder.getSaleCouponList().get(0).getRefundRules());
        }
        //处理固包的特殊情况
        if (VoucherTypesEnum.PACKAGE.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
            int count = couponOrder.getSaleCouponList().size() / 2;
            saleCouponGet.setSaleCouponCount(count);
            saleCouponGet.setPrice((couponOrder.getOrderTotalAmount() + couponOrder.getFfpUseTotalScore()) / count);
        } else {
            saleCouponGet.setSaleCouponCount(couponOrder.getSaleCouponList().size());
        }
        //吉祥镖局
        if (VoucherTypesEnum.DELIVERY.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
            if (!StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList().get(0).getSenderNum())) {
                saleCouponGet.setLinkerHandphone(couponOrder.getSaleCouponList().get(0).getSenderNum());
            }
            //迪士尼单独购票
        } else if (VoucherTypesEnum.DISNEY.getCode().equals(couponOrder.getSaleCouponList().get(0).getCouponSource())) {
            if (!StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList().get(0).getSenderNum())) {
                saleCouponGet.setLinkerHandphone(couponOrder.getLinkerHandphone());
            }
        } else {
            if (!StringUtil.isNullOrEmpty(couponOrder.getSaleCouponList().get(0).getContactTelphone())) {
                saleCouponGet.setLinkerHandphone(couponOrder.getSaleCouponList().get(0).getContactTelphone());
            }
        }
    }


    public void saleOrderListProcess(List<SaleCouponGet> saleOrderList) {
        saleOrderList.forEach(saleCouponGet -> {
            if (saleCouponGet.getCouponSource().matches(PatternCommon.TOTALTYPE)) {
                List<Product> products = themeCardService.selectProduct(saleCouponGet.getCouponSource());
                if (CollectionUtils.isNotEmpty(products)) {
                    String availDatestr = products.get(0).getStartAvailDate() + "至" + products.get(0).getEndAvailDate() + "?(具体使用时间见详细规则)";
                    saleCouponGet.setAvailDate(availDatestr);
                    String finalAvailDatestr = availDatestr;
                    saleCouponGet.getCouponList().forEach(couponShow -> {
                        couponShow.setAvailDate(finalAvailDatestr);
                    });
                }
                saleCouponGet.setTotalType(PackageTypeEnum.THEME_COUPON.getPackType());
            }
        });
    }
}
