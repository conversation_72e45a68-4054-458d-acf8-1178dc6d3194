package com.juneyaoair.mobile.handler.service.impl;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.message.bean.message.MessageResult;
import com.juneyaoair.message.bean.message.SmsUserInfo;
import com.juneyaoair.message.client.MessageClient;
import com.juneyaoair.message.utils.MessageUtil;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.manage.CommonManage;
import com.juneyaoair.mobile.handler.service.SmsService;
import com.juneyaoair.sms.model.SmsRequest;
import com.juneyaoair.sms.model.SmsResponse;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by yaocf on 2022/12/9  15:47.
 */
@Service
@Slf4j
public class SmsServiceImpl extends CommonManage implements SmsService  {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private MessageClient messageClient;

    @Override
    public boolean sendSms(String templateCode, String smsTemplate, String areaId, String phone, Map<String, String> extras) {
        if (StringUtils.isAnyBlank(templateCode, phone) || null == extras || extras.isEmpty()) {
            log.info("请求ID：{} 手机号码:{}, 模板ID：{} 可变量：{} 短信发送参数校验不通过", MdcUtils.getRequestId(), phone, templateCode, null == extras ? "" : JSON.toJSONString(extras));
            return false;
        }
        try {
            // 使用消息平台发送短信
            if (handConfig.messageFlag) {
                List<SmsUserInfo> smsUserInfoList = SmsUserInfo.getSmsUserInfoList(phone, extras);
                // 针对国家区号进行转换
                areaId = MessageUtil.formatAreaId(areaId);
                // 存在国家区号 将国家区号放入手机号中
                if (StringUtils.isNotBlank(areaId)) {
                    for (SmsUserInfo smsUserInfo : smsUserInfoList) {
                        smsUserInfo.setPhone(areaId + smsUserInfo.getPhone());
                    }
                    areaId = null;
                }
                List<MessageResult> messageResultList = messageClient.batchSmsMessage(templateCode, null, areaId, smsUserInfoList, MdcUtils.getRequestId());
                if (CollectionUtils.isEmpty(messageResultList)) {
                    return false;
                }
                MessageResult messageResult = messageResultList.get(0);
                return null != messageResult && StringUtils.isNotBlank(messageResult.getSendMessageId());
            }
            // 直连短信平台发送短信
            String content = replaceTemplate(smsTemplate, extras);
            if (StringUtils.isBlank(content)) {
                log.info("请求ID：{} 手机号码:{},发送短信内容为空，短信模板：{}", MdcUtils.getRequestId(), phone, smsTemplate);
                return false;
            }
            log.info("请求ID：{} 手机号码:{},调用短信接口开始:{}", MdcUtils.getRequestId(), phone, content);
            SmsRequest smsRequest = formatSmsReq(phone, content, templateCode);
            SmsResponse smsResponse = sendSms(smsRequest);
            log.info("请求ID：{} 手机号码:{},调用短信接口结束:{}", MdcUtils.getRequestId(), phone, JSON.toJSONString(smsResponse));
            return "2".equals(smsResponse.getResultcode());
        } catch (CommonException ce) {
            log.error("请求ID：{} 发送短信出现异常，异常信息：{}", MdcUtils.getRequestId(), ce.getMessage());
            return false;
        } catch (Exception e) {
            log.error("请求ID：{} 发送短信出现异常，异常信息：", MdcUtils.getRequestId(), e);
            return false;
        }
    }

    /**
     * 模板变量替换
     * @param smsTemplate
     * @param extras
     * @return
     */
    private String replaceTemplate(String smsTemplate, Map<String, String> extras) {
        if (StringUtils.isBlank(smsTemplate) || null == extras || extras.isEmpty()) {
            return smsTemplate;
        }
        for (Map.Entry<String, String> entry : extras.entrySet()) {
            smsTemplate = smsTemplate.replaceAll("#" + entry.getKey() + "#", entry.getValue());
        }
        return smsTemplate;
    }

    @Override
    public SmsResponse sendSms(SmsRequest smsRequest) {
        String url = handConfig.getSmsUrl();
        try{
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("smsRequest", JsonUtil.objectToJson(smsRequest));
            /**Flag参数如下：
             1.短信发送
             2:一对一批量发送
             3:短信接收
             4:查询状态报告
             5:查询关键字**/
            paramMap.put("flag", "1");
            HttpResult httpResult = HttpUtil.doPostClientForm(paramMap,url,setProxy(url));
            if(httpResult.isResult()){
                SmsResponse smsResponse = JsonUtil.fromJson(httpResult.getResponse(), SmsResponse.class);
                return smsResponse;
            }else {
                throw new NetworkException("短信网络请求异常");
            }
        }catch (Exception exception){
            log.error("发送短信异常",exception);
            SmsResponse smsResponse = new SmsResponse();
            smsResponse.setResultcode("-1");
            return smsResponse;
        }

}

    @Override
    public SmsResponse sendSmsBatch(SmsRequest var1) {
        String url = handConfig.getSmsUrl();
        try{
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("smsRequest", JsonUtil.objectToJson(var1));
            /**Flag参数如下：
             1.短信发送
             2:一对一批量发送
             3:短信接收
             4:查询状态报告
             5:查询关键字**/
            paramMap.put("flag", "2");
            HttpResult httpResult = HttpUtil.doPostClientForm(paramMap,url,setProxy(url));
            if(httpResult.isResult()){
                SmsResponse smsResponse = JsonUtil.fromJson(httpResult.getResponse(), SmsResponse.class);
                return smsResponse;
            }else {
                throw new NetworkException("短信网络请求异常");
            }
        }catch (Exception exception){
            log.error("发送短信异常",exception);
            SmsResponse smsResponse = new SmsResponse();
            smsResponse.setResultcode("-1");
            return smsResponse;
        }
    }

    /**
     * 封装短信平台请求参数
     * @param mobiles
     * @param content
     * @param templateCode
     * @return
     */
    private SmsRequest formatSmsReq(String mobiles, String content, String templateCode) {
        SmsRequest req = new SmsRequest();
        req.setUserId(HandlerConstants.SMS_USER);
        req.setPassword(EncoderHandler.encodeByMD5(HandlerConstants.SMS_PWD));
        req.setSmsType(HandlerConstants.SMS_TYPE);
        req.setContent(content);
        req.setMobiles(mobiles);
        req.setRemark1(templateCode);
        // 当前时间推后十分钟，至少五分钟
        Date date = DateUtils.dateAddOrLessSecond(new Date(), 600);
        String endSendTime = DateUtils.convertDateToString(date, DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        req.setEndSendTime(endSendTime);
        return req;
    }
}
