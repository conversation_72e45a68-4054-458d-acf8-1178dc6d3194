package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.geetest.geeguard.sdk.GeetestLib;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.common.CheckInSeatStatus;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseRequestDTO;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.cuss.response.SeatOrderInfo;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.baseclass.request.newCheckinSeat.*;
import com.juneyaoair.baseclass.request.seat.AddPeerParam;
import com.juneyaoair.baseclass.response.ibe.AdmSeatChart;
import com.juneyaoair.baseclass.response.ibe.SeatChart;
import com.juneyaoair.baseclass.response.newCheckinSeat.*;
import com.juneyaoair.baseclass.response.seat.SeatSelectInfoResult;
import com.juneyaoair.baseclass.response.seat.TravellerTripTipResult;
import com.juneyaoair.cuss.dto.booking.common.FlightTicketSimpleDTO;
import com.juneyaoair.cuss.dto.booking.common.SeatMapDTO;
import com.juneyaoair.cuss.dto.booking.request.checkin.*;
import com.juneyaoair.cuss.dto.booking.request.seat.SeatChartRequestDTO;
import com.juneyaoair.cuss.dto.booking.request.ume.GetBoardingPassRequestDTO;
import com.juneyaoair.cuss.dto.booking.request.ume.GetBoardingPassStatusRequestDTO;
import com.juneyaoair.cuss.dto.booking.response.checkin.*;
import com.juneyaoair.cuss.dto.booking.response.ume.GetBoardingPassResponseDTO;
import com.juneyaoair.cuss.dto.booking.response.ume.GetBoardingPassStatusResponseDTO;
import com.juneyaoair.cuss.dto.booking.response.ume.UMECheckInTravelInfo;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.SourceType;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.FlightTourService;
import com.juneyaoair.mobile.handler.controller.util.CussObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.FraudApiInvoker;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.thirdentity.checkinSeat.request.PtCancelSeatRequestDto;
import com.juneyaoair.thirdentity.checkinSeat.request.PtSeatProductModifyFlyerRequestDto;
import com.juneyaoair.thirdentity.checkinSeat.response.PtSeatProductModifyFlyerResponseDto;
import com.juneyaoair.thirdentity.request.checkin.QueryTourRequestDTO;
import com.juneyaoair.thirdentity.response.checkin.FlightTourDTO;
import com.juneyaoair.thirdentity.response.checkin.QueryTourResponseDTO;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponOrderGetResponse;
import com.juneyaoair.thirdentity.tongdun.FinalDecisionEnum;
import com.juneyaoair.thirdentity.tongdun.response.FraudApiResponse;
import com.juneyaoair.util.ControllerUtil;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:zc
 * @Description:值机选座2.0
 * @Date:Created in 17:19 2018/11/19
 * @Modified by:
 */
@RequestMapping("/checkInSeatService")
@RestController
@Api(value = "CheckInSeatController", description = "值机选座服务")
public class CheckInSeatController extends BassController {

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private SmsService smsService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private GeetestService geetestService;
    @Autowired
    private FlightTourService flightTourService;
    @Autowired
    private CheckInSeatService checkInSeatService;
    @Autowired
    private LocalCacheService localCacheService;

    /**
     * 最新一次检验章状态
     */
    private static final String LAST_UPDATE_STATUS_IMG_URL = "NEWCHECKIN:BOARDINGPASS:STATUSIMGURL";

    /**
     * 超时时间，用于查值机选座列表、查座位图、做值机选座，其他接口默认超时时间8s
     */
    private static final int READ_TIMEOUT = 40000;
    private static final int CONNECT_TIMEOUT = 40000;

    /**
     * 查询所有航班行程信息（未登录，已登陆）
     *
     * @param newCheckInQuery
     * @param request
     * @return
     */
    @ApiOperation(value = "查询所有航班行程信息（未登录，已登陆）", notes = "查询所有航班行程信息（未登录，已登陆）")
    @RequestMapping(value = "/queryAllFlightTours", method = RequestMethod.POST)
    @InterfaceLog
    public FlightTourResponse queryAllFlightTours(@RequestBody NewCheckInQuery newCheckInQuery, HttpServletRequest request) {
        boolean flag1 = "member".equalsIgnoreCase(newCheckInQuery.getType()) || "history".equals(newCheckInQuery.getType());
        //boolean flag2 = StringUtils.isAnyBlank(newCheckInQuery.getFlightNo(), newCheckInQuery.getFlightDate(), newCheckInQuery.getDepCityCode(), newCheckInQuery.getArrCityCode());
        boolean flag2 = StringUtils.isAnyBlank(newCheckInQuery.getFlightNo(), newCheckInQuery.getFlightDate());
        // 非会员查询、历史查询 且 参数存在空
        if (!flag1 && flag2) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "服务升级");
        }
        return flightTourService.queryAllFlightTours(newCheckInQuery, request);
    }

    @ApiOperation(value = "获取旅客选座数据", notes = "获取旅客选座数据")
    @InterfaceLog
    @RequestMapping(value = "/queryMySelectSeatInfo", method = RequestMethod.POST)
    public SelectSeatInfoResponse queryMySelectSeatInfo(@RequestBody NewCheckInQuery newCheckInQuery, HttpServletRequest request) {
        SelectSeatInfoResponse selectSeatInfoResponse = new SelectSeatInfoResponse();
        String ip = this.getClientIP(request);
        String channelCodeHead = request.getHeader("channelCode");
        String clientVersion = request.getHeader(HandlerConstants.CLIENT_VERSION);
        //输入参数校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<NewCheckInQuery>> violations = validator.validate(newCheckInQuery);
        if (null != violations && violations.size() > 0) {
            selectSeatInfoResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            selectSeatInfoResponse.setErrorInfo(violations.iterator().next().getMessage());
            return selectSeatInfoResponse;
        }
        String clientIp = this.getClientIP(request);
        //控制未登录和登陆后限制查询次数的差异
        Map<String, String> map = checkInVisitLimit(newCheckInQuery, clientIp, newCheckInQuery.getFfpId(), newCheckInQuery.getFfpCardNo(), newCheckInQuery.getLoginKeyInfo(), newCheckInQuery.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(map.get("code"))) {
            selectSeatInfoResponse.setResultCode(map.get("code"));
            selectSeatInfoResponse.setErrorInfo(map.get("message"));
            return selectSeatInfoResponse;
        }
        JSONObject tags = new JSONObject();
        tags.put("IP",ip);
        tags.put("FfpCardNo",newCheckInQuery.getFfpCardNo());
        tags.put("ChannelCode",channelCodeHead);
        MetricLogUtil.saveMetricLog("选座结果-客票提取",tags,new BigDecimal(1));
        String multiProxy = IPUtil.isMultyProxy(request) ? "1" : "0";
        FraudApiResponse fraudApiResponse = FraudApiInvoker.checkinSeatControl(newCheckInQuery.getBlackBox(), newCheckInQuery.getPlatformInfo(), channelCodeHead,
                ip, multiProxy, newCheckInQuery.getFfpCardNo(), clientVersion);
        if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
            selectSeatInfoResponse.setResultCode(WSEnum.TONGDUN_CHECKIN_FAIL_TRADE.getResultCode());
            selectSeatInfoResponse.setErrorInfo(WSEnum.TONGDUN_CHECKIN_FAIL_TRADE.getResultInfo());
            return selectSeatInfoResponse;
        }
        String type = "SEAT";
        //接口参数封装
        BaseRequestDTO<QueryTourRequestDTO> baseRequest = new BaseRequestDTO<>();
        baseRequest.setIp(clientIp);
        baseRequest.setChannelCode(getChannelInfo(newCheckInQuery.getChannelCode(), "50"));
        baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        if (StringUtils.isNotBlank(newCheckInQuery.getPsgName()) && StringUtils.isNotBlank(newCheckInQuery.getCertNo())) {
            QueryTourRequestDTO queryTourRequestDTO = new QueryTourRequestDTO();
            queryTourRequestDTO.setSource("QUERY_MY_SELECT_SEAT_INFO");
            queryTourRequestDTO.setCertificateNo(newCheckInQuery.getCertNo());
            queryTourRequestDTO.setPassengerName(newCheckInQuery.getPsgName());
            queryTourRequestDTO.setQueryType(type);
            baseRequest.setRequest(queryTourRequestDTO);
        } else if (0 != newCheckInQuery.getFfpId() && StringUtils.isNotBlank(newCheckInQuery.getFfpCardNo()) && StringUtils.isNotBlank(newCheckInQuery.getLoginKeyInfo())) {
            baseRequest.setUserNo(String.valueOf(newCheckInQuery.getFfpId()));
            QueryTourRequestDTO queryTourRequestDTO = new QueryTourRequestDTO();
            queryTourRequestDTO.setSource("QUERY_MY_SELECT_SEAT_INFO");
            queryTourRequestDTO.setCertificateNo(newCheckInQuery.getCertNo());
            queryTourRequestDTO.setPassengerName(newCheckInQuery.getPsgName());
            queryTourRequestDTO.setQueryType(type);
            baseRequest.setRequest(queryTourRequestDTO);
        } else {
            selectSeatInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
            selectSeatInfoResponse.setErrorInfo("查询航班行程信息请求参数有误！");
            return selectSeatInfoResponse;
        }

        String result;
        try {
            result = this.invokePost(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.QUERY_TOUR, READ_TIMEOUT, CONNECT_TIMEOUT);
            BaseResultDTO<QueryTourResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class, QueryTourResponseDTO.class);
            if (null != res && UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(res.getResultCode())) {
                if (null == res.getResult()) {
                    selectSeatInfoResponse.setResultCode(WSEnum.NO_DATA.getResultCode());
                    selectSeatInfoResponse.setErrorInfo("未查询到可办理线上值机选座的航班，如有疑问可联系客服");
                    return selectSeatInfoResponse;
                }
                //行程列表信息
                List<FlightTourDTO> tours = res.getResult().getTours();
                //筛选出可选座或值机列表和不可选作值机列表
                List<FlightTourSegDetails> checkInSeatSegList = Lists.newArrayList();
                //数据转换
                List<FlightTourSegDetails> segDetails = flightTourService.convertSegPsgInfo(tours, newCheckInQuery.getChannelCode(), clientIp);
                //2020-12-03 app6.2.1查询选座结果传值出发城市三字码、到达城市三字码，需要分渠道筛选
                String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
                String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
                //2021-01-14 不做版本控制，分两次筛选 筛选符合该出发机场、到达机场、航班日期的航段旅客信息
                List<FlightTourSegDetails> collect = segDetails.stream().filter(f ->
                        f.getFlightDate().equals(newCheckInQuery.getFlightDate())
                                && f.getDepAirport().equals(newCheckInQuery.getDepAirportCode())
                                && f.getArrAirport().equals(newCheckInQuery.getArrAirportCode())
                                && f.getFlightNo().equals(newCheckInQuery.getFlightNo())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    //筛选符合该出发城市、到达到达城市、航班日期的航段旅客信息
                    collect = segDetails.stream().filter(f ->
                            f.getFlightDate().equals(newCheckInQuery.getFlightDate())
                                    && f.getDepCityCode().equals(newCheckInQuery.getDepAirportCode())
                                    && f.getArrCityCode().equals(newCheckInQuery.getArrAirportCode())
                                    && f.getFlightNo().equals(newCheckInQuery.getFlightNo())).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(collect)) {
                    FlightTourSegDetails tourSegDetails = collect.get(0);
                    if (tourSegDetails != null) {
                        //行程列表文案
                        TravellerTripTipResult travellerTripTip = CussObjectConvert.getTravellerTripTip(tourSegDetails.getDepAirport(), tourSegDetails.getArrAirport(), Sets.newHashSet("SELECT"));
                        tourSegDetails.setTourTip(travellerTripTip.getTourTip());
                        selectSeatInfoResponse.setTip(travellerTripTip.getTip());
                        checkInSeatSegList.add(tourSegDetails);
                        //排序
                        processCheckInBoardingResponse(checkInSeatSegList, selectSeatInfoResponse, newCheckInQuery, ip);
                        //2020-12-03出发到达时间只有小程序1.4.8包含:(hh:mm)，其他都是hhmm
                        if ((ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= 14800) ||
                                (ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) || (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(headChannelCode))) {
                        } else {
                            selectSeatInfoResponse.setDepDateTime(StringUtils.isNotBlank(selectSeatInfoResponse.getDepDateTime()) ? selectSeatInfoResponse.getDepDateTime().replace(":", "") : selectSeatInfoResponse.getDepDateTime());
                            selectSeatInfoResponse.setArrDateTime(StringUtils.isNotBlank(selectSeatInfoResponse.getArrDateTime()) ? selectSeatInfoResponse.getArrDateTime().replace(":", "") : selectSeatInfoResponse.getArrDateTime());
                        }
                        selectSeatInfoResponse.setSeatShareImgUrl(handConfig.getSeatShareImgUrl());
                    } else {
                        selectSeatInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
                        selectSeatInfoResponse.setErrorInfo(WSEnum.ERROR.getResultInfo());
                        return selectSeatInfoResponse;
                    }
                } else {
                    selectSeatInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    selectSeatInfoResponse.setErrorInfo(WSEnum.ERROR.getResultInfo());
                    return selectSeatInfoResponse;
                }
            } else {
                selectSeatInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
                selectSeatInfoResponse.setErrorInfo(null == res ? "未查询到可办理线上值机选座的航班，如有疑问可联系客服" : res.getErrorMsg());
                return selectSeatInfoResponse;
            }
        } catch (Exception e) {
            log.error("queryMySelectSeatInfo:查询航班行程信息出错：", e);
            selectSeatInfoResponse.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            selectSeatInfoResponse.setErrorInfo("网络出错了，请重新提交");
            return selectSeatInfoResponse;
        }
        return selectSeatInfoResponse;
    }

    private void processCheckInBoardingResponse(List<FlightTourSegDetails> checkInSeatSegList, SelectSeatInfoResponse selectSeatInfoResponse,
                                                NewCheckInQuery newCheckInQuery, String ip) {
        try {
            List<FlightTourSegDetails> checkInSeats = new ArrayList<>();
            List<FlightTourSegDetails> haveCheckInSeats = new ArrayList<>();
            FlightTourPsgInfo flightTourPsgInfo = null;
            for (FlightTourSegDetails flightTourSegDetail : checkInSeatSegList) {
                for (int i = 0; i < flightTourSegDetail.getFlightTourPsgInfoList().size(); i++) {
                    flightTourPsgInfo = flightTourSegDetail.getFlightTourPsgInfoList().get(i);
                    //判断是否有已值机或选座
                    if (!StringUtil.isNullOrEmpty(flightTourPsgInfo.getAsrSeatNo()) || !StringUtil.isNullOrEmpty(flightTourPsgInfo.getCheckInSeatNo())) {
                        haveCheckInSeats.add(flightTourSegDetail);
                        break;
                    }
                    if (i == flightTourSegDetail.getFlightTourPsgInfoList().size() - 1) {
                        checkInSeats.add(flightTourSegDetail);
                    }
                }
            }
            checkInSeats.addAll(haveCheckInSeats);
            if (CollectionUtils.isNotEmpty(checkInSeats)) {
                for (FlightTourSegDetails tourSegDetails : checkInSeats) {
                    BeanUtils.copyProperties(tourSegDetails, selectSeatInfoResponse);
                    selectSeatInfoResponse.setFlightNo(tourSegDetails.getFlightNo());
                    selectSeatInfoResponse.setFlightDate(tourSegDetails.getFlightDate());
                    selectSeatInfoResponse.setDepAirportCode(tourSegDetails.getDepAirport());
                    selectSeatInfoResponse.setArrAirportCode(tourSegDetails.getArrAirport());
                    selectSeatInfoResponse.setDepAirportName(tourSegDetails.getDepAirportName());
                    selectSeatInfoResponse.setArrAirportName(tourSegDetails.getArrAirportName());
                    selectSeatInfoResponse.setDepCityName(tourSegDetails.getDepCityName());
                    selectSeatInfoResponse.setArrCityName(tourSegDetails.getArrCityName());
                    selectSeatInfoResponse.setDepTerminal(tourSegDetails.getDepTerminal());
                    selectSeatInfoResponse.setArrTerminal(tourSegDetails.getArrTerminal());
                    selectSeatInfoResponse.setPlaneType(tourSegDetails.getPlaneType());
                    selectSeatInfoResponse.setPlaneTypeName(tourSegDetails.getPlaneTypeName());
                    selectSeatInfoResponse.setArrDateTime(tourSegDetails.getSchArrTime());
                    selectSeatInfoResponse.setDepDateTime(tourSegDetails.getSchDeptTime());
                    Map<String, AirPortInfoDto> airportInfo = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
                    //到达机场
                    AirPortInfoDto airPortInfoArr = airportInfo.get(tourSegDetails.getArrAirport());
                    selectSeatInfoResponse.setArrTimeZone(airPortInfoArr == null ? 8 : Integer.parseInt(airPortInfoArr.getCityTimeZone()));
                    //出发机场
                    AirPortInfoDto airPortInfoDep = airportInfo.get(tourSegDetails.getDepAirport());
                    selectSeatInfoResponse.setDepTimeZone(airPortInfoDep == null ? 8 : Integer.parseInt(airPortInfoDep.getCityTimeZone()));
                    // 航班信息完整
                    boolean flagFlight = StringUtils.isNoneBlank(tourSegDetails.getFlightDate(), tourSegDetails.getSchArrDate(),
                            tourSegDetails.getSchDeptTime(), tourSegDetails.getSchArrTime()) &&
                            tourSegDetails.getSchDeptTime().length() > 3 && tourSegDetails.getSchArrTime().length() > 3;
                    // 城市信息完整
                    boolean flagCity = null != airPortInfoArr && null != airPortInfoDep && StringUtils.isNoneBlank(airPortInfoDep.getCityTimeZone(), airPortInfoArr.getCityTimeZone());
                    if (flagFlight && flagCity) {
                        String beginDate = tourSegDetails.getFlightDate().concat(" ").concat(tourSegDetails.getSchDeptTime());
                        String endDate = tourSegDetails.getSchArrDate().concat(" ").concat(tourSegDetails.getSchArrTime());
                        String depCityZone = FlightUtil.convertSummerOrWinterTime(airPortInfoDep.getCityTimeZone(), beginDate, airPortInfoDep);
                        String arrCityZone = FlightUtil.convertSummerOrWinterTime(airPortInfoArr.getCityTimeZone(), beginDate, airPortInfoArr);
                        Long flyTime = DateUtils.calDuration(beginDate, depCityZone, endDate, arrCityZone);
                        selectSeatInfoResponse.setFlyTimeLength(flyTime);
                    }
                    List<FlightTourPsgInfo> flightTourPsgInfoList = tourSegDetails.getFlightTourPsgInfoList().stream().filter(info -> info.getPsrName().equals(newCheckInQuery.getPsgName())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(flightTourPsgInfoList)) {
                        FlightTourPsgInfo tourPsgInfo = flightTourPsgInfoList.get(0);
                        List<SelectSeatReservePsgInfo> infos = Lists.newArrayList();
                        SelectSeatReservePsgInfo psgInfo = new SelectSeatReservePsgInfo();
                        psgInfo.setCabin(tourPsgInfo.getCabin());
                        psgInfo.setCabinName(getCabinName(tourPsgInfo.getCabin()));
                        psgInfo.setSeatNo(tourPsgInfo.getAsrSeatNo());
                        String name = StringUtil.isNullOrEmpty(tourPsgInfo.getPsrName()) ? (StringUtil.isNullOrEmpty(tourPsgInfo.getPsrEnName()) ? "" : tourPsgInfo.getPsrEnName()) : tourPsgInfo.getPsrName();
                        psgInfo.setPsgrName(name);
                        psgInfo.setTktNo(tourPsgInfo.getEtCode());
                        psgInfo.setAreaId(tourPsgInfo.getAreaId());
                        psgInfo.setPhone(tourPsgInfo.getPhone());
                        psgInfo.setPnrNo(tourPsgInfo.getPnrNo());
                        psgInfo.setSegNo(tourPsgInfo.getSegNo());
                        // 该接口为查看已选座详情调用 直接默认已选座状态 T
                        psgInfo.setSelectSeatStatus("T");
                        psgInfo.setId(tourPsgInfo.getId());
                        psgInfo.setCardID(tourPsgInfo.getCardId());
                        psgInfo.setCardLevel(tourPsgInfo.getCardLevel());
                        psgInfo.setCardAirline(tourPsgInfo.getCardAirline());
                        psgInfo.setAreaCode(tourPsgInfo.getAreaCode());
                        psgInfo.setCouponCode(null == tourPsgInfo.getPaySeatOrderInfo() ? null : tourPsgInfo.getPaySeatOrderInfo().getCouponCode());
                        infos.add(psgInfo);
                        selectSeatInfoResponse.setSelectSeatReservePsgInfos(infos);
                    }
                }
                selectSeatInfoResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                selectSeatInfoResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return;
            }
            selectSeatInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
            selectSeatInfoResponse.setErrorInfo(WSEnum.ERROR.getResultInfo());
        } catch (Exception e) {
            log.error("值机选座列表排序异常：{}", e.getMessage());
            selectSeatInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
            selectSeatInfoResponse.setErrorInfo(WSEnum.ERROR.getResultInfo());
        }
    }

    /**
     * 获取值机选座座位图
     *
     * @param checkInSeatChartRequest
     * @param request
     * @return
     */
    @ApiOperation(value = "获取航班座位图", notes = "获取航班座位图")
    @RequestMapping(value = "/getFlightSeatCharts", method = RequestMethod.POST)
    public CheckInSeatChartResponse getFlightSeatCharts(@RequestBody CheckInSeatChartRequest checkInSeatChartRequest, HttpServletRequest request) {
        CheckInSeatChartResponse checkInSeatChartResponse = new CheckInSeatChartResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，获取航班座位图：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatChartRequest), DateUtils.getCurrentTimeStr());
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //输入参数校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInSeatChartRequest>> violations = validator.validate(checkInSeatChartRequest);
        if (null != violations && violations.size() > 0) {
            checkInSeatChartResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            checkInSeatChartResponse.setErrorInfo(violations.iterator().next().getMessage());
            return checkInSeatChartResponse;
        }
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(checkInSeatChartRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = checkInSeatChartRequest.getClientIp();
            if (!this.checkToken(checkInSeatChartRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                checkInSeatChartResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                checkInSeatChartResponse.setErrorInfo("您的访问出意外啦！");
                return checkInSeatChartResponse;
            }
        }

        //控制未登录和登陆后限制查询次数的差异
        Map<String, String> map = checkInVisitLimit(checkInSeatChartRequest, clientIp, checkInSeatChartRequest.getFfpId(), checkInSeatChartRequest.getFfpCardNo(), checkInSeatChartRequest.getLoginKeyInfo(), checkInSeatChartRequest.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(map.get("code"))) {
            checkInSeatChartResponse.setResultCode(map.get("code"));
            checkInSeatChartResponse.setErrorInfo(map.get("message"));
            return checkInSeatChartResponse;
        }
        //实际机型，用于航班apollo控制
        String realPlaneType = "";
        if (checkInSeatChartRequest.getCheckInSeatStatus().equals(CheckInSeatStatus.CHECK_IN_SEAT_CLOSE.getCode())) {
            checkInSeatChartResponse.setResultCode(WSEnum.ERROR.getResultCode());
            checkInSeatChartResponse.setErrorInfo("值机选座已关闭，暂不能获取座位图");
            return checkInSeatChartResponse;
        } else {
            //预留座位信息
            List<GetSelectSeatResponse> getIBESelectSeatResponseList = new ArrayList<>();
            if (CheckInSeatStatus.CHECK_IN_OPEN.getCode().equals(checkInSeatChartRequest.getCheckInSeatStatus())) {
                //查值机座位图
                BaseRequestDTO<SeatMapRequestDTO> baseSeatMapRequest = new BaseRequestDTO<>();
                SeatMapRequestDTO seatMapRequest = new SeatMapRequestDTO();
                baseSeatMapRequest.setIp(clientIp);
                baseSeatMapRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
                baseSeatMapRequest.setChannelCode(getChannelInfo(checkInSeatChartRequest.getChannelCode(), "50"));
                baseSeatMapRequest.setUserNo(String.valueOf(checkInSeatChartRequest.getFfpId()));
                seatMapRequest.setArrAirportCode(checkInSeatChartRequest.getArrAirportCode());
                seatMapRequest.setDepAirportCode(checkInSeatChartRequest.getDepAirportCode());
                seatMapRequest.setFlightClass(checkInSeatChartRequest.getGetSeatMapPsgInfos().get(0).getCabin());
                seatMapRequest.setFlightDate(checkInSeatChartRequest.getFlightDate());
                seatMapRequest.setFlightNo(checkInSeatChartRequest.getFlightNo());
                baseSeatMapRequest.setRequest(seatMapRequest);
                String result;
                try {
                    HttpResult httpResult = doPostClient(baseSeatMapRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_QUERY_SEAT_MAP, null, READ_TIMEOUT, CONNECT_TIMEOUT);
                    if (httpResult.isResult()) {
                        BaseResultDTO<SeatMapResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(httpResult.getResponse(), BaseResultDTO.class, SeatMapResponseDTO.class);
                        if (!res.getResultCode().equals("1001")) {
                            checkInSeatChartResponse.setResultCode(WSEnum.ERROR.getResultCode());
                            checkInSeatChartResponse.setErrorInfo(res.getErrorMsg());
                            return checkInSeatChartResponse;
                        }
                        CheckInCharts checkInCharts = new CheckInCharts();
                        BeanUtils.copyNotNullProperties(res.getResult(), checkInCharts);
                        //解析flightType;
                        realPlaneType = checkInCharts.getPlaneType();
                        String planType = checkInCharts.getPlaneType();
                        if ("321".equals(checkInCharts.getPlaneType())) {
                            //根据不同的组别返回机型数据
                            switch (checkInCharts.getPlaneClass()) {
                                case "C31":
                                case "211":
                                    planType = "A32A";
                                    break;
                                case "271X":
                                    planType = "A32Q";
                                    break;
                                default:
                                    planType = "A321";
                                    break;
                            }
                        } else if ("320".equals(checkInCharts.getPlaneType())) {
                            //根据不同的组别返回机型数据
                            switch (checkInCharts.getPlaneClass()) {
                                case "73":
                                case "77":
                                case "73B":
                                case "271N":
                                    planType = "A32N";
                                    break;
                                default:
                                    planType = "A320";
                                    break;
                            }
                        } else if ("787".equals(checkInCharts.getPlaneType())) {
                            planType = "B789";
                        }
                        checkInCharts.setPlaneType(planType);
                        checkInSeatChartResponse.setCheckInCharts(checkInCharts);
                        // 循环处理锁定付费座位 老接口不再支持付费选座
                        if (null != checkInSeatChartResponse.getCheckInCharts() && CollectionUtils.isNotEmpty(checkInSeatChartResponse.getCheckInCharts().getSeatMapList())) {
                            for (SeatMapDTO seatMapDTO : checkInSeatChartResponse.getCheckInCharts().getSeatMapList()) {
                                if ("$".equals(seatMapDTO.getSeatValue())) {
                                    seatMapDTO.setSeatValue("X");
                                }
                            }
                        }
                        checkInSeatChartResponse.setCabinClass(ControllerUtil.getHighestClassCabin(checkInSeatChartRequest.getGetSeatMapPsgInfos().get(0).getCabin()));
                    }
                } catch (Exception e) {
                    log.error("获取值机座位图出错：" + e.getMessage(), e);
                    checkInSeatChartResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    checkInSeatChartResponse.setErrorInfo("获取值机座位图出错");
                    return checkInSeatChartResponse;
                }
                //到值机时间去sypr拿离港预留座位信息
                getIBESelectSeatResponseList = querySYPRSeatInfo(checkInSeatChartRequest, clientIp);
            }
            if (CheckInSeatStatus.SELECT_OPEN.getCode().equals(checkInSeatChartRequest.getCheckInSeatStatus())) {
                //查选座座位图
                //1 登陆下去检查订单支付情况，未登录前段显示未选座
                if (!StringUtil.isNullOrEmpty(map.get("loginFlag")) && "true".equals(map.get("loginFlag"))) {
                    List<String> orderNos = new LinkedList<>();
                    for (GetSeatMapPsgInfoRequest seatMapPsgInfoRequest : checkInSeatChartRequest.getGetSeatMapPsgInfos()) {
                        //验证orderNo加密
//                        boolean checkFlag = checkToken(seatMapPsgInfoRequest.getPayKeyInfo(),seatMapPsgInfoRequest.getTicketNo()+seatMapPsgInfoRequest.getOrderNo(),"");
//                        if(checkFlag){
                        if (!StringUtil.isNullOrEmpty(seatMapPsgInfoRequest.getOrderNo())) {
                            orderNos.add(seatMapPsgInfoRequest.getOrderNo());
                        }
//                        }
                    }
                    if (!StringUtil.isNullOrEmpty(orderNos)) {
                        List<PtSaleCouponOrderGetResponse.CouponOrder> list = flightTourService.querySeatOrderInfo(checkInSeatChartRequest.getChannelCode(), orderNos, clientIp);
                        for (PtSaleCouponOrderGetResponse.CouponOrder couponOrder : list) {
                            if (!"Pay".equals(couponOrder.getPayState())) {
                                checkInSeatChartResponse.setResultCode(WSEnum.HAVE_UNPAY_SEAT.getResultCode());
                                checkInSeatChartResponse.setErrorInfo(WSEnum.HAVE_UNPAY_SEAT.getResultInfo());
                                return checkInSeatChartResponse;
                            }
                        }
                    }
                }
                // 请求参数
                BaseRequestDTO<SeatChartRequestDTO> reqBean = new BaseRequestDTO<SeatChartRequestDTO>();
                SeatChartRequestDTO seatChartRequestDTO = new SeatChartRequestDTO();
                BeanUtils.copyNotNullProperties(checkInSeatChartRequest, seatChartRequestDTO);
                seatChartRequestDTO.setOrgCity(checkInSeatChartRequest.getDepAirportCode());
                seatChartRequestDTO.setDstCity(checkInSeatChartRequest.getArrAirportCode());
                seatChartRequestDTO.setCabin(checkInSeatChartRequest.getGetSeatMapPsgInfos().get(0).getCabin());
                reqBean.setIp(clientIp);
                reqBean.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
                reqBean.setChannelCode(checkInSeatChartRequest.getChannelCode());
                reqBean.setUserNo(String.valueOf(checkInSeatChartRequest.getFfpId()));
                reqBean.setRequest(seatChartRequestDTO);
                try {
                    HttpResult httpResult = doPostClient(reqBean, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_SELECT_SEAT_CHART, null, READ_TIMEOUT, CONNECT_TIMEOUT);
                    if (httpResult.isResult()) {
                        String result = httpResult.getResponse();
                        //返回结果转换
                        BaseResultDTO<AdmSeatChart> resultDTO = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class, AdmSeatChart.class);
                        if (null != resultDTO && "1001".equals(resultDTO.getResultCode())) {
                            AdmSeatChart admSeatChart = resultDTO.getResult();
                            SelectSeatCharts selectSeatCharts = new SelectSeatCharts();
                            selectSeatCharts.setFlightNo(admSeatChart.getAirNo());
                            selectSeatCharts.setDepDate(admSeatChart.getDepDateStr());
                            selectSeatCharts.setDepCityCode(admSeatChart.getOrgCity());
                            selectSeatCharts.setArrCityCode(admSeatChart.getDstCity());
                            selectSeatCharts.setSeatMaplist(admSeatChart.getSeatMaplist());
                            // 循环处理锁定付费座位 老接口不再支持付费选座
                            if (CollectionUtils.isNotEmpty(selectSeatCharts.getSeatMaplist())) {
                                for (SeatChart seatChart : selectSeatCharts.getSeatMaplist()) {
                                    if ('$' == seatChart.getSeatStatus()) {
                                        seatChart.setSeatStatus('X');
                                    }
                                    // 座位存在价格 且 价格大于0 设置X锁 锁定座位
                                    if (null != seatChart.getPrice() && null != seatChart.getPrice().getPrice() && seatChart.getPrice().getPrice().compareTo(BigDecimal.ZERO) > 0) {
                                        seatChart.setSeatStatus('X');
                                    }
                                }
                            }
                            //获取选座321具体机型
                            realPlaneType = admSeatChart.getPlaneType();
                            String planeType = admSeatChart.getPlaneType();
                            if ("3".equals(admSeatChart.getPlaneType().trim().substring(0, 1))) {
                                planeType = "A".concat(admSeatChart.getPlaneType());
                            } else if ("789".equals(admSeatChart.getPlaneType())) {
                                planeType = "B".concat(admSeatChart.getPlaneType());
                            }
                            //32S 320改装后的机型，布局与32N一致
                            if ("32S".equals(realPlaneType)) {
                                planeType = "A32N";
                            }
                            selectSeatCharts.setPlaneType(planeType);
                            checkInSeatChartResponse.setSelectSeatCharts(selectSeatCharts);
                            //获取最高舱位
                            checkInSeatChartResponse.setCabinClass(ControllerUtil.getHighestClassCabin(checkInSeatChartRequest.getGetSeatMapPsgInfos().get(0).getCabin()));
                        } else {
                            log.error("获取选座座位图超时或者出错");
                            checkInSeatChartResponse.setResultCode(WSEnum.ERROR.getResultCode());
                            checkInSeatChartResponse.setErrorInfo(
                                    null != resultDTO ? (StringUtil.isNullOrEmpty(resultDTO.getErrorMsg()) ? "获取选座座位图出错" : resultDTO.getErrorMsg()) : "获取选座座位图超时");

                            return checkInSeatChartResponse;
                        }
                        //在选座时间点去IBE拿预留座位信息
                        getIBESelectSeatResponseList = checkInSeatService.queryIBESeatInfo(checkInSeatChartRequest, clientIp);
                    } else {
                        checkInSeatChartResponse.setResultCode(WSEnum.ERROR.getResultCode());
                        checkInSeatChartResponse.setErrorInfo(httpResult.getResponse());
                        return checkInSeatChartResponse;
                    }
                } catch (Exception e) {
                    log.error("获取选座座位图出错：" + e.getMessage(), e);
                    checkInSeatChartResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    checkInSeatChartResponse.setErrorInfo("网络出错了，请重新提交");
                    return checkInSeatChartResponse;
                }
            }
            checkInSeatChartResponse.setGetIBESelectSeatResponseList(getIBESelectSeatResponseList);
            checkInSeatChartResponse.setAgreements(ControllerUtil.configAgreements(checkInSeatChartRequest.getCheckInSeatStatus()));
            checkInSeatChartResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
            checkInSeatChartResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            log.info("{}_值机选座，返回获取航班座位图：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatChartResponse), DateUtils.getCurrentTimeStr());
        }
        checkInSeatChartResponse.setNewSeatMap(FlightUtil.SeatMap(checkInSeatChartResponse));
        return checkInSeatChartResponse;
    }

    //查旅客离港预留座位信息
    private List<GetSelectSeatResponse> querySYPRSeatInfo(CheckInSeatChartRequest checkInSeatChartRequest, String clientIp) {
        List<GetSelectSeatResponse> syprSeatInfos = new ArrayList<>();
        List<GetSeatMapPsgInfoRequest> psgInfoRequests = checkInSeatChartRequest.getGetSeatMapPsgInfos();
        BaseRequestDTO<SyprRequestDTO> baseRequestDTO = new BaseRequestDTO<SyprRequestDTO>();
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        baseRequestDTO.setChannelCode(getChannelInfo(checkInSeatChartRequest.getChannelCode(), "50"));
        baseRequestDTO.setUserNo("");
        psgInfoRequests.forEach(getSeatMapPsgInfo -> {
            SyprRequestDTO syprRequest = new SyprRequestDTO();
            syprRequest.setEtCode(getSeatMapPsgInfo.getTicketNo());
            //强制刷新缓存信息
            syprRequest.setRefresh(true);
            BeanUtils.copyNotNullProperties(checkInSeatChartRequest, syprRequest);
            baseRequestDTO.setRequest(syprRequest);
            String syprResult;
            try {
                syprResult = this.invokePost(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_SYPR, READ_TIMEOUT, CONNECT_TIMEOUT);
                BaseResultDTO<SyprResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(syprResult, BaseResultDTO.class, SyprResponseDTO.class);
                if (null != res && "1001".equals(res.getResultCode()) && null != res.getResult()) {
                    if (res.getResult().getSyprList().size() > 0) {
                        res.getResult().getSyprList().forEach(checkInInfo -> {
                            if (!StringUtil.isNullOrEmpty(checkInInfo.getAsrSeat())) {
                                GetSelectSeatResponse selectSeatResponse = new GetSelectSeatResponse();
                                selectSeatResponse.setArrAirportCode(checkInInfo.getArrAirport());
                                selectSeatResponse.setDepAirportCode(checkInInfo.getDepAirport());
                                selectSeatResponse.setPsrEnName(checkInInfo.getPsrEnName());
                                selectSeatResponse.setPsrName(checkInInfo.getPsrName());
                                selectSeatResponse.setTicketNo(checkInInfo.getEtCode());
                                selectSeatResponse.setSeatNo(checkInInfo.getAsrSeat());
                                syprSeatInfos.add(selectSeatResponse);
                            }
                        });
                        //20190328 缓存SYPR信息
                        String key = "CHECKIN_SYPR:" + checkInSeatChartRequest.getDepAirportCode()
                                + checkInSeatChartRequest.getArrAirportCode()
                                + checkInSeatChartRequest.getFlightDate()
                                + ":" + getSeatMapPsgInfo.getTicketNo().replace("-", "");
                        apiRedisService.putData(key, JsonUtil.objectToJson(res.getResult().getSyprList()), 15 * 60L);//缓存15分钟
                    }
                }
            } catch (Exception e) {
                log.error("查询离港数据doSYPR错误：" + e.getMessage(), e);
            }
        });
        return syprSeatInfos;
    }

 /*   @ApiOperation(value = "操作值机选座", notes = "操作值机选座")
    @RequestMapping(value = "/doCheckInSelectSeat", method = RequestMethod.POST)
    public CheckInSeatReserveResponse doCheckInSelectSeat(@RequestBody CheckInSeatReserveRequest checkInSeatReserveRequest, HttpServletRequest request) {
        CheckInSeatReserveResponse checkInSeatReserveResponse = new CheckInSeatReserveResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，操作值机选座：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatReserveRequest), DateUtils.getCurrentTimeStr());
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInSeatReserveRequest>> violationsCheckIn = validator.validate(checkInSeatReserveRequest);
        if (CollectionUtils.isNotEmpty(violationsCheckIn)) {
            checkInSeatReserveResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            checkInSeatReserveResponse.setErrorInfo(violationsCheckIn.iterator().next().getMessage());
            return checkInSeatReserveResponse;
        }
        if (CheckInSeatStatus.CHECK_IN_SEAT_CLOSE.getCode().equals(checkInSeatReserveRequest.getCheckInSeatStatus())) {
            checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
            checkInSeatReserveResponse.setErrorInfo("值机选座已关闭，不能进行选座");
            return checkInSeatReserveResponse;
        }
        if (checkInSeatReserveRequest.getFfpId().equals(0L)){
            checkInSeatReserveResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            checkInSeatReserveResponse.setErrorInfo("参数错误");
            return checkInSeatReserveResponse;
        }
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(checkInSeatReserveRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = checkInSeatReserveRequest.getClientIp();
            if (!this.checkToken(checkInSeatReserveRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                checkInSeatReserveResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                checkInSeatReserveResponse.setErrorInfo("您的访问出意外啦！");
                return checkInSeatReserveResponse;
            }
        }

        //控制未登录和登陆后限制查询次数的差异
        Map<String, String> mapResult = checkInVisitLimit(checkInSeatReserveRequest, clientIp, checkInSeatReserveRequest.getFfpId(), checkInSeatReserveRequest.getFfpCardNo(), checkInSeatReserveRequest.getLoginKeyInfo(), checkInSeatReserveRequest.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(mapResult.get("code"))) {
            checkInSeatReserveResponse.setResultCode(mapResult.get("code"));
            checkInSeatReserveResponse.setErrorInfo(mapResult.get("message"));
            return checkInSeatReserveResponse;
        }
        //输入参数校验
        if (CheckInSeatStatus.CHECK_IN_OPEN.getCode().equals(checkInSeatReserveRequest.getCheckInSeatStatus()) && null != checkInSeatReserveRequest.getCheckInReserveInfos()) {
            //值机
            for (CheckInReserveInfo checkInReserveInfo : checkInSeatReserveRequest.getCheckInReserveInfos()) {
                Set<ConstraintViolation<CheckInReserveInfo>> violations = validator.validate(checkInReserveInfo);
                if (null != violations && violations.size() > 0) {
                    checkInSeatReserveResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    checkInSeatReserveResponse.setErrorInfo(violations.iterator().next().getMessage());
                    return checkInSeatReserveResponse;
                }
            }
            //20190328 判断外渠道值过机的是否改座位
            for (CheckInReserveInfo checkInReserveInfo : checkInSeatReserveRequest.getCheckInReserveInfos()) {
                String key = "CHECKIN_SYPR:" + checkInReserveInfo.getDepAirportCode()
                        + checkInReserveInfo.getArrAirportCode()
                        + checkInReserveInfo.getFlightDate()
                        + ":" + checkInReserveInfo.getEtCode().replace("-", "");
                String redisInfo = apiRedisService.getData(key);
                if (!StringUtil.isNullOrEmpty(redisInfo)) {
                    List<CheckInInfoDTO> checkInInfoDTOList = JsonMapper.buildNonNullMapper().fromJsonToList(redisInfo, CheckInInfoDTO.class);
                    //校验是否官方渠道,非官方渠道不能更改值机座位
                    if (checkInInfoDTOList.get(0).getCheckInStatus().equals("AC") && !checkInInfoDTOList.get(0).getCheckInOfficial() && !checkInInfoDTOList.get(0).getSeatNo().equals(checkInReserveInfo.getSeatNo())) {
                        checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
                        String name = StringUtil.isNullOrEmpty(checkInInfoDTOList.get(0).getPsrName()) ? (StringUtil.isNullOrEmpty(checkInInfoDTOList.get(0).getPsrEnName()) ? "" : checkInInfoDTOList.get(0).getPsrEnName()) : checkInInfoDTOList.get(0).getPsrName();
                        checkInSeatReserveResponse.setErrorInfo("旅客" + name + "在非吉祥航空官方渠道办理过值机，暂不支持更改座位");
                        return checkInSeatReserveResponse;
                    }

                    //OP:开放状态，CI:航班初始关闭，CL:中间关闭，CC:最后关闭，EC:航班处于 AEC保护，PC: 航 班 被 保 护（不能接受旅客），注：只有OP才能办理业务
                    String flightStatus = checkInInfoDTOList.get(0).getFlightStatus();
                    if (!"OP".equals(flightStatus)) {
                        checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
                        checkInSeatReserveResponse.setErrorInfo("航班当前状态未开放值机，请稍后再试(" + flightStatus + ")");
                        return checkInSeatReserveResponse;
                    }
                }
            }
            //查询航班初始化情况
            String flightNo = checkInSeatReserveRequest.getCheckInReserveInfos().get(0).getFlightNo();
            String flightDate = checkInSeatReserveRequest.getCheckInReserveInfos().get(0).getFlightDate();
            String depAirportCode = checkInSeatReserveRequest.getCheckInReserveInfos().get(0).getDepAirportCode();
            Map<String, Object> initMap = checkInSeatService.verifyCurrentCheckInSeatStatus(flightNo, flightDate, depAirportCode, checkInSeatReserveRequest.getChannelCode(), clientIp);
            if (initMap.get("code").equals("ok")) {
                if (!(boolean) initMap.get("initFlag")) {
                    //未初始化，提示重新操作
                    log.error("flightNo:{},flightDate:{},depAirportCode:{},航班未初始化", flightNo, flightDate, depAirportCode);
                    checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    checkInSeatReserveResponse.setErrorInfo("航班未初始化");
                    return checkInSeatReserveResponse;
                }
            }
            Map<String, Object> map = doCheckIn(checkInSeatReserveRequest.getCheckInReserveInfos(), String.valueOf(checkInSeatReserveRequest.getFfpId()), checkInSeatReserveRequest.getFfpCardNo(), checkInSeatReserveRequest.getChannelCode(), clientIp);
            CheckInReserveResponse checkInRsults = (CheckInReserveResponse) map.get("result");
            checkInSeatReserveResponse.setCheckInReserveResponse(checkInRsults);
            if (map.get("code").equals(WSEnum.SUCCESS.getResultCode())) {
                // 添加赠送优惠券校验的缓存
                checkInSeatReserveRequest.getCheckInReserveInfos().forEach(checkInReserveInfo ->
                        apiRedisService.putData(RedisKeyConfig.genCheckSendCouponKey(checkInReserveInfo.getEtCode(), checkInSeatReserveRequest.getFfpCardNo()),
                                checkInReserveInfo.getEtCode() + checkInSeatReserveRequest.getFfpCardNo(), 2 * 60 * 60L));
                checkInSeatReserveResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                checkInSeatReserveResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                log.info("{}_值机选座，返回操作值机成功信息：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatReserveResponse), DateUtils.getCurrentTimeStr());
                return checkInSeatReserveResponse;
            } else {
                checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
                checkInSeatReserveResponse.setErrorInfo((String) map.get("message"));
                log.info("{}_值机选座，返回操作值机失败信息：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatReserveResponse), DateUtils.getCurrentTimeStr());
                return checkInSeatReserveResponse;
            }

        } else if (CheckInSeatStatus.SELECT_OPEN.getCode().equals(checkInSeatReserveRequest.getCheckInSeatStatus())) {
            //付费选座时token问题
            if (null != checkInSeatReserveRequest.getPayAmount() && checkInSeatReserveRequest.getPayAmount().intValue() > 0) {
                String token = request.getHeader("token");
                String channel = request.getHeader("channelCode");
                if (StringUtil.isNullOrEmpty(checkInSeatReserveRequest.getFfpCardNo())) {
                    checkInSeatReserveResponse.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                    checkInSeatReserveResponse.setErrorInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                    log.info("{}_付费选座需要登录{}", JsonUtil.objectToJson(checkInSeatReserveRequest), JsonUtil.objectToJson(checkInSeatReserveResponse));
                    return checkInSeatReserveResponse;
                }
                if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(channel) || ChannelCodeEnum.WEIXIN.getChannelCode().equalsIgnoreCase(channel)) {
                    VerifyTokenResp verifyTokenResp = TokenUtils.verifyTokenAndRenewal(request.getHeader("channelCode"), token);
                    //app会员信息不能为空  m站增加token验证
                    if ((verifyTokenResp.getCode().equals(VerifyTokenResult.FAIL_KEY))) {
                        checkInSeatReserveResponse.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                        checkInSeatReserveResponse.setErrorInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                        log.info("{}_付费选座token认证失败{}", JsonUtil.objectToJson(checkInSeatReserveRequest), JsonUtil.objectToJson(checkInSeatReserveResponse));
                        return checkInSeatReserveResponse;
                    }
                }
            }

            //选座
            List<SelectSeatReserveInfo> infoList = checkInSeatReserveRequest.getSelectSeatReserveInfos();
            for (SelectSeatReserveInfo selectSeatReserveInfo : infoList) {
                Set<ConstraintViolation<SelectSeatReserveInfo>> violations = validator.validate(selectSeatReserveInfo);
                if (null != violations && violations.size() > 0) {
                    checkInSeatReserveResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    checkInSeatReserveResponse.setErrorInfo(violations.iterator().next().getMessage());
                    return checkInSeatReserveResponse;
                }
            }
            String flightNo = infoList.get(0).getFlightNo();
            String flightDate = infoList.get(0).getFlightDate();
            String depAirportCode = infoList.get(0).getDepAirportCode();
            Map<String, Object> initMap = checkInSeatService.verifyCurrentCheckInSeatStatus(flightNo, flightDate, depAirportCode, checkInSeatReserveRequest.getChannelCode(), clientIp);
            if (initMap.get("code").equals("ok")) {
                if ((boolean) initMap.get("initFlag")) {
                    //已初始化，提示重新操作
                    log.info("flightNo:{},flightDate:{},depAirportCode:{},航班已初始化", flightNo, flightDate, depAirportCode);
                    checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    checkInSeatReserveResponse.setErrorInfo("该航班现在可值机，请退出重新查询");
                    return checkInSeatReserveResponse;
                }
            }
            //座位不变不去后台
            List<SelectSeatReserveInfo> selectSeatReserveInfos = checkInSeatReserveRequest.getSelectSeatReserveInfos();
            List<SelectSeatReserveInfo> selectSeatList = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(selectSeatReserveInfos)) {
                for (SelectSeatReserveInfo selectSeatReserveInfo : selectSeatReserveInfos) {
                    if (!selectSeatReserveInfo.getSeatNo().equalsIgnoreCase(selectSeatReserveInfo.getExistSeatNo())) {
                        selectSeatList.add(selectSeatReserveInfo);
                    }
                }
                checkInSeatReserveRequest.setSelectSeatReserveInfos(selectSeatList);
            }
            Map<String, Object> map = new HashMap<>();
            if (StringUtil.isNullOrEmpty(selectSeatList)) {
                checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
                checkInSeatReserveResponse.setErrorInfo("座位未发生改变,重新选定座位");
                log.info("{}_值机选座，返回操作选座失败信息：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatReserveResponse), DateUtils.getCurrentTimeStr());
                return checkInSeatReserveResponse;
            }
            map = doSelectSeat(checkInSeatReserveRequest, checkInSeatReserveRequest.getChannelCode(), clientIp);
            SelectSeatReserveResponse selectSeatResult = (SelectSeatReserveResponse) map.get("result");
            String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
            //2021-05-20 值机小程序 不处理时间:，统一返回hh:mm，其他渠道返回hhmm
            if ((ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode))){
                String depDateTime = selectSeatResult.getDepDateTime();
                selectSeatResult.setDepDateTime(StringUtils.isNotBlank(depDateTime) ? depDateTime.substring(0,2)+":"+depDateTime.substring(2) : "");
                String arrDateTime = selectSeatResult.getArrDateTime();
                selectSeatResult.setArrDateTime(StringUtils.isNotBlank(arrDateTime)? arrDateTime.substring(0,2)+":"+arrDateTime.substring(2) : "");
            }
            checkInSeatReserveResponse.setSelectSeatReserveResponse(selectSeatResult);
            if (map.get("code").equals(WSEnum.SUCCESS.getResultCode())) {
                // 添加赠送优惠券校验的缓存
                infoList.forEach(reserveInfo -> apiRedisService.putData(RedisKeyConfig.genCheckSendCouponKey(reserveInfo.getTktNo(), checkInSeatReserveRequest.getFfpCardNo()),
                        reserveInfo.getTktNo() + checkInSeatReserveRequest.getFfpCardNo(), 2 * 60 * 60L));
                checkInSeatReserveResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                log.info("{}_值机选座，返回操作选座成功信息：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatReserveResponse), DateUtils.getCurrentTimeStr());
                return checkInSeatReserveResponse;
            } else {
                checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
                checkInSeatReserveResponse.setErrorInfo((String) map.get("message"));
                log.info("{}_值机选座，返回操作选座失败信息：{},时间：{}", uuid, JsonUtil.objectToJson(checkInSeatReserveResponse), DateUtils.getCurrentTimeStr());
                return checkInSeatReserveResponse;
            }
        } else {
            checkInSeatReserveResponse.setResultCode(WSEnum.ERROR.getResultCode());
            checkInSeatReserveResponse.setErrorInfo("数据错误");
            return checkInSeatReserveResponse;
        }
    }*/

    /**
     * 取消预留座位
     *
     * @param paramCancelSeat
     * @param request         20190326
     * @return
     */
    @ApiOperation(value = "取消付费选座", notes = "取消选座")
    @RequestMapping(value = "cancelSeat", method = RequestMethod.POST)
    public PaySelectSeatCancelResponse refundPaySeat(@RequestBody PaySelectSeatCancelRequest paramCancelSeat, HttpServletRequest request) {
        PaySelectSeatCancelResponse response = new PaySelectSeatCancelResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，取消付费选座Request:{},时间：{}", uuid, JsonUtil.objectToJson(paramCancelSeat), DateUtils.getCurrentTimeStr());
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<PaySelectSeatCancelRequest>> violations = validator.validate(paramCancelSeat);
        if (null != violations && violations.size() > 0) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        if (paramCancelSeat.getFfpId().equals(0L)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo("参数错误");
            return response;
        }
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(paramCancelSeat.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = paramCancelSeat.getClientIp();
            if (!this.checkToken(paramCancelSeat.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo("您的访问出意外啦！");
                return response;
            }
        }


        //控制未登录和登陆后限制查询次数的差异
        Map<String, String> mapResult = checkInVisitLimit(paramCancelSeat, clientIp, paramCancelSeat.getFfpId(), paramCancelSeat.getFfpCardNo(), paramCancelSeat.getLoginKeyInfo(), paramCancelSeat.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(mapResult.get("code"))) {
            response.setResultCode(mapResult.get("code"));
            response.setErrorInfo(mapResult.get("message"));
            return response;
        }

        try {
            //请求参数
            PtCancelSeatRequestDto paySeatCancelReq = CussObjectConvert.createCancelSeatRequest(paramCancelSeat, clientIp, getChannelInfo(paramCancelSeat.getChannelCode(), "10"));
            //调取消接口
            String result = invokePost(paySeatCancelReq, HandlerConstants.URL_FARE_API + HandlerConstants.PAY_CANCEL_SELECTSEAT);
            log.debug("取消付费选座response:" + result);
            if (!StringUtil.isNullOrEmpty(result)) {
                PaySelectSeatCancelResponse cancelResult = (PaySelectSeatCancelResponse) JsonUtil.jsonToBean(result, PaySelectSeatCancelResponse.class);
                if ("1001".equals(cancelResult.getResultCode())) {
                    response.setResultCode(WSEnum.SUCCESS.getResultCode());
                    response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    log.info("{}_值机选座，返回取消付费选座成功信息:{},时间：{}", uuid, JsonUtil.objectToJson(response), DateUtils.getCurrentTimeStr());
                } else if ("83001".equals(cancelResult.getResultCode())) {
                    response.setResultCode(WSEnum.REFUND_PAY_SEAT_TIME_LIMIT.getResultCode());
                    response.setErrorInfo(WSEnum.REFUND_PAY_SEAT_TIME_LIMIT.getResultInfo());
                    log.info("{}_值机选座，返回取消付费选座失败信息:{},时间：{}", uuid, JsonUtil.objectToJson(response), DateUtils.getCurrentTimeStr());
                    return response;
                } else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setErrorInfo(cancelResult.getErrorInfo());
                    log.info("{}_值机选座，返回取消付费选座失败信息:{},时间：{}", uuid, JsonUtil.objectToJson(response), DateUtils.getCurrentTimeStr());
                    return response;
                }
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("网络超时，请稍后再试");
                return response;
            }
        } catch (Exception e) {
            log.error("取消选座Error:" + e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("网络超时，请稍后再试");
            return response;
        }
        return response;
    }

    /**
     * 取消值机
     */
    @ApiOperation(value = "取消值机", notes = "取消值机")
    @RequestMapping(value = "/cancelCheckIn", method = RequestMethod.POST)
    public CheckInCancelResponse cancelCheckIn(@RequestBody CheckInCancelRequest conCheckInInfo, HttpServletRequest request) {
        CheckInCancelResponse resp = new CheckInCancelResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，取消值机request:{},时间：{}", uuid, JsonUtil.objectToJson(conCheckInInfo), DateUtils.getCurrentTimeStr());
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(conCheckInInfo.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = conCheckInInfo.getClientIp();
            if (!this.checkToken(conCheckInInfo.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("您的访问出意外啦！");
                return resp;
            }
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInCancelRequest>> violations = validator.validate(conCheckInInfo);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        if (conCheckInInfo.getFfpId().equals(0L)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("参数错误");
            return resp;
        }
        //控制未登录和登陆后限制查询次数的差异
        Map<String, String> mapResult = checkInVisitLimit(conCheckInInfo, clientIp, conCheckInInfo.getFfpId(), conCheckInInfo.getFfpCardNo(), conCheckInInfo.getLoginKeyInfo(), conCheckInInfo.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(mapResult.get("code"))) {
            resp.setResultCode(mapResult.get("code"));
            resp.setErrorInfo(mapResult.get("message"));
            return resp;
        }
        //2020-10-28 判断取消值机增加验证码输入是否正确 加上版本控制
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        if ((ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= 62100)
                || (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= NumberUtils.toInt(handConfig.getMwebOnlineVer()))
                || (ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= NumberUtils.toInt(handConfig.getMiniOnlineVer()))) {
            if (StringUtils.isBlank(conCheckInInfo.getCellPhone())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("航班不支持在线取消值机，有问题请联系95520");
                return resp;
            }
            if (StringUtils.isBlank(conCheckInInfo.getVerifyCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("取消值机验证码不能为空");
                return resp;
            }
            if (!checkVeriCode("SMS:" + conCheckInInfo.getCellPhone() + SourceType.CANCEL_CHECKIN_SOURCE.getValue(), conCheckInInfo.getVerifyCode())) {
                String errorInfo = "验证码错误或已经失效！";
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(errorInfo);
                return resp;
            } else {
                this.clearDayVisit("", conCheckInInfo.getCellPhone(), SourceType.CANCEL_CHECKIN_SOURCE.getValue(), "");//验证成功，清除账号限制
            }
        }
        //取消值机参数封装
        BaseRequestDTO<CancelPsrRequestDTO> baseRequestDTO = new BaseRequestDTO<CancelPsrRequestDTO>();
        baseRequestDTO.setIp(clientIp);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequestDTO.setChannelCode(getChannelInfo(conCheckInInfo.getChannelCode(), "50"));
        baseRequestDTO.setUserNo(String.valueOf(conCheckInInfo.getFfpId()));
        CancelPsrRequestDTO cancelPsrRequestDTO = new CancelPsrRequestDTO();
        BeanUtils.copyNotNullProperties(conCheckInInfo, cancelPsrRequestDTO);
        cancelPsrRequestDTO.setCertType(getCertTypeByCertNo(conCheckInInfo.getCertNo()));
        baseRequestDTO.setRequest(cancelPsrRequestDTO);
        String result;
        try {
            result = this.invokePost(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_CANCEL_PSR, READ_TIMEOUT, CONNECT_TIMEOUT);
            BaseResultDTO res = (BaseResultDTO) JsonUtil.jsonToBean(result, BaseResultDTO.class);
            if (null != res) {
                if ("1001".equals(res.getResultCode())) {
                    //值机成功清除SYPR缓存信息
                    String key = "CHECKIN_SYPR:" + conCheckInInfo.getDepAirportCode()
                            + conCheckInInfo.getArrAirportCode()
                            + conCheckInInfo.getFlightDate()
                            + ":" + conCheckInInfo.getCertNo().replace("-", "");
                    apiRedisService.removeData(key);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());

                    log.info("{}_值机选座，返回取消值机成功信息:{},时间：{}", uuid, JsonUtil.objectToJson(resp), DateUtils.getCurrentTimeStr());
                } else if ("31004".equals(res.getResultCode())) {
                    resp.setResultCode(WSEnum.CHECKIN_CANCEL_COUNT_LIMIT.getResultCode());
                    resp.setErrorInfo(WSEnum.CHECKIN_CANCEL_COUNT_LIMIT.getResultInfo());
                    log.info("{}_值机选座，返回取消值机失败信息:{},时间：{}", uuid, JsonUtil.objectToJson(resp), DateUtils.getCurrentTimeStr());
                    return resp;
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(res.getErrorMsg());
                    log.info("{}_值机选座，返回取消值机失败信息:{},时间：{}", uuid, JsonUtil.objectToJson(resp), DateUtils.getCurrentTimeStr());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("网络出错了，请重新提交");
                return resp;
            }
        } catch (Exception e) {
            log.error("取消值机出错：" + e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("取消值机出错");
            return resp;
        }
        return resp;
    }

    public boolean checkVeriCode(String key, String code) {
        String veryCodeCacheStr = apiRedisService.getData(key);
        log.info("取消值机 key：" + key);
        log.info("取消值机验证码：" + veryCodeCacheStr);
        return !(StringUtils.isBlank(code) || StringUtils.isBlank(veryCodeCacheStr) || !code.equals(veryCodeCacheStr));
    }


    /**
     * 电子登机数据
     *
     * @param boardingPassRequest
     * @param request
     * @return
     */
    @ApiOperation(value = "获取航信通数据", notes = "获取航信通数据")
    @RequestMapping(value = "/getBoardingPass", method = RequestMethod.POST)
    public BoardingPassResponse getBoardingPass(@RequestBody CheckInBoardingPassRequest boardingPassRequest, HttpServletRequest request) {
        BoardingPassResponse response = new BoardingPassResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，电子登机数据request:{}，时间：{}", uuid, JsonUtil.objectToJson(boardingPassRequest), DateUtils.getCurrentTimeStr());
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInBoardingPassRequest>> violations = validator.validate(boardingPassRequest);
        if (null != violations && violations.size() > 0) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(boardingPassRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = boardingPassRequest.getClientIp();
            if (!this.checkToken(boardingPassRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo("您的访问出意外啦！");
                return response;
            }
        }
        if (0 != boardingPassRequest.getFfpId() && !StringUtil.isNullOrEmpty(boardingPassRequest.getLoginKeyInfo())) {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(String.valueOf(boardingPassRequest.getFfpId()), boardingPassRequest.getLoginKeyInfo(), boardingPassRequest.getChannelCode());
            if (!flag) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return response;
            }
        }

        BaseRequestDTO<GetBoardingPassRequestDTO> baseRequest = new BaseRequestDTO<GetBoardingPassRequestDTO>();
        GetBoardingPassRequestDTO getBoardingPassRequest = new GetBoardingPassRequestDTO();
        BeanUtils.copyNotNullProperties(boardingPassRequest, getBoardingPassRequest);
        getBoardingPassRequest.setTicketStatus("CHECKED IN");
        baseRequest.setRequest(getBoardingPassRequest);
        baseRequest.setIp(clientIp);
        baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequest.setChannelCode(getChannelInfo(boardingPassRequest.getChannelCode(), "50"));
        baseRequest.setUserNo(String.valueOf(boardingPassRequest.getFfpId()));
        String result;
        try {
            log.debug("获取电子登机数据request:" + JsonUtil.objectToJson(baseRequest));
            result = this.invokePost(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.GET_BOARDING_PASS, READ_TIMEOUT, CONNECT_TIMEOUT);
            log.debug("获取电子登机数据response:" + result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("查询登机牌出错");
            return response;
        }
        BaseResultDTO<GetBoardingPassResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class, GetBoardingPassResponseDTO.class);
        if (null != res && ("1001").equals(res.getResultCode())) {
            BeanUtils.copyNotNullProperties(res.getResult(), response);
            UMECheckInTravelInfo travelInfo = response.getCheckinTravelInfo();
            //过滤机场显示
            String regex = "[国际]*机场";
            travelInfo.setDeptAirportName(travelInfo.getDeptAirportName().replaceAll(regex, ""));
            travelInfo.setDestAirportName(travelInfo.getDestAirportName().replaceAll(regex, ""));
            response.setCheckinTravelInfo(travelInfo);
            //redis取最新一次检验章状态
            String statusImgUrl = apiRedisService.getData(LAST_UPDATE_STATUS_IMG_URL + ":" + boardingPassRequest.getTktNum() + boardingPassRequest.getDeptCode() + boardingPassRequest.getDestCode());
            response.setLastUpdatePassStatusUrl(statusImgUrl);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            log.info("{}_值机选座，返回电子登机数据信息:{}，时间：{}", uuid, JsonUtil.objectToJson(response), DateUtils.getCurrentTimeStr());
            return response;
        } else {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            String errorInfo = null == res || StringUtils.isBlank(res.getErrorMsg()) || !res.getErrorMsg().contains("对不起，该行程不支持电子登机牌。") ? "获取电子登机数据失败" : "对不起，该行程不支持电子登机牌。";
            response.setErrorInfo(errorInfo);
            return response;
        }

    }

    /**
     * 电子登机验讫
     */
    @ApiOperation(value = "获取航信通验讫章", notes = "获取航信通验讫章")
    @RequestMapping(value = "/getBoardingPassStatus", method = RequestMethod.POST)
    public BoardingPassStatusResponse getBoardingPassStatus(@RequestBody CheckInBoardingStatusRequest boardingPassStatusRequest, HttpServletRequest request) {
        BoardingPassStatusResponse response = new BoardingPassStatusResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，获取航信通验讫章:{}，时间：{}", uuid, JsonUtil.objectToJson(boardingPassStatusRequest), DateUtils.getCurrentTimeStr());
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInBoardingStatusRequest>> violations = validator.validate(boardingPassStatusRequest);
        if (null != violations && violations.size() > 0) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(boardingPassStatusRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = boardingPassStatusRequest.getClientIp();
            if (!this.checkToken(boardingPassStatusRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo("您的访问出意外啦！");
                return response;
            }
        }
        if (0 != boardingPassStatusRequest.getFfpId() && !StringUtil.isNullOrEmpty(boardingPassStatusRequest.getLoginKeyInfo())) {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(String.valueOf(boardingPassStatusRequest.getFfpId()), boardingPassStatusRequest.getLoginKeyInfo(), boardingPassStatusRequest.getChannelCode());
            if (!flag) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return response;
            }
        }
        BaseRequestDTO<GetBoardingPassStatusRequestDTO> baseRequest = new BaseRequestDTO<GetBoardingPassStatusRequestDTO>();
        GetBoardingPassStatusRequestDTO getBoardingPassStatusRequest = new GetBoardingPassStatusRequestDTO();
        BeanUtils.copyNotNullProperties(boardingPassStatusRequest, getBoardingPassStatusRequest);
        baseRequest.setRequest(getBoardingPassStatusRequest);
        baseRequest.setIp(clientIp);
        baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        //渠道编码
        baseRequest.setChannelCode(getChannelInfo(boardingPassStatusRequest.getChannelCode(), "50"));
        baseRequest.setUserNo(String.valueOf(boardingPassStatusRequest.getFfpId()));
        String result;
        try {
            result = this.invokePost(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.GET_BOARDING_PASS_STATUS, READ_TIMEOUT, CONNECT_TIMEOUT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_QUERY_ERROR.getResultInfo());
            return response;
        }
        BaseResultDTO<GetBoardingPassStatusResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class, GetBoardingPassStatusResponseDTO.class);
        if (null != res && ("1001").equals(res.getResultCode())) {
            //最新一次检验章地址写入缓存
            apiRedisService.replaceData(LAST_UPDATE_STATUS_IMG_URL + ":" + boardingPassStatusRequest.getTktNum() +
                    boardingPassStatusRequest.getDeptCode() + boardingPassStatusRequest.getDestCode(), res.getResult().getStatusImgUrl(), 24 * 60 * 60L);
            BeanUtils.copyNotNullProperties(res.getResult(), response);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            log.info("{}_值机选座，获取航信通验讫章:{}，时间：{}", uuid, JsonUtil.objectToJson(response), DateUtils.getCurrentTimeStr());
            return response;
        } else if (null != res && ("31009").equals(res.getResultCode())) {
            response.setResultCode(WSEnum.AIRPORT_BOARDING_PASS_NOT_SUPPORT.getResultCode());
            response.setErrorInfo(WSEnum.AIRPORT_BOARDING_PASS_NOT_SUPPORT.getResultInfo());
            return response;
        } else {
            response.setResultCode(WSEnum.ERROR_CON_CHECK_NULL.getResultCode());
            if (null != res && StringUtils.isNotBlank(res.getErrorMsg())) {
                response.setErrorInfo(res.getErrorMsg());
            } else {
                response.setErrorInfo(WSEnum.ERROR.getResultInfo());
            }
            return response;
        }

    }

    /**
     * 保存常旅客卡号
     *
     * @param saveFrequestPsgCardInfoRequest
     * @param request
     * @return
     */
    @ApiOperation(value = "保存常旅客卡号", notes = "保存常旅客卡号")
    @RequestMapping(value = "/saveFrequestPsgCardInfo", method = RequestMethod.POST)
    public SaveFrequentPsgCardInfoResponse saveFrequestPsgCardInfo(@RequestBody SaveFrequestPsgCardInfoRequest saveFrequestPsgCardInfoRequest, HttpServletRequest request) {
        SaveFrequentPsgCardInfoResponse saveFrequentPsgCardInfoResponse = new SaveFrequentPsgCardInfoResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，保存常旅客卡号request:{}，时间：{}", uuid, JsonUtil.objectToJson(saveFrequestPsgCardInfoRequest), DateUtils.getCurrentTimeStr());
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<SaveFrequestPsgCardInfoRequest>> violations = validator.validate(saveFrequestPsgCardInfoRequest);
        if (null != violations && violations.size() > 0) {
            saveFrequentPsgCardInfoResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            saveFrequentPsgCardInfoResponse.setErrorInfo(violations.iterator().next().getMessage());
            return saveFrequentPsgCardInfoResponse;
        }
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(saveFrequestPsgCardInfoRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = saveFrequestPsgCardInfoRequest.getClientIp();
            if (!this.checkToken(saveFrequestPsgCardInfoRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                saveFrequentPsgCardInfoResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                saveFrequentPsgCardInfoResponse.setErrorInfo("您的访问出意外啦！");
                return saveFrequentPsgCardInfoResponse;
            }
        }
        if (0 != saveFrequestPsgCardInfoRequest.getFfpId() && !StringUtil.isNullOrEmpty(saveFrequestPsgCardInfoRequest.getLoginKeyInfo())) {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(String.valueOf(saveFrequestPsgCardInfoRequest.getFfpId()), saveFrequestPsgCardInfoRequest.getLoginKeyInfo(), saveFrequestPsgCardInfoRequest.getChannelCode());
            if (!flag) {
                saveFrequentPsgCardInfoResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                saveFrequentPsgCardInfoResponse.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return saveFrequentPsgCardInfoResponse;
            }
        }
        try {
            BaseResultDTO baseResult = new BaseResultDTO();
            BaseRequestDTO baseRequest = new BaseRequestDTO();
            baseRequest.setIp(clientIp);
            baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
            //渠道编码
            baseRequest.setChannelCode(getChannelInfo(saveFrequestPsgCardInfoRequest.getChannelCode(), "50"));
            baseRequest.setUserNo(String.valueOf(saveFrequestPsgCardInfoRequest.getFfpId()));
            if (saveFrequestPsgCardInfoRequest.getCheckInOrSelectSeat().equals("CHECKIN")) {
                //值机添加常旅客卡号信息
                ModifyCheckInFrequentFlyerRequestDTO dtoModifyCheckInInfo = new ModifyCheckInFrequentFlyerRequestDTO();
                FlightTicketSimpleDTO flightTicketSimple = new FlightTicketSimpleDTO();
                BeanUtils.copyNotNullProperties(saveFrequestPsgCardInfoRequest, flightTicketSimple);
                BeanUtils.copyNotNullProperties(saveFrequestPsgCardInfoRequest, dtoModifyCheckInInfo);
                dtoModifyCheckInInfo.setCheckInId(Long.valueOf(saveFrequestPsgCardInfoRequest.getId()));
                dtoModifyCheckInInfo.setFlightTicket(flightTicketSimple);
                baseRequest.setRequest(dtoModifyCheckInInfo);
                String result = this.invokePost(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.MODIFY_CHECKIN_FREQUENT_INFO, READ_TIMEOUT, CONNECT_TIMEOUT);
                baseResult = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class);
            } else if (saveFrequestPsgCardInfoRequest.getCheckInOrSelectSeat().equals("SELECTSEAT")) {
                //选座添加常旅客卡号信息
                PtSeatProductModifyFlyerRequestDto ptSeatProductModifyFlyerRequestDto1 = CussObjectConvert.convertModifySeatFlyerBean(saveFrequestPsgCardInfoRequest, clientIp, getChannelInfo(saveFrequestPsgCardInfoRequest.getChannelCode(), "10"));
                String result = this.invokePost(ptSeatProductModifyFlyerRequestDto1, HandlerConstants.URL_FARE_API + HandlerConstants.MODIFY_SEAT_FLYER, READ_TIMEOUT, CONNECT_TIMEOUT);
                PtSeatProductModifyFlyerResponseDto ptSeatProductModifyFlyerResponseDto = JsonMapper.buildNonNullMapper().fromJson(result, PtSeatProductModifyFlyerResponseDto.class);
                baseResult.setResultCode(ptSeatProductModifyFlyerResponseDto.getResultCode());
                baseResult.setErrorMsg(ptSeatProductModifyFlyerResponseDto.getErrorInfo());
            }
            if ("1001".equals(baseResult.getResultCode())) {
                saveFrequentPsgCardInfoResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                saveFrequentPsgCardInfoResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                log.info("{}_值机选座，返回保存常旅客卡号信息:{}，时间：{}", uuid, JsonUtil.objectToJson(saveFrequentPsgCardInfoResponse), DateUtils.getCurrentTimeStr());
            } else {
                saveFrequentPsgCardInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
                saveFrequentPsgCardInfoResponse.setErrorInfo(baseResult.getErrorMsg());
                log.info("{}_值机选座，返回保存常旅客卡号信息:{}，时间：{}", uuid, JsonUtil.objectToJson(saveFrequentPsgCardInfoResponse), DateUtils.getCurrentTimeStr());
                return saveFrequentPsgCardInfoResponse;
            }
        } catch (Exception e) {
            log.error("保存常旅客卡号ERROR:" + e.getMessage(), e);
            saveFrequentPsgCardInfoResponse.setResultCode(WSEnum.ERROR.getResultCode());
            saveFrequentPsgCardInfoResponse.setErrorInfo("保存常旅客卡号出错");
            return saveFrequentPsgCardInfoResponse;
        }
        return saveFrequentPsgCardInfoResponse;
    }

    /**
     * 添加同行人
     *
     * @param addPeerRequest
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "添加同行人", notes = "添加同行人")
    @RequestMapping(value = "/addPeer", method = RequestMethod.POST)
    public AddPeerResponse addPeer(@RequestBody @Validated AddPeerRequest addPeerRequest, BindingResult bindingResult, HttpServletRequest request) {
        AddPeerResponse addPeerResponse = new AddPeerResponse();
        if (bindingResult.hasErrors()) {
            addPeerResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            addPeerResponse.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return addPeerResponse;
        }
        String clientIp = this.getClientIP(request);
        //一次值机选座最多添加四名同行人
        if (null != addPeerRequest.getPeerInfos() && addPeerRequest.getPeerInfos().size() >= 10) {
            addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
            addPeerResponse.setErrorInfo("对不起，一次值机最多只能添加9名乘机人！");
            return addPeerResponse;
        }
        AddPeerParam addPeerParam = new AddPeerParam();
        addPeerParam.setFlightNo(addPeerRequest.getFlightNo());
        addPeerParam.setFlightDate(addPeerRequest.getFlightDate());
        addPeerParam.setDepAirportCode(addPeerRequest.getDepAirportCode());
        addPeerParam.setArrAirportCode(addPeerRequest.getArrAirportCode());
        addPeerParam.setPeerCertNo(addPeerRequest.getPeerCertNo());
        addPeerParam.setPeerName(addPeerRequest.getPeerName());
        addPeerParam.setPeerInfoList(addPeerRequest.getPeerInfos());
        SeatSelectInfoResult seatSelectInfoResult = checkInSeatService.addPeer(addPeerParam, addPeerRequest.getChannelCode(), clientIp);
        addPeerResponse.setPsrName(seatSelectInfoResult.getPsrName());
        addPeerResponse.setEtCode(seatSelectInfoResult.getTicketNo());
        addPeerResponse.setPnrNo(seatSelectInfoResult.getPnrNo());
        addPeerResponse.setSegNo(seatSelectInfoResult.getSegNo());
        addPeerResponse.setCabin(seatSelectInfoResult.getCabin());
        addPeerResponse.setAsrSeatNo(seatSelectInfoResult.getSeatNo());
        addPeerResponse.setFlightAge(seatSelectInfoResult.getFlightAge());
        addPeerResponse.setSeatMemberCode(seatSelectInfoResult.getSeatMemberCode());
        //获取城市信息
        AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(addPeerRequest.getDepAirportCode(), DateUtils.toDate(addPeerRequest.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN));
        AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(addPeerRequest.getDepAirportCode(), DateUtils.toDate(addPeerRequest.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN));
        addPeerResponse.setDepCityName(depAirPortInfo.getCityName());
        addPeerResponse.setArrCityName(arrAirportInfo.getCityName());
        addPeerResponse.setDepCityCode(depAirPortInfo.getCityCode());
        addPeerResponse.setArrCityCode(arrAirportInfo.getCityCode());
        addPeerResponse.setDepAirportName(depAirPortInfo.getAirPortName());
        addPeerResponse.setArrAirportName(arrAirportInfo.getAirPortName());
        addPeerResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
        addPeerResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        return addPeerResponse;
    }
    /* 添加同行人校验逻辑统一由cuss处理
    private void no() {

        //根据证件号和姓名查客票信息
        List<FlightTourDTO> peerFlightTours = new ArrayList<>();
        String result;
        try {
            BaseRequestDTO<QueryTourRequestDTO> baseRequest = new BaseRequestDTO<QueryTourRequestDTO>();
            baseRequest.setIp(clientIp);
            baseRequest.setChannelCode(getChannelInfo(addPeerRequest.getChannelCode(), "50"));
            baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
            QueryTourRequestDTO queryTourRequest = new QueryTourRequestDTO();
            queryTourRequest.setPassengerName(addPeerRequest.getPeerName());
            queryTourRequest.setCertificateNo(addPeerRequest.getPeerCertNo());
            //当前为增加同行人
            queryTourRequest.setTogetherPsr(true);
            queryTourRequest.setWithOrder(false);
            baseRequest.setRequest(queryTourRequest);
            //调接口
            result = this.invokePost(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.QUERY_TOUR, READ_TIMEOUT, CONNECT_TIMEOUT);
            BaseResultDTO<QueryTourResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class, QueryTourResponseDTO.class);
            if (null != res && res.getResultCode().equals("1001")) {
                if (null != res.getResult() && res.getResult().getTours().size() > 0) {
                    peerFlightTours = res.getResult().getTours();
                } else {
                    addPeerResponse.setResultCode(WSEnum.NO_DATA.getResultCode());
                    addPeerResponse.setErrorInfo("未查到客票信息");
                    return addPeerResponse;
                }
            } else {
                addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
                addPeerResponse.setErrorInfo(null != res ? res.getErrorMsg() : "未查到客票信息");
                return addPeerResponse;
            }
        } catch (Exception e) {
            log.error("查询同行人客票信息出错 ERROR:" + e.getMessage(), e);
            addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
            addPeerResponse.setErrorInfo("查询同行人客票信息出错");
            return addPeerResponse;
        }

        int validCnt = 0;
        if (!addPeerRequest.getCheckInSeatStatus().equals(CheckInSeatStatus.CHECK_IN_OPEN.getCode()) && !addPeerRequest.getCheckInSeatStatus().equals(CheckInSeatStatus.SELECT_OPEN.getCode())) {
            addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
            addPeerResponse.setErrorInfo("值机选座状态出错");
            return addPeerResponse;
        }
        //值机下添加同行人
        for (FlightTourDTO flightTour : peerFlightTours) {

            // 20190529 过滤掉pnr中其他人
            if (null == flightTour.getPsrEnName()) {
                flightTour.setPsrEnName("");
            }
            if (null == flightTour.getPsrName()) {
                flightTour.setPsrName("");
            }
            //1.判断是否有效乘机人
            if (addPeerRequest.getFlightNo().equals(flightTour.getFlightNo()) &&
                    addPeerRequest.getFlightDate().equals(flightTour.getFlightDate()) &&
                    addPeerRequest.getDepAirportCode().equals(flightTour.getDepAirport()) &&
                    addPeerRequest.getArrAirportCode().equals(flightTour.getArrAirport()) &&
                    (patternName(addPeerRequest.getPeerName(), flightTour.getPsrName()) ||
                            patternName(addPeerRequest.getPeerName(), flightTour.getPsrEnName()))
            ) {
                //2.不能添加特殊旅客特殊旅客
                FlightTourPsgInfo flightTourPsgInfo = new FlightTourPsgInfo();
                CussObjectConvert.covertSpecialPsgInfo(flightTourPsgInfo, flightTour);

                if (!StringUtil.isNullOrEmpty(flightTour.getSpecialPassenger())) {
                    addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    addPeerResponse.setErrorInfo(flightTourPsgInfo.getSpecialPsgTip());
                    log.info("{}_票号增加同行人，返回添加同行人信息:{}，时间：{}", uuid, JsonUtil.objectToJson(addPeerResponse), DateUtils.getCurrentTimeStr());
                    return addPeerResponse;
                }
                //3.有效乘机人，需判断是否已选座
                if (addPeerRequest.getCheckInSeatStatus().equals(CheckInSeatStatus.CHECK_IN_OPEN.getCode()) && !StringUtil.isNullOrEmpty(flightTour.getCheckInSeatNo())) {
                    addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    addPeerResponse.setErrorInfo(addPeerRequest.getPeerName() + "已选座，座位号:" + flightTour.getCheckInSeatNo());
                    log.info("{}_值机选座，返回添加同行人信息:{}，时间：{}", uuid, JsonUtil.objectToJson(addPeerResponse), DateUtils.getCurrentTimeStr());
                    return addPeerResponse;
                }
                if (addPeerRequest.getCheckInSeatStatus().equals(CheckInSeatStatus.SELECT_OPEN.getCode()) && !StringUtil.isNullOrEmpty(flightTour.getAsrSeatNo())) {
                    addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    addPeerResponse.setErrorInfo(addPeerRequest.getPeerName() + "已选座，座位号:" + flightTour.getAsrSeatNo());
                    log.info("{}_值机选座，返回添加同行人信息:{}，时间：{}", uuid, JsonUtil.objectToJson(addPeerResponse), DateUtils.getCurrentTimeStr());
                    return addPeerResponse;
                }
                //4.过滤重复添加同行人
                for (PeerInfo peerInfo : addPeerRequest.getPeerInfos()) {
                    if (peerInfo.getTktNo().equals(flightTour.getEtCode()) && peerInfo.getPeerName().equalsIgnoreCase(flightTour.getPsrName())) {
                        addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
                        addPeerResponse.setErrorInfo("无需重复添加同行人");
                        log.info("{}_值机选座，返回添加同行人信息:{}，时间：{}", uuid, JsonUtil.objectToJson(addPeerResponse), DateUtils.getCurrentTimeStr());
                        return addPeerResponse;
                    }
                }
                //5.不能添加非同等舱位同行人
                String addPeerCabin = ControllerUtil.getHighestClassCabin(flightTour.getCabin());
                String peerCabin = ControllerUtil.getHighestClassCabin(addPeerRequest.getPeerInfos().get(0).getCabin());
                if (!addPeerCabin.equalsIgnoreCase(peerCabin)) {
                    addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
                    addPeerResponse.setErrorInfo("该乘机人不能在此舱位选座位");
                    log.info("{}_值机选座，返回添加同行人信息:{}，时间：{}", uuid, JsonUtil.objectToJson(addPeerResponse), DateUtils.getCurrentTimeStr());
                    return addPeerResponse;
                }
                //6.验证航班初始化
                Map<String, Object> initMap = checkInSeatService.verifyCurrentCheckInSeatStatus(addPeerRequest.getFlightNo(), addPeerRequest.getFlightDate(), addPeerRequest.getDepAirportCode(), addPeerRequest.getChannelCode(), clientIp);
                if (initMap.get("code").equals("ok") && (boolean) initMap.get("initFlag")) {
                    //5.值机时 检查婴儿携带人不能加入同行人
                    BaseRequestDTO<SyprRequestDTO> baseRequestDTO = new BaseRequestDTO<SyprRequestDTO>();
                    baseRequestDTO.setIp(clientIp);
                    baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
                    baseRequestDTO.setChannelCode(getChannelInfo(addPeerRequest.getChannelCode(), "50"));
                    baseRequestDTO.setUserNo("");
                    SyprRequestDTO syprRequestDTO = new SyprRequestDTO();
                    BeanUtils.copyNotNullProperties(addPeerRequest, syprRequestDTO);
                    syprRequestDTO.setEtCode(flightTour.getEtCode());
                    baseRequestDTO.setRequest(syprRequestDTO);
                    String syprResult;
                    try {
                        syprResult = this.invokePost(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_DO_SYPR, READ_TIMEOUT, CONNECT_TIMEOUT);
                        BaseResultDTO<SyprResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(syprResult, BaseResultDTO.class, SyprResponseDTO.class);
                        if (!"1001".equals(res.getResultCode())) {
                            addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
                            addPeerResponse.setErrorInfo(res.getErrorMsg());
                            return addPeerResponse;
                        }
                    } catch (Exception e) {
                        log.error("添加同行人doSYPR错误：" + e.getMessage(), e);
                    }
                }

                //6.通过校验
                BeanUtils.copyNotNullProperties(flightTour, addPeerResponse);
                // 获取旅客会员信息
                MemberRequest memberRequest = new MemberRequest();
                memberRequest.setTicketNumber(flightTour.getEtCode());
                MemberResponse memberResponse = checkInSeatService.getMemberInfo(memberRequest, addPeerRequest.getChannelCode(), clientIp);
                if (null != memberResponse){
                    Integer flightAge = DateUtils.getDateAge(DateUtils.toDate(flightTour.getFlightDate()), DateUtils.toDate(memberResponse.getTravellerBirthdate()));
                    addPeerResponse.setFlightAge(flightAge);
                    addPeerResponse.setSeatMemberCode(memberResponse.getSeatMemberCode());
                    addPeerResponse.setMemberLevelCode(memberResponse.getMemberLevelCode());
                }
                //获取城市信息
                AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(flightTour.getDepAirport(),DateUtils.toDate(flightTour.getFlightDate(),DateUtils.YYYY_MM_DD_PATTERN));
                AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(flightTour.getArrAirport(),DateUtils.toDate(flightTour.getFlightDate(),DateUtils.YYYY_MM_DD_PATTERN));
                addPeerResponse.setDepCityName(depAirPortInfo.getCityName());
                addPeerResponse.setArrCityName(arrAirportInfo.getCityName());
                addPeerResponse.setDepCityCode(depAirPortInfo.getCityCode());
                addPeerResponse.setArrCityCode(arrAirportInfo.getCityCode());
                addPeerResponse.setDepAirportName(depAirPortInfo.getAirPortName());
                addPeerResponse.setArrAirportName(arrAirportInfo.getAirPortName());
                addPeerResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                addPeerResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                log.info("{}_值机选座，返回添加同行人信息:{}，时间：{}", uuid, JsonUtil.objectToJson(addPeerResponse), DateUtils.getCurrentTimeStr());
                break;
            } else {
                validCnt++;
            }
        }
        //无有效乘机人
        if (validCnt == peerFlightTours.size()) {
            addPeerResponse.setResultCode(WSEnum.ERROR.getResultCode());
            addPeerResponse.setErrorInfo("无法添加乘机人，请确认信息是否输入正确");
            log.info("{}_值机选座，返回添加同行人信息:{}，时间：{}", uuid, JsonUtil.objectToJson(addPeerResponse), DateUtils.getCurrentTimeStr());
            return addPeerResponse;
        }
        return addPeerResponse;
    }*/

    /**
     * 查询我的登机牌
     *
     * @param checkInBoardingRequest
     * @param request
     * @return
     */
    @ApiOperation(value = "获取旅客值机数据", notes = "获取旅客值机数据")
    @RequestMapping(value = "/queryMyCheckInInfo", method = RequestMethod.POST)
    public CheckInBoardingResponse queryMyCheckInInfo(@RequestBody CheckInBoardingRequest checkInBoardingRequest, HttpServletRequest request) {
        CheckInBoardingResponse checkInBoardingResponse = new CheckInBoardingResponse();
        List<CheckInReservePsgInfo> checkInReservePsgInfos = new ArrayList<>();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，获取旅客值机数据request:{}，时间：{}", uuid, JsonUtil.objectToJson(checkInBoardingRequest), DateUtils.getCurrentTimeStr());
        if (StringUtils.isBlank(checkInBoardingRequest.getVerifyCode())) {
            checkInBoardingResponse.setResultCode(WSEnum.NEED_SEND_VERIFY_CODE.getResultCode());
            checkInBoardingResponse.setErrorInfo(WSEnum.NEED_SEND_VERIFY_CODE.getResultInfo());
            return checkInBoardingResponse;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInBoardingRequest>> violations = validator.validate(checkInBoardingRequest);
        if (null != violations && violations.size() > 0) {
            checkInBoardingResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            checkInBoardingResponse.setErrorInfo(violations.iterator().next().getMessage());
            return checkInBoardingResponse;
        }
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(checkInBoardingRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = checkInBoardingRequest.getClientIp();
            if (!this.checkToken(checkInBoardingRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                checkInBoardingResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                checkInBoardingResponse.setErrorInfo("您的访问出意外啦！");
                return checkInBoardingResponse;
            }
        }
        if (0 != checkInBoardingRequest.getFfpId() && !StringUtil.isNullOrEmpty(checkInBoardingRequest.getLoginKeyInfo())) {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(String.valueOf(checkInBoardingRequest.getFfpId()), checkInBoardingRequest.getLoginKeyInfo(), checkInBoardingRequest.getChannelCode());
            if (!flag) {
                checkInBoardingResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                checkInBoardingResponse.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return checkInBoardingResponse;
            }
        } else {
            // 未登录验证签名
            if (StringUtils.isAnyBlank(checkInBoardingRequest.getExpiresTime(), checkInBoardingRequest.getSign())) {
                checkInBoardingResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                checkInBoardingResponse.setErrorInfo("参数校验不通过");
                return checkInBoardingResponse;
            }
            if (System.currentTimeMillis() / 1000 > Long.parseLong(checkInBoardingRequest.getExpiresTime())) {
                checkInBoardingResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                checkInBoardingResponse.setErrorInfo("链接已失效");
                return checkInBoardingResponse;
            }
            CheckInSmsParam checkInSmsParam = new CheckInSmsParam();
            org.springframework.beans.BeanUtils.copyProperties(checkInBoardingRequest, checkInSmsParam);
            if (!Md5Util.checkMd5Hex(HandlerConstants.CUSS_SALTY_SMS, checkInSmsParam, checkInBoardingRequest.getSign())) {
                checkInBoardingResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                checkInBoardingResponse.setErrorInfo("签名验证不通过");
                return checkInBoardingResponse;
            }
        }
        try {
            // 查询值机信息
            CheckInInfoResponseDTO checkInInfoResponse = getCheckInInfoResponseDTO(checkInBoardingRequest, clientIp);
            if (!checkVeriCode("SMS:" + checkInInfoResponse.getPhone() + SourceType.CHECK_IN_VERIFY_CODE.getValue(), checkInBoardingRequest.getVerifyCode())) {
                String errorInfo = "验证码错误或已经失效！";
                checkInBoardingResponse.setResultCode(WSEnum.ERROR.getResultCode());
                checkInBoardingResponse.setErrorInfo(errorInfo);
                return checkInBoardingResponse;
            } else {
                this.clearDayVisit("", checkInInfoResponse.getPhone(), SourceType.CHECK_IN_VERIFY_CODE.getValue(), "");//验证成功，清除账号限制
            }
            BeanUtils.copyNotNullProperties(checkInInfoResponse, checkInBoardingResponse);
            if (!StringUtil.isNullOrEmpty(checkInBoardingResponse.getDepBaseDateTime()) && checkInBoardingResponse.getDepBaseDateTime().length() > 3
                    && !StringUtil.isNullOrEmpty(checkInBoardingResponse.getArrBaseDateTime()) && checkInBoardingResponse.getArrBaseDateTime().length() > 3
                    && !StringUtil.isNullOrEmpty(checkInBoardingResponse.getDepBaseFlightDate()) && !StringUtil.isNullOrEmpty(checkInBoardingResponse.getArrBaseFlightDate())) {

                String beginDate = checkInBoardingResponse.getDepBaseFlightDate().concat(" ").concat(checkInBoardingResponse.getDepBaseDateTime().substring(0, 2)).concat(":").concat(checkInBoardingResponse.getDepBaseDateTime().substring(2, 4));
                String endDate = checkInBoardingResponse.getArrBaseFlightDate().concat(" ").concat(checkInBoardingResponse.getArrBaseDateTime().substring(0, 2)).concat(":").concat(checkInBoardingResponse.getArrBaseDateTime().substring(2, 4));
                Long flyTime = DateUtils.calDuration(beginDate, String.valueOf(checkInBoardingResponse.getDepBaseDateLocalTimeZone()),
                        endDate, String.valueOf(checkInBoardingResponse.getArrBaseDateLocalTimeZone()));
                checkInBoardingResponse.setTimeLength(flyTime);
                checkInBoardingResponse.setDepBaseDateTime(checkInBoardingResponse.getDepBaseDateTime().substring(0, 2).concat(":").concat(checkInBoardingResponse.getDepBaseDateTime().substring(2, 4)));
                checkInBoardingResponse.setArrBaseDateTime(checkInBoardingResponse.getArrBaseDateTime().substring(0, 2).concat(":").concat(checkInBoardingResponse.getArrBaseDateTime().substring(2, 4)));
            }
            checkInBoardingResponse.setDepAirportCode(checkInInfoResponse.getDepAirport());
            checkInBoardingResponse.setArrAirportCode(checkInInfoResponse.getArrAirport());
            CheckInReservePsgInfo checkInReservePsgInfo = new CheckInReservePsgInfo();
            BeanUtils.copyNotNullProperties(checkInInfoResponse, checkInReservePsgInfo);
            checkInReservePsgInfo.setCouponCode(checkInInfoResponse.getCouponCode());
            //舱位名称转换
            checkInReservePsgInfo.setCabinName(getCabinName(checkInReservePsgInfo.getCabin()));
            if (StringUtil.isNullOrEmpty(checkInReservePsgInfo.getValidationTypes())) {
                checkInReservePsgInfo.setValidationTypes(new ArrayList<>());
            }

            CheckInInfoResponseDTO checkInInfo = checkInInfoResponse;
            // 存在订单号封装订单信息
            if (StringUtils.isNotBlank(checkInInfo.getOrderNo())) {
                SeatOrderInfo orderResult = checkInSeatService.getSeatOrderInfo(checkInBoardingRequest.getChannelCode(), clientIp, checkInInfo.getOrderNo(), checkInInfo.getChannelOrderNo(), checkInInfo.getCouponCode());
                checkInBoardingResponse.setOrderResult(orderResult);
            }
            checkInReservePsgInfos.add(checkInReservePsgInfo);
            checkInBoardingResponse.setCheckInReservePsgInfos(checkInReservePsgInfos);
            AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(checkInBoardingResponse.getDepAirportCode(), DateUtils.toDate(checkInBoardingResponse.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN));
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(checkInBoardingResponse.getArrAirportCode(), DateUtils.toDate(checkInBoardingResponse.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN));
            checkInBoardingResponse.setDepCityName(depAirPortInfo.getCityName());
            checkInBoardingResponse.setArrCityName(arrAirportInfo.getCityName());
            checkInBoardingResponse.setDepAirportName(depAirPortInfo.getAirPortName());
            checkInBoardingResponse.setArrAirportName(arrAirportInfo.getAirPortName());
            //计算飞行时长

            //登机口为????转为待定
            checkInBoardingResponse.setBoardingGateNumber(checkInBoardingResponse.getBoardingGateNumber().contains("?") ? "待定" : checkInBoardingResponse.getBoardingGateNumber());
            //机型名称判定
            checkInBoardingResponse.setPlaneTypeName(ControllerUtil.getPlaneTypeName(checkInBoardingResponse.getPlaneType()));
            checkInBoardingResponse.setPlaneType(ControllerUtil.getNewPlaneType(checkInBoardingResponse.getPlaneType()));
            checkInBoardingResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
            checkInBoardingResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            log.info("{}_值机选座，返回获取旅客值机数据:{}，时间：{}", uuid, JsonUtil.objectToJson(checkInBoardingResponse), DateUtils.getCurrentTimeStr());
        } catch (CommonException ce) {
            checkInBoardingResponse.setResultCode(ce.getResultCode());
            checkInBoardingResponse.setErrorInfo(ce.getErrorMsg());
            return checkInBoardingResponse;
        } catch (Exception e) {
            log.error("查询我的登机牌出错ERROR:" + e.getMessage(), e);
            checkInBoardingResponse.setResultCode(WSEnum.ERROR.getResultCode());
            checkInBoardingResponse.setErrorInfo("查询我的登机牌出错");
            return checkInBoardingResponse;
        }
        return checkInBoardingResponse;
    }

    /**
     * 查询值机记录
     * @param checkInBoardingRequest
     * @param clientIp
     * @return
     */
    private CheckInInfoResponseDTO getCheckInInfoResponseDTO(CheckInBoardingRequest checkInBoardingRequest, String clientIp) {
        BaseRequestDTO<CheckInInfoQueryRequestDTO> baseRequest = new BaseRequestDTO<>();
        baseRequest.setIp(clientIp);
        baseRequest.setChannelCode(getChannelInfo(checkInBoardingRequest.getChannelCode(), "50"));
        baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        CheckInInfoQueryRequestDTO checkInInfoQueryRequest = new CheckInInfoQueryRequestDTO();
        BeanUtils.copyNotNullProperties(checkInBoardingRequest, checkInInfoQueryRequest);
        baseRequest.setRequest(checkInInfoQueryRequest);
        String result = this.invokePost(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.QUERY_CHECKIN_INFO, READ_TIMEOUT, CONNECT_TIMEOUT);
        BaseResultDTO<CheckInInfoResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class, CheckInInfoResponseDTO.class);
        if (null == res || !res.getResultCode().equals("1001")) {
            String message = StringUtils.isNotBlank(res.getErrorMsg()) ? res.getErrorMsg(): WSEnum.ERROR.getResultInfo();
            throw new CommonException(WSEnum.ERROR.getResultCode(), message);
        }
        CheckInInfoResponseDTO checkInInfoResponse = res.getResult();
        if (null == checkInInfoResponse) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "未查询到值机记录,请至原值机渠道查看");
        }
        return checkInInfoResponse;
    }

    /**
     * 获取旅客值机数据发送短信验证码
     * @param checkInBoardingRequest
     * @param request
     * @return
     */
    @ApiOperation(value = "获取旅客值机数据发送短信验证码", notes = "获取旅客值机数据发送短信验证码")
    @RequestMapping(value = "/checkIn/sendVerifyCode", method = RequestMethod.POST)
    public BaseResultDTO<Object> sendVerifyCode(@RequestBody CheckInBoardingRequest checkInBoardingRequest, HttpServletRequest request) {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInBoardingRequest>> violations = validator.validate(checkInBoardingRequest);
        if (null != violations && violations.size() > 0) {
            throw new CommonException(WSEnum.ERROR_REQUEST_PARAMS.getResultCode(), violations.iterator().next().getMessage());
        }
        String clientIp = this.getClientIP(request);
        // 验证签名
        if (StringUtils.isAnyBlank(checkInBoardingRequest.getExpiresTime(), checkInBoardingRequest.getSign())) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "参数校验不通过");
        }
        if (System.currentTimeMillis() / 1000 > Long.parseLong(checkInBoardingRequest.getExpiresTime())) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "链接已失效");
        }
        CheckInSmsParam checkInSmsParam = new CheckInSmsParam();
        org.springframework.beans.BeanUtils.copyProperties(checkInBoardingRequest, checkInSmsParam);
        if (!Md5Util.checkMd5Hex(HandlerConstants.CUSS_SALTY_SMS, checkInSmsParam, checkInBoardingRequest.getSign())) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "签名验证不通过");
        }
        if (StringUtils.isAnyBlank(checkInBoardingRequest.getClient_type(), checkInBoardingRequest.getGeetest_challenge(),
                checkInBoardingRequest.getGeetest_validate(),checkInBoardingRequest.getGeetest_seccode())) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "极验参数不能为空");
        }
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.SEAT_SMS.getGeetestType());
        HashMap<String, String> param = new HashMap<>();
        param.put("user_id", clientIp); //网站用户id  设备号
        param.put("client_type", checkInBoardingRequest.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", clientIp); //传输用户请求验证时所携带的IP
        Geetest geetest = new Geetest(checkInBoardingRequest.getGeetest_challenge(), checkInBoardingRequest.getGeetest_validate(), checkInBoardingRequest.getGeetest_seccode());
        geetestService.validateMd5(gtSdk, geetest, param);
        // 查看值机信息
        CheckInInfoResponseDTO checkInInfoResponse = getCheckInInfoResponseDTO(checkInBoardingRequest, clientIp);
        // 发送短信
        SourceType sourceType = SourceType.CHECK_IN_VERIFY_CODE;
        String sendCode = getChkCode();
        apiRedisService.replaceData("SMS:" + checkInInfoResponse.getPhone() + sourceType.getValue(), sendCode, 300);
        Map<String, String> extras = Maps.newHashMap();
        extras.put("sendCode", sendCode);
        extras.put("purpose", sourceType.getPurpose());
        //此处调整下 有模板号的直接传值
        String templateCode = sourceType.getTemplateCode();
        boolean flag = smsService.sendSms(templateCode, null, checkInInfoResponse.getAreaId(), checkInInfoResponse.getPhone(), extras);
        BaseResultDTO<Object> baseResult = new BaseResultDTO<>();
        if (flag) {
            baseResult.setResultCode(WSEnum.SUCCESS.getResultCode());
            baseResult.setErrorMsg(WSEnum.SUCCESS.getResultInfo());
        } else {
            baseResult.setResultCode(WSEnum.ERROR.getResultCode());
            baseResult.setErrorMsg("短信发送失败");
        }
        return baseResult;
    }

    public String getChkCode() {
        StringBuilder stringBuilder = new StringBuilder("");
        for (int i = 0; i < 6; i++) {
            String rand = String.valueOf(new Random().nextInt(10));
            stringBuilder.append(rand);
        }
        return stringBuilder.toString();
    }

    @ApiOperation(value = "查询旅客值机次数", notes = "查询旅客值机次数")
    @RequestMapping(value = "/queryCheckInCount", method = RequestMethod.POST)
    public QueryCheckInCounrResponse queryCheckInCount(@RequestBody QueryCheckInCountRequest queryCheckInCountRequest, HttpServletRequest request) {
        QueryCheckInCounrResponse queryCheckInCounrResponse = new QueryCheckInCounrResponse();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，查询旅客值机次数request:{},时间：{}", uuid, JsonUtil.objectToJson(queryCheckInCountRequest), DateUtils.getCurrentTimeStr());
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryCheckInCountRequest>> violations = validator.validate(queryCheckInCountRequest);
        if (null != violations && violations.size() > 0) {
            queryCheckInCounrResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            queryCheckInCounrResponse.setErrorInfo(violations.iterator().next().getMessage());
            return queryCheckInCounrResponse;
        }
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(queryCheckInCountRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = queryCheckInCountRequest.getClientIp();
            if (!this.checkToken(queryCheckInCountRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                queryCheckInCounrResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                queryCheckInCounrResponse.setErrorInfo("您的访问出意外啦！");
                return queryCheckInCounrResponse;
            }
        }
        try {
            BaseRequestDTO<CheckInCountQueryRequestDTO> baseRequest = new BaseRequestDTO<CheckInCountQueryRequestDTO>();
            CheckInCountQueryRequestDTO checkInCountQueryRequest = new CheckInCountQueryRequestDTO();
            List<FlightTicketSimpleDTO> flightTicketSimples = new ArrayList<>();
            FlightTicketSimpleDTO flightTicketSimple = null;
            for (CheckInCountInfo checkInCountInfo : queryCheckInCountRequest.getCheckInCountInfos()) {
                flightTicketSimple = new FlightTicketSimpleDTO();
                BeanUtils.copyNotNullProperties(checkInCountInfo, flightTicketSimple);
                BeanUtils.copyNotNullProperties(queryCheckInCountRequest, flightTicketSimple);
                flightTicketSimples.add(flightTicketSimple);
            }
            checkInCountQueryRequest.setTickets(flightTicketSimples);
            baseRequest.setIp(clientIp);
            baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
            baseRequest.setChannelCode(getChannelInfo(queryCheckInCountRequest.getChannelCode(), "50"));
            baseRequest.setUserNo(String.valueOf(queryCheckInCountRequest.getFfpId()));
            baseRequest.setRequest(checkInCountQueryRequest);
            String result = this.invokePost(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.QUERY_CHECKIN_COUNT, READ_TIMEOUT, CONNECT_TIMEOUT);
            BaseResultDTO<CheckInCountQueryResponseDTO> checkInCounts = JsonMapper.buildNonNullMapper().fromJson(result, BaseResultDTO.class, CheckInCountQueryResponseDTO.class);
            if ("1001".equals(checkInCounts.getResultCode()) && null != checkInCounts.getResult()) {
                List<String> checkedInList = new ArrayList<>();
                List<String> checkedInMaxList = new ArrayList<>();
                for (CheckInCountQueryResultDTO checkInCountQueryResult : checkInCounts.getResult().getItems()) {
                    if (checkInCountQueryResult.getCount().intValue() == 3) {
                        checkedInMaxList.add(checkInCountQueryResult.getTicket().getPsrName());
                    } else if (checkInCountQueryResult.getCount().intValue() != 0) {
                        checkedInList.add(checkInCountQueryResult.getTicket().getPsrName());
                    }
                }
                if (null != checkedInList && checkedInList.size() > 0) {
                    queryCheckInCounrResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                    queryCheckInCounrResponse.setErrorInfo(checkedInList.toString() + "已存在选座记录，最多可改座2次，确认是否改座");
                    log.info("{}_值机选座，查询旅客值机次数response:{},时间：{}", uuid, JsonUtil.objectToJson(queryCheckInCounrResponse), DateUtils.getCurrentTimeStr());
                    return queryCheckInCounrResponse;
                }
                if (null != checkedInMaxList && checkedInMaxList.size() > 0) {
                    if (checkedInMaxList.size() == queryCheckInCountRequest.getCheckInCountInfos().size()) {
                        queryCheckInCounrResponse.setResultCode(WSEnum.ERROR.getResultCode());
                        queryCheckInCounrResponse.setErrorInfo(checkedInMaxList.toString() + "已选座过3次，不能再次选座");
                        log.info("{}_值机选座，查询旅客值机次数response:{},时间：{}", uuid, JsonUtil.objectToJson(queryCheckInCounrResponse), DateUtils.getCurrentTimeStr());
                        return queryCheckInCounrResponse;
                    }
                    queryCheckInCounrResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                    queryCheckInCounrResponse.setErrorInfo(checkedInMaxList.toString() + "已达最大选座次数，最多可改座2次，确认是否为其他旅客改座");
                    log.info("{}_值机选座，查询旅客值机次数response:{},时间：{}", uuid, JsonUtil.objectToJson(queryCheckInCounrResponse), DateUtils.getCurrentTimeStr());
                    return queryCheckInCounrResponse;
                }
                queryCheckInCounrResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                queryCheckInCounrResponse.setResultCode(WSEnum.ERROR.getResultCode());
                queryCheckInCounrResponse.setErrorInfo(checkInCounts.getErrorMsg());
                log.info("{}_值机选座，查询旅客值机次数response:{},时间：{}", uuid, JsonUtil.objectToJson(queryCheckInCounrResponse), DateUtils.getCurrentTimeStr());
                return queryCheckInCounrResponse;
            }
        } catch (Exception e) {
            log.error("查询旅客值机次数ERROR:{}", e.getMessage());
            queryCheckInCounrResponse.setResultCode(WSEnum.ERROR.getResultCode());
            queryCheckInCounrResponse.setErrorInfo("访问超时，请重试！");
            return queryCheckInCounrResponse;
        }
        return queryCheckInCounrResponse;
    }


    @ApiOperation(value = "获取值机选座协议", notes = "获取值机选座协议")
    @RequestMapping(value = "checkInSeatAgreements", method = RequestMethod.GET)
    public CheckInSeatAgreementResponse checkInSeatAgreements(HttpServletRequest request) {
        try {
            CheckInSeatAgreementResponse checkInSeatAgreementResponse = new CheckInSeatAgreementResponse(WSEnum.SUCCESS.getResultCode(), WSEnum.SUCCESS.getResultInfo());
            // 值机选座协议
            List<CheckInSeatAgreement> agreements = handConfig.getCheckInPolicyConfig().getCheckInSeatAgreements();
            checkInSeatAgreementResponse.setAgreements(agreements);
            // 优选座位协议配置
            List<CheckInSeatAgreement> preferAgreements = handConfig.getPreferredSeatAgreements().getCheckInSeatAgreements();
            checkInSeatAgreementResponse.setPreferredSeatAgreements(preferAgreements);
            return checkInSeatAgreementResponse;
        } catch (Exception e) {
            log.error("查询值机选座协议出现异常！", e);
            return new CheckInSeatAgreementResponse(WSEnum.ERROR.getResultCode(), WSEnum.ERROR.getResultInfo());
        }
    }

    //舱位名称判定
    private String getCabinName(String cabin) {
        if (ControllerUtil.getHighestClassCabin(cabin).equals("Y")) {
            return "经济舱";
        } else if (ControllerUtil.getHighestClassCabin(cabin).equals("C")) {
            return "公务舱";
        } else if (ControllerUtil.getHighestClassCabin(cabin).equals("J")) {
            return "公务舱";
        }
        return "";
    }
}