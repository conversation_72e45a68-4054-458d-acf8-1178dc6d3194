package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.activity.ActivityVIPExperienceCardEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.order.SubOrderTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.invoice.request.*;
import com.juneyaoair.baseclass.invoice.response.InvoiceTicketInfo;
import com.juneyaoair.baseclass.invoice.response.InvoiceTicketInfoResponse;
import com.juneyaoair.baseclass.member.response.MileageRecordQueryResponse;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CheckErrorException;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.exception.RequestParamErrorException;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.core.bean.unitorder.request.*;
import com.juneyaoair.mobile.core.bean.unitorder.response.*;
import com.juneyaoair.mobile.core.service.unitorder.UnitOrderHttpInterfaceService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.invoice.CouponSourceEnum;
import com.juneyaoair.mobile.handler.bean.invoice.TicketOrderSort;
import com.juneyaoair.mobile.handler.comm.CommodityEnum;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.MailSendUtil;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.mapstruct.InvoiceTicketInfoMapping;
import com.juneyaoair.mobile.webservice.client.inpsur.client.InspurTaxationWSClient;
import com.juneyaoair.mobile.webservice.client.inpsur.client.request.ElectricOutputInvoiceRequestDetail;
import com.juneyaoair.mobile.webservice.client.inpsur.client.response.ElectricOutputInvoiceResponse;
import com.juneyaoair.mobile.webservice.client.inpsur.client.response.ElectricOutputInvoiceResponseSuccessInfo;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.common.invoice.request.ModifyGeneralInvoiceRequest;
import com.juneyaoair.thirdentity.common.invoice.response.ModifyGeneralInvoiceResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.http.HttpsUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailAttachment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName InvoiceController
 * @Description 电子发票服务controller
 * <AUTHOR>
 * @Date 2019/4/25 14:02
 **/
@RequestMapping("/invoice/")
@RestController
@Api(value = "InvoiceController")
public class InvoiceController extends BassController {

    @Resource(name = "inspurTaxationWSClient")
    private InspurTaxationWSClient inspurTaxationWSClient;
    @Autowired
    private IBasicService basicService;

    @Resource(name = "unitOrderHttpInterfaceService")
    private UnitOrderHttpInterfaceService unitOrderHttpInterfaceService;
    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private IMemberService memberService;

    private static final String TICKET_NO_NOT_NULL_MSG = "票号不能为空";

    private static final String INVOKE_UNIT_ORDER_FAILED_MSG = "订单中心无响应，开票申请失败";

    private static final String NO_INVOICE_DATA_MSG = "未查询到可申请电子发票订单，如有疑问可联系客服。";

    private static final String UNSUPPORTED_ORDER_TYPE = "不支持开票的订单类型";

    private static final String ONE = "1";

    private static final String TICKET_QUERY_SUCCESS_CODE = "1001";


    @Autowired
    private HandConfig handConfig;

    @RequestMapping(value = "electricOutputInvoice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "开票申请接口", notes = "开票申请接口")
    @InterfaceLog
    public BaseResp electricOutputInvoice(@RequestBody @Validated BaseReq<ElectricOutputInvoiceRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<String> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        try {
            //校验参数
            if (bindingResult.hasErrors()) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return response;
            }
            //判断是否登录
            ElectricOutputInvoiceRequest reqParam = req.getRequest();
            boolean flag = this.checkKeyInfo(reqParam.getFfpId(),
                    reqParam.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return response;
            }

            //抬头
            String buyerName = reqParam.getBuyerName();
            //抬头类型
            String buyerTaxType = reqParam.getBuyerTaxType();
            OrderInvoiceRequestDto requestDto = new OrderInvoiceRequestDto();
            if ("个人".equals(buyerName)) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setResultInfo("抬头不能为\"个人\"");
                return response;
            }
            //去除税号空格
            if (StringUtils.isNotBlank(reqParam.getBuyerTaxID())) {
                reqParam.setBuyerTaxID(reqParam.getBuyerTaxID().replace(" ", ""));
            }
            //去除账户空格
            if (StringUtils.isNotBlank(reqParam.getBankAccount())) {
                reqParam.setBankAccount(reqParam.getBankAccount().replace(" ", ""));
            }
//            if (/*企业*/"1".equals(buyerTaxType) && StringUtils.isBlank(reqParam.getBuyerTaxID())) {
//                throw new RequestParamErrorException("企业税号不能为空");
//            }
            if (StringUtils.isNotBlank(reqParam.getCompanyPhone()) && reqParam.getCompanyPhone().length() > 30) {
                throw new RequestParamErrorException("电话号码太长！");
            }
            List<String> ticketOrderSorts = new ArrayList<>();
            switch (reqParam.getOrderType()) {
                //查询普通机票订单和升舱订单
                case 1:
                    if (StringUtils.isBlank(reqParam.getTicketNo())) {
                        throw new RequestParamErrorException(TICKET_NO_NOT_NULL_MSG);
                    }
                    reqParam.setTicketNo(reqParam.getTicketNo());
                    // 票号特殊处理
                    if (!reqParam.getTicketNo().contains("-")) {
                        String ticketNo = reqParam.getTicketNo().substring(0, 3) + "-" + reqParam.getTicketNo().substring(3);
                        reqParam.setTicketNo(ticketNo);
                    }
                    requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                    ticketOrderSorts.add(TicketOrderSort.Normal.getCode());
//                    ticketOrderSorts.add(TicketOrderSort.Upgrade.code); //升舱改期订单暂时不开票
                    ticketOrderSorts.add(TicketOrderSort.UnCompanion.getCode());
                    ticketOrderSorts.add(TicketOrderSort.Change.getCode());
                    break;
                //查询退票订单
                case 2:
                    if (StringUtils.isBlank(reqParam.getTicketNo())) {
                        throw new RequestParamErrorException(TICKET_NO_NOT_NULL_MSG);
                    }
                    reqParam.setTicketNo(reqParam.getTicketNo());
                    // 票号特殊处理
                    if (!reqParam.getTicketNo().contains("-")) {
                        String ticketNo = reqParam.getTicketNo().substring(0, 3) + "-" + reqParam.getTicketNo().substring(3);
                        reqParam.setTicketNo(ticketNo);
                    }
                    requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                    ticketOrderSorts.add(TicketOrderSort.Refund.getCode());
                    break;
                //查询其他服务
                case 3:
                    if (StringUtils.isBlank(reqParam.getOrderNo())) {
                        throw new RequestParamErrorException("订单号不能为空");
                    }
                    requestDto.setCouponCode(reqParam.getInvoiceTicketInfo().getCouponCode());
                    requestDto.setPsgrName(null);// 券类产品无旅客姓名
                    requestDto.setSubOrderType(SubOrderTypeEnum.COUPON_ORDER.getOrdrType());
                    break;
                default:
                    throw new OperationFailedException("订单类型不正确，开票失败");
            }
            requestDto.setChannelCode(req.getChannelCode());
            requestDto.setVersion(SystemConstants.VERSION);
            requestDto.setUserNo(this.getChannelInfo(req.getChannelCode(), "10"));
            requestDto.setTktNo(reqParam.getTicketNo());
            requestDto.setOrderNo(reqParam.getOrderNo());
            requestDto.setTicketOrderSorts(ticketOrderSorts);
            requestDto.setPageNo(1);
            requestDto.setPageSize(10);
            //2021-07-23 判断打包券只有吉祥礼券开具电子发票的 配置活动编号到阿波罗
            List<String> invoiceActivityNos = handConfig.getInvoiceActivityNos();
            requestDto.setBundleIdList(invoiceActivityNos);
            //查询订单详情准备开票
            OrderInvoiceResponseDto responseDto = unitOrderHttpInterfaceService.QueryOrderInvoice(requestDto, ip);
            if (null == responseDto) {
                throw new OperationFailedException(INVOKE_UNIT_ORDER_FAILED_MSG);
            }
            if (!UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_SUCCESS.equals(responseDto.getResultCode())) {
                if (UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_EMPTY.equals(responseDto.getResultCode())) {
                    response.setResultCode(WSEnum.NO_DATA.getResultCode());
                    response.setResultInfo(NO_INVOICE_DATA_MSG);
                    return response;
                }
                if (StringUtils.isNotBlank(responseDto.getErrorInfo())) {
                    throw new OperationFailedException(responseDto.getErrorInfo());
                } else {
                    throw new OperationFailedException(INVOKE_UNIT_ORDER_FAILED_MSG);
                }
            }
            List<OrderInvoiceInfoDto> orderInvoiceInfoList = this.filtInvoiceableOrders(responseDto, reqParam.getOrderType());
            if (CollectionUtils.isEmpty(orderInvoiceInfoList)) {
                response.setResultCode(WSEnum.NO_DATA.getResultCode());
                response.setResultInfo("未查询到可以开票的订单");
                return response;
            }
            if (StringUtils.isNotBlank(reqParam.getOrderNo())) {
                orderInvoiceInfoList.removeIf(item -> !(reqParam.getOrderNo().equals(item.getOrderNo())));
            }
            if (StringUtils.isNotBlank(reqParam.getTicketNo())) {
                orderInvoiceInfoList.removeIf(item -> !(reqParam.getTicketNo().equals(item.getTicketNo())));
            }
            Boolean success = false;
            //订单系统的订单号
            String businessId;
            if (CollectionUtils.isNotEmpty(orderInvoiceInfoList)) {
                //附加服务订单
                if (SubOrderTypeEnum.COUPON_ORDER.getOrdrType().equals(reqParam.getSubOrderType())) {
                    for (OrderInvoiceInfoDto orderInvoiceInfoDto : orderInvoiceInfoList) {
                        if (orderInvoiceInfoDto.getCouponOrderId().equals(reqParam.getInvoiceTicketInfo().getCouponOrderId())) {
                            businessId = orderInvoiceInfoDto.getCouponOrderId();
                            List<ElectricOutputInvoiceRequestDetail> details = new ArrayList<>();
                            if (null != orderInvoiceInfoDto.getCouponPayPrice() && orderInvoiceInfoDto.getCouponPayPrice().doubleValue() > 0) {
                                CommodityEnum commodityEnum;
                                CouponSourceEnum couponSourceEnum = CouponSourceEnum.getByCode(orderInvoiceInfoDto.getCouponSource());
                                if (null == couponSourceEnum) {
                                    if ("HOBG".equals(orderInvoiceInfoDto.getCouponSource())) {
                                        //"逾重行李券"
                                        commodityEnum = CommodityEnum.S01004;
                                    } else if ("HOUP".equals(orderInvoiceInfoDto.getCouponSource())) {
                                        //"升舱券"
                                        commodityEnum = CommodityEnum.S01006;
                                    } else if ("HOLO".equals(orderInvoiceInfoDto.getCouponSource())) {
                                        //贵宾休息室
                                        commodityEnum = CommodityEnum.S01008;
                                    } else if (ActivityVIPExperienceCardEnum.getVipExperienceCardTypeList().contains(orderInvoiceInfoDto.getCouponSource())) {
                                        commodityEnum = CommodityEnum.S01506;
                                    } else {
                                        throw new OperationFailedException(UNSUPPORTED_ORDER_TYPE);
                                    }
                                } else {
                                    switch (couponSourceEnum) {
                                        case UNLIMITED_FLY_V2:
                                        case UNLIMITED_FLY_V2_SF:
                                        case ADULTUNLIMITEDFLY:
                                        case COUPON:
                                        case CHILDUNLIMITEDFLY:
                                        case THEME_SEAFOOD_COUPON:
                                        case THEME_MILKTEA_COUPON:
                                        case THEME_CHERRY_COUPON:
                                        case THEME_OUTHIKE_COUPON:
                                        case THEME_VERMIC_COUPON:
                                        case THEME_SEALAND_COUPON:
                                        case THEME_SKITRIP_COUPON:
                                        case THEME_HOTPOT_COUPON:
                                            commodityEnum = CommodityEnum.S01001;
                                            break;
                                        case BAGGAGECOUPON:
                                        case Baggage:
                                            if (this.isInternationalAirport(orderInvoiceInfoDto.getDepAirport()) || this.isInternationalAirport(orderInvoiceInfoDto.getArrAirport())) {
                                                commodityEnum = CommodityEnum.S01086;
                                            } else {
                                                commodityEnum = CommodityEnum.S01004;
                                            }
                                            break;
                                        case LOUNGECOUPON:
                                        case Lounge:
                                            commodityEnum = CommodityEnum.S01008;
                                            break;
                                        case UPGRADEUNLIMITED: // 无限升舱卡
                                        case AIRPLANE_UPGRADE: // 机上升舱
                                        case UPGRADECOUPON:
                                        case Upgrade:
                                            commodityEnum = CommodityEnum.S01006;
                                            break;
                                        case MailTravel:
                                            commodityEnum = CommodityEnum.S01034;
                                            break;
                                        case HOCAR:
                                            commodityEnum = CommodityEnum.S10401996;
                                            break;
                                        case BRANDMEALS:
                                            // 毛毯业务Category编号 BM987D0E5F
                                            if ("BM987D0E5F".equals(orderInvoiceInfoDto.getCategory())) {
                                                commodityEnum = CommodityEnum.S01574;
                                            } else {
                                                commodityEnum = CommodityEnum.S01458;
                                            }
                                            break;
                                        case PAYSEAT:
                                        case DISNEY:
                                            commodityEnum = CommodityEnum.S01037;
                                            break;
                                        case EXTRABAGGAGE:
                                            commodityEnum = CommodityEnum.S01001BAG;
                                            break;
                                        case RESCHEDULE:
                                            commodityEnum = CommodityEnum.S01007;
                                            break;
                                        case MEMBERRENEWAL:
                                        case MEMBERUPGRADE:
                                            commodityEnum = CommodityEnum.S01506;
                                            break;
                                        default:
                                            throw new OperationFailedException(UNSUPPORTED_ORDER_TYPE);
                                    }
                                }
                                Double invoiceAmont = 0d;
                                if (StringUtils.isNotBlank(orderInvoiceInfoDto.getBundleName())) {
                                    if (orderInvoiceInfoDto.getAmount().doubleValue() != reqParam.getTotalPriceIncludeTax()) {
                                        throw new OperationFailedException("开票金额与实际金额不符");
                                    }
                                    invoiceAmont = orderInvoiceInfoDto.getAmount().doubleValue();
                                } else {
                                    if (orderInvoiceInfoDto.getCouponPayPrice().doubleValue() != reqParam.getTotalPriceIncludeTax()) {
                                        throw new OperationFailedException("开票金额与实际金额不符");
                                    }
                                    invoiceAmont = orderInvoiceInfoDto.getCouponPayPrice().doubleValue();
                                }
                                Double taxRate = 0d;
                                if (couponSourceEnum != null && CouponSourceEnum.PAYSEAT.getCode().equals(couponSourceEnum.getCode())) {
                                    taxRate = 0.06d;
                                }
                                details.add(new ElectricOutputInvoiceRequestDetail("", commodityEnum.getCommodityName(),
                                        commodityEnum.getCommodityNum(), "", "", 0d, 1d,
                                        0d, taxRate, 0d, invoiceAmont));
                                String note = "";
                                if (orderInvoiceInfoDto.getCouponSource().matches(PatternCommon.TOTALTYPE)) {
                                    note = reqParam.getNote() + " " + orderInvoiceInfoDto.getCouponCode();
                                } else {
                                    note = reqParam.getNote() + " " + orderInvoiceInfoDto.getOrderNo();
                                }

                                //系统内开票编码
                                String couponNo = orderInvoiceInfoDto.getCouponCode();
                                String orderNo = orderInvoiceInfoDto.getOrderNo();
                                if (couponSourceEnum != null && (CouponSourceEnum.PAYSEAT.getCode().equals(couponSourceEnum.getCode())
                                        || CouponSourceEnum.EXTRABAGGAGE.getCode().equals(couponSourceEnum.getCode()))) {
                                    couponNo = orderInvoiceInfoDto.getCouponRemark();
                                    orderNo = "";
                                }
                                String srcDocCode = this.genCouponSrcDocCode(commodityEnum, couponSourceEnum,
                                        orderNo, couponNo);
                                String InvoiceNum = ONE;
                                ElectricOutputInvoiceResponseSuccessInfo successInfo = this.invoiceAndSenMessage(srcDocCode,
                                        orderInvoiceInfoDto.getInvoiceAmount().doubleValue(), buyerTaxType,
                                        reqParam.getBuyerTaxID(), buyerName, reqParam.getRecMail(), note, details,
                                        reqParam.getCompanyAddress(), reqParam.getCompanyPhone(), reqParam.getBank(), reqParam.getBankAccount());
                                this.saveInvoiceInfo(requestDto.getChannelCode(), requestDto.getUserNo(), requestDto.getVersion(),
                                        businessId, buyerTaxType, reqParam.getBuyerTaxID(), buyerName, reqParam.getBank(),
                                        reqParam.getBankAccount(), orderInvoiceInfoDto.getInvoiceAmount(), reqParam.getCompanyAddress(),
                                        reqParam.getCompanyPhone(), reqParam.getNote(), reqParam.getRecMail(), successInfo.getInvoiceCode(),
                                        successInfo.getInvoiceNum(), successInfo.getMakeOutDate(), successInfo.getEInvoiceURL(),
                                        reqParam.getSubOrderType(), orderInvoiceInfoDto.getTicketOrderSort(), reqParam.getNote(), InvoiceNum, orderInvoiceInfoDto.getInvoiceAmount(), orderInvoiceInfoDto.getOrderNo(), ip, requestDto.getTicketOrderSorts());
                                success = true;
                            }
                        }
                    }
                }
                // 机票订单
                else if (SubOrderTypeEnum.TICKET_ORDER.getOrdrType().equals(reqParam.getSubOrderType())) {
                    for (OrderInvoiceInfoDto orderInvoiceInfoDto : orderInvoiceInfoList) {
                        businessId = orderInvoiceInfoDto.getTicketNo();
                        List<ElectricOutputInvoiceRequestDetail> details = new ArrayList<>();
                        TicketOrderSort ticketOrderSort = TicketOrderSort.getByCode(orderInvoiceInfoDto.getTicketOrderSort());
                        if (ticketOrderSort == null) {
                            throw new OperationFailedException(UNSUPPORTED_ORDER_TYPE);
                        }
                        // 发票价格明细
                        InvoicePriceDetail invoicePriceDetail = orderInvoiceInfoDto.getInvoicePriceDetail();
                        BigDecimal totalInvoiceAmount = new BigDecimal(0);
                        //退票订单
                        if (reqParam.getOrderType() == 2) {
                            BigDecimal refundFee = new BigDecimal(invoicePriceDetail.getRefundFee());
                            //区分国内国际，税率由类目号指定
                            if ("D".equals(orderInvoiceInfoDto.getTktNatSign())) {
                                details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01005.getCommodityName(),
                                        CommodityEnum.S01005.getCommodityNum(), "", "", 0d, 1d,
                                        0d, 0d, 0d, refundFee.doubleValue()));
                                totalInvoiceAmount = totalInvoiceAmount.add(refundFee);
                            } else if ("I".equals(orderInvoiceInfoDto.getTktNatSign())) {
                                details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01081.getCommodityName(),
                                        CommodityEnum.S01081.getCommodityNum(), "", "", 0d, 1d,
                                        0d, 0d, 0d, refundFee.doubleValue()));
                                totalInvoiceAmount = totalInvoiceAmount.add(refundFee);
                            }
                        } else {
                            // 普通订单需计算燃油及机建
                            if (TicketOrderSort.Normal.equals(ticketOrderSort) || TicketOrderSort.UnCompanion.equals(ticketOrderSort)) {
                                //国内机票，国内升舱费，退票费抬头直接读取乘机人姓名
                                if (reqParam.getOrderType() == 1 && "9".equals(reqParam.getBuyerTaxType())) {
                                    buyerName = orderInvoiceInfoDto.getPassengerName();
                                }
                                //普通机票订单PricePaid为机票实付价格，总开票价格需要加上燃油及机建
                                //机票或升舱费价格
                                BigDecimal ticketPrice = StringUtils.isBlank(invoicePriceDetail.getTicketPrice()) ? null : new BigDecimal(invoicePriceDetail.getTicketPrice());
                                // 燃油附加费
                                BigDecimal yqFee = StringUtils.isBlank(invoicePriceDetail.getYqFee()) ? null : new BigDecimal(invoicePriceDetail.getYqFee());
                                // 机建建设费
                                BigDecimal cnFee = StringUtils.isBlank(invoicePriceDetail.getCnFee()) ? null : new BigDecimal(invoicePriceDetail.getCnFee());
                                //区分国内国际
                                if ("D".equals(orderInvoiceInfoDto.getTktNatSign())) {
                                    //机票款
                                    if (null != ticketPrice && ticketPrice.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01001.getCommodityName(),
                                                CommodityEnum.S01001.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, ticketPrice.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(ticketPrice);
                                    }
                                    // 燃油附加费
                                    if (null != yqFee && yqFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01003.getCommodityName(),
                                                CommodityEnum.S01003.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, yqFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(yqFee);
                                    }
                                    // 机建建设费
                                    if (null != cnFee && cnFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01002.getCommodityName(),
                                                CommodityEnum.S01002.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, cnFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(cnFee);
                                    }
                                } else if ("I".equals(orderInvoiceInfoDto.getTktNatSign())) {
                                    //机票款
                                    if (null != ticketPrice && ticketPrice.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01001.getCommodityName(),
                                                CommodityEnum.S01001.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, ticketPrice.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(ticketPrice);
                                    }
                                    // 燃油附加费
                                    //国际电子发票中的燃油税包含所有税种，因为开票只有燃油税这一个类目
                                    if (null != yqFee && yqFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01082.getCommodityName(),
                                                CommodityEnum.S01082.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, yqFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(yqFee);
                                    }
                                }
                            }
                            // 升舱 改期订单
                            else if (TicketOrderSort.Upgrade.equals(ticketOrderSort) || TicketOrderSort.Change.equals(ticketOrderSort)) {
                                //机票或升舱费价格
                                BigDecimal ticketPrice = StringUtils.isBlank(invoicePriceDetail.getTicketPrice()) ? null : new BigDecimal(invoicePriceDetail.getTicketPrice());
                                // 燃油附加费
                                BigDecimal yqFee = StringUtils.isBlank(invoicePriceDetail.getYqFee()) ? null : new BigDecimal(invoicePriceDetail.getYqFee());
                                // 机建建设费
                                BigDecimal cnFee = StringUtils.isBlank(invoicePriceDetail.getCnFee()) ? null : new BigDecimal(invoicePriceDetail.getCnFee());
                                // 升舱费
                                BigDecimal upgradeFee = StringUtils.isBlank(invoicePriceDetail.getUpgradeFee()) ? null : new BigDecimal(invoicePriceDetail.getUpgradeFee());
                                //国内升舱订单PricePaid为客人实付价格，升舱费需要扣除燃油及机建
                                if ("D".equals(orderInvoiceInfoDto.getTktNatSign())) {
                                    // 燃油附加费
                                    if (null != yqFee && yqFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01003.getCommodityName(),
                                                CommodityEnum.S01003.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, yqFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(yqFee);
                                    }
                                    // 机建建设费
                                    if (null != cnFee && cnFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01002.getCommodityName(),
                                                CommodityEnum.S01002.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, cnFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(cnFee);
                                    }
                                    // 升舱费
                                    if (null != upgradeFee && upgradeFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01006.getCommodityName(),
                                                CommodityEnum.S01006.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, upgradeFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(upgradeFee);
                                    }
                                    // 改期费
                                    BigDecimal exchangeFee = StringUtils.isBlank(invoicePriceDetail.getExchangeFee()) ? null : new BigDecimal(invoicePriceDetail.getExchangeFee());
                                    if (null != exchangeFee && exchangeFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01007.getCommodityName(),
                                                CommodityEnum.S01007.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, exchangeFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(exchangeFee);
                                    }
                                } else if ("I".equals(orderInvoiceInfoDto.getTktNatSign())) {
                                    //机票款
                                    if (null != ticketPrice && ticketPrice.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01080.getCommodityName(),
                                                CommodityEnum.S01080.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, ticketPrice.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(ticketPrice);
                                    }
                                    //燃油
                                    if (null != yqFee && yqFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01082.getCommodityName(),
                                                CommodityEnum.S01082.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, yqFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(yqFee);
                                    }
                                    // 升舱费
                                    if (null != upgradeFee && upgradeFee.compareTo(BigDecimal.ZERO) > 0) {
                                        details.add(new ElectricOutputInvoiceRequestDetail("", CommodityEnum.S01083.getCommodityName(),
                                                CommodityEnum.S01083.getCommodityNum(), "", "", 0d, 1d,
                                                0d, 0d, 0d, upgradeFee.doubleValue()));
                                        totalInvoiceAmount = totalInvoiceAmount.add(upgradeFee);
                                    }
                                }
                            }
                        }
                        if (reqParam.getTotalPriceIncludeTax() != orderInvoiceInfoDto.getInvoiceAmount().doubleValue()
                                || totalInvoiceAmount.doubleValue() != reqParam.getTotalPriceIncludeTax()) {
                            throw new OperationFailedException("开票金额与实际金额不符");
                        }
                        String note = reqParam.getNote() + " " + reqParam.getTicketNo() + " " + orderInvoiceInfoDto.getPassengerName()
                                + " " + reqParam.getInvoiceTicketInfo().getRute();
                        String srcDocCode = this.genTicketSrcDocCode(reqParam.getOrderType(), ticketOrderSort, orderInvoiceInfoDto.getTicketNo());
                        String InvoiceNum = ONE;
                        ElectricOutputInvoiceResponseSuccessInfo successInfo = this.invoiceAndSenMessage(srcDocCode,
                                orderInvoiceInfoDto.getInvoiceAmount().doubleValue(), buyerTaxType, reqParam.getBuyerTaxID(),
                                buyerName, reqParam.getRecMail(), note, details, reqParam.getCompanyAddress(),
                                reqParam.getCompanyPhone(), reqParam.getBank(), reqParam.getBankAccount());
                        this.saveInvoiceInfo(requestDto.getChannelCode(), requestDto.getUserNo(), requestDto.getVersion(),
                                businessId, buyerTaxType, reqParam.getBuyerTaxID(), buyerName, reqParam.getBank(),
                                reqParam.getBankAccount(), orderInvoiceInfoDto.getInvoiceAmount(), reqParam.getCompanyAddress(),
                                reqParam.getCompanyPhone(), reqParam.getNote(), reqParam.getRecMail(), successInfo.getInvoiceCode(),
                                successInfo.getInvoiceNum(), successInfo.getMakeOutDate(), successInfo.getEInvoiceURL(),
                                reqParam.getSubOrderType(), orderInvoiceInfoDto.getTicketOrderSort(), reqParam.getNote(), InvoiceNum, orderInvoiceInfoDto.getInvoiceAmount(), orderInvoiceInfoDto.getOrderNo(), ip, requestDto.getTicketOrderSorts());
                        success = true;
                    }
                }
            } else {
                response.setResultInfo("该订单已经开过发票了，请勿重复申请");
                response.setResultCode(WSEnum.ERROR.getResultCode());
                return response;
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            ModifyGeneralInvoiceRequest modifyGeneralInvoiceRequest = new ModifyGeneralInvoiceRequest(HandlerConstants.VERSION, channelCode, userNo);
            modifyGeneralInvoiceRequest.setChannelCustomerNo(req.getRequest().getFfpId());
            modifyGeneralInvoiceRequest.setChannelCustomerType("CRM");
            ElectricOutputInvoiceRequest electricOutputInvoiceRequest = req.getRequest();
            modifyGeneralInvoiceRequest.setGeneralInvoiceId(electricOutputInvoiceRequest.getRecordId() == null ? 0 : electricOutputInvoiceRequest.getRecordId());
            modifyGeneralInvoiceRequest.setBank(electricOutputInvoiceRequest.getBank());
            modifyGeneralInvoiceRequest.setBankAccount(electricOutputInvoiceRequest.getBankAccount());
            modifyGeneralInvoiceRequest.setPayable(electricOutputInvoiceRequest.getBuyerName());
            modifyGeneralInvoiceRequest.setPayableType(electricOutputInvoiceRequest.getBuyerTaxType());
            modifyGeneralInvoiceRequest.setTaxNumber(electricOutputInvoiceRequest.getBuyerTaxID());
            modifyGeneralInvoiceRequest.setCompanyPhone(electricOutputInvoiceRequest.getCompanyPhone());
            modifyGeneralInvoiceRequest.setCompanyAddress(electricOutputInvoiceRequest.getCompanyAddress());
            //常用报销凭证列表中同步人
            if (success) {
                addCommonInvoice(modifyGeneralInvoiceRequest, ip);
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setResultInfo(WSEnum.ERROR.getResultInfo());
            }
        } catch (OperationFailedException e) {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setResultInfo(e.getMessage());
        } catch (Exception e) {
            log.error("开票申请异常:", e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return response;
    }


    /**
     * 判断是否为国际机场
     *
     * @param airportCode
     * @return
     */
    boolean isInternationalAirport(String airportCode) {
        if (StringUtils.isBlank(airportCode)) {
            return false;
        }
        AirPortInfoDto airPortInfoDto = localCacheService.getLocalAirport(airportCode);
        if (null == airPortInfoDto) {
            return false;
        }
        return airPortInfoDto.getIsInternational().equals(HandlerConstants.TRIP_TYPE_I);
    }


    private void addCommonInvoice(ModifyGeneralInvoiceRequest modifyGeneralInvoiceRequest, String ip) {
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");

        if (modifyGeneralInvoiceRequest.getGeneralInvoiceId() != 0) {
            HttpResult httpResult = this.doPostClient(modifyGeneralInvoiceRequest, HandlerConstants.URL_FARE_API + HandlerConstants.COMMON_INVOICE_MODIFY, headMap);
            if (httpResult.isResult()) {
                ModifyGeneralInvoiceResponse modifyGeneralInvoiceResponse;
                if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                    log.error("CRM无响应,修改报销凭证失败");
                }
                modifyGeneralInvoiceResponse = (ModifyGeneralInvoiceResponse) JsonUtil.jsonToBean(httpResult.getResponse(), ModifyGeneralInvoiceResponse.class);
                if (null == modifyGeneralInvoiceResponse) {
                    log.error("CRM无响应,修改报销凭证失败！");
                }

            }
        } else {
            HttpResult httpResult = this.doPostClient(modifyGeneralInvoiceRequest, HandlerConstants.URL_FARE_API + HandlerConstants.COMMON_INVOICE_ADD, headMap);
            if (httpResult.isResult()) {
                ModifyGeneralInvoiceResponse modifyGeneralInvoiceResponse;
                if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                    log.error("常用报销凭证列表中同步保存失败,{}", httpResult.toString());
                } else {
                    modifyGeneralInvoiceResponse = (ModifyGeneralInvoiceResponse) JsonUtil.jsonToBean(httpResult.getResponse(), ModifyGeneralInvoiceResponse.class);
                    if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(modifyGeneralInvoiceResponse.getResultCode())) {
                        log.error("常用报销凭证列表中同步保存失败,{}", httpResult.toString());
                    }
                }
            }

        }


    }

    

    /**
     * 开票并发送邮件通知
     */
    private ElectricOutputInvoiceResponseSuccessInfo invoiceAndSenMessage(String srcDocCode, double invoiceAmount, String buyerTaxType, String buyerTaxID,
                                                                          String buyerName, String recMail, String note, List<ElectricOutputInvoiceRequestDetail> details,
                                                                          String address, String telephone, String bank, String bankAccount) {
        //开票
        ElectricOutputInvoiceResponse result = this.inspurTaxationWSClient.electricOutputInvoice(srcDocCode,
                invoiceAmount, buyerTaxType, "9".equals(buyerTaxType) ? null : buyerTaxID,
                buyerName, recMail, note, address, telephone, bank, bankAccount, details);
        List<ElectricOutputInvoiceResponseSuccessInfo> successInfos = result.getReData();
        if (CollectionUtils.isNotEmpty(successInfos)) {
            ElectricOutputInvoiceResponseSuccessInfo successInfo = successInfos.get(0);
            if (!InspurTaxationWSClient.ELECTRIC_OUTPUT_INVOICE_RESULTCODE_SUCCESS.equals(successInfo.getIsOK())) {
                if (StringUtils.isNotBlank(successInfo.getMsg())) {
                    if (successInfo.getMsg().contains("税务系统已存在该单据")) {
                        throw new OperationFailedException("该订单已经开过发票了");
                    } else {
                        throw new OperationFailedException(successInfo.getMsg());
                    }
                } else {
                    throw new OperationFailedException("提交开票申请失败");
                }
            }
            if (StringUtils.isAnyBlank(successInfo.getInvoiceCode(), successInfo.getInvoiceNum(),
                    successInfo.getEInvoiceURL(), successInfo.getMakeOutDate())) {
                throw new OperationFailedException("开票系统返回发票信息为空");
            }
            String invoiceDate = successInfo.getMakeOutDate();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                Date date = dateFormat.parse(invoiceDate);
                invoiceDate = simpleDateFormat.format(date);
            } catch (ParseException e) {
                log.error("InvoiceController日期转换异常:", e);
            }
            //发送邮件
            this.sendMail(successInfo.getInvoiceCode(), successInfo.getInvoiceNum(), invoiceDate,
                    successInfo.getEInvoiceURL(), recMail);
            return successInfo;
        } else {
            throw new OperationFailedException("开票系统返回发票信息为空");
        }
    }

    /**
     * 调用订单中心保存发票信息操作
     */
    private void saveInvoiceInfo(String channelCode, String userNo, String version, String businessId,
                                 String buyerTaxType, String buyerTaxId, String buyerName, String bank, String bankAccount,
                                 BigDecimal price, String companyAddress, String companyPhone, String content, String recMail,
                                 String invoiceCode, String invoiceNo, String invoiceDate, String downloadURL,
                                 String subOrderType, String ticketOrderSort, String InvoiceContent, String InvoiceNum, BigDecimal InvoicePrice, String orderNo, String ip, List<String> ticketOrderSorts) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String invoiceDateStr = invoiceDate;
        try {
            Date date = simpleDateFormat.parse(invoiceDateStr);
            invoiceDateStr = dateFormat.format(date);
        } catch (ParseException e) {
            log.error("InvoiceController日期转换异常:", e);
        }
        InvoiceInfoMaintainDto invoiceInfoMaintainDto = new InvoiceInfoMaintainDto(
                null,
                businessId,
                subOrderType,
                buyerTaxType,
                buyerName,
                buyerTaxId,
                bank,
                bankAccount,
                price,
                companyAddress,
                companyPhone,
                content,
                recMail,
                invoiceCode,
                invoiceNo,
                invoiceDateStr,
                downloadURL,
                ticketOrderSort,
                InvoiceContent,
                InvoiceNum,
                InvoicePrice,
                orderNo
        );
        CreateOrUpdateInvoiceInfoResponse response = this.unitOrderHttpInterfaceService.saveInvoiceInfo(channelCode, userNo,
                version, ticketOrderSorts, invoiceInfoMaintainDto, ip);
        if (!UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_SUCCESS.equals(response.getResultCode())) {
            throw new OperationFailedException("开票申请成功，但发票信息保存失败，请妥善保管电子发票");
        }
    }

    @InterfaceLog
    @RequestMapping(value = "/queryOrderInvoice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "获取订单行程列表", notes = "获取订单行程列表")
    public BaseResp queryOrderInvoice(@RequestBody @Validated BaseReq<QueryOrderInvoiceRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<InvoiceTicketInfoResponse> response = new BaseResp<>();
        List<InvoiceTicketInfo> invoiceTicketInfos = new ArrayList<>();
        List<InvoiceTicketInfo> invoicedOrderInfos = new ArrayList<>();
        InvoiceTicketInfoResponse invoiceInfoDto = new InvoiceTicketInfoResponse(
                invoiceTicketInfos, invoicedOrderInfos, req.getRequest().getPageSize(), req.getRequest().getPageNo(),
                0, 0);
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        try {
            //校验参数
            if (bindingResult.hasErrors()) {
                throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            //判断是否登录
            QueryOrderInvoiceRequest reqParam = req.getRequest();
            boolean flag = this.checkKeyInfo(reqParam.getFfpId(),reqParam.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                throw new CheckErrorException("参数校验失败");
            }
            OrderInvoiceRequestDto requestDto = new OrderInvoiceRequestDto();
            requestDto.setChannelCode(req.getChannelCode());
            requestDto.setVersion(SystemConstants.VERSION);
            requestDto.setUserNo(this.getChannelInfo(req.getChannelCode(), "10"));
            requestDto.setPageNo(reqParam.getPageNo());
            requestDto.setPageSize(reqParam.getPageSize());
            List<String> ticketOrderSorts = new ArrayList<>();
            switch (reqParam.getOrderType()) {
                //查询普通机票订单和升舱订单
                case 1:
                    requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                    ticketOrderSorts.add(TicketOrderSort.Change.getCode());
                    ticketOrderSorts.add(TicketOrderSort.Normal.getCode());
//                    ticketOrderSorts.add(TicketOrderSort.Upgrade.getCode()); //升舱改期暂时不开票
                    ticketOrderSorts.add(TicketOrderSort.UnCompanion.getCode());
                    break;
                //查询退票订单
                case 2:
                    requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                    ticketOrderSorts.add(TicketOrderSort.Refund.getCode());
                    break;
                //查询其他服务
                case 3:
                    requestDto.setSubOrderType(SubOrderTypeEnum.COUPON_ORDER.getOrdrType());
                    requestDto.setCouponCode(reqParam.getCouponNo());
                    reqParam.setPassengerName(null);// 券类产品无旅客姓名
                    break;
                default:
                    throw new OperationFailedException("查询类型不正确，获取列表失败");
            }
            requestDto.setTicketOrderSorts(ticketOrderSorts);
            if (StringUtils.isNotBlank(reqParam.getTicketNo())) {
                //票号类型
                String certType = CertNoUtil.getCertTypeByCertNo(reqParam.getTicketNo());
                if ("NI".equals(certType)) {
                    requestDto.setIdNbr(reqParam.getTicketNo());
                } else {
                    if (!reqParam.getTicketNo().contains("-")) {
                        String ticketNo = reqParam.getTicketNo().substring(0, 3) + "-" + reqParam.getTicketNo().substring(3);
                        requestDto.setTktNo(ticketNo);
                    } else {
                        requestDto.setTktNo(reqParam.getTicketNo());
                    }
                }
            } else {
                requestDto.setFfpId(reqParam.getFfpId());
            }
            requestDto.setPsgrName(reqParam.getPassengerName());
            requestDto.setChannelOrderNo(reqParam.getChannelOrderNo());
            requestDto.setOrderNo(reqParam.getOrderNo());
            //2021-07-23 判断打包券只有吉祥礼券开具电子发票的 配置活动编号到阿波罗
            List<String> invoiceActivityNos = handConfig.getInvoiceActivityNos();
            requestDto.setBundleIdList(invoiceActivityNos);

            if (reqParam.getOrderType() == 3) {
                requestDto.setPageNo(1);
                requestDto.setPageSize(500);
            }

            OrderInvoiceResponseDto responseDto = unitOrderHttpInterfaceService.QueryOrderInvoice(requestDto, ip);

            if (null == responseDto) {
                response.setResultInfo("订单中心无响应，获取列表失败");
                response.setResultCode(WSEnum.ERROR.getResultCode());
                return response;
            }
            if (UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_SUCCESS.equals(responseDto.getResultCode())) {

                invoiceInfoDto.setRecordCount(responseDto.getRecordCount());
                invoiceInfoDto.setPageCount(responseDto.getPageCount());
                List<OrderInvoiceInfoDto> orderInvoiceInfoList = this.filtInvoiceableOrders(responseDto, reqParam.getOrderType());

                if (reqParam.getOrderType() == 3) {
                    //附加服务的列表需要拿全量数据 代码层做分页
                    if (StringUtils.isEmpty(reqParam.getPassengerName())) {
                        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
                        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(reqParam.getFfpCardNo(), reqParam.getFfpId(), request, req.getChannelCode(), items);
                        PtCRMResponse<PtMemberDetail> ptCRMResponse = this.memberService.memberDetail(ptApiCRMRequest);
                        if (ptCRMResponse.getCode() != 0) {
                            response.setResultCode(WSEnum.ERROR.getResultCode());
                            response.setErrorInfo(ptCRMResponse.getMsg());
                            response.setResultInfo(ptCRMResponse.getMsg());
                            return response;
                        }
                        PtMemberDetail ptMemberDetail = ptCRMResponse.getData();
                        reqParam.setPassengerName(CRMReqUtil.getChinaName(ptMemberDetail.getBasicInfo()));
                    }
                    ExcessBaggageRespDTO excessBaggageRespDTO = toGainTheWholeExcessBaggage(reqParam, ip);
                    PageInfo<OrderInvoiceInfoDto> orderInvoiceInfoDtoPageInfo = toPage(orderInvoiceInfoList, excessBaggageRespDTO, reqParam);
                    if (null != orderInvoiceInfoDtoPageInfo) {
                        orderInvoiceInfoList = orderInvoiceInfoDtoPageInfo.getList();
                        invoiceInfoDto.setRecordCount((int) (orderInvoiceInfoDtoPageInfo.getTotal()));
                        invoiceInfoDto.setPageCount(orderInvoiceInfoDtoPageInfo.getPages());
                    }
                }

                if (CollectionUtils.isNotEmpty(orderInvoiceInfoList)) {
                    //机票订单按起飞时间排序
                    if (reqParam.getOrderType() != 3) {
                        orderInvoiceInfoList.sort((e1, e2) -> {
                            String date1 = e1.getSubOrderList().get(0).getFlightDate();
                            String date2 = e2.getSubOrderList().get(0).getFlightDate();
                            if (StringUtils.isAnyBlank(date1, date2)) {
                                return 0;
                            } else {
                                try {
                                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                    Date flightDate1 = dateFormat.parse(date1);
                                    Date flightDate2 = dateFormat.parse(date2);
                                    return flightDate1.before(flightDate2) ? 1 : -1;
                                } catch (ParseException e) {
                                    // 排序失败不做处理
                                }
                                return 0;
                            }
                        });
                    }
                    orderInvoiceInfoList.forEach(orderInvoiceInfoDto -> {
                        String invoiceNo = "";
                        if (orderInvoiceInfoDto.getInvoiceInfo() != null) {
                            invoiceNo = orderInvoiceInfoDto.getInvoiceInfo().getTaxNumber();
                        }
                        //机票订单
                        if (SubOrderTypeEnum.TICKET_ORDER.getOrdrType().equals(orderInvoiceInfoDto.getSubOrderType())) {
                            List<SubOrderInfoDto> subOrders = orderInvoiceInfoDto.getSubOrderList();
                            if (CollectionUtils.isNotEmpty(subOrders)) {
                                Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
                                //航班日期
                                StringBuilder flightDate = new StringBuilder();
                                //航线
                                StringBuilder flightRute = new StringBuilder();
                                StringBuilder upgradeSegment = new StringBuilder();
                                for (int i = 0; i < subOrders.size(); i++) {
                                    if ((!StringUtils.isAnyBlank(subOrders.get(i).getFormerCabinClass(),
                                            subOrders.get(i).getFormerCabin(), subOrders.get(i).getCabinClass(),
                                            subOrders.get(i).getCabin()))
                                            && !subOrders.get(i).getFormerCabin().equals(subOrders.get(i).getCabin())) {
                                        upgradeSegment.append(CommonUtil.showCabinClassName(subOrders.get(i).getFormerCabinClass()))
                                                .append("(").append(subOrders.get(i).getFormerCabin()).append(")升至")
                                                .append(CommonUtil.showCabinClassName(subOrders.get(i).getCabinClass()))
                                                .append("(").append(subOrders.get(i).getCabin()).append(")");
                                    }
                                    if (i > 0) {
                                        flightDate.append("/").append(subOrders.get(i).getFlightDate());
                                        flightRute.append("-").append(FlightUtil.getAirPort(subOrders.get(i).getArrAirportCode(), airPortInfoMap) == null ?
                                                "" : FlightUtil.getAirPort(subOrders.get(i).getArrAirportCode(), airPortInfoMap).getCityName());
                                    } else {
                                        flightRute.append(FlightUtil.getAirPort(subOrders.get(i).getDepAirportCode(), airPortInfoMap) == null ?
                                                        "" : FlightUtil.getAirPort(subOrders.get(i).getDepAirportCode(), airPortInfoMap).getCityName())
                                                .append("-").append(FlightUtil.getAirPort(subOrders.get(i).getArrAirportCode(), airPortInfoMap) == null ?
                                                        "" : FlightUtil.getAirPort(subOrders.get(i).getArrAirportCode(), airPortInfoMap).getCityName());
                                        flightDate.append(subOrders.get(i).getFlightDate());
                                    }
                                }
                                String itemName = genInvoiceItemName(SubOrderTypeEnum.TICKET_ORDER, reqParam.getOrderType() == 2 ? "Refund" : orderInvoiceInfoDto.getTicketOrderSort(), orderInvoiceInfoDto);
                                InvoiceTicketInfo invoiceTicketInfo = InvoiceTicketInfoMapping.getInvoiceTicketInfo(orderInvoiceInfoDto,
                                                                        orderInvoiceInfoDto.getTicketNo().replace("-", ""), orderInvoiceInfoDto.getPassengerName(), flightDate.toString(), flightRute.toString(),
                                                                        itemName, upgradeSegment.toString(), invoiceNo, true, true,
                                                                 "", "", "", 0, "");
                                if (invoiceTicketInfo.isHasInvoiced()) {
                                    invoicedOrderInfos.add(invoiceTicketInfo);
                                } else {
                                    invoiceTicketInfos.add(invoiceTicketInfo);
                                }
                            }
                        } else {
                            //航班日期
                            StringBuilder flightDate = new StringBuilder();
                            //航线
                            StringBuilder flightRute = new StringBuilder();
                            String ticketNo = "";
                            String passangerName = "";
                            if (CouponSourceEnum.EXTRABAGGAGE.getCode().equals(orderInvoiceInfoDto.getCouponSource())) {
                                flightDate.append(orderInvoiceInfoDto.getFlightDate());
                                if (orderInvoiceInfoDto.getDepAirport() != null) {
                                    AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(orderInvoiceInfoDto.getDepAirport());
                                    flightRute.append(depAirPortInfo == null ? "" : depAirPortInfo.getCityName()).append("-");
                                }
                                if (orderInvoiceInfoDto.getArrAirport() != null) {
                                    AirPortInfoDto arrAirPortInfo = localCacheService.getLocalAirport(orderInvoiceInfoDto.getArrAirport());
                                    flightRute.append(arrAirPortInfo == null ? "" : arrAirPortInfo.getCityName());
                                }
                            }
                            if (CouponSourceEnum.PAYSEAT.getCode().equals(orderInvoiceInfoDto.getCouponSource())) {
                                flightDate.append(orderInvoiceInfoDto.getFlightDate());
                                ticketNo = StringUtils.isBlank(orderInvoiceInfoDto.getTicketNo()) ? "" : orderInvoiceInfoDto.getTicketNo().replace("-", "");
                                passangerName = StringUtils.isBlank(orderInvoiceInfoDto.getPassengerName()) ? "" : orderInvoiceInfoDto.getPassengerName();
                                if (orderInvoiceInfoDto.getDepAirport() != null) {
                                    AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(orderInvoiceInfoDto.getDepAirport());
                                    flightRute.append(depAirPortInfo == null ? "" : depAirPortInfo.getCityName()).append("-");
                                }
                                if (orderInvoiceInfoDto.getArrAirport() != null) {
                                    AirPortInfoDto arrAirPortInfo = localCacheService.getLocalAirport(orderInvoiceInfoDto.getArrAirport());
                                    flightRute.append(arrAirPortInfo == null ? "" : arrAirPortInfo.getCityName());
                                }
                            }

                            String itemName = this.genInvoiceItemName(SubOrderTypeEnum.COUPON_ORDER, orderInvoiceInfoDto.getCouponSource(), orderInvoiceInfoDto);
                            InvoiceTicketInfo invoiceTicketInfo = InvoiceTicketInfoMapping.getInvoiceTicketInfo(orderInvoiceInfoDto,
                                    ticketNo, passangerName, flightDate.toString(), flightRute.toString(),
                                    itemName, "", invoiceNo, true, true,
                                    StringUtils.isBlank(orderInvoiceInfoDto.getSeatNo()) ? "" : orderInvoiceInfoDto.getSeatNo(), "", "", 0, "");
                            // VIP体验卡处理
                            if (Arrays.stream(ActivityVIPExperienceCardEnum.values()).map(ActivityVIPExperienceCardEnum::getThemeCardType).collect(Collectors.toList()).contains(orderInvoiceInfoDto.getCouponSource())) {
                                invoiceTicketInfo.setItemName("其他权益性无形资产-会员费");
                                invoiceTicketInfo.setCouponSource(orderInvoiceInfoDto.getCouponSource());
                                invoiceTicketInfo.setTotalPrice(orderInvoiceInfoDto.getInvoiceAmount().doubleValue());
                            }
                            //逾重行李特殊处理
                            if (reqParam.getOrderType() == 3 && CouponSourceEnum.EXCESSBAGGAGEINVOICE.getCode().equals(orderInvoiceInfoDto.getCouponSource())) {
                                invoiceTicketInfo = new InvoiceTicketInfo();
                                invoiceTicketInfo.setTicketNo(orderInvoiceInfoDto.getTicketNo());
                                invoiceTicketInfo.setCouponSource(orderInvoiceInfoDto.getCouponSource());
                                invoiceTicketInfo.setRute("");
                                invoiceTicketInfo.setItemName("逾重行李费");
                                invoiceTicketInfo.setInvoiceTitle(orderInvoiceInfoDto.getInvoiceTitle());
                                invoiceTicketInfo.setTotalPrice(orderInvoiceInfoDto.getInvoiceAmount().doubleValue());
                                invoiceTicketInfo.setExcessStatus(orderInvoiceInfoDto.getExcessStatus());
                                invoiceTicketInfo.setTicketDeliveryId(orderInvoiceInfoDto.getTicketDeliveryId());
                                invoiceTicketInfo.setEmail(orderInvoiceInfoDto.getEmail());
                                invoiceTicketInfo.setHasInvoiced(toCheckInvoice(orderInvoiceInfoDto.getExcessStatus()));
                                invoiceTicketInfo.setCancelReason(orderInvoiceInfoDto.getCancelReason());
                            }
                            if (StringUtils.isNotBlank(orderInvoiceInfoDto.getBundleName())) {
                                if (orderInvoiceInfoDto.getBundleName().contains("吉享礼券")) {
                                    invoiceTicketInfo.setItemName("国内机票(吉享礼券优惠礼包)");
                                } else {
                                    invoiceTicketInfo.setItemName(orderInvoiceInfoDto.getBundleName());
                                }
                                invoiceTicketInfo.setTotalPrice(orderInvoiceInfoDto.getAmount().doubleValue());
                            }
                            if (invoiceTicketInfo.isHasInvoiced()) {
                                invoicedOrderInfos.add(invoiceTicketInfo);
                            } else {
                                invoiceTicketInfos.add(invoiceTicketInfo);
                            }
                        }
                    });
                }
            } else if (UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_EMPTY.equals(responseDto.getResultCode()) && reqParam.getOrderType() == 3) {
                //附加服务的列表需要拿全量数据 代码层做分页
                if (StringUtils.isEmpty(reqParam.getPassengerName())) {
                    String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
                    PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(reqParam.getFfpCardNo(), reqParam.getFfpId(), request, req.getChannelCode(), items);
                    PtCRMResponse<PtMemberDetail> ptCRMResponse = this.memberService.memberDetail(ptApiCRMRequest);
                    if (ptCRMResponse.getCode() != 0) {
                        response.setResultCode(WSEnum.ERROR.getResultCode());
                        response.setErrorInfo(ptCRMResponse.getMsg());
                        response.setResultInfo(ptCRMResponse.getMsg());
                        return response;
                    }
                    PtMemberDetail ptMemberDetail = ptCRMResponse.getData();
                    reqParam.setPassengerName(CRMReqUtil.getChinaName(ptMemberDetail.getBasicInfo()));
                }
                ExcessBaggageRespDTO excessBaggageRespDTO = toGainTheWholeExcessBaggage(reqParam, ip);
                List<OrderInvoiceInfoDto> orderInvoiceInfoDtos = new ArrayList<>();
                PageInfo<OrderInvoiceInfoDto> orderInvoiceInfoDtoPageInfo = toPage(orderInvoiceInfoDtos, excessBaggageRespDTO, reqParam);
                if (null != orderInvoiceInfoDtoPageInfo) {
                    orderInvoiceInfoDtos = orderInvoiceInfoDtoPageInfo.getList();
                    invoiceInfoDto.setRecordCount((int) (orderInvoiceInfoDtoPageInfo.getTotal()));
                    invoiceInfoDto.setPageCount(orderInvoiceInfoDtoPageInfo.getPages());
                }

                if (CollectionUtils.isNotEmpty(orderInvoiceInfoDtos)) {
                    orderInvoiceInfoDtos.forEach(orderInvoiceInfoDto -> {
                        String invoiceNo = "";
                        if (orderInvoiceInfoDto.getInvoiceInfo() != null) {
                            invoiceNo = orderInvoiceInfoDto.getInvoiceInfo().getTaxNumber();
                        }

                        String itemName = this.genInvoiceItemName(SubOrderTypeEnum.COUPON_ORDER, orderInvoiceInfoDto.getCouponSource(), orderInvoiceInfoDto);
                        InvoiceTicketInfo invoiceTicketInfo = InvoiceTicketInfoMapping.getInvoiceTicketInfo(orderInvoiceInfoDto,
                                "", "", "", "",
                                itemName, "", invoiceNo, true, true,
                                StringUtils.isBlank(orderInvoiceInfoDto.getSeatNo()) ? "" : orderInvoiceInfoDto.getSeatNo(), "", "", 0, "");

                        // VIP体验卡处理
                        if (ActivityVIPExperienceCardEnum.getVipExperienceCardTypeList().contains(orderInvoiceInfoDto.getCouponSource())) {
                            invoiceTicketInfo.setItemName("其他权益性无形资产-会员费");
                            invoiceTicketInfo.setCouponSource(orderInvoiceInfoDto.getCouponSource());
                            invoiceTicketInfo.setTotalPrice(orderInvoiceInfoDto.getInvoiceAmount().doubleValue());
                        }
                        //逾重行李特殊处理
                        if (reqParam.getOrderType() == 3 && CouponSourceEnum.EXCESSBAGGAGEINVOICE.getCode().equals(orderInvoiceInfoDto.getCouponSource())) {
                            invoiceTicketInfo = new InvoiceTicketInfo();
                            invoiceTicketInfo.setTicketNo(orderInvoiceInfoDto.getTicketNo());
                            invoiceTicketInfo.setCouponSource(orderInvoiceInfoDto.getCouponSource());
                            invoiceTicketInfo.setRute("");
                            invoiceTicketInfo.setItemName("逾重行李费");
                            invoiceTicketInfo.setInvoiceTitle(orderInvoiceInfoDto.getInvoiceTitle());
                            invoiceTicketInfo.setTotalPrice(orderInvoiceInfoDto.getInvoiceAmount().doubleValue());
                            invoiceTicketInfo.setExcessStatus(orderInvoiceInfoDto.getExcessStatus());
                            invoiceTicketInfo.setTicketDeliveryId(orderInvoiceInfoDto.getTicketDeliveryId());
                            invoiceTicketInfo.setEmail(orderInvoiceInfoDto.getEmail());
                            invoiceTicketInfo.setHasInvoiced(toCheckInvoice(orderInvoiceInfoDto.getExcessStatus()));
                            invoiceTicketInfo.setCancelReason(orderInvoiceInfoDto.getCancelReason());
                        }
                        if (StringUtils.isNotBlank(orderInvoiceInfoDto.getBundleName())) {
                            if (orderInvoiceInfoDto.getBundleName().contains("吉享礼券")) {
                                invoiceTicketInfo.setItemName("国内机票(吉享礼券优惠礼包)");
                            } else {
                                invoiceTicketInfo.setItemName(orderInvoiceInfoDto.getBundleName());
                            }
                            invoiceTicketInfo.setTotalPrice(orderInvoiceInfoDto.getAmount().doubleValue());
                        }
                        if (invoiceTicketInfo.isHasInvoiced()) {
                            invoicedOrderInfos.add(invoiceTicketInfo);
                        } else {
                            invoiceTicketInfos.add(invoiceTicketInfo);
                        }

                    });
                }

            } else if (UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_EMPTY.equals(responseDto.getResultCode())) {
                //按照需求无数据不提示错误

            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                if (StringUtils.isNotBlank(responseDto.getErrorInfo())) {
                    response.setResultInfo(responseDto.getErrorInfo());
                } else {
                    response.setResultInfo("查询订单信息失败，获取列表失败");
                }
                return response;
            }
            response.setObjData(invoiceInfoDto);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    /**
     * 对逾重行李和其他附加服务做代码分页
     *
     * @param orderInvoiceInfoDtoList
     * @param excessBaggageRespDTO
     * @param reqParam
     */
    private PageInfo<OrderInvoiceInfoDto> toPage(List<OrderInvoiceInfoDto> orderInvoiceInfoDtoList, ExcessBaggageRespDTO excessBaggageRespDTO, QueryOrderInvoiceRequest reqParam) {
        if (null == excessBaggageRespDTO || CollectionUtils.isEmpty(excessBaggageRespDTO.getTicketDeliveryList())) {
            return null;
        }

        Page<OrderInvoiceInfoDto> page;

        ArrayList<OrderInvoiceInfoDto> orderInvoiceInfoDtos = new ArrayList<>();

        orderInvoiceInfoDtos.addAll(orderInvoiceInfoDtoList);

        List<TicketDelivery> ticketDeliveryList = excessBaggageRespDTO.getTicketDeliveryList();
        ticketDeliveryList = ticketDeliveryList.stream().sorted(Comparator.comparing(TicketDelivery::getCreateDatetime, Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());
        for (TicketDelivery ticket : ticketDeliveryList
        ) {
            OrderInvoiceInfoDto orderInvoiceInfoDto = new OrderInvoiceInfoDto();
            orderInvoiceInfoDto.setCouponSource(CouponSourceEnum.EXCESSBAGGAGEINVOICE.getCode());
            orderInvoiceInfoDto.setTicketNo(ticket.getTicketNo());
            orderInvoiceInfoDto.setInvoiceAmount(new BigDecimal(ticket.getInvoiceAmount()));
            orderInvoiceInfoDto.setInvoiceTitle(ticket.getPayable());
            orderInvoiceInfoDto.setExcessStatus(toConvertStatus(ticket.getDeliveryState()));
            orderInvoiceInfoDto.setTicketDeliveryId(ticket.getTicketDeliveryId());
            orderInvoiceInfoDto.setEmail(ticket.getEmail());
            orderInvoiceInfoDto.setCancelReason(ticket.getCancelReason());
            orderInvoiceInfoDtos.add(orderInvoiceInfoDto);
        }

        int pageNum = reqParam.getPageNo();
        int pageSize = reqParam.getPageSize();

        //首先需要从数据库中一次性查询出全部数据，然后代码分页
        page = new Page<>(pageNum, pageSize);
        page.setTotal(orderInvoiceInfoDtos.size());
        page.setPages(orderInvoiceInfoDtos.size() / reqParam.getPageSize() + 1);
        //计算当前需要显示的数据下标起始值
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, orderInvoiceInfoDtos.size());
        //从连表中截取需要显示的子链表，并加入到Page
        page.addAll(orderInvoiceInfoDtos.subList(startIndex, endIndex));
        //以Page创建PageInfo
        return new PageInfo<>(page);

    }

    //获取逾重行李的全部数据
    private ExcessBaggageRespDTO toGainTheWholeExcessBaggage(QueryOrderInvoiceRequest reqParam, String ip) {
        ExcessBaggageRequest excessBaggageRequest = new ExcessBaggageRequest();
        excessBaggageRequest.setCreatorId(reqParam.getFfpId());
        excessBaggageRequest.setCreatorName(reqParam.getPassengerName());
        return unitOrderHttpInterfaceService.QueryExcessBaggageInvoice(excessBaggageRequest, ip);
    }

    /**
     * 获取发票项目名称
     *
     * @Param subOrderTypeEnum 传订单类型
     * @Param orderTypeInfo 机票订单传 TicketOrderSort 附加服务传 CouponSource
     */
    private String genInvoiceItemName(SubOrderTypeEnum subOrderTypeEnum, String orderTypeInfo, OrderInvoiceInfoDto orderInvoiceInfoDto) {
        if (orderTypeInfo == null) {
            return null;
        }
        String name = null;
        if (subOrderTypeEnum.equals(SubOrderTypeEnum.TICKET_ORDER)) {
            name = TicketOrderSort.genInvoiceItemName(orderInvoiceInfoDto.getTktNatSign(), TicketOrderSort.getByCode(orderTypeInfo));
        } else if (subOrderTypeEnum.equals(SubOrderTypeEnum.COUPON_ORDER)) {
            name = CouponSourceEnum.getByCode(orderTypeInfo) == null ?
                    "" : CouponSourceEnum.getByCode(orderTypeInfo).getCouponName();
            // http://zentao.juneyaoair.com/zentao/story-view-4767.html
            if (CouponSourceEnum.BRANDMEALS.getCode().equals(orderTypeInfo)) {
                // 根据Category判断是否为 机供品-毛毯
                if ("BM987D0E5F".equals(orderInvoiceInfoDto.getCategory())) {
                    name = CommodityEnum.S01574.getCommodityName();
                } else {
                    name = CommodityEnum.S01458.getCommodityName();
                }
            }else if (CouponSourceEnum.DISNEY.getCode().equals(orderTypeInfo)){
                name = CommodityEnum.S01037.getCommodityName();
            }
            //旧券
            if (StringUtils.isBlank(name)) {
                if ("HOBG".equals(orderTypeInfo)) {
                    name = "逾重行李券";
                } else if ("HOUP".equals(orderTypeInfo)) {
                    name = "升舱券";
                } else if ("HOLO".equals(orderTypeInfo)) {
                    name = "贵宾休息室";
                }
            }
        }
        if (PassengerTypeEnum.INF.getPassType().equals(orderInvoiceInfoDto.getPassengerType())) {
            name += "（婴儿）";
        }
        return name;
    }

    /**
     * 状态转换
     *
     * @param state
     * @return
     */
    private String toConvertStatus(String state) {
        if (StringUtils.isEmpty(state)) {
            return "";
        }
        switch (state) {
            case "未处理":
            case "一审通过": {
                return "0";
            }
            case "已取消": {
                return "1";
            }
            case "处理完成": {
                return "2";
            }
            default: {
                return "";
            }
        }
    }

    /**
     * 判断是否已开票
     *
     * @param state
     * @return
     */
    private boolean toCheckInvoice(String state) {
        if (StringUtils.isEmpty(state)) {
            return false;
        }
        if ("2".equals(state)) {
            return true;
        }
        return false;
    }

    /**
     * 过滤可开票的订单
     *
     * @param responseDto
     * @return
     */
    private List<OrderInvoiceInfoDto> filtInvoiceableOrders(OrderInvoiceResponseDto responseDto, int orderType) {
        if (responseDto == null) {
            return new ArrayList<>();
        }
        List<OrderInvoiceInfoDto> result = responseDto.getOrderInvoiceInfoList();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (CollectionUtils.isNotEmpty(result)) {
            Iterator<OrderInvoiceInfoDto> it = result.iterator();
            while (it.hasNext()) {
                OrderInvoiceInfoDto dto = it.next();
                //非人民币购买的无法开票
                if (!"Y".equals(dto.getInvoiceState()) || !"CNY".equals(dto.getCurrency())) {
                    log.info("1.非人民币支付，暂不支持开票,订单号：{}", dto.getOrderNo());
                    it.remove();
                    continue;
                }
                //附加服务
                if (SubOrderTypeEnum.COUPON_ORDER.getOrdrType().equals(dto.getSubOrderType())) {
                    String dateStr = dto.getUseDate();
                    if (StringUtils.isBlank(dateStr)) {
                        dateStr = dto.getEndDate();
                    }
                    //附加服务包含：逾重行李券，贵宾休息室券，升舱券
                    if (CouponSourceEnum.Upgrade.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.UPGRADECOUPON.getCode().equals(dto.getCouponSource())
                            || "HOUP"/*升舱券*/.equals(dto.getCouponSource())) {
                        //升舱券：有效期从使用之日起一个月内，可申请电子发票
                        try {
                            Date date = dateFormat.parse(dateStr);
                            if (DateUtils.dateDiff(new Date(), date) > 31) {
                                log.info("2.附加服务超过有效期，暂不支持开票,订单号：{} 权益使用日期：{}", dto.getOrderNo(), dateStr);
                                it.remove();
                            }
                        } catch (ParseException e) {
                            log.info("3.附加服务有效期校验异常，暂不支持开票,订单号：{} 权益使用日期：{}", dto.getOrderNo(), dateStr);
                            it.remove();
                        }
                    } else if (CouponSourceEnum.Lounge.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.Baggage.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.MailTravel.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.UPGRADEUNLIMITED.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.CHILDUNLIMITEDFLY.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.ADULTUNLIMITEDFLY.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.ONBOARDPRODUCT.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.BRANDMEALS.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.AIRPLANE_UPGRADE.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.UNLIMITED_FLY_V2.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.UNLIMITED_FLY_V2_SF.getCode().equals(dto.getCouponSource())
                            || "HOBG"/*逾重行李券*/.equals(dto.getCouponSource())
                            || "HOLO "/*休息室*/.equals(dto.getCouponSource())
                            || CouponSourceEnum.LOUNGECOUPON.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.BAGGAGECOUPON.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.COUPON.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.EXTRABAGGAGE.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.RESCHEDULE.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.PAYSEAT.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.MEMBERRENEWAL.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.MEMBERUPGRADE.getCode().equals(dto.getCouponSource())
                            || CouponSourceEnum.HOCAR.getCode().equals(dto.getCouponSource())
                            || handConfig.getThemeCouponList().contains(dto.getCouponSource())
                            || CouponSourceEnum.DISNEY.getCode().equals(dto.getCouponSource())
                            || ActivityVIPExperienceCardEnum.getVipExperienceCardTypeList().contains(dto.getCouponSource())) {
                        //逾重行李券，贵宾休息室券，邮递行程单：有效期从使用之日起一年内，可申请电子发票
                        try {
                            Date date = dateFormat.parse(dateStr);
                            if (DateUtils.dateDiff(new Date(), date) > 366) {
                                log.info("4.附加服务有效期校验异常，暂不支持开票,订单号：{} 权益使用日期：{}", dto.getOrderNo(), dateStr);
                                it.remove();
                            }
                        } catch (Exception e) {
                            log.info("5.附加服务有效期校验异常，暂不支持开票,订单号：{} 权益使用日期：{}", dto.getOrderNo(), dateStr);
                            it.remove();
                        }
                    } else {
                        log.info("6.暂不支持的附加服务类型，暂不支持开票,订单号：{} 附加服务类型：{}", dto.getOrderNo(), dto.getCouponSource());
                        it.remove();
                    }
                } else if (SubOrderTypeEnum.TICKET_ORDER.getOrdrType().equals(dto.getSubOrderType())) {
                    List<SubOrderInfoDto> subOrders = dto.getSubOrderList();
                    if (CollectionUtils.isNotEmpty(subOrders)) {
                        subOrders.sort((e1, e2) -> {
                            if (StringUtils.isAnyBlank(e1.getFlightDate(), e2.getFlightDate())) {
                                return 0;
                            } else {
                                try {
                                    Date date1 = dateFormat.parse(e1.getFlightDate());
                                    Date date2 = dateFormat.parse(e2.getFlightDate());
                                    return date1.before(date2) ? -1 : 1;
                                } catch (ParseException e) {
                                    // 排序失败不做处理
                                }
                                return 0;
                            }
                        });
                    } else {
                        log.info("7.机票类型不存在机票子订单信息，暂不支持开票,订单号：{} 订单信息：{}", dto.getOrderNo(), JSON.toJSONString(dto));
                        it.remove();
                        continue;
                    }
                    //普通机票
                    if (orderType == 1 && (TicketOrderSort.Normal.getCode().equals(dto.getTicketOrderSort())
                            || TicketOrderSort.UnCompanion.getCode().equals(dto.getTicketOrderSort()))) {
                        String lastFlightDate = subOrders.get(subOrders.size() - 1).getFlightDate();
                        try {
                            Date lastDate = dateFormat.parse(lastFlightDate);
                            //2023.12.25 普通机票可开票日期从31调整为366
                            if (DateUtils.dateDiff(new Date(), lastDate) > 366) {
                                log.info("8.超过机票可开票日期，暂不支持开票,订单号：{} 航班日期：{}", dto.getOrderNo(), lastFlightDate);
                                it.remove();
                            }
                        } catch (ParseException e) {
                            log.info("9.校验机票可开票日期失败，暂不支持开票,订单号：{} 航班日期：{}", dto.getOrderNo(), lastFlightDate);
                            it.remove();
                        }
                        //其余票可开票日期为一年
                    } else if (TicketOrderSort.Upgrade.getCode().equals(dto.getTicketOrderSort())
                            || TicketOrderSort.Refund.getCode().equals(dto.getTicketOrderSort())
                            || TicketOrderSort.Normal.getCode().equals(dto.getTicketOrderSort())
                            || TicketOrderSort.Change.getCode().equals(dto.getTicketOrderSort())
                            || TicketOrderSort.UnCompanion.getCode().equals(dto.getTicketOrderSort())) {
                        String lastFlightDate = subOrders.get(subOrders.size() - 1).getFlightDate();
                        try {
                            Date lastDate = dateFormat.parse(lastFlightDate);
                            if (DateUtils.dateDiff(new Date(), lastDate) > 366) {
                                log.info("10.开票日期校验不通过，暂不支持开票,订单号：{} 航班日期：{}", dto.getOrderNo(), lastFlightDate);
                                it.remove();
                            }
                        } catch (ParseException e) {
                            log.info("11.开票日期校验异常，暂不支持开票,订单号：{} 航班日期：{}", dto.getOrderNo(), lastFlightDate);
                            it.remove();
                        }
                    } else {
                        log.info("12.机票订单类型暂不支持，暂不支持开票,订单号：{} 航班日期：{}", dto.getOrderNo(), dto.getTicketOrderSort());
                        it.remove();
                    }
                } else {
                    log.info("13.子订单类型暂不支持，暂不支持开票,订单号：{} 航班日期：{}", dto.getOrderNo(), dto.getSubOrderType());
                    it.remove();
                }
            }
        }
        return result;
    }

    /**
     * 发送邮件
     *
     * <AUTHOR>
     * @Description
     * @Date 2019/5/5 9:01
     * @Param invoiceCode 发票代码
     * @Param invoiceNum 发票代码
     * @Param makeOutDate 开票时间 yyyy-MM-dd HH:mm:ss
     * @Param linkUrl 下载链接
     * @Param toMailAddress 收件人信箱
     * @Return
     **/
    private void sendMail(String invoiceCode, String invoiceNum, String makeOutDate, String linkUrl,
                          String toMailAddress) {
        List<EmailAttachment> attachments = new ArrayList<>();
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n")
                .append("\t<head>\n")
                .append("\t\t<style>\n")
                .append("\t\t\t.divcss5{text-indent:25px}\n")
                .append("\t\t\n")
                .append("\t\t</style>\n")
                .append("\t</head>\n")
                .append("\t<body>\n")
                .append("\t<div class=\"divcss5\">\n")
                .append("\t\t<p>您好！</p>\n")
                .append("\t\t<p>您提交的增值税电子普通发票请求已开具成功，本次生成的电子发票代码为")
                .append(invoiceCode).append("发票号码为").append(invoiceNum).append("，开票日期为").append(makeOutDate)
                .append("。详见附件，请查收。</p> \n")
                .append("\t\t<p>如有疑问，请勿回复本邮件，请直接致电95520咨询，感谢您选择吉祥航空。</p>\n")
                .append("\t</div>\n")
                .append("<a href='").append(linkUrl).append("'>下载发票</a>")
                .append("\t</body>\n")
                .append("</html>");
        MailSendUtil.sendMail("吉祥航空增值税电子普通发票", html.toString(), Collections.singletonList(toMailAddress), attachments
                , HandlerConstants.INVOICE_EMAIL_USER_NAME, null);
    }

    /**
     * 获取订单发票唯一票号
     * 规则：前缀3位+票号13
     * 共16位
     *
     * @return
     */
    private String genTicketSrcDocCode(int orderType, TicketOrderSort ticketOrderSort, String ticketNo) {
        String prefix = "";
        switch (ticketOrderSort) {
            case UnCompanion:
            case Normal:
                prefix = "JPK";
                break;
            case Refund:
                prefix = "TSF";
                break;
            case Upgrade:
                prefix = "SCF";
                break;
        }
        //退票费
        if (orderType == 2) {
            prefix = "TSF";
        }
        return prefix + ticketNo.replace("-", "");
    }

    /**
     * 前缀3位+订单号17位+券号16位
     * 共计36位
     */
    private String genCouponSrcDocCode(CommodityEnum commodityEnum, CouponSourceEnum couponSourceEnum, String orderNo, String couponNo) {
        String prefix = "";
        switch (commodityEnum) {
            case S01001:
            case S01001BAG:
                prefix = "JPK";
                break;
            case S01004:
                prefix = "YZQ";
                break;
            case S01008:
                prefix = "GBQ";
                break;
            case S01006:
                prefix = "SCQ";
                break;
            case S01034:
                prefix = "PSF";
                break;
            case S01458:
                prefix = "OBM";
                break;
            case S01037:
                prefix = "FWF";
                break;
        }
        if (CouponSourceEnum.UPGRADEUNLIMITED.equals(couponSourceEnum)) {
            prefix = "SCF";
        }
        return prefix + orderNo + couponNo;
    }

    @RequestMapping(value = "resendEmail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "重发邮件", notes = "重发邮件")
    public BaseResp resendEmail(@RequestBody @Validated BaseReq<ResendEmailRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<MileageRecordQueryResponse> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Invoice_ResendEmail";
        try {
            //校验参数
            if (bindingResult.hasErrors()) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return response;
            }
            //判断是否登录
            ResendEmailRequest reqParam = req.getRequest();
            boolean flag = this.checkKeyInfo(reqParam.getFfpId(),
                    reqParam.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return response;
            }
            OrderInvoiceRequestDto requestDto = new OrderInvoiceRequestDto();
            List<String> ticketOrderSorts = new ArrayList<>();
            switch (reqParam.getOrderType()) {
                //查询普通机票订单和升舱订单
                case 1:
                    if (StringUtils.isBlank(reqParam.getTicketNo())) {
                        throw new RequestParamErrorException(TICKET_NO_NOT_NULL_MSG);
                    }
                    reqParam.setTicketNo(reqParam.getTicketNo());
                    // 票号特殊处理
                    if (!reqParam.getTicketNo().contains("-")) {
                        String ticketNo = reqParam.getTicketNo().substring(0, 3) + "-" + reqParam.getTicketNo().substring(3);
                        reqParam.setTicketNo(ticketNo);
                    }
                    requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                    ticketOrderSorts.add(TicketOrderSort.Normal.getCode());
                    ticketOrderSorts.add(TicketOrderSort.Upgrade.getCode());
                    ticketOrderSorts.add(TicketOrderSort.UnCompanion.getCode());
                    break;
                //查询退票订单
                case 2:
                    if (StringUtils.isBlank(reqParam.getTicketNo())) {
                        throw new RequestParamErrorException(TICKET_NO_NOT_NULL_MSG);
                    }
                    reqParam.setTicketNo(reqParam.getTicketNo());
                    // 票号特殊处理
                    if (!reqParam.getTicketNo().contains("-")) {
                        String ticketNo = reqParam.getTicketNo().substring(0, 3) + "-" + reqParam.getTicketNo().substring(3);
                        reqParam.setTicketNo(ticketNo);
                    }
                    requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                    ticketOrderSorts.add(TicketOrderSort.Refund.getCode());
                    break;
                //查询其他服务
                case 3:
                    if (StringUtils.isBlank(reqParam.getOrderNo())) {
                        throw new RequestParamErrorException("订单号不能为空");
                    }
                    requestDto.setSubOrderType(SubOrderTypeEnum.COUPON_ORDER.getOrdrType());
                    requestDto.setCouponCode(reqParam.getInvoiceTicketInfo().getCouponCode());
                    requestDto.setPsgrName(null);// 券类产品不传旅客姓名
                    break;
                default:
                    throw new OperationFailedException("订单类型不正确，开票失败");
            }
            requestDto.setChannelCode(req.getChannelCode());
            requestDto.setVersion(SystemConstants.VERSION);
            requestDto.setUserNo(this.getChannelInfo(req.getChannelCode(), "10"));
            requestDto.setTktNo(reqParam.getTicketNo());
            requestDto.setSubOrderType(reqParam.getSubOrderType());
            requestDto.setTicketOrderSorts(ticketOrderSorts);
            requestDto.setOrderNo(reqParam.getOrderNo());
            requestDto.setPageSize(10);
            requestDto.setPageNo(1);
            //2021-07-23 判断打包券只有吉祥礼券开具电子发票的 配置活动编号到阿波罗
            List<String> invoiceActivityNos = handConfig.getInvoiceActivityNos();
            requestDto.setBundleIdList(invoiceActivityNos);
            OrderInvoiceResponseDto responseDto = unitOrderHttpInterfaceService.QueryOrderInvoice(requestDto, ip);
            if (null == responseDto) {
                response.setResultInfo(INVOKE_UNIT_ORDER_FAILED_MSG);
                response.setResultCode(WSEnum.ERROR.getResultCode());
                return response;
            }
            if (!UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_SUCCESS.equals(responseDto.getResultCode())) {
                if (UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_EMPTY.equals(responseDto.getResultCode())) {
                    response.setResultCode(WSEnum.NO_DATA.getResultCode());
                    response.setResultInfo(NO_INVOICE_DATA_MSG);
                    return response;
                }
                response.setResultCode(WSEnum.ERROR.getResultCode());
                if (StringUtils.isNotBlank(responseDto.getErrorInfo())) {
                    response.setResultInfo(responseDto.getErrorInfo());
                } else {
                    response.setResultInfo(WSEnum.ERROR.getResultInfo());
                }
                return response;
            }
            List<OrderInvoiceInfoDto> orderInvoiceInfoList = responseDto.getOrderInvoiceInfoList();
            if (CollectionUtils.isEmpty(orderInvoiceInfoList)) {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setResultInfo("订单不存在，重发邮件失败");
                return response;
            }
            boolean success = false;
            for (OrderInvoiceInfoDto orderInvoiceInfoDto : orderInvoiceInfoList) {
                if ((SubOrderTypeEnum.TICKET_ORDER.getOrdrType().equals(reqParam.getSubOrderType())
                        && orderInvoiceInfoDto.getTicketNo().equals(reqParam.getTicketNo()))
                        || (SubOrderTypeEnum.COUPON_ORDER.getOrdrType().equals(reqParam.getSubOrderType())
                        && orderInvoiceInfoDto.getCouponOrderId().equals(reqParam.getInvoiceTicketInfo().getCouponOrderId()))) {
                    if (null != orderInvoiceInfoDto.getInvoiceInfo()) {
                        InvoiceInfoDto invoiceInfo = orderInvoiceInfoDto.getInvoiceInfo();
                        if (StringUtils.isAnyBlank(invoiceInfo.getInvoiceCode(), invoiceInfo.getInvoiceNumber(), invoiceInfo.getInvoiceDate(),
                                invoiceInfo.getDownloadUrl())) {
                            throw new OperationFailedException("订单发票信息不正确，无法重新发送");
                        }
                        this.sendMail(invoiceInfo.getInvoiceCode(), invoiceInfo.getInvoiceNumber(), invoiceInfo.getInvoiceDate(),
                                invoiceInfo.getDownloadUrl(), reqParam.getRecMail());
                        InvoiceInfoMaintainDto invoiceInfoMaintainDto = new InvoiceInfoMaintainDto();
                        invoiceInfoMaintainDto.setOrderInvoiceId(invoiceInfo.getOrderInvoiceId());
                        invoiceInfoMaintainDto.setCompanyMail(reqParam.getRecMail());
                        this.unitOrderHttpInterfaceService.updateInvoiceInfo(requestDto.getChannelCode(), requestDto.getUserNo(),
                                requestDto.getVersion(), requestDto.getTicketOrderSorts(), invoiceInfoMaintainDto, ip);
                        success = true;
                    } else {
                        throw new OperationFailedException("无发票信息，发送邮件失败");
                    }
                }
            }
            if (success) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setResultInfo(WSEnum.ERROR.getResultInfo());
                response.setResultCode(WSEnum.ERROR.getResultCode());
            }
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    @RequestMapping(value = "resendExcessEmail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "逾重行李重发电子邮件", notes = "逾重行李重发电子邮件")
    public BaseResp resendExcessEmail(@RequestBody @Validated BaseReq<ResendExcessEmailReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<MileageRecordQueryResponse> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Invoice_ResendExcessEmail";
        try {
            //校验参数
            if (bindingResult.hasErrors()) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return response;
            }
            //判断是否登录
            ResendExcessEmailReq reqParam = req.getRequest();
            boolean flag = this.checkKeyInfo(reqParam.getFfpId(),
                    reqParam.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return response;
            }

            ExcessResendEmailResp resendResponse;
            PtResendExcessEmailReq ptResendExcessEmailReq = new PtResendExcessEmailReq();
            ptResendExcessEmailReq.setTicketDeliveryId(reqParam.getTicketDeliveryId());
            ptResendExcessEmailReq.setEmail(reqParam.getEmail());
            log.info("逾重行李重发电子发票,请求参数:{}", JsonUtil.objectToJson(ptResendExcessEmailReq));
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            String result = this.invokePost(ptResendExcessEmailReq, SystemConstants.UNIT_ORDER_URL + SystemConstants.EXCESSBAGGAGE_RESEND_EMAIL, headMap);
            log.info("逾重行李重发电子发票,响应结果:{}", result);
            if (StringUtils.isNotBlank(result)) {
                resendResponse = (ExcessResendEmailResp) JsonUtil.jsonToBean(result, ExcessResendEmailResp.class);
                if (TICKET_QUERY_SUCCESS_CODE.equals(resendResponse.getResultCode())) {
                    response.setResultCode(WSEnum.SUCCESS.getResultCode());
                    response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    return response;
                } else {
                    response.setResultCode(resendResponse.getResultCode());
                    response.setResultInfo(resendResponse.getErrorInfo());
                    return response;
                }
            } else {
                response.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
                response.setResultInfo(WSEnum.NETWORK_ERROR.getResultInfo());
                return response;
            }
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    /**
     * 查询其他客票，返回哪类客票有数据
     *
     * @param request
     * @param req
     * @return
     */
    @InterfaceLog
    @RequestMapping(value = "findOtherTicket", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询其他客票数据", notes = "查询其他客票数据")
    public BaseResp findOtherTicket(@RequestBody @Validated BaseReq<QueryOrderInvoiceRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<Integer> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        try {
            //校验参数
            if (bindingResult.hasErrors()) {
                throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            //判断是否登录
            QueryOrderInvoiceRequest reqParam = req.getRequest();
            boolean flag = this.checkKeyInfo(reqParam.getFfpId(),
                    reqParam.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                throw new CheckErrorException("参数校验失败");
            }
            OrderInvoiceRequestDto requestDto = new OrderInvoiceRequestDto();
            requestDto.setChannelCode(req.getChannelCode());
            requestDto.setVersion(SystemConstants.VERSION);
            requestDto.setUserNo(this.getChannelInfo(req.getChannelCode(), "10"));
            requestDto.setPageNo(1);
            requestDto.setPageSize(10);
            requestDto.setPsgrName(reqParam.getPassengerName());
            if (StringUtils.isNotBlank(reqParam.getTicketNo())) {
                //票号类型
                String certType = CertNoUtil.getCertTypeByCertNo(reqParam.getTicketNo());
                if ("NI".equals(certType)) {
                    requestDto.setIdNbr(reqParam.getTicketNo());
                } else {
                    if (!reqParam.getTicketNo().contains("-")) {
                        String ticketNo = reqParam.getTicketNo().substring(0, 3) + "-" + reqParam.getTicketNo().substring(3);
                        requestDto.setTktNo(ticketNo);
                    } else {
                        requestDto.setTktNo(reqParam.getTicketNo());
                    }
                }
            } else {
                requestDto.setFfpId(reqParam.getFfpId());
            }
            boolean hasData = false;
            //查询其他客票退可能查询出普通机票、票费或者行李券
            for (int i = 1; i <= 3; i++) {
                List<String> ticketOrderSorts = new ArrayList<>();
                switch (i) {
                    //查询普通机票订单和升舱订单
                    case 1:
                        requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                        ticketOrderSorts.add(TicketOrderSort.Normal.getCode());
                        ticketOrderSorts.add(TicketOrderSort.Change.getCode());
//                        ticketOrderSorts.add(TicketOrderSort.Upgrade.code); //升舱改期暂时不开票
                        ticketOrderSorts.add(TicketOrderSort.UnCompanion.getCode());
                        break;
                    //查询退票订单
                    case 2:
                        requestDto.setSubOrderType(SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                        ticketOrderSorts.add(TicketOrderSort.Refund.getCode());
                        break;
                    //查询其他服务
                    case 3:
                        requestDto.setSubOrderType(SubOrderTypeEnum.COUPON_ORDER.getOrdrType());
                        requestDto.setPsgrName(null);// 券类产品无旅客姓名
                        break;
                    default:
                        throw new OperationFailedException("查询类型不正确，获取列表失败");
                }
                requestDto.setTicketOrderSorts(ticketOrderSorts);
                OrderInvoiceResponseDto responseDto = unitOrderHttpInterfaceService.QueryOrderInvoice(requestDto, ip);
                if (null != responseDto && UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_SUCCESS.equals(responseDto.getResultCode())) {
                    List<OrderInvoiceInfoDto> orderInvoiceInfoDtos = this.filtInvoiceableOrders(responseDto, i);
                    if (CollectionUtils.isNotEmpty(orderInvoiceInfoDtos)) {
                        hasData = true;
                        response.setObjData(i);
                        break;
                    }
                }
            }
            if (hasData) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setResultCode(WSEnum.NO_DATA.getResultCode());
                response.setResultInfo(NO_INVOICE_DATA_MSG);
            }
        } catch (Exception e) {
            this.logError(response, MdcUtils.getRequestId(), ip, req, e);
        }
        return response;
    }

    @RequestMapping(value = "/checkTickStatus", method = RequestMethod.POST)
    @ApiOperation(value = "检验客票状态", notes = "检验客票状态")
    public BaseResp<Boolean> checkTickStatus(@RequestBody TicketStatusRequest ticketStatusRequest, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<Boolean> response = new BaseResp<>();
        //校验参数
        if (bindingResult.hasErrors()) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return response;
        }
        String clientIp = this.getClientIP(request);
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(ticketStatusRequest.getFfpId(), ticketStatusRequest.getLoginKeyInfo(), ticketStatusRequest.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }

        try {
            if (ticketStatusRequest.getTicketNo().length() < 3) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), "客票长度不符，请重新填写");
            }
            TicketListInfoResponse ticketListInfo = this.queryTicket(ticketStatusRequest.getTicketNo(), clientIp);
            if (null == ticketListInfo || !TICKET_QUERY_SUCCESS_CODE.equals(ticketListInfo.getResultCode())) {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setResultInfo("您的客票信息输入有误，请检查后重新填写");
                return response;
            }

            List<PtIBETicketInfo> ptIBETicketInfos = ticketListInfo.getIBETicketInfoList();
            if (CollectionUtils.isEmpty(ptIBETicketInfos)) {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setResultInfo("机票暂无航段信息，无法开票");
                return response;
            }

            PtIBETicketInfo ptIBETicketInfo = ptIBETicketInfos.get(0);
            if (null != ptIBETicketInfo) {
                List<PtSegmentInfo> segmentInfoList = ptIBETicketInfo.getSegmentInfoList();
                if (CollectionUtils.isNotEmpty(segmentInfoList)) {
                    PtSegmentInfo ptSegmentInfo = segmentInfoList.get(0);

                    //客票信息是否有误
                    if (StringUtils.isNotEmpty(ptSegmentInfo.getOperationAirline()) && StringUtils.isNotEmpty(ptSegmentInfo.getMarketingAirline()) && !"HO".equals(ptSegmentInfo.getOperationAirline())) {
                        response.setResultCode(WSEnum.ERROR.getResultCode());
                        response.setResultInfo("您的客票非吉祥航空承运客票，请咨询承运航司相关情况");
                        return response;
                    }

                    //三方出票，吉祥承运，需联系95520开票
                    if (StringUtils.isNotEmpty(ptSegmentInfo.getOperationAirline()) && StringUtils.isNotEmpty(ptSegmentInfo.getMarketingAirline()) && "HO".equals(ptSegmentInfo.getOperationAirline())) {
                        response.setResultCode(WSEnum.ERROR.getResultCode());
                        response.setResultInfo("您的客票为代码共享航班，请致电95520申请发票");
                        return response;
                    }

                    //行程是否结束
                    if (!HandlerConstants.USED_FLOWN.equals(ptSegmentInfo.getTicketStatus())) {
                        response.setResultCode(WSEnum.CANNOT_QUERY_USE_TICKET.getResultCode());
                        response.setResultInfo("您的行程未完成，请在旅行结束后提交申请");
                        return response;
                    }

                    //客票是否过期
                    if (DateUtils.checkDateLimit(DateUtils.toDate(ptSegmentInfo.getDepTime()), 366)) {
                        response.setResultCode(WSEnum.ERROR.getResultCode());
                        response.setResultInfo("客票已过期，请联系95520处理");
                        return response;
                    }

                } else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setResultInfo("您的客票信息输入有误，请检查后重新填写");
                    return response;
                }
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setResultInfo("您的客票信息输入有误，请检查后重新填写");
                return response;
            }
        } catch (CommonException commonException) {
            response.setResultCode(commonException.getResultCode());
            response.setResultInfo(commonException.getErrorMsg());
            return response;
        }
        response.setResultCode(WSEnum.SUCCESS.getResultCode());
        response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return response;

    }

    private TicketListInfoResponse queryTicket(String ticketNo, String clientIp) {
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, "MOBILE", "");
        ticketInfoRequest.setCertType("TN");
        ticketInfoRequest.setTicketNo(ticketNo);
        TicketListInfoResponse ticketListInfoResponse = null;
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_TICKET_INFO;
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        HttpResult result = this.doPostClient(ticketInfoRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (!result.isResult() || StringUtil.isNullOrEmpty(result.getResponse())) {
            ticketListInfoResponse = new TicketListInfoResponse();
            ticketListInfoResponse.setErrorInfo("获取客票信息为空！");
            ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
        } else {
            try {
                ticketListInfoResponse = (TicketListInfoResponse) JsonUtil.jsonToBean(result.getResponse(), TicketListInfoResponse.class);
            } catch (Exception ex) {
                log.error("请求参数：{}，返回结果：{}，错误信息：", JsonUtil.objectToJson(ticketInfoRequest), result, ex);
            }
        }
        return ticketListInfoResponse;
    }


    @RequestMapping(value = "excessBaggageInvoice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "逾重行李开票申请接口", notes = "逾重行李开票申请接口")
    @InterfaceLog
    @SuppressWarnings("rawtypes")
    public BaseResp excessBaggageInvoice(@RequestBody @Validated BaseReq<ExcessBaggageInvoiceReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp response = new BaseResp<>();
        response.setResultCode(WSEnum.SUCCESS.getResultCode());
        response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        try {
            //校验参数
            if (bindingResult.hasErrors()) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return response;
            }

            if (req.getChannelCode().equals("MWEB")) {
                req.setChannelCode("MOBILE");
            }
            //判断是否登录
            ExcessBaggageInvoiceReq excessBaggageInvoiceReq = req.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(excessBaggageInvoiceReq.getFfpId(), excessBaggageInvoiceReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return response;
            }

            //抬头
            String buyerName = excessBaggageInvoiceReq.getBuyerName();
            //抬头类型

            ExcessBaggageReqDTO excessBaggageReqDTO = new ExcessBaggageReqDTO();

            if ("个人".equals(buyerName)) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setResultInfo("抬头不能为\"个人\"");
                return response;
            }
            //去除税号空格
            if (StringUtils.isNotBlank(excessBaggageInvoiceReq.getBuyerTaxID())) {
                excessBaggageInvoiceReq.setBuyerTaxID(excessBaggageInvoiceReq.getBuyerTaxID().trim());
            }
            //去除账户空格
            if (StringUtils.isNotBlank(excessBaggageInvoiceReq.getBankAccount())) {
                excessBaggageInvoiceReq.setBankAccount(excessBaggageInvoiceReq.getBankAccount().trim());
            }
            if (StringUtils.isNotBlank(excessBaggageInvoiceReq.getCompanyPhone()) && excessBaggageInvoiceReq.getCompanyPhone().length() > 30) {
                throw new RequestParamErrorException("电话号码太长！");
            }

            excessBaggageReqDTO.setType("电子发票");
            ItemsEntity itemsEntity = new ItemsEntity();
            itemsEntity.setChildContent("S01004");
            itemsEntity.setChildAmount(String.valueOf(excessBaggageInvoiceReq.getAmount()));
            ArrayList<ItemsEntity> itemsEntities = new ArrayList<>();
            itemsEntities.add(itemsEntity);
            excessBaggageReqDTO.setItems(itemsEntities);
            excessBaggageReqDTO.setCustomerName(excessBaggageInvoiceReq.getName());
            excessBaggageReqDTO.setHandphoneNumber(excessBaggageInvoiceReq.getMobile());
            excessBaggageReqDTO.setElectronicTicketNo(excessBaggageInvoiceReq.getTicketNo());
            excessBaggageReqDTO.setTypeCode("电子发票");
            excessBaggageReqDTO.setSendType("邮件");
            excessBaggageReqDTO.setPayableType(changePayableType(excessBaggageInvoiceReq.getBuyerTaxType()));
            excessBaggageReqDTO.setDeliveryFee(0L);
            excessBaggageReqDTO.setInvoiceRemark(excessBaggageInvoiceReq.getBaggageTicketNo());
            excessBaggageReqDTO.setEmail(excessBaggageInvoiceReq.getRecMail());
            excessBaggageReqDTO.setTotalInvoice(String.valueOf(excessBaggageInvoiceReq.getAmount()));
            excessBaggageReqDTO.setPayable(excessBaggageInvoiceReq.getBuyerName());
            excessBaggageReqDTO.setTaxNumber(excessBaggageInvoiceReq.getBuyerTaxID());
            excessBaggageReqDTO.setBank(excessBaggageInvoiceReq.getBank());
            excessBaggageReqDTO.setBankAccount(excessBaggageInvoiceReq.getBankAccount());
            excessBaggageReqDTO.setCompanyAddress(excessBaggageInvoiceReq.getCompanyAddress());
            excessBaggageReqDTO.setCompanyPhone(excessBaggageInvoiceReq.getCompanyPhone());
            excessBaggageReqDTO.setTextRemark(toContactText(excessBaggageInvoiceReq.getEnclosureUrl()));
            excessBaggageReqDTO.setCreatorId(excessBaggageInvoiceReq.getFfpId());
            String name;

            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
            PtApiCRMRequest ptApiCRMRequest = buildCommReqNoToken(request, req.getChannelCode());
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(excessBaggageInvoiceReq.getFfpCardNo());
            ptMemberDetailRequest.setRequestItems(items);
            ptApiCRMRequest.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
            if (ptCRMResponse.getCode() == 0) {
                name = CRMReqUtil.formatMemName(ptCRMResponse.getData());
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("会员信息不存在");
                return response;
            }
            excessBaggageReqDTO.setCreatorName(name);
            excessBaggageReqDTO.setChannelNo(req.getChannelCode());

            String url = HandlerConstants.URL_FARE_API + HandlerConstants.EXCESSBAGGAGE_INVOICE_URL;

            // 调用接口请求数据
            log.info("{} 逾重行李开票请求参数：【{}】", url, JSON.toJSONString(excessBaggageReqDTO));
            String result = HttpsUtil.invokePost(excessBaggageReqDTO, url);
            log.info("{} 逾重行李开票返回结果：【{}】", url, result);
            // 接口返回为空，返回异常
            if (StringUtils.isBlank(result)) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), WSEnum.ERROR.getResultInfo());
            }

            TypeReference<BaseResultDTO> typeReference = new TypeReference<BaseResultDTO>() {
            };
            // 解析数据
            BaseResultDTO baseResultDTO = JSON.parseObject(result, typeReference);
            // 检查数据
            if (null == baseResultDTO) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), WSEnum.ERROR.getResultInfo());
            }
            // 检查返回编码是否返回成功
            if (!UnitOrderHttpInterfaceService.UNIT_ORDER_RESULT_CODE_SUCCESS.equals(baseResultDTO.getResultCode())) {
                String code = StringUtils.isBlank(baseResultDTO.getResultCode()) ? WSEnum.ERROR.getResultCode() : baseResultDTO.getResultCode();
                String errorMessage = StringUtils.isBlank(baseResultDTO.getErrorMsg()) ? "发票开具失败，请稍后重试！！" : baseResultDTO.getErrorMsg();
                throw new CommonException(code, errorMessage);
            }

        } catch (CommonException ce) {
            response.setResultCode(ce.getResultCode());
            response.setErrorInfo(ce.getErrorMsg());
            return response;
        } catch (Exception e) {
            log.error("开票申请异常:{}", e.toString());
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR.getResultCode());
            return response;
        }
        return response;
    }

    /**
     * 将图片地址展示成富文本信息
     *
     * @return
     */
    private String toContactText(String originalUrl) {
        if (StringUtils.isEmpty(originalUrl)) {
            return "";
        }

        return "<p><img src=" + originalUrl + "></p>";

    }


    private String changePayableType(String origin) {
        if (null == origin || origin.length() == 0) {
            return "";
        }
        if ("1".equals(origin)) {
            return "企业单位";
        } else if ("9".equals(origin)) {
            return "个人/非企业单位";
        }
        return "";
    }


}
