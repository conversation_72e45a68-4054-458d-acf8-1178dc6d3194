package com.juneyaoair.mobile.handler.controller;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.request.BaseRequestDTO;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.request.ibe.ParamCancelSeat;
import com.juneyaoair.baseclass.request.ibe.ParamDetrInfo;
import com.juneyaoair.baseclass.request.ibe.ParamReserveSeat;
import com.juneyaoair.baseclass.request.ibe.ParamSeatChartInfo;
import com.juneyaoair.baseclass.request.newCheckinSeat.CancelSeatRequest;
import com.juneyaoair.baseclass.request.newCheckinSeat.CustomerCertRequest;
import com.juneyaoair.baseclass.request.newCheckinSeat.ReserveSeatRequest;
import com.juneyaoair.baseclass.response.console.BassResponse;
import com.juneyaoair.baseclass.response.ibe.*;
import com.juneyaoair.baseclass.response.newCheckinSeat.DetrSegInfoResponse;
import com.juneyaoair.baseclass.response.newCheckinSeat.DetrSegmentInfo;
import com.juneyaoair.baseclass.response.newCheckinSeat.DetrSegmentInfosResponse;
import com.juneyaoair.cuss.dto.booking.request.seat.SeatChartRequestDTO;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.flight.AirPortInfo;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 16:02 2018/6/26
 * @Modified by:
 */
@RequestMapping("newSelectSeat")
@RestController
public class NewSelectSeatController extends BassController{
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    private static final String ERROR_MESSAGE = "您的访问出意外啦！";

    private static final String VERSION = "2.0";
    //根据证件号获取航段信息
    @RequestMapping(value = "getDetrSegInfo", method = RequestMethod.POST)
    public DetrSegInfoResponse getDetrSegInfo(@RequestBody ParamDetrInfo paramDetrInfo, HttpServletRequest request) {
        DetrSegInfoResponse response = new DetrSegInfoResponse();
        String reqId="getDetrSegInfo_"+ StringUtil.newGUID();
        String logStr = JsonUtil.objectToJson(paramDetrInfo);
        log.debug("请求号:{}, 根据证件号获取航段信息 request:{}", reqId, logStr);
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(paramDetrInfo.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = paramDetrInfo.getClientIp();
            if (!this.checkToken(paramDetrInfo.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                response.setStatus(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setMessage(ERROR_MESSAGE);
                return response;
            }
        }
        if (!this.chkDayVisit(clientIp, HandlerConstants.CHECKIN_SELECT_SOURCE, "SeatSelect", 120)) {
            response.setStatus(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setMessage("访问过于频繁，请稍后再试！");
            return response;
        }

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ParamDetrInfo>> violations = validator.validate(paramDetrInfo);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setStatus(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setMessage(violations.iterator().next().getMessage());
            return response;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(paramDetrInfo.getFfpId(), paramDetrInfo.getLoginKeyInfo(), paramDetrInfo.getChannelCode());
        if (!flag) {
            response.setStatus(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setMessage(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        //visa卡号验证
        if (!StringUtil.isNullOrEmpty(paramDetrInfo.getVisaCardNo()) && !this.checkVisaCardNo(paramDetrInfo.getVisaCardNo())) {
            response.setStatus(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setMessage("用户VISA卡号验证不通过");
            return response;
        }
       //请求参数
        BaseRequestDTO<CustomerCertRequest> detrMemInfo = new BaseRequestDTO<>();
        CustomerCertRequest customerCertRequestDTO = new CustomerCertRequest();
        BeanUtils.copyNotNullProperties(paramDetrInfo,customerCertRequestDTO);
        detrMemInfo.setVersion(VERSION);
        detrMemInfo.setIp(clientIp);
        detrMemInfo.setChannelCode(paramDetrInfo.getChannelCode());
        detrMemInfo.setUserNo(getChannelInfo(paramDetrInfo.getChannelCode(),"10"));
        detrMemInfo.setRequest(customerCertRequestDTO);
        try {
            HttpResult result = doPostClient(detrMemInfo, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_GET_DETR_SEG_INFO);
            log.debug("根据证件号获取航段信息Response:{}", result);
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                BaseResultDTO<DetrSegmentInfosResponse> resultDTO  = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, DetrSegmentInfosResponse.class);
                DetrSegmentInfosResponse detrlist;
                if (resultDTO.getResultCode() .equals("1001")) {
                    detrlist =resultDTO.getResult();
                    if (null == detrlist || detrlist.getDetrSegmentInfos().isEmpty()) {
                        response.setStatus(WSEnum.NO_DATA.getResultCode());
                        response.setMessage(WSEnum.NO_DATA.getResultInfo());
                        return response;
                    }else{
                        for(DetrSegmentInfo detrSegmentInfo:detrlist.getDetrSegmentInfos()){
                            detrSegmentInfo.setDepCityName(getCityName(detrSegmentInfo.getDepAirportCode()));
                            detrSegmentInfo.setArrCityName(getCityName(detrSegmentInfo.getArrAirportCode()));
                            //VISA卡号
                            detrSegmentInfo.setVisaCardNo(StringUtil.isNullOrEmpty(paramDetrInfo.getVisaCardNo())?"":paramDetrInfo.getVisaCardNo());
                        }
                        response.setStatus(WSEnum.SUCCESS.getResultCode());
                        response.setMessage(WSEnum.SUCCESS.getResultInfo());
                        response.setData(detrlist.getDetrSegmentInfos());
                        String url=HandlerConstants.MWEB_URL+"/newCheckIn/select_seat_flight.html";//APP跳转地址
                        Map param = new HashMap<>();
                        param.put("reqId",reqId);
                        param.put("sign",this.createAccesskey(reqId,HandlerConstants.ACCESSSECRET));
                        String paramGet=StringUtil.assemblyGETParam(param);
                        url=url+"?"+paramGet;
                        url= URLEncoder.encode(url,"UTF-8");
                        response.setMessage(url);
                        response.setUserInfo(new UserInfo(paramDetrInfo.getFfpId(),paramDetrInfo.getFfpCardNo(),paramDetrInfo.getLoginKeyInfo()));
                        response.setSeatPassInfo(new SeatPassInfo(paramDetrInfo.getPassengerNm(),paramDetrInfo.getCertNo(),paramDetrInfo.getPhoneNum()));
                        apiRedisService.putData("SELECT_SEAT:" + reqId, JsonUtil.objectToJson(response), 2 * 60L);//APP查询结果保存
                        String logStr2 = JsonUtil.objectToJson(response);
                        log.info("请求号:{},响应结果:{}", reqId, logStr2);
                    }
                }else{
                    response.setStatus(WSEnum.ERROR.getResultCode());
                    response.setMessage(resultDTO.getErrorMsg());
                    return response;
                }
            } else {
                response.setStatus(WSEnum.ERROR.getResultCode());
                response.setMessage("获取航段信息错误！");
                return response;
            }
        } catch (Exception e) {
            log.error("根据证件号获取航段信息Error" + e.getMessage(), e);
            response.setStatus(WSEnum.ERROR.getResultCode());
            response.setMessage(WSEnum.ERROR.getResultInfo());
            return response;
        }
        return response;
    }


    //获取座位图
    @RequestMapping(value = "getSeatChart", method = RequestMethod.POST)
    public AdmSeatChartResponse getSeatChart(@RequestBody ParamSeatChartInfo paramSeatChartInfo, HttpServletRequest request) {
        AdmSeatChartResponse response = new AdmSeatChartResponse();
        String logStr = JsonUtil.objectToJson(paramSeatChartInfo);
        log.debug("查询飞机座位图信息 request:{}", logStr);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ParamSeatChartInfo>> violations = validator.validate(paramSeatChartInfo);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setStatus(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setMessage(violations.iterator().next().getMessage());
            return response;
        }
        //验证IP
        String clientIp;
        if (StringUtil.isNullOrEmpty(paramSeatChartInfo.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = paramSeatChartInfo.getClientIp();
            if (!this.checkToken(paramSeatChartInfo.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                response.setStatus(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setMessage(ERROR_MESSAGE);
                return response;
            }
        }
        if (!this.chkDayVisit(clientIp, HandlerConstants.CHECKIN_SELECT_SOURCE, "SeatSelect", 120)) {
            response.setStatus(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setMessage("访问过于频繁，请稍后再试！");
            return response;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(paramSeatChartInfo.getFfpId(), paramSeatChartInfo.getLoginKeyInfo(), paramSeatChartInfo.getChannelCode());
        if (!flag) {
            response.setStatus(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setMessage(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
     // 请求参数
        BaseRequestDTO<SeatChartRequestDTO> reqBean = new  BaseRequestDTO<>();
        SeatChartRequestDTO seatChartRequestDTO = new SeatChartRequestDTO();
        BeanUtils.copyNotNullProperties(paramSeatChartInfo, seatChartRequestDTO);
        seatChartRequestDTO.setOrgCity(paramSeatChartInfo.getDepAirportCode());
        seatChartRequestDTO.setDstCity(paramSeatChartInfo.getArrAirportCode());
        reqBean.setIp(clientIp);
        reqBean.setVersion(VERSION);
        reqBean.setChannelCode(paramSeatChartInfo.getChannelCode());
        reqBean.setUserNo(getChannelInfo(paramSeatChartInfo.getChannelCode(),"10"));
        reqBean.setRequest(seatChartRequestDTO);
        try {
            HttpResult result = doPostClient(reqBean, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_SELECT_SEAT_CHART);
            log.debug("查询飞机座位图信息response:{}", result);
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                //返回结果转换
                BaseResultDTO<AdmSeatChart> resultDTO;
                resultDTO  = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, AdmSeatChart.class);
                if (resultDTO.getResultCode().equals("1001")) {
                    AdmSeatChart admSeatChart =resultDTO.getResult();
                    if(null != admSeatChart){
                        if(!StringUtil.isNullOrEmpty(paramSeatChartInfo.getVisaCardNo()) && checkVisaCardNo(paramSeatChartInfo.getVisaCardNo())){
                            //visa客户选座，开放C状态
                            admSeatChart = checkNotVisaSeatOpen(admSeatChart,true);
                        }else{
                            //普通客户不可选C状态座位
                            admSeatChart = checkNotVisaSeatOpen(admSeatChart,false);
                        }
                        response.setStatus(WSEnum.SUCCESS.getResultCode());
                        response.setMessage(WSEnum.SUCCESS.getResultInfo());
                        response.setData(admSeatChart);
                    }else{
                        response.setStatus(WSEnum.NO_DATA.getResultCode());
                        response.setMessage(WSEnum.NO_DATA.getResultInfo());
                        return response;
                    }
                }else{
                    response.setStatus(WSEnum.ERROR.getResultCode());
                    response.setMessage(resultDTO.getErrorMsg());
                    return response;
                }
            } else {
                response.setStatus(WSEnum.ERROR.getResultCode());
                response.setMessage("查询飞机座位图出错");
                return response;
            }
        } catch (Exception e) {
            log.error("查询飞机座位图信息error:" + e.getMessage(), e);
            response.setStatus(WSEnum.ERROR.getResultCode());
            response.setMessage(WSEnum.ERROR.getResultInfo());
            return response;
        }
        return response;
    }

    /**
     * visa用户才开放C状态座位，否则不可选
     * @param admSeatChart
     * @return
     */
    private AdmSeatChart checkNotVisaSeatOpen(AdmSeatChart admSeatChart,boolean visaFlag){
        for(SeatChart seatChart : admSeatChart.getSeatMaplist()){
            if(seatChart.getSeatStatus() == 'C'){
                if(visaFlag){
                    seatChart.setSeatStatus('*');
                }else{
                    seatChart.setSeatStatus('X');
                }
            }
        }
        return admSeatChart;
    }


    //预定座位
    @RequestMapping(value = "reserveSeat", method = RequestMethod.POST)
    public BassResponse reserveSeat(@RequestBody ParamReserveSeat paramReserveSeat, HttpServletRequest request) {
        BassResponse response = new BassResponse();
        String logStr = JsonUtil.objectToJson(paramReserveSeat);
        log.debug("预定座位Request:{}", logStr);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ParamReserveSeat>> violations = validator.validate(paramReserveSeat);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(paramReserveSeat.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = paramReserveSeat.getClientIp();
            if (!this.checkToken(paramReserveSeat.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo(ERROR_MESSAGE);
                return response;
            }
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(paramReserveSeat.getFfpId(), paramReserveSeat.getLoginKeyInfo(), paramReserveSeat.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        //visa卡号验证
        if (!StringUtil.isNullOrEmpty(paramReserveSeat.getVisaCardNo()) && !this.checkVisaCardNo(paramReserveSeat.getVisaCardNo())) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo("用户VISA卡号验证不通过");
            return response;
        }
        //请求参数
        BaseRequestDTO<ReserveSeatRequest> reserveSeatBean = new BaseRequestDTO<>();
        ReserveSeatRequest requestDTO = new ReserveSeatRequest();
        BeanUtils.copyNotNullProperties(paramReserveSeat, requestDTO);
        reserveSeatBean.setIp(clientIp);
        reserveSeatBean.setVersion(VERSION);
        reserveSeatBean.setChannelCode(paramReserveSeat.getChannelCode());
        reserveSeatBean.setUserNo(getChannelInfo(paramReserveSeat.getChannelCode(),"10"));
        reserveSeatBean.setRequest(requestDTO);
        try {
            //调预定接口
            HttpResult result = doPostClient(reserveSeatBean, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_CHECKIN_RESERVE_SEAT);
            log.debug("预定座位response:{}", result);
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                BaseResultDTO reserveResult = (BaseResultDTO) JsonUtil.jsonToBean(result.getResponse(), BaseResultDTO.class);
                if (reserveResult.getResultCode().equals("1001")) {
                    response.setResultCode(WSEnum.SUCCESS.getResultCode());
                    response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setErrorInfo(reserveResult.getErrorMsg());
                    return response;
                }
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("预定座位出错");
                return response;
            }
        } catch (Exception e) {
            log.error("预定座位Error:" + e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return response;
        }
        return response;
    }

    //取消座位
    @RequestMapping(value = "cancelSeat", method = RequestMethod.POST)
    public BassResponse cancelSeat(@RequestBody ParamCancelSeat paramCancelSeat, HttpServletRequest request) {
        BassResponse response = new BassResponse();
        String logStr = JsonUtil.objectToJson(paramCancelSeat);
        log.debug("取消选座Request:{}", logStr);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ParamCancelSeat>> violations = validator.validate(paramCancelSeat);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(paramCancelSeat.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = paramCancelSeat.getClientIp();
            if (!this.checkToken(paramCancelSeat.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setErrorInfo(ERROR_MESSAGE);
                return response;
            }
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(paramCancelSeat.getFfpId(), paramCancelSeat.getLoginKeyInfo(), paramCancelSeat.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        //请求参数
        BaseRequestDTO<CancelSeatRequest> cancelSeatBean = new BaseRequestDTO<>();
        CancelSeatRequest cancelSeatRequestDTO = new CancelSeatRequest();
        BeanUtils.copyNotNullProperties(paramCancelSeat, cancelSeatRequestDTO);
        cancelSeatBean.setIp(clientIp);
        cancelSeatBean.setVersion(VERSION);
        cancelSeatBean.setChannelCode(paramCancelSeat.getChannelCode());
        cancelSeatBean.setUserNo(getChannelInfo(paramCancelSeat.getChannelCode(),"10"));
        cancelSeatBean.setRequest(cancelSeatRequestDTO);
        try {
            //调预定接口
            HttpResult result = doPostClient(cancelSeatBean, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_CHECKIN_CANCEL_SEAT);
            log.debug("取消选座response:{}", result);
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                BaseResultDTO cancelResult = (BaseResultDTO) JsonUtil.jsonToBean(result.getResponse(), BaseResultDTO.class);
                if (cancelResult.getResultCode().equals("1001")) {
                    response.setResultCode(WSEnum.SUCCESS.getResultCode());
                    response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setErrorInfo(cancelResult.getErrorMsg());
                    return response;
                }
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("取消选座出错");
                return response;
            }
        } catch (Exception e) {
            log.error("取消选座Error:" + e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return response;
        }
        return response;
    }

    //获取对应城市名称
    private String getCityName(String airportCode) {
        if (!StringUtil.isNullOrEmpty(airportCode)) {
            String redisData = apiRedisService.getData("airportInfoJson");
            if (!StringUtil.isNullOrEmpty(redisData)) {
                Map<String, AirPortInfo> airportMap = (Map<String, AirPortInfo>) JsonUtil.jsonToMap(redisData, new TypeToken<Map<String, AirPortInfo>>() {}.getType());
                for(Map.Entry<String, AirPortInfo> entry : airportMap.entrySet()){
                    if(entry.getKey().equals(airportCode)){
                        AirPortInfo airPortInfo = entry.getValue();
                        if(null != airPortInfo){
                            return airPortInfo.getCityName();
                        }
                    }
                }
            }
        }
        return "";
    }
}
