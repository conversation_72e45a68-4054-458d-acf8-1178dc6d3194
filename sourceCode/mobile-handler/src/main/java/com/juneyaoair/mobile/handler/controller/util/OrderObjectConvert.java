package com.juneyaoair.mobile.handler.controller.util;

import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.baseclass.request.booking.*;
import com.juneyaoair.baseclass.request.order.refund.confirm.OrderRefundRequest;
import com.juneyaoair.baseclass.request.order.refund.query.RefundType;
import com.juneyaoair.baseclass.response.order.comm.OrderPassengerInfo;
import com.juneyaoair.baseclass.response.order.comm.SegmentPriceInfo;
import com.juneyaoair.baseclass.response.order.comm.TaxInfo;
import com.juneyaoair.baseclass.ticketInfo.IBETicketInfo;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.thirdentity.comm.RefundGroup;
import com.juneyaoair.thirdentity.comm.RefundInfo;
import com.juneyaoair.thirdentity.request.booking.*;
import com.juneyaoair.thirdentity.request.order.refund.confirm.PtRefundReq;
import com.juneyaoair.thirdentity.request.order.refund.confirm.PtTaxInfo;
import com.juneyaoair.thirdentity.request.score.ScoreUseRuleRequest;
import com.juneyaoair.thirdentity.response.detr.IdentityInfo;
import com.juneyaoair.thirdentity.response.detr.SegmentInfo;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class OrderObjectConvert {

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    //生成机票订单
    public static List<PtTicketOrderInfo> ToTicketOrderInfoList(TicketBookingRequestV2 source, Boolean useScore) {
        int i = 0;// 机票子订单序号
        List<PtTicketOrderInfo> tOrderList = new ArrayList<>();
        Map<String, List<PassengerInfo>> dicTicketGroup = toGroupPassenger(source);// 子订单分组乘客  ADT CHD GMJC
        for (Map.Entry<String, List<PassengerInfo>> entry : dicTicketGroup.entrySet()) {
            List<PassengerInfo> passGroupList = entry.getValue();
            Map<String, List<?>> segPriceMap;
            segPriceMap = ToSegmentInfoList(source.getFlightInfoList(), entry.getKey(), passGroupList, useScore);
            // 计算乘客运价
            for (PassengerInfo passengerInfo : passGroupList) {
                List<PtPSPriceRel> priceList = new ArrayList<>();//该乘客的 运价 航段对应关系
                List<PtPSPriceRel> curPriceRelList = (List<PtPSPriceRel>) segPriceMap.get("listPSP");//该类乘客的 乘客 运价 航段对应关系
                for (PtPSPriceRel ptPriceRel : curPriceRelList) {// 乘客对应所有价格
                    if (ptPriceRel.getPassengerNO() == passengerInfo.getPassengerNO()) {
                        priceList.add(ptPriceRel);
                    }
                }
                List<PtPrice> curAllPriceList = (List<PtPrice>) segPriceMap.get("listPrice");//该类乘客的运价
                // 计算价格
                double ticketPrice = 0.0;
                double pricePaid = 0.0;
                double YQTax = 0.0;
                double CNTax = 0.0;
                for (PtPSPriceRel ptPSPriceRel : priceList) {
                    for (PtPrice ptPrice : curAllPriceList) {
                        if (ptPSPriceRel.getPriceNO() == ptPrice.getPriceNO()) {
                            ticketPrice += ptPrice.getRSP();
                            pricePaid += ptPrice.getPriceValue();
                            if (HandlerConstants.FLIGHT_INTER_D.equals(source.getInterFlag())) {// 国内机建燃油费
                                YQTax += ptPrice.getYQTax();
                                CNTax += ptPrice.getCNTax();
                            }
                        }
                    }
                }
                passengerInfo.setTicketPrice(ticketPrice);
                passengerInfo.setPricePaid(pricePaid);
                if (HandlerConstants.FLIGHT_INTER_D.equals(source.getInterFlag())) {
                    passengerInfo.setyQTax(YQTax);
                    passengerInfo.setcNTax(CNTax);
                }
            }
            PtTicketOrderInfo tickOrder = toTicketOrderInfo(source, i++, passGroupList, segPriceMap, entry.getKey());
            if (dicTicketGroup.containsKey(HandlerConstants.PASSENGER_TYPE_ADT) && dicTicketGroup.containsKey(HandlerConstants.PASSENGER_TYPE_GMJC)) {//同时拥有成人和军残，优惠券只保持一份
                if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(entry.getKey())) {
                    tickOrder.setUseScoreTotal(0);
                    tickOrder.setCouponCode("");
                    tickOrder.setPromoCode("");
                }
            }
            tOrderList.add(tickOrder);
        }
        return tOrderList;
    }

    // 乘客分组
    public static Map<String, List<PassengerInfo>> toGroupPassenger(TicketBookingRequestV2 source) {
        return toGroupPassenger(source.getPassengerInfoList(), source.getFlightInfoList(), source.getInterFlag());
    }

    private static Map<String, List<PassengerInfo>> toGroupPassenger(List<PassengerInfo> passengerInfoList, List<FlightInfo> flightInfoList, String interFlag) {
        Map<String, List<PassengerInfo>> mapTicketGroup = new HashMap<>();
        List<PassengerInfo> chdList = new ArrayList<>();
        List<PassengerInfo> adtList = new ArrayList<>();
        List<PassengerInfo> infList = new ArrayList<>();
        List<PassengerInfo> gmjcList = new ArrayList<>();
        for (PassengerInfo pass : passengerInfoList) {
            // 生僻字拼音下单处理
            if (StringUtils.isNotBlank(pass.getPassengerName())) {
                pass.setPassengerName(pass.getPassengerName().toUpperCase());
            }
            if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                chdList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                if ("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType())) {
                    gmjcList.add(pass);
                } else if (HandlerConstants.PASSENGER_TYPE_OLD.equals(pass.getPassengerIdentity())) {
                    adtList.add(pass);
                } else {
                    adtList.add(pass);
                }
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {
                gmjcList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(pass.getPassengerType())) {
                infList.add(pass);
            }
        }
        //成人与婴儿分组合并
        if (CollectionUtils.isNotEmpty(adtList)) {
            adtList.addAll(infList);
        } else {
            gmjcList.addAll(infList);
        }
        // 1、国际直接返回
        if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);
            return mapTicketGroup;
        } else if (HandlerConstants.FLIGHT_INTER_D.equals(interFlag) && CollectionUtils.isEmpty(chdList)) {
            //无儿童直接返回
            if (CollectionUtils.isNotEmpty(adtList)) {
                mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);
            }
            if (CollectionUtils.isNotEmpty(gmjcList)) {
                mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
            }
            return mapTicketGroup;
        }
        // 2、判断舱位
        int cabinCnt = 0;
        for (FlightInfo flightInfo : flightInfoList) {
            List<CabinFare> listCabinFare = flightInfo.getCabinFareList();
            if(CollectionUtils.isNotEmpty(listCabinFare)){
                String cabinCode = listCabinFare.get(0).getCabinCode();
                if (("F").equals(cabinCode) || ("Y").equals(cabinCode)) {
                    cabinCnt++;
                }
            }
        }
        //如果成人都是标准舱的情况下，可以直接返回
        if (cabinCnt == flightInfoList.size()) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);// //因为成人、儿童与婴儿航段相同，使用成人类型取相同航段
            return mapTicketGroup;
        }
        // 不同情况分别取
        if (CollectionUtils.isNotEmpty(adtList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);// 因为成人与婴儿航段相同，使用成人类型取相同航段
        }
        if (CollectionUtils.isNotEmpty(chdList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_CHD, chdList);
        }
        if (CollectionUtils.isNotEmpty(gmjcList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
        }
        return mapTicketGroup;
    }

    // 航段、价格列表
    private static Map<String, List<?>> ToSegmentInfoList(List<FlightInfo> flightList,
                                                          String passengerType, List<PassengerInfo> passengerList, Boolean useScore) {
        //4ADT 1CHD 2INF 多人中转往返 造成运价序号重复，所以将CHD 初始化序号赋值提高到 30
        int i = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 30 : 0;// 航段序号赋值
        int j = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 30 : 0;// 运价序号赋值
        int h = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 30 : 0;// 乘客序号赋值
        for (PassengerInfo pass : passengerList) {// 乘客集合
            pass.setPassengerNO(h++);
        }
        List<PtSegmentInfo> listSeg = new ArrayList<>();// 航段集合
        List<PtPrice> listPrice = new ArrayList<>();// 运价集合
        List<PtPSPriceRel> listPSP = new ArrayList<>();// 乘客、航段与运价关系集合

        for (FlightInfo flight : flightList) {
            PtSegmentInfo seg = ToSegmentInfo(flight, i++, passengerType);//航段信息
            listSeg.add(seg);
            Map<String, List<PassengerInfo>> passMap = toPassageGroup(passengerList);// 乘客分组  ADT CHD INF GMJC
            for (Map.Entry<String, List<PassengerInfo>> entry : passMap.entrySet()) {
                List<CabinFare> cabinfareList;
                if (HandlerConstants.PASSENGER_TYPE_ADT.equals(entry.getKey())) {
                    cabinfareList = flight.getCabinFareList();
                } else if (HandlerConstants.PASSENGER_TYPE_OLD.equals(entry.getKey())) {//老人
                    cabinfareList = flight.getCabinOLDFareList();
                } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(entry.getKey())) {//军残警残
                    cabinfareList = flight.getCabinGMJCFareList();
                } else {
                    cabinfareList = flight.getCabinCHDINFFareList();//获取婴儿儿童舱位
                }
                //积分后置接口参数
                if (useScore) {
                    for (PassengerInfo passengerInfo : entry.getValue()) {
                        if (passengerInfo != null) {
                            CabinFare cabinFare = getCabinFare(cabinfareList, entry.getKey(), passengerInfo, flightList.size());
                            if (null != cabinFare) {
                                PtPrice price = getPriceByPassengerType(cabinFare, j++);
                                listPrice.add(price);
                                // 人与价格关系
                                listPSP.add(new PtPSPriceRel(passengerInfo.getPassengerNO(), seg.getSegNO(), price.getPriceNO(), true));
                            }
                        }
                    }
                } else {
                    CabinFare cabinFare = getCabinFare(cabinfareList, entry.getKey(), null, 0);
                    if (null != cabinFare) {
                        PtPrice price = getPriceByPassengerType(cabinFare, j++);
                        listPrice.add(price);
                        // 人与价格关系
                        for (PassengerInfo pass : entry.getValue()) {
                            listPSP.add(new PtPSPriceRel(pass.getPassengerNO(), seg.getSegNO(), price
                                    .getPriceNO(), true));
                        }
                    }
                }
            }
        }
        Map<String, List<?>> map = new HashMap<>();
        map.put("listSeg", listSeg);
        map.put("listPrice", listPrice);
        map.put("listPSP", listPSP);
        return map;
    }

    // 构建航线
    private static PtSegmentInfo ToSegmentInfo(FlightInfo flight, int num, String passengerType) {
        PtSegmentInfo ptSegmentInfo = new PtSegmentInfo();
        BeanUtils.copyProperties(flight, ptSegmentInfo);
        ptSegmentInfo.setSegNO(num);
        ptSegmentInfo.setIsCodeShare(flight.getCodeShare());
        ptSegmentInfo.setCarrierFlightNo(flight.getCarrierNo());
        ptSegmentInfo.setIsSeatedOnPlane(flight.getASR());
        ptSegmentInfo.setPlaneStyle(flight.getFType());
        List<CabinFare> cabinList = null;
        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passengerType)) {
            cabinList = flight.getCabinFareList();
        } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passengerType)) {
            cabinList = flight.getCabinGMJCFareList();
        } else {
            cabinList = flight.getCabinCHDINFFareList();
        }

        if (cabinList == null && CollectionUtils.isNotEmpty(flight.getCabinOLDFareList())) {
            cabinList = flight.getCabinOLDFareList();
        }
        for (CabinFare cabinFare : cabinList) {
            if (passengerType.equals(cabinFare.getPassengerType())) {
                ptSegmentInfo.setCabin(cabinFare.getCabinCode());
                ptSegmentInfo.setCabinClass(cabinFare.getCabinClass());
                break;
            }
        }
        return ptSegmentInfo;
    }

    // 乘客分组
    public static Map<String, List<PassengerInfo>> toPassageGroup(List<PassengerInfo> passengerList) {
        Map<String, List<PassengerInfo>> group = new HashMap<>();
        List<PassengerInfo> adtList = new ArrayList<>();
        List<PassengerInfo> chdList = new ArrayList<>();
        List<PassengerInfo> infList = new ArrayList<>();
        List<PassengerInfo> gmjcList = new ArrayList<>();
        List<PassengerInfo> oldList = new ArrayList<>();
        for (PassengerInfo pass : passengerList) {
            if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                if ("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType())) {//军残警残处理
                    gmjcList.add(pass);
                } else if (HandlerConstants.PASSENGER_TYPE_OLD.equals(pass.getPassengerIdentity())) {
                    oldList.add(pass);
                } else {
                    adtList.add(pass);
                }
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {
                gmjcList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                chdList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(pass.getPassengerType())) {
                infList.add(pass);
            }
        }
        if (CollectionUtils.isNotEmpty(adtList)) {
            group.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);
        }
        if (CollectionUtils.isNotEmpty(chdList)) {
            group.put(HandlerConstants.PASSENGER_TYPE_CHD, chdList);
        }
        if (CollectionUtils.isNotEmpty(infList)) {
            group.put(HandlerConstants.PASSENGER_TYPE_INF, infList);
        }
        if (CollectionUtils.isNotEmpty(gmjcList)) {
            group.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
        }
        if (CollectionUtils.isNotEmpty(oldList)) {
            group.put(HandlerConstants.PASSENGER_TYPE_OLD, oldList);
        }
        return group;
    }


    // 取乘客运价
    public static CabinFare getCabinFare(List<CabinFare> cabinList, String passengerType, PassengerInfo passengerInfo, int size) {
        for (CabinFare cabinFare : cabinList) {
            CabinFare newCabinFare = null;
            if (passengerType.equals(cabinFare.getPassengerType())) {
                newCabinFare = cabinFare;
            }
            if (passengerType.equals(cabinFare.getPassengerIdentity())) {
                newCabinFare = cabinFare;
            }
            if (newCabinFare != null && passengerInfo != null) {
                newCabinFare.setPriceValue(passengerInfo.getPricePaid() / size);
            }
            if (newCabinFare != null && !HandlerConstants.PASSENGER_TYPE_INF.equals(passengerType)) {
                return newCabinFare;
            }
        }
        return null;
    }

    // 生成运价
    public static PtPrice getPriceByPassengerType(CabinFare cabinFare, int num) {
        PtPrice ptPrice = new PtPrice();
        BeanUtils.copyProperties(cabinFare, ptPrice);
        ptPrice.setPriceNO(num);
        ptPrice.setIsGiftScore(null != cabinFare.getScoreGiftInfo());
        //多程惠达优惠金额
        ptPrice.setDiscountdiff(cabinFare.getDiscountPriceValue());
        if (null != cabinFare.getScoreGiftInfo()) {
            PtScoreGift ptScoreGift = new PtScoreGift();
            BeanUtils.copyProperties(cabinFare.getScoreGiftInfo(), ptScoreGift);
            ptPrice.setScoreGiftInfo(ptScoreGift);
        } else {
            ptPrice.setScoreGiftInfo(null);
        }
        return ptPrice;

    }

    // 生成子订单
    private static PtTicketOrderInfo toTicketOrderInfo(TicketBookingRequestV2 source, int orderNo,
                                                       List<PassengerInfo> passList, Map<String, List<?>> segPriceMap, String passengerType) {
        PtTicketOrderInfo ptTicketOrderInfo = new PtTicketOrderInfo();
        BeanUtils.copyProperties(source, ptTicketOrderInfo);
        ptTicketOrderInfo.setFFPId(source.getFfpId());
        ptTicketOrderInfo.setFFPCardType("Member");
        ptTicketOrderInfo.setFFPLevel(source.getFfpLevel());
        if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passengerType)) {
            ptTicketOrderInfo.setFFPLevel("");
            ptTicketOrderInfo.setUseScoreTotal(0);
            ptTicketOrderInfo.setCouponCode("");
            ptTicketOrderInfo.setPromoCode("");
        }
        List<PtPassengerInfo> ptPassList = new ArrayList<>();
        for (PassengerInfo pass : passList) {
            PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
            BeanUtils.copyProperties(pass, ptPassengerInfo);
            ptPassengerInfo.setIsSaveCommon("N");//默认保存到常用联系人
            if (("Y").equals(pass.getIsBuyInsurance()) && CollectionUtils.isNotEmpty(pass.getInsuranceList())) {
                List<PtInsuranceInfo> insureList = new ArrayList<>();
                for (InsuranceInfo insure : pass.getInsuranceList()) {
                    PtInsuranceInfo ptInsure = new PtInsuranceInfo();
                    BeanUtils.copyProperties(insure, ptInsure);
                    insureList.add(ptInsure);
                }
                ptPassengerInfo.setInsuranceList(insureList);
            } else {
                ptPassengerInfo.setIsBuyInsurance("N");
                ptPassengerInfo.setInsuranceList(null);
            }
            ptPassList.add(ptPassengerInfo);
        }
        ptTicketOrderInfo.setTicketOrderId(orderNo);
        ptTicketOrderInfo.setPassengerInfoList(ptPassList);
        ptTicketOrderInfo.setSegmentInfoList((List<PtSegmentInfo>) segPriceMap.get("listSeg"));
        ptTicketOrderInfo.setPriceInfoList((List<PtPrice>) segPriceMap.get("listPrice"));
        ptTicketOrderInfo.setPSPriceRelList((List<PtPSPriceRel>) segPriceMap.get("listPSP"));

        return ptTicketOrderInfo;
    }

    // 计算价格
    public static double sumPrice(List<PtTicketOrderInfo> orderInfoList) {
        double payAmount = 0.0;
        for (PtTicketOrderInfo ptOrder : orderInfoList) {
            for (PtPassengerInfo ptPassenger : ptOrder.getPassengerInfoList()) {
                double insureAmt = 0.0;
                if (("Y").equals(ptPassenger.getIsBuyInsurance())) {
                    for (PtInsuranceInfo insure : ptPassenger.getInsuranceList()) {
                        insureAmt += insure.getInsuranceAmount();
                    }
                }
                payAmount += ptPassenger.getPricePaid() + ptPassenger.getYQTax()
                        + ptPassenger.getCNTax() + ptPassenger.getOtherTax()
                        + ptPassenger.getQFee() + insureAmt;
            }
        }
        return payAmount;
    }

    /**
     * 退票费计算人员分组
     *
     * @param
     * @return
     */
    public static List<RefundGroup> ToRefundGroupList(List<OrderPassengerInfo> pass,
                                                      boolean isRefundInsurance) {
        List<RefundGroup> refundGroup = new ArrayList<RefundGroup>();
        for (OrderPassengerInfo orderPassengerInfo : pass) {
            List<RefundInfo> refundList = ToRefundInfoList(orderPassengerInfo, isRefundInsurance);
            double sPriceTotal = 0.0, deductionTotal = 0.0, useFlowdPriceTotal = 0.0, refundXTaxTotal = 0.0, refundOtherTotal = 0.0;
            for (RefundInfo ptRefundInfo : refundList) {
                sPriceTotal += ptRefundInfo.getSPrice();
                deductionTotal += ptRefundInfo.getDeduction();
                useFlowdPriceTotal += ptRefundInfo.getUseFlowdPrice();
                refundXTaxTotal += ptRefundInfo.getRefundXTax();
                refundOtherTotal += ptRefundInfo.getRefundOther();
            }
            RefundGroup group = new RefundGroup();
            group.setPassengerID(String.valueOf(orderPassengerInfo.getPassengerID()));
            group.setPassengerType(orderPassengerInfo.getPassengerType());
            group.setSPriceTotal(sPriceTotal);
            group.setRefundInfoList(refundList);
            group.setDeductionTotal(deductionTotal);
            group.setUseFlowdPriceTotal(useFlowdPriceTotal);
            group.setRefundXTaxTotal(refundXTaxTotal);
            group.setRefundOtherTotal(refundOtherTotal);
            group.setRefundSign(orderPassengerInfo.getRefundSign());
            refundGroup.add(group);
        }

        return refundGroup;
    }

    private static List<RefundInfo> ToRefundInfoList(OrderPassengerInfo passInfo, boolean isRefundInsurance) {
        List<RefundInfo> result = new ArrayList<RefundInfo>();
        boolean first = true;
        for (SegmentPriceInfo segmentPriceInfo : passInfo.getSegmentPriceInfoList()) {
            RefundInfo ptRefundInfo = new RefundInfo();//每段的退款信息
            BeanUtils.copyProperties(segmentPriceInfo, ptRefundInfo);
            ptRefundInfo.setID("" + segmentPriceInfo.getID());
            ptRefundInfo.setSPrice(segmentPriceInfo.getPricePaid() + segmentPriceInfo.getUseScore() + segmentPriceInfo.getCouponAmount()
                    - segmentPriceInfo.getUpgradeFee() - segmentPriceInfo.getUpgradeCouponAmount());
            ptRefundInfo.setXTax(segmentPriceInfo.getYQTax() + segmentPriceInfo.getCNTax());
            ptRefundInfo.setIsRefundInsurance(segmentPriceInfo.getIsBuyInsurance() && isRefundInsurance);
            if (null == ptRefundInfo.getRefundTaxDetailList()
                    || ptRefundInfo.getRefundTaxDetailList().size() == 0) {
                List<PtTaxInfo> ptTaxInfoList = new ArrayList<>();
                ptRefundInfo.setRefundTaxDetailList(ptTaxInfoList);
            }
            if ((segmentPriceInfo.getYQTax() + segmentPriceInfo.getCNTax()) == 0) { // 国际票的税费  只计算一次
                if (first && HandlerConstants.OPEN_FOR_USE.equals(segmentPriceInfo.getTKTStatus())) {
                    //除YQ,CN，Q以外的其他税费
                    List<PtTaxInfo> otherTax = new ArrayList<PtTaxInfo>();
                    double taxAmount = 0.0;
                    for (TaxInfo taxInfo : passInfo.getTaxInfoList()) {
                        if (!(CommonBaseConstants.YQ_TAX_CODE.equalsIgnoreCase(taxInfo.getTaxCode()) ||
                                CommonBaseConstants.CN_TAX_CODE.equalsIgnoreCase(taxInfo.getTaxCode()) ||
                                CommonBaseConstants.Q_FEE_CODE.equalsIgnoreCase(taxInfo.getTaxCode()))) {
                            PtTaxInfo ptTaxInfo = new PtTaxInfo();
                            BeanUtils.copyProperties(taxInfo, ptTaxInfo);
                            otherTax.add(ptTaxInfo);
                            taxAmount += ptTaxInfo.getTaxAmount();
                        }
                    }
                    if (otherTax.size() > 0) {
                        List<PtTaxInfo> taxList = ptRefundInfo.getRefundTaxDetailList();
                        otherTax.addAll(taxList);
                        ptRefundInfo.setRefundTaxDetailList(otherTax);
                    }
                    ptRefundInfo.setXTax(passInfo.getYQTax() + passInfo.getCNTax() + passInfo.getQFee() + taxAmount);
                    ptRefundInfo.setRefundXTax(ptRefundInfo.getXTax());
                    List<PtTaxInfo> curtaxList = ptRefundInfo.getRefundTaxDetailList();
                    //追加YQ,CN,Q税费列表
                    curtaxList.addAll(GetTaxInfoList(passInfo.getYQTax(), passInfo.getCNTax(), passInfo.getQFee()));
                    ptRefundInfo.setRefundTaxDetailList(curtaxList);
                    first = false;//取消第一次标识
                }
            } else {//国内
                if (segmentPriceInfo.getRefundXTax() > 0) {
                    ptRefundInfo.setRefundTaxDetailList(GetTaxInfoList(segmentPriceInfo.getYQTax(),
                            segmentPriceInfo.getCNTax()));
                }
            }
            result.add(ptRefundInfo);
        }
        return result;
    }

    private static List<PtTaxInfo> GetTaxInfoList(double yq, double cn) {
        List<PtTaxInfo> taxList = new ArrayList<PtTaxInfo>();
        PtTaxInfo taxYq = new PtTaxInfo(CommonBaseConstants.YQ_TAX_CODE,
                HandlerConstants.CURRENCY_CODE, yq);
        PtTaxInfo taxCn = new PtTaxInfo(CommonBaseConstants.CN_TAX_CODE,
                HandlerConstants.CURRENCY_CODE, cn);
        taxList.add(taxYq);
        taxList.add(taxCn);
        return taxList;
    }

    private static List<PtTaxInfo> GetTaxInfoList(double yq, double cn, double q) {
        List<PtTaxInfo> taxList = GetTaxInfoList(yq, cn);
        taxList.add(new PtTaxInfo(CommonBaseConstants.Q_FEE_CODE, HandlerConstants.CURRENCY_CODE,
                q));
        return taxList;
    }

    // 退票确认请求
    public static List<PtRefundReq> ToTicketRefundRequestList(OrderRefundRequest req) {
        Map<String, List<OrderPassengerInfo>> refundPassMap = refundPassGroup(req
                .getOrderPassengerInfoList());
        LinkedList<PtRefundReq> list = new LinkedList<>();
        for (Map.Entry<String, List<OrderPassengerInfo>> entry : refundPassMap.entrySet()) {
            List<OrderPassengerInfo> curPass = entry.getValue();
            PtRefundReq refund = new PtRefundReq(HandlerConstants.VERSION, req.getBookChannelCode(),
                    req.getUserNo(), HandlerConstants.CURRENCY_CODE);
            refund.setProposer(req.getProposer());
            refund.setLinkTelphone(req.getLinkTelphone());
            if (null != req.getRefundType()) {
                refund.setIsVoluntaryRefund(req.getRefundType().isVoluntaryRefund());
            } else {
                refund.setIsVoluntaryRefund(req.getIsVoluntaryRefund());
            }
            //会员id
            refund.setCustomerNo(req.getCustomerNo());
            //退票类型  E错购退票,Y自愿退票，N非自愿退票   T特情退票 2021-03-05
            if (refund.isIsVoluntaryRefund()) {
                refund.setRefundType("Y");
            } else if (req.getRefundType().isSpecialSituationRefund()) {
                refund.setRefundType("T");
            } else {
                if (req.getRefundType().isErrorBuyRefund()) {
                    refund.setRefundType("E");
                } else {
                    refund.setRefundType("N");
                }
            }
            List<OrderPassengerInfo>  hoOrderPassengerInfo=curPass.stream().filter(pass->pass.getSegmentPriceInfoList().stream().anyMatch(segmentPriceInfo -> segmentPriceInfo.getFlightNo().startsWith("AQ"))).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(hoOrderPassengerInfo)){
                RefundType refundType = req.getRefundType();
                String dataId = refundType.getDataId();
                switch (dataId) {
                    //航变退
                    case "2": {
                        req.setRefundReason("1");
                        break;
                    }
                    //病退
                    case "4":{
                        req.setRefundReason("2");
                        break;
                    }
                    default: {
                        req.setRefundReason("");
                        break;
                    }
                }
            }

            refund.setRefundReason(req.getRefundReason());
            refund.setIsPostRefundCert(false);
            refund.setTicketOrderNo(entry.getKey());
            refund.setRefundGroupList(ToRefundGroupList(curPass, req.getIsRefundInsurance()));
            if (curPass.get(0).getPassengerType().equals(HandlerConstants.PASSENGER_TYPE_CHD)) {
                list.offerFirst(refund);
            } else {
                list.add(refund);
            }

        }
        return list;
    }


    //机票订单号分组
    private static Map<String, List<OrderPassengerInfo>> refundPassGroup(
            List<OrderPassengerInfo> orderPass) {
        String ticketOrderNo;
        List<OrderPassengerInfo> orderPassList;
        Map<String, List<OrderPassengerInfo>> passGroupMap = new HashMap<>();
        for (OrderPassengerInfo orderPassengerInfo : orderPass) {
            ticketOrderNo = orderPassengerInfo.getTicketOrderNo();
            if (passGroupMap.containsKey(ticketOrderNo)) {
                continue;
            }
            orderPassList = new ArrayList<>();
            for (OrderPassengerInfo curPass : orderPass) {
                if (curPass.getTicketOrderNo().equals(ticketOrderNo)) {
                    orderPassList.add(curPass);
                }
            }
            passGroupMap.put(ticketOrderNo, orderPassList);

        }
        return passGroupMap;
    }

    //生成积分查询
    public static ScoreUseRuleRequest ToScoreUseRuleRequest(TicketBookingRequestV2 bookInfo, String userNo, Boolean useScore) {
        ScoreUseRuleRequest req = new ScoreUseRuleRequest(HandlerConstants.VERSION, bookInfo.getChannelCode(), userNo, null);
        List<PtTicketOrderInfo> tickOrderList = ToTicketOrderInfoList(bookInfo, useScore);
        PtTicketOrderInfo adtOrder = tickOrderList.get(0);
        if (tickOrderList.size() > 1) {//儿童也成人分开的情况
            PtTicketOrderInfo chdOrder = tickOrderList.get(1);
            adtOrder.getPassengerInfoList().addAll(chdOrder.getPassengerInfoList());
            adtOrder.getSegmentInfoList().addAll(chdOrder.getSegmentInfoList());
            adtOrder.getPSPriceRelList().addAll(chdOrder.getPSPriceRelList());
            adtOrder.getPriceInfoList().addAll(chdOrder.getPriceInfoList());
        }
        req.setBookingInfo(adtOrder);
        return req;
    }

    //<editor-fold desc="单独购保时生成订单对象">
    /*单独购保时生成订单对象*/
    public static PtTicketBookingReq ToInsurePlatformReq(InsuranceBookingRequest bookReq, String userNo, String ip, IRedisService iRedisService) {
        PtTicketBookingReq target = new PtTicketBookingReq(HandlerConstants.VERSION,
                bookReq.getChannelCode(), userNo, HandlerConstants.CURRENCY_CODE, HandlerConstants.LANG_CODE);
        BeanUtils.copyProperties(bookReq, target);
        target.setOrderRequestIp(ip);
        target.setTicketOrderSort("Insurance");
        target.setTicketOrderType("Person");
        List<PtTicketOrderInfo> tOrderList = ToInsuranceOrderInfoList(bookReq, iRedisService);
        target.setTicketOrderInfoList(tOrderList);
        double payAmount = sumInsurePrice(tOrderList);
        target.setPayAmount(payAmount);
        target.setIsPostTripCert(false);
        target.setBuyWifi(false);
        target.setBuyLounge(false);
        target.setUseScoreTotal(0);
        return target;
    }

    //生成保险订单对象
    public static List<PtTicketOrderInfo> ToInsuranceOrderInfoList(InsuranceBookingRequest source, IRedisService iRedisService) {
        List<PtTicketOrderInfo> ptTicketOrderInfoList = new ArrayList<PtTicketOrderInfo>();
        PtTicketOrderInfo ptTicketOrderInfo = new PtTicketOrderInfo();
        List<PtSegmentInfo> segmentInfoList = ToSegmentInfoList(source.getTicketInfo());
        Map<String, List<?>> map = GetPrice(source.getTicketInfo(), segmentInfoList);

        ptTicketOrderInfo.setTicketOrderId(0);
        ptTicketOrderInfo.setPnr(source.getTicketInfo().getSegmentInfoList().get(0).getPnrNo());
        ptTicketOrderInfo.setTicketNo(source.getTicketInfo().getTicketNo());
        List<PtPassengerInfo> passengerInfoList = new ArrayList<PtPassengerInfo>();
        passengerInfoList.add(GetPassengerInfo(source.getTicketInfo(), source.getInsuranceList(), source.getLinkerHandphone(), source.getInfIdentityNo(), iRedisService));
        ptTicketOrderInfo.setPassengerInfoList(passengerInfoList);
        ptTicketOrderInfo.setSegmentInfoList(segmentInfoList);
        ptTicketOrderInfo.setSfCardNo(source.getFfpCardNo());
        ptTicketOrderInfo.setFFPId(source.getFfpId());
        ptTicketOrderInfo.setFFPCardType("Member");
        ptTicketOrderInfo.setLinker(source.getLinker());
        ptTicketOrderInfo.setLinkerHandphone(source.getLinkerHandphone());
        ptTicketOrderInfo.setLinkerEMail("");
        ptTicketOrderInfo.setLinkerTelphone("");
        ptTicketOrderInfo.setPSPriceRelList((List<PtPSPriceRel>) map.get("listPSP"));
        ptTicketOrderInfo.setPriceInfoList((List<PtPrice>) map.get("listPrice"));
        ptTicketOrderInfoList.add(ptTicketOrderInfo);
        return ptTicketOrderInfoList;
    }

    //生成航线
    public static List<PtSegmentInfo> ToSegmentInfoList(InsuranceTicketInfo ticketInfo) {
        List<PtSegmentInfo> segList = new ArrayList<PtSegmentInfo>();
        for (SegmentInfo ticketSeg : ticketInfo.getSegmentInfoList()) {
            PtSegmentInfo seg = new PtSegmentInfo();
            BeanUtils.copyProperties(ticketSeg, seg);
            seg.setSegNO(ticketSeg.getSegmentIndex() - 1);
            seg.setFlightDirection("G");
            seg.setDepDateTime(ticketSeg.getDepTime());
            seg.setArrDateTime(ticketSeg.getArrTime());
            seg.setDepCity(ticketSeg.getDepCityCode());
            seg.setArrCity(ticketSeg.getArrCityCode());
            seg.setDepAirport(ticketSeg.getDepAirportCode());
            seg.setArrAirport(ticketSeg.getArrAirportCode());
            seg.setCabin(ticketSeg.getCabin());
            seg.setCabinClass(GetCabinClass(ticketSeg.getCabin()));
            seg.setIsCodeShare(false);
            seg.setCarrierFlightNo(ticketSeg.getFlightNo());
            seg.setMealCode("0");
            seg.setIsSeatedOnPlane(false);
            seg.setPlaneStyle("320");
            seg.setDepTerm(ticketSeg.getDepAirportTerminal());
            seg.setArrTerm(ticketSeg.getArrAirportTerminal());
            seg.setStopNumber(0);
            segList.add(seg);
        }
        return segList;
    }

    //单独购保乘客运价
    private static Map<String, List<?>> GetPrice(InsuranceTicketInfo ticketInfo, List<PtSegmentInfo> ptSegmentInfoList) {
        List<PtPrice> listPrice = new ArrayList<>();// 运价集合
        List<PtPSPriceRel> listPSP = new ArrayList<>();// 乘客、航段与运价关系集合
        PtPrice price = new PtPrice();
        price.setPriceNO(0);
        price.setRSP(ticketInfo.getFare());
        price.setPriceValue(ticketInfo.getFare());
        price.setCNTax(getTax(ticketInfo.getTaxInfoList(), "CN"));
        price.setYQTax(getTax(ticketInfo.getTaxInfoList(), "YQ"));
        listPrice.add(price);
        for (PtSegmentInfo ptSegmentInfo : ptSegmentInfoList) {
            PtPSPriceRel psPriceRel = new PtPSPriceRel(0, ptSegmentInfo.getSegNO(), 0, true);
            listPSP.add(psPriceRel);
        }
        Map<String, List<?>> map = new HashMap<String, List<?>>();
        map.put("listPrice", listPrice);
        map.put("listPSP", listPSP);
        return map;
    }

    //单独购保乘客信息
    private static PtPassengerInfo GetPassengerInfo(InsuranceTicketInfo ticketInfo, List<InsuranceInfo> insuranceList, String linkerPhone, String infIdentityNo, IRedisService iRedisService) {
        //从缓存中拿出生日期信息
        if (CollectionUtils.isEmpty(ticketInfo.getIdentityInfoList())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "下单时证件信息不能为空");
        }
        String redisKey = RedisKeyConfig.createInsuranceFlightInfoPassKey(ticketInfo.getPassengerName(), ticketInfo.getTicketNo());
        String redisServiceData = iRedisService.getData(redisKey);
        if (StringUtils.isEmpty(redisServiceData)) {
            throw new CommonException(WSEnum.OPERATION_TIMEOUT_50016.getResultCode(), "操作超时，请退出重新查询(1)");
        }
        IdentityInfo identityInfo = IdentityInfoUtil.getIdentityInfo(ticketInfo.getIdentityInfoList());
        //缓存的客票信息
        IBETicketInfo ibeTicketInfoRedis = (IBETicketInfo) JsonUtil.jsonToBean(redisServiceData, IBETicketInfo.class);
        List<com.juneyaoair.baseclass.ticketInfo.IdentityInfo> identityInfoList = ibeTicketInfoRedis.getIdentityInfoList();
        if (CollectionUtils.isEmpty(identityInfoList)) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "证件信息缺失");
        }
        com.juneyaoair.baseclass.ticketInfo.IdentityInfo identity = identityInfoList.stream().filter(identityInfoTemp ->
                        identityInfo.getIdType().equals(identityInfoTemp.getIdType()))
                .findFirst()
                .orElse(null);
        if (null == identity) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "未能获取到证件信息，请退出重新查询(2)");
        }
        PtPassengerInfo pass = new PtPassengerInfo();
        pass.setPassengerNO(0);
        pass.setPassengerName(ticketInfo.getPassengerName());
        pass.setPassengerType(GetPassengerType(ticketInfo.getPassengerType()));
        pass.setCertType(identity.getIdType());
        pass.setCertNo(identity.getIdNo());
        if (!"NI".equals(identityInfo.getIdType())) {
                pass.setBirthdate(identity.getBirthdate());
                try {
                    //以下为冗余字段，防止后期需要而提前添加的，目前（2023-04-14）出错不应该影响下单
                    pass.setCertValidity(identity.getCertValidity());
                    pass.setSex(identity.getSex());
                    pass.setNationality(identity.getNationality());
                    pass.setBelongCountry(identity.getBelongCountry());
                } catch (Exception exception) {
                    log.error("设置乘客信息出错，出错信息为:{}", exception.getMessage());
                }
        }
        //婴儿旅客处理
        if (CommonBaseConstants.IBE_PASSENGER_TYPE_INF.equals(ticketInfo.getPassengerType()) && StringUtils.isNotBlank(infIdentityNo)) {
            pass.setCertType(CertificateTypeEnum.ID_CARD.getShowCode());
            pass.setCertNo(infIdentityNo);
        }
        pass.setPricePaid(ticketInfo.getTotalAmount());
        pass.setTicketPrice(ticketInfo.getFare());
        pass.setYQTax(getTax(ticketInfo.getTaxInfoList(), "YQ"));
        pass.setCNTax(getTax(ticketInfo.getTaxInfoList(), "CN"));
        pass.setOtherTax(getTax(ticketInfo.getTaxInfoList(), "CN"));
        pass.setQFee(getTax(ticketInfo.getTaxInfoList(), "CN"));
        pass.setIsBuyInsurance("Y");
        pass.setHandphoneNo(linkerPhone);
        List<PtInsuranceInfo> insurList = new ArrayList<PtInsuranceInfo>();
        for (InsuranceInfo insuranceInfo : insuranceList) {
            PtInsuranceInfo ptinsuranceInfo = new PtInsuranceInfo();
            BeanUtils.copyProperties(insuranceInfo, ptinsuranceInfo);
            insurList.add(ptinsuranceInfo);
        }
        pass.setInsuranceList(insurList);
        return pass;
    }

    // 根据舱位代码，判断舱位等级
    public static String GetCabinClass(String cabin) {
        if (cabin.equals("F") || cabin.equals("A")) {
            return "F";
        }
        if (cabin.equals("C") || cabin.equals("D")) {
            return "C";
        }

        return "Y";
    }

    //取机建燃油
    public static Double getTax(List<com.juneyaoair.thirdentity.response.detr.TaxInfo> taxInfoList, String type) {
        Double taxVal = 0.0;
        for (com.juneyaoair.thirdentity.response.detr.TaxInfo tax : taxInfoList) {
            if (tax.getTaxCode().equals(type)) {
                taxVal = tax.getTaxAmount();
                break;
            }
        }
        return taxVal;
    }

    //转换乘客类型
    public static String GetPassengerType(String passType) {
        if (passType.equals(CommonBaseConstants.IBE_PASSENGER_TYPE_INF)) {
            return CommonBaseConstants.PASSENGER_TYPE_INF;
        }
        if (passType.equals(CommonBaseConstants.IBE_PASSENGER_TYPE_ADT)) {
            return CommonBaseConstants.PASSENGER_TYPE_ADT;
        }
        if (passType.equals(CommonBaseConstants.IBE_PASSENGER_TYPE_CHD)) {
            return CommonBaseConstants.PASSENGER_TYPE_CHD;
        }

        return CommonBaseConstants.PASSENGER_TYPE_CHD;
    }

    //单独购保乘客保险金额合计
    private static double sumInsurePrice(List<PtTicketOrderInfo> orderInfoList) {
        double payAmount = 0.0;
        for (PtTicketOrderInfo ptOrder : orderInfoList) {
            for (PtPassengerInfo ptPassenger : ptOrder.getPassengerInfoList()) {
                double insureAmt = 0.0;
                if (("Y").equals(ptPassenger.getIsBuyInsurance())) {
                    for (PtInsuranceInfo insure : ptPassenger.getInsuranceList()) {
                        insureAmt += insure.getInsuranceAmount();
                    }
                }
                payAmount += insureAmt;
            }
        }
        return payAmount;
    }
    //</editor-fold>

    /**
     * 按照证件分组乘客信息
     *
     * @param passengerInfoList
     * @return
     */
    public static Map<String, List<PassengerInfo>> passGroupByCertNo(List<PassengerInfo> passengerInfoList) {
        Map<String, List<PassengerInfo>> passMap = passengerInfoList.stream().collect(Collectors.groupingBy(passengerInfo -> passengerInfo.getCertType() + passengerInfo.getCertNo()));
        return passMap;
    }

    /**
     * 调整乘客类型
     *
     * @param birthDate       出生日期
     * @param finalFlightDate 最终的航班出发日期
     * @return
     */
    public static String adjustPassType(String birthDate, String finalFlightDate) {
        int age = DateUtils.getAgeByBirthIncludeBirthDay(birthDate, finalFlightDate, DateUtils.YYYY_MM_DD_PATTERN);
        return toPassType(age);
    }


    public static String therePassType(String birthDate, String finalFlightDate) {
        int age = DateUtils.getAgeByBirthIncludeBirthDay(birthDate, finalFlightDate, DateUtils.YYYY_MM_DD_PATTERN);
        return toPassType(age);
    }

    //根据年龄转换乘客类型
    public static String toPassType(int age) {
        if (age >= 0 && age < 2) {
            return PassengerTypeEnum.INF.getPassType();
        } else if (age >= 2 && age < 12) {
            return PassengerTypeEnum.CHD.getPassType();
        } else if (age >= 12) {
            return PassengerTypeEnum.ADT.getPassType();
        }
        return PassengerTypeEnum.ADT.getPassType();
    }

    public static String toDisneyPassType(int age) {
        if (age >= 0 && age < 3) {
            return PassengerTypeEnum.INF.getPassType();
        } else if (age >= 60) {
            return PassengerTypeEnum.OLD.getPassType();
        }
        return null;
    }

    /**
     * 迪士尼乘客类型
     *
     * @param birthDate
     * @param finalFlightDate
     * @return
     */
    public static String disneyPassType(String birthDate, String finalFlightDate) {
        int age = DateUtils.getAgeByBirthIncludeBirthDay(birthDate, finalFlightDate, DateUtils.YYYY_MM_DD_PATTERN);
        return toDisneyPassType(age);
    }

}
