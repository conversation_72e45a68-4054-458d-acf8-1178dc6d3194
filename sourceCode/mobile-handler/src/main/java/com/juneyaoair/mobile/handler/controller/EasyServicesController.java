package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.request.easyServices.ChildrenOrderApplyRequest;
import com.juneyaoair.baseclass.request.easyServices.MpGoodsLostReq;
import com.juneyaoair.baseclass.response.easyServices.ChildrenOrderApplyResponse;
import com.juneyaoair.baseclass.response.easyServices.MpGoodsLostResp;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * Created by zhuangjinlei on 2016/9/6.
 */
@RequestMapping("easyServices")
@RestController
public class EasyServicesController extends BassController {

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private HandConfig handConfig;

    @RequestMapping(value="/goodsLostSub",method = RequestMethod.POST)
    public MpGoodsLostResp goodsLostSubmit(@RequestBody MpGoodsLostReq req, HttpServletRequest request){
        MpGoodsLostResp response = new MpGoodsLostResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MpGoodsLostReq>> violations = validator.validate(req);
        if(null!=violations && violations.size()>0){
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String sRandom = apiRedisService.getData(req.getChannelId()+"LoseArticleCode");//缓存的验证码
        String vcode=req.getvCode();//用户输入的验证码
        log.info("手机登录 验证码:传入{},缓存中为{}", vcode, sRandom);
        if (StringUtil.isNullOrEmpty(sRandom) ||StringUtil.isNullOrEmpty(vcode)|| (!(vcode.toUpperCase()).equals(sRandom))) {
            String msg = "验证码错误";
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(msg);
            return response;
        }
        try {
            String fltNo = "HO"+req.getFlightNo();
            String params = "userid=B2C&password=" + StringUtil.encodePwd("123456") + "&fltNo="+fltNo+"&fltDt="+req.getFlightDateStr()+"&assgnSt="+req.getAssgnSt()+"&paxChnNm="+req.getContactName()+"&phoNbr="+req.getContactPhone()+"&lstStf="+req.getDetails();
            String url = HandlerConstants.GOODS_LOST_URL;
            if(!StringUtil.isNullOrEmpty(sRandom)){
                apiRedisService.removeData(req.getChannelId());//清除缓存的验证码
            }
            HttpResult result = HttpUtil.doGetClient(url+"?"+params, handConfig.getReadTimeout(),handConfig.getConnectTimeout());
            JSONObject jsonObject = JSONObject.parseObject(result.getResponse());
            if (jsonObject.get("resultCode").equals("success")) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("提交失败");
            }
        } catch (Exception e) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo("操作失败");
        }


        return response;
    }

    @RequestMapping(value="/minorApply",method = RequestMethod.POST)
    public ChildrenOrderApplyResponse minorApply(@RequestBody ChildrenOrderApplyRequest req, HttpServletRequest request){
        ChildrenOrderApplyResponse response = new ChildrenOrderApplyResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ChildrenOrderApplyRequest>> violations = validator.validate(req);
        if(null!=violations && violations.size()>0){
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }

        req.setOrderRequestIp(IPUtil.getIpAddr(request));
        req.setVersion(HandlerConstants.VERSION);
        req.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
        req.setUserNo(HandlerConstants.M_USER_NO);
        try {
            String url = HandlerConstants.UNACCOMPANIED_MINORWEB_URL+HandlerConstants.UNACCOMPANIED_MINOR_APPLY;
            response = JsonUtil.getGson().fromJson( this.doHttp(req, url, "POST").getResponse(), ChildrenOrderApplyResponse.class);
            if (response.getResultCode().equals("1001")) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                String logStr = JsonUtil.objectToJson(response);
                log.error("调用呼叫中心无陪接口失败response：{}", logStr);
            }

        } catch (Exception e) {
            log.error("调用呼叫中心无陪接口异常", e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("操作失败");
        }
        return response;
    }
}
