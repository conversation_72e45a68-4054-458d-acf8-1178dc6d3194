package com.juneyaoair.mobile.handler.controller.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.base.CheckDayLicense;
import com.juneyaoair.baseclass.common.base.DayLicenseEnum;
import com.juneyaoair.baseclass.common.request.BaseRequestDTO;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.cuss.response.SeatOrderInfo;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductOrderGetRequestDto;
import com.juneyaoair.baseclass.newcoupon.req.NewCouponOrderDetailReq;
import com.juneyaoair.baseclass.newcoupon.req.protocol.NewBasicCouponOrderDetail;
import com.juneyaoair.baseclass.newcoupon.resp.NewBasicOrderDetailResponse;
import com.juneyaoair.baseclass.request.av.AirCompany;
import com.juneyaoair.baseclass.request.newCheckinSeat.NewCheckInQuery;
import com.juneyaoair.baseclass.response.newCheckinSeat.FlightTourPsgInfo;
import com.juneyaoair.baseclass.response.newCheckinSeat.FlightTourResponse;
import com.juneyaoair.baseclass.response.newCheckinSeat.FlightTourSegDetails;
import com.juneyaoair.baseclass.response.newCheckinSeat.PaySeatOrderInfo;
import com.juneyaoair.baseclass.response.order.query.SaleCouponGetResponse;
import com.juneyaoair.baseclass.response.seat.TravellerTripTipResult;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.cuss.dto.booking.ENUM_RESPONSE_STATUS;
import com.juneyaoair.cuss.dto.booking.response.flighttour.OrderInfoDTO;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.mileage.AirlineMileageResult;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.FlightTourService;
import com.juneyaoair.mobile.handler.controller.util.CussObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.FraudApiInvoker;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.CheckLicenseService;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberCertificateSoaModelV2;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.checkin.QueryTourRequestDTO;
import com.juneyaoair.thirdentity.response.checkin.FlightTourDTO;
import com.juneyaoair.thirdentity.response.checkin.QueryTourResponseDTO;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponOrderGetResponse;
import com.juneyaoair.thirdentity.tongdun.FinalDecisionEnum;
import com.juneyaoair.thirdentity.tongdun.response.FraudApiResponse;
import com.juneyaoair.util.ControllerUtil;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.MetricLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: caolei
 * @Description: FlightTourService
 * @Date: 2021/6/22 17:37
 * @Modified by:
 */
@Slf4j
@Service
public class FlightTourServiceImpl implements FlightTourService {

    /**
     * 暂未开放值机选座服务，请前往承运方———东方航空官网办理
     */
    private static final String CLOSE_CHECKIN_SEAT_MU = "暂未开放值机选座服务，请前往承运方———东方航空官网办理";

    /**
     * 距起飞少于2小时，不能办理值机选座
     */
    private static final String CLOSE_CHECKIN_SEAT_C = "距航班起飞已少于50分钟，已不能办理线上值机选座，请提前至值机柜台办理";

    private static final String BACK_UP_TICKET_TIP = "当前客票为免票，请到柜台办理值机";

    /**
     * 值机开放
     */
    private static final String CHECKIN_OPEN = "CHECKIN OPEN";
    /**
     * 选座开放
     */
    private static final String SELECT_OPEN = "SELECT OPEN";
    /**
     * 值机选座关闭
     */
    private static final String CHECKINSEAT_CLOSE = "NONE";

    /**
     * 超时时间，用于查值机选座列表、查座位图、做值机选座，其他接口默认超时时间8s
     */
    private static final int READ_TIMEOUT = 40000;
    private static final int CONNECT_TIMEOUT = 40000;

    @Autowired
    private HandConfig handConfig;

    @Autowired
    private IMemberService memberService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private CheckLicenseService checkLicenseService;
    @Autowired
    private OrderManage orderManage;

    @Override
    public FlightTourResponse queryAllFlightTours(NewCheckInQuery newCheckInQuery, HttpServletRequest request) {
        FlightTourResponse flightTourResponse = new FlightTourResponse();
        String ip = IPUtil.getIpAddr(request);
        String channelCodeHead = request.getHeader("channelCode");
        String clientVersion = request.getHeader(HandlerConstants.CLIENT_VERSION);
        String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        //输入参数校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<NewCheckInQuery>> violations = validator.validate(newCheckInQuery);
        if (null != violations && !violations.isEmpty()) {
            flightTourResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            flightTourResponse.setErrorInfo(violations.iterator().next().getMessage());
            return flightTourResponse;
        }
        String clientIp = IPUtil.getIpAddr(request);
        //前端参数不完整的情况下，采用头部平台信息
        if (StringUtils.isBlank(newCheckInQuery.getPlatformInfo())) {
            newCheckInQuery.setPlatformInfo(platform);
        }
        //会员查询时不进行同盾检验
        if ("Y".equals(handConfig.getCheckinSeatTongDun())) {
            String multiProxy = IPUtil.isMultyProxy(request) ? "1" : "0";
            FraudApiResponse fraudApiResponse = FraudApiInvoker.checkinSeatControl(newCheckInQuery.getBlackBox(), newCheckInQuery.getPlatformInfo(), channelCodeHead,
                    ip, multiProxy, newCheckInQuery.getFfpCardNo(), clientVersion);
            if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                flightTourResponse.setResultCode(WSEnum.TONGDUN_CHECKIN_FAIL_TRADE.getResultCode());
                flightTourResponse.setErrorInfo(WSEnum.TONGDUN_CHECKIN_FAIL_TRADE.getResultInfo());
                return flightTourResponse;
            }
        }

        //type使用,历史原因，机场二维码支持值机不传参数
        String source = request.getHeader("token");

        String type = "";
        if ("CHECKIN".equals(source) || StringUtil.isNullOrEmpty(channelCodeHead) || channelCodeHead.equals(HandlerConstants.W_CHANNEL_CODE) || channelCodeHead.equals("MWEB")) {
            if (StringUtil.isNullOrEmpty(newCheckInQuery.getQueryType()) || newCheckInQuery.getQueryType().equalsIgnoreCase("checkin")) {
                type = "CHECKIN";
            } else if (newCheckInQuery.getQueryType().equalsIgnoreCase("seat")) {
                type = "SEAT";
            }
        }
        List<QueryTourRequestDTO> queryTourList = Lists.newArrayList();
        // 20210112 Special Treatment: MOBILE/MWEB/WXAPP terminal forgot to set the parameter——type.
        // When type is empty and passenger info is empty, use member info to search flight tours.
        if (StringUtils.isBlank(newCheckInQuery.getCertNo()) && StringUtils.isBlank(newCheckInQuery.getType()) && StringUtils.isNotBlank(newCheckInQuery.getFfpCardNo())) {
            newCheckInQuery.setType("member");
        }
        if (StringUtils.isNotBlank(newCheckInQuery.getType())) {
            if (!ControllerUtil.checkKeyInfo(newCheckInQuery.getFfpId() + "", newCheckInQuery.getLoginKeyInfo(), newCheckInQuery.getChannelCode())) {
                flightTourResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                flightTourResponse.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return flightTourResponse;
            }
        }
        // 会员本人查询
        if ("member".equalsIgnoreCase(newCheckInQuery.getType())) {
            List<QueryTourRequestDTO> memberCardList = dealPsgNameAndCertNo(request, newCheckInQuery);
            queryTourList.addAll(memberCardList);
        }
        // 历史记录查询
        else if ("history".equals(newCheckInQuery.getType())) {
            // 查询账号值机选座记录
            QueryTourRequestDTO queryTourRequestDTO = new QueryTourRequestDTO();
            queryTourRequestDTO.setMemberId(String.valueOf(newCheckInQuery.getFfpId()));
            queryTourList.add(queryTourRequestDTO);
        } else {
            //度量记录
            JSONObject tags = new JSONObject();
            tags.put("IP",ip);
            tags.put("FfpCardNo",newCheckInQuery.getFfpCardNo());
            tags.put("ChannelCode",channelCodeHead);
            MetricLogUtil.saveMetricLog("值机选座-客票提取",tags,new BigDecimal(1));
            if (StringUtils.isAnyBlank(newCheckInQuery.getCertNo(), newCheckInQuery.getPsgName())) {
                flightTourResponse.setResultCode(WSEnum.ERROR.getResultCode());
                flightTourResponse.setErrorInfo("查询航班行程信息请求参数有误！");
                return flightTourResponse;
            }
            //if (StringUtils.isAnyBlank(newCheckInQuery.getFlightNo(), newCheckInQuery.getFlightDate(), newCheckInQuery.getDepCityCode(), newCheckInQuery.getArrCityCode())) {
            if (StringUtils.isBlank(newCheckInQuery.getFlightNo())) {
                flightTourResponse.setResultCode(WSEnum.ERROR.getResultCode());
                flightTourResponse.setErrorInfo("查询航班行程信息请求参数有误！");
                return flightTourResponse;
            }
            QueryTourRequestDTO queryTourRequestDTO = new QueryTourRequestDTO();
            queryTourRequestDTO.setCertificateNo(newCheckInQuery.getCertNo());
            queryTourRequestDTO.setPassengerName(newCheckInQuery.getPsgName());
            queryTourRequestDTO.setQueryType(type);
            queryTourList.add(queryTourRequestDTO);
            // 限制查询次数
            CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(newCheckInQuery.getFfpCardNo(), DayLicenseEnum.QUERY_FLIGHT_TOURS_FFP, "航班行程查询达到上限");
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(newCheckInQuery.getFfpCardNo(), DayLicenseEnum.QUERY_FLIGHT_TOURS_IP, "航班行程查询达到上限");
            checkLicenseService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        }
        // 行程清单
        List<FlightTourDTO> tours = Lists.newArrayList();
        String requestId = MdcUtils.getRequestId();
        // 基于查询条件查询行程信息
        queryTourList.parallelStream().forEach(queryTourRequest -> {
            BaseRequestDTO<QueryTourRequestDTO> baseRequest = new BaseRequestDTO<>();
            try {
                //接口参数封装
                baseRequest.setIp(clientIp);
                baseRequest.setChannelCode(channelCodeHead);
                baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
                baseRequest.setRequest(queryTourRequest);
                HttpResult httpResult = HttpUtil.doPostClient(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.QUERY_TOUR, null, READ_TIMEOUT, CONNECT_TIMEOUT);
                if (!httpResult.isResult()) {
                    return;
                }
                BaseResultDTO<QueryTourResponseDTO> res = JsonMapper.buildNonNullMapper().fromJson(httpResult.getResponse(), BaseResultDTO.class, QueryTourResponseDTO.class);
                if (null == res || !ENUM_RESPONSE_STATUS.SUCC.code().equals(res.getResultCode())) {
                    return;
                }
                if (null == res.getResult() || CollectionUtils.isEmpty(res.getResult().getTours())) {
                    return;
                }
                tours.addAll(res.getResult().getTours());
            } catch (Exception e) {
                log.error("查询旅客行程信息失败，请求ID：{} 请求参数：{}", requestId, JSON.toJSONString(baseRequest));
            }
        });
        //筛选出可选座或值机列表和不可选作值机列表
        List<FlightTourSegDetails> checkInSeatSegList = Lists.newArrayList();
        List<FlightTourSegDetails> unPaySegList = Lists.newArrayList();
        List<FlightTourSegDetails> unCheckInSeatSegList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(tours)) {
            if ("history".equals(newCheckInQuery.getType())) {
                flightTourResponse.setCheckInSeatSegList(checkInSeatSegList);
                flightTourResponse.setUnCheckInSeatSegList(unCheckInSeatSegList);
                flightTourResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                flightTourResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return flightTourResponse;
            }
            flightTourResponse.setResultCode(WSEnum.ERROR.getResultCode());
            flightTourResponse.setErrorInfo("对应证件号无匹配行程，请确认证件号是否有误");
            return flightTourResponse;
        }
        try {
            // 外航承运客票处理 传入票号验证是否存在外航承运航班
            String certNo = null != newCheckInQuery.getCertNo() ? newCheckInQuery.getCertNo() : null;
            boolean queryByTn = StringUtils.isNotBlank(certNo) && "TN".equals(CertNoUtil.getCertTypeByCertNo(certNo));

            //数据转换
            List<FlightTourSegDetails> segDetails = convertSegPsgInfo(tours, newCheckInQuery.getChannelCode(), clientIp);
            Stream<FlightTourSegDetails> stream = segDetails.stream();
            // 基于入参筛选数据
            if (StringUtils.isNotBlank(newCheckInQuery.getFlightNo())) {
                stream = stream.filter(f -> newCheckInQuery.getFlightNo().equalsIgnoreCase(f.getMarketingFlightNo()) ||
                                            newCheckInQuery.getFlightNo().equalsIgnoreCase(f.getFlightNo()) ||
                                            newCheckInQuery.getFlightNo().equalsIgnoreCase(f.getOperationFlightNo()));
            }
            if (StringUtils.isNotBlank(newCheckInQuery.getFlightDate())) {
                stream = stream.filter(f -> newCheckInQuery.getFlightDate().equals(f.getFlightDate()));
            }
            /*
            if (StringUtils.isNotBlank(newCheckInQuery.getDepCityCode())) {
                stream = stream.filter(f -> newCheckInQuery.getDepCityCode().equals(f.getDepCityCode()));
            }
            if (StringUtils.isNotBlank(newCheckInQuery.getArrCityCode())) {
                stream = stream.filter(f -> newCheckInQuery.getArrCityCode().equals(f.getArrCityCode()));
            }*/
            segDetails = stream.collect(Collectors.toList());
            if (CollectionUtils.isEmpty(segDetails)) {
                flightTourResponse.setResultCode(WSEnum.NO_DATA.getResultCode());
                flightTourResponse.setErrorInfo("无匹配行程信息，请确认行程信息是否有误");
                return flightTourResponse;
            }
            //遍历出相同航段的旅客
            for (FlightTourSegDetails flightTourSegDetails : segDetails) {
                // 承运航司不为空 且 非吉祥承运
                if (StringUtils.isNotBlank(flightTourSegDetails.getOperationAirline()) && !"HO".equalsIgnoreCase(flightTourSegDetails.getOperationAirline())) {
                    // 基于票号查询
                    if (queryByTn) {
                        flightTourResponse.setResultCode(WSEnum.ERROR.getResultCode());
                        flightTourResponse.setErrorInfo("请至实际承运本航班的航空公司办理值机选座");
                        return flightTourResponse;
                    } else {
                        log.info("航班号：{} 航班日期：{} 请至实际承运本航班的航空公司办理值机选座，实际承运航司：{}", flightTourSegDetails.getFlightNo(), flightTourSegDetails.getFlightDate(), flightTourSegDetails.getOperationAirline());
                        continue;
                    }
                }
                // 存在承运航班 且 非吉祥承运 标记不支持值机选座
                if (StringUtils.isNotBlank(flightTourSegDetails.getOperationFlightNo()) && !flightTourSegDetails.getOperationFlightNo().startsWith("HO")) {
                    log.info("航班号：{} 航班日期：{} 非吉祥承运航班，数据信息：{}", flightTourSegDetails.getFlightNo(), flightTourSegDetails.getFlightDate(), JsonUtil.objectToJson(flightTourSegDetails));
                    flightTourSegDetails.setCanCheckInSelect(false);
                    flightTourSegDetails.setUnCheckSeatReason(CLOSE_CHECKIN_SEAT_MU);
                    flightTourSegDetails.setCheckInSeatStatus(CHECKINSEAT_CLOSE);
                    unCheckInSeatSegList.add(flightTourSegDetails);
                    continue;
                }
                //不可值机选座原因分析
                //值机选座关闭
                if (flightTourSegDetails.getCheckInSeatStatus().equals(CHECKINSEAT_CLOSE)) {
                    //不显示选座值机按钮
                    flightTourSegDetails.setCanCheckInSelect(false);
                    Date now = new Date();
                    String flightTimeStr = flightTourSegDetails.getFlightDate() + " " + flightTourSegDetails.getSchDeptTime() + ":00";
                    Date flightTime = DateUtils.toTotalDate(flightTimeStr);
                    //判断当前时间距起飞时间是否小于50分钟
                    if ((flightTime.getTime() - now.getTime()) < 50 * 60 * 1000) {
                        boolean flag = false;
                        for (FlightTourPsgInfo flightTourPsgInfo : flightTourSegDetails.getFlightTourPsgInfoList()) {
                            if (!StringUtil.isNullOrEmpty(flightTourPsgInfo.getCheckInSeatNo())) {
                                flag = true;
                                break;
                            }
                        }
                        flightTourSegDetails.setUnCheckSeatReason(CLOSE_CHECKIN_SEAT_C);
                        if (flag) {
                            checkInSeatSegList.add(flightTourSegDetails);
                            continue;
                        }
                    } else {
//                                flightTourSegDetails.getFlightTourPsgInfoList().stream().filter(flightTourPsgInfo ->
//                                        "N".equals(flightTourPsgInfo.getCabin())).forEach(flightTourPsgInfo ->
//                                        flightTourPsgInfo.setSpecialPsgTip(BACK_UP_TICKET_TIP));
                        if (flightTourSegDetails.getFlightTourPsgInfoList().stream().anyMatch(flightTourPsgInfo -> "N".equals(flightTourPsgInfo.getCabin()))) {
                            flightTourSegDetails.setUnCheckSeatReason(BACK_UP_TICKET_TIP);
                        } else {
                            flightTourSegDetails.setUnCheckSeatReason(handConfig.getClosedCheckInTips());
                        }
                    }
                    unCheckInSeatSegList.add(flightTourSegDetails);
                } else {
                    //值机选座未关闭
                    //过滤全部旅客都为特殊旅客
                    int con = 0;
                    for (FlightTourPsgInfo flightTourPsgInfo : flightTourSegDetails.getFlightTourPsgInfoList()) {
                        if (!StringUtil.isNullOrEmpty(flightTourPsgInfo.getSpecialPsgTip())) {
                            con++;
                        }
                    }
                    if (con == flightTourSegDetails.getFlightTourPsgInfoList().size()) {
                        unCheckInSeatSegList.add(flightTourSegDetails);
                        continue;
                    }
                    int cnt = 0;
                    //判断选座值机时选座按钮是否显示
                    if (flightTourSegDetails.getCheckInSeatStatus().equals(SELECT_OPEN)) {
                        for (FlightTourPsgInfo flightTourPsgInfo : flightTourSegDetails.getFlightTourPsgInfoList()) {
                            //判断订单信息为空，兼容m站或官网的选座信息
                            if (!StringUtil.isNullOrEmpty(flightTourPsgInfo.getAsrSeatNo()) && (null == flightTourPsgInfo.getPaySeatOrderInfo() || flightTourPsgInfo.getPaySeatOrderInfo().getPaySeatState() == 2 || flightTourPsgInfo.getPaySeatOrderInfo().getPaySeatState() == 3)) {
                                cnt++;
                            }
                        }
                    } else if (flightTourSegDetails.getCheckInSeatStatus().equals(CHECKIN_OPEN)) {
                        for (FlightTourPsgInfo flightTourPsgInfo : flightTourSegDetails.getFlightTourPsgInfoList()) {
                            if (!StringUtil.isNullOrEmpty(flightTourPsgInfo.getCheckInSeatNo())) {
                                cnt++;
                            }
                        }
                    }
                    flightTourSegDetails.setCanCheckInSelect(cnt != flightTourSegDetails.getFlightTourPsgInfoList().size());
                    if (flightTourSegDetails.getFlightTourPsgInfoList().stream().anyMatch(flightTourPsgInfo -> "N".equals(flightTourPsgInfo.getCabin()))) {
                        flightTourSegDetails.setCanCheckInSelect(false);
                        flightTourSegDetails.setUnCheckSeatReason(BACK_UP_TICKET_TIP);
                        unCheckInSeatSegList.add(flightTourSegDetails);
                    } else if (flightTourSegDetails.getFlightTourPsgInfoList().stream().
                            anyMatch(flightTourPsgInfo -> null != flightTourPsgInfo.getPaySeatOrderInfo() && flightTourPsgInfo.getPaySeatOrderInfo().getPaySeatState() == 1)) {
                        // 订单状态为未支付状态 不在展示选座按钮
                        flightTourSegDetails.setCanCheckInSelect(false);
                        flightTourSegDetails.setCheckInSeatStatus(CHECKINSEAT_CLOSE);
                        unPaySegList.add(flightTourSegDetails);
                    } else {
                        List<FlightTourPsgInfo> flightTourPsgInfoList = flightTourSegDetails.getFlightTourPsgInfoList();
                        Set<String> typeSet = Sets.newHashSet();
                        for (FlightTourPsgInfo flightTourPsgInfo : flightTourPsgInfoList) {
                            // 选座座位号 值机座位号都不存在
                            if (StringUtils.isBlank(flightTourPsgInfo.getAsrSeatNo()) && StringUtils.isBlank(flightTourPsgInfo.getCheckInSeatNo())) {
                                typeSet.add("NONE");
                            }
                            // 存在值机座位号
                            else if (StringUtils.isNotBlank(flightTourPsgInfo.getCheckInSeatNo())) {
                                typeSet.add("CHECK");
                            } else {
                                typeSet.add("SEAT");
                            }
                        }
                        TravellerTripTipResult travellerTripTip = CussObjectConvert.getTravellerTripTip(flightTourSegDetails.getDepAirport(), flightTourSegDetails.getArrAirport(), typeSet);
                        //行程列表文案
                        flightTourSegDetails.setTourTip(travellerTripTip.getTourTip());
                        checkInSeatSegList.add(flightTourSegDetails);
                    }
                }
            }
            // 按起飞时间排序
            Collections.sort(unPaySegList);
            Collections.sort(checkInSeatSegList);
            Collections.sort(unCheckInSeatSegList);

            List<FlightTourSegDetails> checkInSeatSegTmpList = Lists.newArrayList();
            checkInSeatSegTmpList.addAll(unPaySegList);
            checkInSeatSegTmpList.addAll(checkInSeatSegList);
            flightTourResponse.setCheckInSeatSegList(checkInSeatSegTmpList);
            flightTourResponse.setUnCheckInSeatSegList(unCheckInSeatSegList);
            //排序
            flightTourResponse = sortFlightTours(flightTourResponse);
            flightTourResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
            flightTourResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            log.error("queryAllFlightTours出现异常，异常原因：", e);
            flightTourResponse.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            flightTourResponse.setErrorInfo("网络出错了，请重新提交");
            return flightTourResponse;
        }
        return flightTourResponse;
    }

    private List<QueryTourRequestDTO> dealPsgNameAndCertNo(HttpServletRequest request, NewCheckInQuery newCheckInQuery) {
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
        PtApiCRMRequest ptApiCRMRequest = ControllerUtil.buildCommReqNoToken(request, newCheckInQuery.getChannelCode());
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(newCheckInQuery.getFfpCardNo());
        ptMemberDetailRequest.setRequestItems(items);
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        if (ptCRMResponse.getCode() != 0) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "查询会员信息出现异常");
        }
        List<MemberCertificateSoaModelV2> certificateInfoList = ptCRMResponse.getData().getCertificateInfo();
        if (CollectionUtils.isEmpty(certificateInfoList)) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), WSEnum.NO_REAL_NAME.getResultInfo());
        }
        // 是否存在实名证件
        Optional<MemberCertificateSoaModelV2> certInfo = certificateInfoList.stream().filter(MemberCertificateSoaModelV2::isVerify).findAny();
        if (!certInfo.isPresent()) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), WSEnum.NO_REAL_NAME.getResultInfo());
        }
        List<QueryTourRequestDTO> queryTourList = Lists.newArrayList();
        for (MemberCertificateSoaModelV2 memberCertificateSoaModel : certificateInfoList) {
            CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkType(memberCertificateSoaModel.getCertificateType());
            String certificateNo;
            String passengerName;
            // 身份证使用中文姓名，其他证件使用英文姓名
            if (CertificateTypeEnum.ID_CARD.equals(certificateTypeEnum)) {
                passengerName = ptCRMResponse.getData().getBasicInfo().getCLastName() + ptCRMResponse.getData().getBasicInfo().getCFirstName();
                certificateNo = memberCertificateSoaModel.getCertificateNumber();
            } else if (CertificateTypeEnum.PASSPORT.equals(certificateTypeEnum) || CertificateTypeEnum.HK_MACAO_PASS.equals(certificateTypeEnum)) {
                passengerName = ptCRMResponse.getData().getBasicInfo().getELastName() + "/" + ptCRMResponse.getData().getBasicInfo().getEFirstName();
                certificateNo = memberCertificateSoaModel.getCertificateNumber();
            } else {
                continue;
            }
            QueryTourRequestDTO queryTourRequestDTO = new QueryTourRequestDTO();
            queryTourRequestDTO.setCertificateNo(certificateNo);
            queryTourRequestDTO.setPassengerName(passengerName);
            queryTourList.add(queryTourRequestDTO);
        }
        if (CollectionUtils.isEmpty(queryTourList)) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), "无有效证件信息");
        }
        return queryTourList;
    }

    /** 列表排序
     * 已选座、已值机类别，位于待办类别下方，且按起飞时间由近及远排序；
     * @param flightTours
     * @return
     */
    private FlightTourResponse sortFlightTours(FlightTourResponse flightTours) {
        FlightTourResponse flightTourResponse = new FlightTourResponse();
        try {
            List<FlightTourSegDetails> checkInSeats = new ArrayList<>();
            List<FlightTourSegDetails> haveCheckInSeats = new ArrayList<>();
            FlightTourPsgInfo flightTourPsgInfo = null;
            for (FlightTourSegDetails flightTourSegDetail : flightTours.getCheckInSeatSegList()) {
                for (int i = 0; i < flightTourSegDetail.getFlightTourPsgInfoList().size(); i++) {
                    flightTourPsgInfo = flightTourSegDetail.getFlightTourPsgInfoList().get(i);
                    //判断是否有已值机或选座
                    if (!StringUtil.isNullOrEmpty(flightTourPsgInfo.getAsrSeatNo()) || !StringUtil.isNullOrEmpty(flightTourPsgInfo.getCheckInSeatNo())) {
                        haveCheckInSeats.add(flightTourSegDetail);
                        break;
                    }
                    if (i == flightTourSegDetail.getFlightTourPsgInfoList().size() - 1) {
                        checkInSeats.add(flightTourSegDetail);
                    }
                }
            }
            checkInSeats.addAll(haveCheckInSeats);
            flightTourResponse.setCheckInSeatSegList(checkInSeats);
            flightTourResponse.setUnCheckInSeatSegList(flightTours.getUnCheckInSeatSegList());
        } catch (Exception e) {
            log.error("值机选座列表排序异常：{}", e.getMessage());
        }
        return flightTourResponse;
    }

    /**
     * 将详细的明细信息转换
     *
     * @param tourList
     * @return
     */
    @Override
    public List<FlightTourSegDetails> convertSegPsgInfo(List<FlightTourDTO> tourList, String channelCode, String ip) {
        List<FlightTourSegDetails> responseList = new ArrayList<>();
        FlightTourSegDetails flightTourInfoDetails = null;
        List<FlightTourPsgInfo> psgList = null;
        FlightTourPsgInfo flightTourPsgInfo = null;
        //标志位map
        Map<String, String> markMap = new HashMap<>();
        for (FlightTourDTO flightTour : tourList) {
            String mark = flightTour.getEtCode() + flightTour.getFlightDate() + flightTour.getFlightNo() + flightTour.getDepAirport() + flightTour.getArrAirport();
            markMap.put(mark, "");
        }
        Map<String, AirCompany> airCompanyMap = FlightUtil.toAirCompanyMap(handConfig.getAirCompany());

        //遍历出相同航段的旅客
        outer : for (String key : markMap.keySet()) {
            flightTourInfoDetails = new FlightTourSegDetails();
            psgList = new ArrayList<>();
            for (FlightTourDTO temp : tourList) {
                flightTourPsgInfo = new FlightTourPsgInfo();
                String tempMark = temp.getEtCode() + temp.getFlightDate() + temp.getFlightNo() + temp.getDepAirport() + temp.getArrAirport();
                if (key.equals(tempMark)) {
                    BeanUtils.copyNotNullProperties(temp, flightTourInfoDetails);
                    BeanUtils.copyNotNullProperties(temp, flightTourPsgInfo);
                    //特殊旅客信息转换
                    CussObjectConvert.covertSpecialPsgInfo(flightTourPsgInfo, temp);
                    //选座id
                    flightTourPsgInfo.setId(temp.getSelectSeatInfoId());
                    OrderInfoDTO orderInfo = temp.getOrderInfo();
                    // 存在订单信息封装订单信息
                    if (null != orderInfo && StringUtils.isNotBlank(orderInfo.getOrderNo())){
                        // 20190328 支付订单信息
                        PaySeatOrderInfo paySeatOrderInfo = new PaySeatOrderInfo();
                        BeanUtils.copyProperties(orderInfo, paySeatOrderInfo);
                        String payKeyInfo = ControllerUtil.createAccesskey(temp.getEtCode() + temp.getOrderInfo().getOrderNo(), "");
                        paySeatOrderInfo.setPayKeyInfo(payKeyInfo);
                        // 查订单状态
                        NewCouponOrderDetailReq couponOrderDetailReq = new NewCouponOrderDetailReq();
                        couponOrderDetailReq.setChannelCode(channelCode);
                        couponOrderDetailReq.setRequestIp(ip);
                        couponOrderDetailReq.setVersion(HandlerConstants.VERSION);
                        couponOrderDetailReq.setUserNo(ControllerUtil.getChannelInfo(channelCode, "10"));
                        couponOrderDetailReq.setOrderNo(orderInfo.getOrderNo());
                        couponOrderDetailReq.setChannelOrderNo(orderInfo.getChannelOrderNo());
                        couponOrderDetailReq.setCouponCodeList(Lists.newArrayList(orderInfo.getCouponCode()));
                        NewBasicOrderDetailResponse newBasicOrderDetailResponse = orderManage.queryCouponOrderDetail(couponOrderDetailReq);
                        if (null == newBasicOrderDetailResponse || CollectionUtils.isEmpty(newBasicOrderDetailResponse.getBasicCouponOrderDtoList())){
                            continue outer;
                        }
                        // 外部订单信息
                        SeatOrderInfo seatOrderInfo = new SeatOrderInfo();
                        seatOrderInfo.setOrderNo(orderInfo.getOrderNo());
                        seatOrderInfo.setOrderChannelOrderNo(orderInfo.getChannelOrderNo());
                        seatOrderInfo.setIsSelf(newBasicOrderDetailResponse.getIsSelf());
                        flightTourInfoDetails.setOrderResult(seatOrderInfo);
                        //当前人座位的状态
                        String couponState = "";
                        for (NewBasicCouponOrderDetail newBasicCouponOrderDetail : newBasicOrderDetailResponse.getBasicCouponOrderDtoList()) {
                            if (newBasicCouponOrderDetail.getTicketNo().equals(flightTourPsgInfo.getEtCode()) &&
                                "PaySeat".equals(newBasicCouponOrderDetail.getCouponSource())) {
                                couponState = newBasicCouponOrderDetail.getCouponState();
                            }
                        }
                        // 处理前端支付/选座按钮展示
                        int paySeatState = 0;
                        // 未支付
                        if ("UnPay".equals(newBasicOrderDetailResponse.getPayState()) && !"Cancel".equals(couponState)) {
                            paySeatState = 1;
                        }
                        // 已选座
                        else if ("Not".equals(couponState)) {
                            paySeatState = 2;
                        }
                        // 确认中
                        else if ("Apply".equals(couponState)) {
                            paySeatState = 3;
                        }
                        paySeatOrderInfo.setPaySeatState(paySeatState);
                        flightTourPsgInfo.setPaySeatOrderInfo(paySeatOrderInfo);
                    }
                    //机型名称
                    flightTourInfoDetails.setPlaneTypeName(ControllerUtil.getPlaneTypeName(flightTourInfoDetails.getPlaneType()));
                    flightTourInfoDetails.setPlaneType(ControllerUtil.getPlaneType(flightTourInfoDetails.getPlaneType()));
                    if (!temp.isCanCheckIn() && !temp.isCanSelectSeat()) {
                        flightTourInfoDetails.setCheckInSeatStatus(CHECKINSEAT_CLOSE);
                    } else if (temp.isCanSelectSeat()) {
                        flightTourInfoDetails.setCheckInSeatStatus(SELECT_OPEN);
                    } else if (temp.isCanCheckIn()) {
                        flightTourInfoDetails.setCheckInSeatStatus(CHECKIN_OPEN);
                    }
                    //值机关闭
                    flightTourInfoDetails.setCloseCheckIn(temp.getCloseCheckIn() == null ? false : temp.getCloseCheckIn());
                    psgList.add(flightTourPsgInfo);
                    flightTourInfoDetails.setFlightTourPsgInfoList(psgList);
                }
            }
            //机场名城市名获取
            AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(flightTourInfoDetails.getDepAirport(),flightTourInfoDetails.getFlightDate());
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(flightTourInfoDetails.getArrAirport(),flightTourInfoDetails.getFlightDate());
            if (!StringUtil.isNullOrEmpty(flightTourInfoDetails.getStopAirport())) {
                AirPortInfoDto stopAirportInfo = localCacheService.getLocalAirport(flightTourInfoDetails.getStopAirport(),flightTourInfoDetails.getFlightDate());
                flightTourInfoDetails.setStopCityName(stopAirportInfo.getCityName());
                flightTourInfoDetails.setStopAirportName(stopAirportInfo.getAirPortName());
            }
            // 间隔天数
            int days = DateUtils.diffDays(flightTourInfoDetails.getSchArrDate(), flightTourInfoDetails.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
            flightTourInfoDetails.setCrossDay(Math.max(days, 0));
            //处理时长
            String duration = null != flightTourInfoDetails.getDuration() ? flightTourInfoDetails.getDuration() : "";
            flightTourInfoDetails.setDuration(duration.startsWith("0") ? duration.substring(1) : duration);
            flightTourInfoDetails.setDepCityName(depAirPortInfo.getCityName());
            flightTourInfoDetails.setArrCityName(arrAirportInfo.getCityName());
            flightTourInfoDetails.setDepCityCode(depAirPortInfo.getCityCode());
            flightTourInfoDetails.setArrCityCode(arrAirportInfo.getCityCode());
            flightTourInfoDetails.setDepAirportName(depAirPortInfo.getAirPortName());
            flightTourInfoDetails.setArrAirportName(arrAirportInfo.getAirPortName());
            flightTourInfoDetails.setMarketingAirLineIcon(FlightUtil.convertCompany(airCompanyMap, flightTourInfoDetails.getMarketingFlightNo()));
            flightTourInfoDetails.setOperationAirLineIcon(FlightUtil.convertCompany(airCompanyMap, flightTourInfoDetails.getOperationFlightNo()));
            if (null == flightTourInfoDetails.getDepTerminal() || flightTourInfoDetails.getDepTerminal().indexOf("--") > -1) {
                flightTourInfoDetails.setDepTerminal("");
            }
            if (null == flightTourInfoDetails.getArrTerminal() || flightTourInfoDetails.getArrTerminal().indexOf("--") > -1) {
                flightTourInfoDetails.setArrTerminal("");
            }
            AirlineMileageResult airlineMileageResult = basicService.getAirlineMileageByCity(depAirPortInfo.getCityCode(), arrAirportInfo.getCityCode());
            if (null != airlineMileageResult){
                flightTourInfoDetails.setMileage(airlineMileageResult.getMileage());
            }
            responseList.add(flightTourInfoDetails);
        }
        return responseList;
    }

    @Override
    public List<PtSaleCouponOrderGetResponse.CouponOrder> querySeatOrderInfo(String channelCode, List<String> orderNos, String ip) {
        List<PtSaleCouponOrderGetResponse.CouponOrder> saleOrderList = new LinkedList<>();
        //构建请求类
        CouponProductOrderGetRequestDto requestDto = new CouponProductOrderGetRequestDto();
        requestDto.setVersion(HandlerConstants.VERSION);
        requestDto.setChannelCode(channelCode);
        requestDto.setUserNo(ControllerUtil.getChannelInfo(channelCode, "10"));
        requestDto.setIsRemoved("0");
        requestDto.setPageNo(1);
        requestDto.setPageSize(1000);
        //发起请求
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        for (String order : orderNos) {
            requestDto.setOrderNo(order);
            String result = HttpUtil.invokePost(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_ORDER_PRODUCT, headMap);
            if (!StringUtil.isNullOrEmpty(result)) {
                SaleCouponGetResponse resp = (SaleCouponGetResponse) JsonUtil.jsonToBean(result, SaleCouponGetResponse.class);
                if ("1001".equals(resp.getResultCode())) {
                    //为保持与原有数据结构保持一致
                    saleOrderList.addAll(resp.getCouponOrderList());
                }
            }
        }
        return saleOrderList;
    }
}