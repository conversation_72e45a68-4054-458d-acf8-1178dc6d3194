package com.juneyaoair.mobile.handler.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.basicsys.response.AirLineInfoArrCityDto;
import com.juneyaoair.baseclass.basicsys.response.AirLineInfoDepCityDto;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.RequestData;
import com.juneyaoair.baseclass.common.response.ResponseCode;
import com.juneyaoair.baseclass.common.response.ResponseData;
import com.juneyaoair.baseclass.newcoupon.req.protocol.OrderCouponDto;
import com.juneyaoair.baseclass.theme.*;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IThemeCardService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.comm.request.BindingSearchRequest;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.request.ThemeCardBaseRequest;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.comm.response.ThemeCardBaseResponse;
import com.juneyaoair.thirdentity.comm.response.ThemeCouponBoundDetail;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ProductInfo;
import com.juneyaoair.thirdentity.theme.request.QueryProductsRequestDto;
import com.juneyaoair.util.ControllerUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/30  17:59.
 */
@Slf4j
@Service
public class ThemeCardService implements IThemeCardService {

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private OrderManage orderManage;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private HandConfig handConfig;
    /**
     * 查询主题卡支持的航线对
     *
     * @param themeAirLine
     * @return
     */
    @Override
    public List<AirLineInfoDepCityDto> queryThemeAirLine(ThemeAirLine themeAirLine, Map<String, String> headMap, String channelCode) {
         String themeType   = themeAirLine.getThemeType().split(",")[0];
        String productNum   = themeAirLine.getThemeType().split(",")[1];
        String key = new StringBuilder(RedisKeyConfig.THEME_AIRLINE).append(themeAirLine.getThemeType()).toString();
        boolean exist = apiRedisService.existKey(key);
        if (exist) {
            List<String> airlineList = apiRedisService.getHashValue(key);
            List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = airlineList.parallelStream().map(a -> (AirLineInfoDepCityDto) JsonUtil.jsonToBean(a, AirLineInfoDepCityDto.class)).collect(Collectors.toList());
            airLineInfoDepCityDtoList.sort(Comparator.comparing(AirLineInfoDepCityDto::getCityPinYin));
            return airLineInfoDepCityDtoList;
        } else {
            PtRequest<QueryProductsRequestDto> ptRequest = new PtRequest<>(HandlerConstants.VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), ControllerUtil.getChannelInfo(channelCode, "10"));
            QueryProductsRequestDto queryProductsRequestDto = new QueryProductsRequestDto();
            queryProductsRequestDto.setSearchTypes(themeType);
            queryProductsRequestDto.setProNum(productNum);
            ptRequest.setFfpCardNo(themeAirLine.getFfpCardNo());
            ptRequest.setFfpId(themeAirLine.getFfpId());
            ptRequest.setRequest(queryProductsRequestDto);
            PtResponse<List<ProductInfo>> ptResponse = orderManage.queryThemeProducts(ptRequest, headMap);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode()) && CollectionUtils.isNotEmpty(ptResponse.getResult())) {
                List<ProductInfo> productInfoList = ptResponse.getResult();
                Set<String> set = new HashSet<>();
                //整合所有的航线对，去重
                productInfoList.parallelStream().forEach(productInfo ->
                        productInfo.getProducts().parallelStream().forEach(product ->
                                product.getProductRuleLimit().getAirLine().parallelStream().forEach(airlineLimit ->
                                        set.addAll(airlineLimit.getValue())
                                )
                        )
                );
                List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = basicService.selectAllAirline();
                //重新封装组合航线数据 去除非国内的站点
                if (set.contains("DOMESTIC-DOMESTIC")) {
                    airLineInfoDepCityDtoList.removeIf(airLineInfoDepCityDto -> HandlerConstants.FLIGHT_INTER_I.equals(airLineInfoDepCityDto.getIsInternational()));
                    airLineInfoDepCityDtoList.stream().forEach(airLineInfoDepCityDto ->
                            airLineInfoDepCityDto.getAirline().removeIf(airLineInfoArrCityDto -> HandlerConstants.FLIGHT_INTER_I.equals(airLineInfoArrCityDto.getIsInternational()))
                    );
                } else {
                    //按照出发城市分组
                    Map<String, List<String>> cityPairMap = set.stream().collect(Collectors.groupingBy(s -> s.split("-")[0]));
                    Set<String> depSet = cityPairMap.keySet();
                    //过滤符合条件的出发城市 存在*的情况下默认保留所有出发城市
                    if (!depSet.contains("*")) {
                        airLineInfoDepCityDtoList = airLineInfoDepCityDtoList.stream().filter(airLineInfoDepCityDto -> depSet.contains(airLineInfoDepCityDto.getCityCode())).collect(Collectors.toList());
                    }
                    for (AirLineInfoDepCityDto airLineInfoDepCityDto : airLineInfoDepCityDtoList) {
                        //获取产品的航线对
                        List<String> proAirLine = cityPairMap.get(airLineInfoDepCityDto.getCityCode());
                        //默认支持的航线
                        List<AirLineInfoArrCityDto> arrCityLineList = airLineInfoDepCityDto.getAirline();
                        if (CollectionUtils.isNotEmpty(proAirLine)) {
                            //筛选之后的航线
                            List<AirLineInfoArrCityDto> airLineInfoArrCityDtoList = new ArrayList<>();
                            for (AirLineInfoArrCityDto airLineInfoArrCityDto : arrCityLineList) {
                                //判断是否符合航线
                                if (isSuitAirLine(airLineInfoDepCityDto, airLineInfoArrCityDto, proAirLine)) {
                                    airLineInfoArrCityDtoList.add(airLineInfoArrCityDto);
                                }
                            }
                            airLineInfoDepCityDto.setAirline(airLineInfoArrCityDtoList);
                        } else {
                            airLineInfoDepCityDto.setAirline(arrCityLineList);
                        }
                    }
                    //清除没有到达城市的出发城市对
                    airLineInfoDepCityDtoList.removeIf(airLineInfoDepCityDto -> CollectionUtils.isEmpty(airLineInfoDepCityDto.getAirline()));
                }
                saveRedisData(key, airLineInfoDepCityDtoList);
                return airLineInfoDepCityDtoList;
            } else {
                return new ArrayList<>();
            }
        }
    }

    @Override
    public List<AirLineInfoDepCityDto> queryActivityAirLine(QueryActivityInfoReq queryActivityInfoReq, String ip) {
        Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
        String key = new StringBuilder(RedisKeyConfig.THEME_AIRLINE).append(queryActivityInfoReq.getActivityNo()).toString();
        boolean exist = apiRedisService.existKey(key);
        if (exist) {
            List<String> airlineList = apiRedisService.getHashValue(key);
            List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = airlineList.parallelStream().map(a -> (AirLineInfoDepCityDto) JsonUtil.jsonToBean(a, AirLineInfoDepCityDto.class)).collect(Collectors.toList());
            airLineInfoDepCityDtoList.sort(Comparator.comparing(AirLineInfoDepCityDto::getCityPinYin));
            return airLineInfoDepCityDtoList;
        }
        RequestData<QueryActivityInfoReq> requestData = getQueryActivityInfoReqRequestData(queryActivityInfoReq, ip);
        ResponseData<QueryActivityInfoResp> responseData = queryActivityInfo(requestData);
        if (ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
            List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = basicService.selectAllAirline();
            Set<String> set = new HashSet<>();
            QueryActivityInfoResp queryActivityInfoResp = responseData.getData();
            ProductRuleLimitInfo productRuleLimitInfo = queryActivityInfoResp.getRuleLimit();
            List<AirlineLimitInfo> airlineLimitInfoList = productRuleLimitInfo.getAirLineLimits();
            if (airlineLimitInfoList != null) {
                airlineLimitInfoList.forEach(airlineLimitInfo -> {
                    if (airlineLimitInfo != null && airlineLimitInfo.getValue() != null) {
                        set.addAll(airlineLimitInfo.getValue());
                    }
                });
            }
            Map<String, List<String>> cityPairMap=null;
            //重新封装组合航线数据 去除非国内的站点
            if (set.contains("DOMESTIC-DOMESTIC")) {
                airLineInfoDepCityDtoList.removeIf(airLineInfoDepCityDto -> HandlerConstants.FLIGHT_INTER_I.equals(airLineInfoDepCityDto.getIsInternational()));
                airLineInfoDepCityDtoList.stream().forEach(airLineInfoDepCityDto ->
                        airLineInfoDepCityDto.getAirline().removeIf(airLineInfoArrCityDto -> HandlerConstants.FLIGHT_INTER_I.equals(airLineInfoArrCityDto.getIsInternational()))
                );
            } else {
                if (handConfig.getQueryActivityList().contains(queryActivityInfoReq.getProductType())){
                    Set<String>  depSet = new HashSet<>();
                    Set<String> finalDepSet = depSet;
                    set.forEach(s ->{
                                String   dep  =s.split("-")[0];
                                String   air  =s.split("-")[1];
                                AirPortInfoDto  depPort = FlightUtil.getAirPort(dep,airPortInfoMap);
                                AirPortInfoDto  airPort = FlightUtil.getAirPort(air,airPortInfoMap);
                                finalDepSet.add(depPort.getCityCode()+"-"+airPort.getCityCode());
                            }
                    );
                    //按照出发城市分组
                     cityPairMap = finalDepSet.stream().collect(Collectors.groupingBy(s -> s.split("-")[0]));
                    Set<String>   depSetCt = cityPairMap.keySet();
                    //过滤符合条件的出发城市 存在*的情况下默认保留所有出发城市
                    if (!depSet.contains("*")) {
                        airLineInfoDepCityDtoList = airLineInfoDepCityDtoList.stream().filter(airLineInfoDepCityDto -> depSetCt.contains(airLineInfoDepCityDto.getCityCode())).collect(Collectors.toList());
                    }
                }else {
                    //按照出发城市分组
                    cityPairMap = set.stream().collect(Collectors.groupingBy(s -> s.split("-")[0]));
                    Set<String>   depSet = cityPairMap.keySet();
                    airLineInfoDepCityDtoList = airLineInfoDepCityDtoList.stream().filter(airLineInfoDepCityDto -> depSet.contains(airLineInfoDepCityDto.getCityCode())).collect(Collectors.toList());
                }
                for (AirLineInfoDepCityDto airLineInfoDepCityDto : airLineInfoDepCityDtoList) {
                    //获取产品的航线对
                    List<String> proAirLine = cityPairMap.get(airLineInfoDepCityDto.getCityCode());
                    //默认支持的航线
                    List<AirLineInfoArrCityDto> arrCityLineList = airLineInfoDepCityDto.getAirline();
                    if (CollectionUtils.isNotEmpty(proAirLine)) {
                        //筛选之后的航线
                        List<AirLineInfoArrCityDto> airLineInfoArrCityDtoList = new ArrayList<>();
                        for (AirLineInfoArrCityDto airLineInfoArrCityDto : arrCityLineList) {
                            //判断是否符合航线
                            if (isSuitAirLine(airLineInfoDepCityDto, airLineInfoArrCityDto, proAirLine)) {
                                airLineInfoArrCityDtoList.add(airLineInfoArrCityDto);
                            }
                        }
                        airLineInfoDepCityDto.setAirline(airLineInfoArrCityDtoList);
                    } else {
                        airLineInfoDepCityDto.setAirline(arrCityLineList);
                    }
                }
                //清除没有到达城市的出发城市对
                airLineInfoDepCityDtoList.removeIf(airLineInfoDepCityDto -> CollectionUtils.isEmpty(airLineInfoDepCityDto.getAirline()));
            }
            saveRedisData(key, airLineInfoDepCityDtoList);
            return airLineInfoDepCityDtoList;
        } else {
            return Collections.emptyList();
        }

    }

    private RequestData<QueryActivityInfoReq> getQueryActivityInfoReqRequestData(QueryActivityInfoReq queryActivityInfoReq, String ip) {
        QueryActivityInfoReq queryActivityInfoReq1 = new QueryActivityInfoReq();
        queryActivityInfoReq1.setActivityNo(queryActivityInfoReq.getActivityNo());
        queryActivityInfoReq1.setProductType(queryActivityInfoReq.getProductType());
        RequestData  requestData = RequestData.builder()
                .channelNo(ChannelCodeEnum.MOBILE.getChannelCode())
                .ffpId(queryActivityInfoReq.getFfpId())
                .ffpNo(queryActivityInfoReq.getFfpCardNo())
                .originIp(ip)
                .data(queryActivityInfoReq1).build();
        return requestData;
    }


    /**
     * open票产品航线查询
     * @return
     */
    @Override
    public Map<String,List<CouponFlightInfoDto>>  queryOpenProductAirLine(BaseReq<CouponFlightRequest> baseReq) {
        try {
            CouponFlightRequestDto  couponFlightRequestDto =new CouponFlightRequestDto();
            couponFlightRequestDto.setCouponCode(baseReq.getRequest().getCouponCode());
            couponFlightRequestDto.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
            couponFlightRequestDto.setFfpId(baseReq.getRequest().getFfpId());
            couponFlightRequestDto.setVersion("1.0");
            couponFlightRequestDto.setFfpCardNo(baseReq.getRequest().getFfpCardNo());
            QueryCouponResponse queryCouponResponse =   queryFlightByCoupon(couponFlightRequestDto);
            if ("1001".equals(queryCouponResponse.getResultCode())) {
                Map<String, List<CouponFlightInfoDto>>   couponFlightInfoList  =queryCouponResponse.getCouponFlightInfo();
                Map<String, List<CouponFlightInfoDto>>   couponFlightInfoHashMap  = new HashMap<>();
                couponFlightInfoList.forEach((s, couponFlightInfoDtos) -> {
                    List<CouponFlightInfoDto>  couponFlightInfoDtoNew = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(couponFlightInfoDtos)) {
                        //按照 出发城市到达城市分组
                        Map<String, List<CouponFlightInfoDto>>  couponFlightInfoDtoMap =
                                couponFlightInfoDtos.stream().collect(Collectors.groupingBy(flightInfoDto -> flightInfoDto.getDepCity() + "_" + flightInfoDto.getArrCity()));
                        couponFlightInfoDtoMap.forEach((k, CouponFlightInfoDto) -> {
                            if (CollectionUtils.isNotEmpty(CouponFlightInfoDto)) {
                                CouponFlightInfoDto.forEach(couponFlightInfoDto -> {
                                    CityInfoDto depCity = localCacheService.getLocalCity(couponFlightInfoDto.getDepCity());
                                    CityInfoDto arrCity = localCacheService.getLocalCity(couponFlightInfoDto.getArrCity());
                                    couponFlightInfoDto.setDepCityName(depCity.getCityName());
                                    couponFlightInfoDto.setArrCityName(arrCity.getCityName());
                                    couponFlightInfoDto.setDepCityPinYin(depCity.getCityPinYin());
                                    couponFlightInfoDto.setArrCityPinYin(arrCity.getCityPinYin());
                                });
                                //一个城市下面多个机场
                                if (CouponFlightInfoDto.size()>1){
                                    //按照城市查询航班列表
                                    CouponFlightInfoDto.forEach(couponFlightInfo->{
                                        couponFlightInfo.setArrAirport("");
                                        couponFlightInfo.setDepAirport("");
                                    });
                                     //按照 出发城市到达城市去重
                                    List<CouponFlightInfoDto> distinctList = CouponFlightInfoDto.stream()
                                            .collect(Collectors.groupingBy(
                                                    dto -> Arrays.asList(dto.getDepCity(), dto.getArrCity())
                                            ))
                                            .values()
                                            .stream()
                                            .map(group -> group.get(0))  // 取每组第一个元素
                                            .collect(Collectors.toList());
                                    couponFlightInfoDtoNew.addAll(distinctList);
                                }else {
                                    couponFlightInfoDtoNew.addAll(CouponFlightInfoDto);
                                }
                            }
                        });

                    }
                    couponFlightInfoHashMap.put(s,couponFlightInfoDtoNew);
                });

                return couponFlightInfoHashMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> Optional.ofNullable(entry.getValue()).orElse(Collections.emptyList())
                                        .stream()
                                        .sorted(Comparator.comparing(CouponFlightInfoDto::getDepCity))  // 对 List 进行排序（出发城市三字码顺序）
                                        .collect(Collectors.toList()),
                                (oldValue, newValue) -> oldValue,
                                LinkedHashMap::new
                        ));
            }else {
                return Collections.emptyMap();
            }
        } catch (Exception e) {
            return Collections.emptyMap();
        }

    }
    public QueryCouponResponse queryFlightByCoupon(CouponFlightRequestDto ptRequest) {
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.QUERY_FLIGHT_BY_COUPON;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<QueryCouponResponse>() {
                }.getType();
                QueryCouponResponse responseData = JsonUtil.fromJson(result.getResponse(), type);
                return responseData;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }



    public ResponseData<QueryActivityInfoResp> queryActivityInfo(RequestData<QueryActivityInfoReq> ptRequest) {
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.QUERY_ACTIVITY_INFO;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<ResponseData<QueryActivityInfoResp>>() {
                }.getType();
                ResponseData responseData = JsonUtil.fromJson(result.getResponse(), type);
                return responseData;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }


    //判断是否满足航线条件
    private boolean isSuitAirLine(AirLineInfoDepCityDto depCity, AirLineInfoArrCityDto arrCity, List<String> proAirLineList) {
        List<String> suitAirLine = new ArrayList();
        //默认支持的通配类型
        suitAirLine.add(new StringBuilder(depCity.getCityCode()).append("-").append(arrCity.getCityCode()).toString());
        suitAirLine.add(new StringBuilder(depCity.getCityCode()).append("-*").toString());
        suitAirLine.add(new StringBuilder("*-").append(arrCity.getCityCode()).toString());

        if (HandlerConstants.FLIGHT_INTER_D.equals(depCity.getIsInternational())) {
            suitAirLine.add(new StringBuilder("DOMESTIC-").append(arrCity.getCityCode()).toString());
            suitAirLine.add(new StringBuilder("DOMESTIC-*").toString());
        } else if (HandlerConstants.FLIGHT_INTER_I.equals(depCity.getIsInternational())) {
            suitAirLine.add(new StringBuilder("INTL-").append(arrCity.getCityCode()).toString());
            suitAirLine.add(new StringBuilder("INTL-*").toString());
        }
        if (HandlerConstants.FLIGHT_INTER_D.equals(arrCity.getIsInternational())) {
            suitAirLine.add(new StringBuilder(depCity.getCityCode()).append("-DOMESTIC").toString());
            suitAirLine.add(new StringBuilder("*-DOMESTIC").toString());
        } else if (HandlerConstants.FLIGHT_INTER_I.equals(arrCity.getIsInternational())) {
            suitAirLine.add(new StringBuilder(depCity.getCityCode()).append("-INTL").toString());
            suitAirLine.add(new StringBuilder("*-INTL").toString());
        }
        return suitAirLine.stream().anyMatch(airLine -> proAirLineList.contains(airLine));
    }

    /**
     * 缓存策略处理
     *
     * @param key
     * @param airLineInfoDepCityDtoList
     */
    private void saveRedisData(String key, List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList) {
        if (CollectionUtils.isNotEmpty(airLineInfoDepCityDtoList)) {
            Map<String, String> airlineListMap = airLineInfoDepCityDtoList.parallelStream().collect(Collectors.toMap(AirLineInfoDepCityDto::getCityCode, airLineInfoDepCityDto -> JsonUtil.objectToJson(airLineInfoDepCityDto)));
            apiRedisService.setHashData(key, airlineListMap, 3600 * 4L);
        }
    }

    @Override
    public ThemeCardBaseResponse redeemBindingList(ThemeCardBaseRequest<BindingSearchRequest> themeCardBaseRequest) {
        Map<String, String> headMap = HttpUtil.getHeaderMap("", "");
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.REDEEM_BINDING_LIST;
        HttpResult result = HttpUtil.doPostClient(themeCardBaseRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<ThemeCardBaseResponse<List<ThemeCouponBoundDetail>>>() {
                }.getType();
                ThemeCardBaseResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            log.info(result.getResponse());
            return null;
        }
    }

    @Override
    public List<OrderCouponDto> queryTicketRedeem(String ticketNo, String depCity, String arrCity) {
        QueryTicketRedeemDto queryTicketRedeemDto =new QueryTicketRedeemDto();
        queryTicketRedeemDto.setTicketNo(ticketNo);
        queryTicketRedeemDto.setDepCity(depCity);
        queryTicketRedeemDto.setArrCity(arrCity);
        queryTicketRedeemDto.setVersion("10");
        queryTicketRedeemDto.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        Map<String, String> headMap = HttpUtil.getHeaderMap("", "");
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.QUERY_TICKET_REDEEM;
        HttpResult result = HttpUtil.doPostClient(queryTicketRedeemDto, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<QueryTicketRedeemResp>() {
                }.getType();
                QueryTicketRedeemResp ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                if (ptResponse != null&&"1001".equals(ptResponse.getResultCode())) {
                    return ptResponse.getOrderCouponDtoList();
                }else {
                    return  Collections.emptyList();
                }
            }
        } else {
            log.info(result.getResponse());
            return  Collections.emptyList();
        }
    }

}
