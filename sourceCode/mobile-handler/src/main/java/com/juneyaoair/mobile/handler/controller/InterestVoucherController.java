package com.juneyaoair.mobile.handler.controller;

import com.google.common.collect.Sets;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.order.YouHuiCouponSourceEnum;
import com.juneyaoair.appenum.order.OrderCouponStateEnum;
import com.juneyaoair.appenum.order.OrderStateEnum;
import com.juneyaoair.appenum.order.PayEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.common.base.UserInfoNoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.response.order.query.OrderTotalBriefInfo;
import com.juneyaoair.baseclass.response.order.query.OrderTotalBriefResp;
import com.juneyaoair.baseclass.response.order.query.OtherOrderInfo;
import com.juneyaoair.baseclass.response.payment.PaymentResp;
import com.juneyaoair.baseclass.salecoupon.request.*;
import com.juneyaoair.baseclass.salecoupon.response.AbleCouponInfo;
import com.juneyaoair.baseclass.salecoupon.response.CouponShow;
import com.juneyaoair.baseclass.salecoupon.response.SaleCoupon;
import com.juneyaoair.baseclass.salecoupon.response.SaleCouponGet;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.VirtualPaymentConvert;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.AccountType;
import com.juneyaoair.mobile.webservice.client.crm.MemberAccountQueryResponseForClient;
import com.juneyaoair.mobile.webservice.client.crm.MemberAccountType;
import com.juneyaoair.mobile.webservice.client.crm.VerifyConsumePasswdResponseForClient;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.request.order.query.PtOrderTotalBriefReq;
import com.juneyaoair.thirdentity.salecoupon.request.*;
import com.juneyaoair.thirdentity.salecoupon.response.*;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/7/17  16:57.
 * 标注过期的方法已经废弃使用
 */
@RequestMapping("/interest")
@RestController
@Api(value = "权益券服务", tags = "权益券服务")
public class InterestVoucherController extends BassController {
    private static final String COMMON_LINKINFO_TABLE= "salecoupon";
    private static final String COMMON_LINKINFO_CEL = "linkInfo";
    private static final String COMMON_LOUNGE_LINKINFO_TABLE= "saleLoungecoupon";
    private static final String COMMON_LOUNGE_LINKINFO_CEL = "loungeLinkInfo";
    private static final String COUPON_PRO_CHANNELCODE = "CRM";
    @ApiOperation(value = "行李券产品", notes = "行李券产品查询")
    @RequestMapping(value = "/querybaggagepro", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @Deprecated
    public BaseResp<AbleCouponInfo> queryBaggagePro(HttpServletRequest request, @RequestBody BaseReq<UserInfoNoMust> req) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            /*
            * 1.未登录  不展示积分
            * 2.登录情况下 默认展示共XX积分
            * */
            UserInfoNoMust userInfo = req.getRequest();
            boolean flag;  //用户正常登录标记
            String channelCode = req.getChannelCode();
            AbleCouponInfo ableCouponInfo = new AbleCouponInfo();
            long totalScore = 0;
            if (userInfo != null && !StringUtil.isNullOrEmpty(userInfo.getFfpId())) {
                //验证用户查询是否正常
                flag = this.checkKeyInfo(userInfo.getFfpId(), userInfo.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                } else {//获取最近的一次订单信息联系人，展示总积分
                    MemberAccountQueryResponseForClient clientResponse = crmClient.memberAccountQuery(userInfo.getFfpId(), "", "", channelCode, getClientPwd(channelCode));
                    if ("S000".equals(clientResponse.getMessageHeader().getErrorCode())) {
                        MemberAccountType memberAccountType = clientResponse.getMemberAccount();
                        AccountType sumAccount = memberAccountType.getSumAccount();
                        totalScore = sumAccount.getClubMiles() + sumAccount.getSupplierMiles() + sumAccount.getExtraMiles() + sumAccount.getPromotionMiles()
                                - sumAccount.getRedeemMiles() - sumAccount.getExpiredMiles();
                    }
                    Map<String,String> linkMap = redisService.getMapData(COMMON_LINKINFO_TABLE+":"+COMMON_LINKINFO_CEL+":"+userInfo.getFfpCardNo());
                    if(linkMap!=null){
                        ableCouponInfo.setLinkName(linkMap.get("linkName"));
                        ableCouponInfo.setLinkPhone(linkMap.get("linkPhone"));
                    }
                }
            }
            //查询第三方接口获取产品信息
            String ip = getClientIP(request);
            String userNo = getChannelInfo(channelCode, "10");
            PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, COUPON_PRO_CHANNELCODE, userNo);
            ptRequest.setCouponSource(YouHuiCouponSourceEnum.HOBG.coupoSource);
            /*Map<String,String> headMap =  HttpUtil.getHeaderMap(ip,"");
            String result = invokePost(ptRequest, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_ALE_COUPON_QUERY,headMap);
            if (!StringUtil.isNullOrEmpty(result)) {
                PtSaleCouponQueryResponse ptSaleCouponQueryResponse = (PtSaleCouponQueryResponse) JsonUtil.jsonToBean(result, PtSaleCouponQueryResponse.class);*/
            HttpResult serviceResult = doPost(ptRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_QUERY);
            if (null != serviceResult && serviceResult.isResult()) {
                PtSaleCouponQueryResponse ptSaleCouponQueryResponse = (PtSaleCouponQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponQueryResponse.class);
                List<SaleCoupon> saleCouponList = ptSaleCouponQueryResponse.getSaleCouponList();
                ableCouponInfo.setAvailableScore(Integer.parseInt(String.valueOf(totalScore)));
                if (StringUtil.isNullOrEmpty(saleCouponList)) {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setResultInfo("暂无可售产品！");
                } else {
                    //产品类别名称渲染
                    renderCoupon(saleCouponList);
                    //产品公斤数排序
                    sortCouponPro(saleCouponList);
                    ableCouponInfo.setSaleCouponList(saleCouponList);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setObjData(ableCouponInfo);
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    @ApiOperation(value = "可售休息室券查询", notes = "可售休息室券查询")
    @RequestMapping(value = "/queryloungepro", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @Deprecated
    public BaseResp<AbleCouponInfo> queryLoungePro(HttpServletRequest request, @RequestBody BaseReq<UserInfoNoMust> req) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            /*
            * 1.未登录  不展示积分
            * 2.登录情况下 默认展示共XX积分
            * */
            UserInfoNoMust userInfo = req.getRequest();
            boolean flag;  //用户正常登录标记
            String channelCode = req.getChannelCode();
            AbleCouponInfo ableCouponInfo = new AbleCouponInfo();
            long totalScore = 0;
            if (userInfo != null && !StringUtil.isNullOrEmpty(userInfo.getFfpId())) {
                //验证用户查询是否正常
                flag = this.checkKeyInfo(userInfo.getFfpId(), userInfo.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                } else {//获取最近的一次订单信息联系人，展示总积分
                    MemberAccountQueryResponseForClient clientResponse = crmClient.memberAccountQuery(userInfo.getFfpId(), "", "", channelCode, getClientPwd(channelCode));
                    if ("S000".equals(clientResponse.getMessageHeader().getErrorCode())) {
                        MemberAccountType memberAccountType = clientResponse.getMemberAccount();
                        AccountType sumAccount = memberAccountType.getSumAccount();
                        totalScore = sumAccount.getClubMiles() + sumAccount.getSupplierMiles() + sumAccount.getExtraMiles() + sumAccount.getPromotionMiles()
                                - sumAccount.getRedeemMiles() - sumAccount.getExpiredMiles();
                    }
                    Map<String,String> linkMap = redisService.getMapData(COMMON_LOUNGE_LINKINFO_TABLE+":"+COMMON_LOUNGE_LINKINFO_CEL+":"+userInfo.getFfpCardNo());
                    if(linkMap!=null){
                        ableCouponInfo.setLinkName(linkMap.get("linkName"));
                        ableCouponInfo.setLinkPhone(linkMap.get("linkPhone"));
                    }
                }
            }
            //查询第三方接口获取产品信息
            String ip = getClientIP(request);
            String userNo = getChannelInfo(channelCode, "10");
            //使用CRM渠道获取信息
            PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, COUPON_PRO_CHANNELCODE, userNo);
            ptRequest.setCouponSource(YouHuiCouponSourceEnum.HOLO.coupoSource);
            //Map<String,String> headMap =  HttpUtil.getHeaderMap(ip,"");
            //String result = invokePost(ptRequest, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_ALE_COUPON_QUERY,headMap);
            //if (!StringUtil.isNullOrEmpty(result)) {
                //PtSaleCouponQueryResponse ptSaleCouponQueryResponse = (PtSaleCouponQueryResponse) JsonUtil.jsonToBean(result, PtSaleCouponQueryResponse.class);
            HttpResult serviceResult = doPost(ptRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_QUERY);
            if (null != serviceResult && serviceResult.isResult()) {
                PtSaleCouponQueryResponse ptSaleCouponQueryResponse = (PtSaleCouponQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponQueryResponse.class);
                List<SaleCoupon> saleCouponList = ptSaleCouponQueryResponse.getSaleCouponList();
                ableCouponInfo.setAvailableScore(Integer.parseInt(String.valueOf(totalScore)));
                if (StringUtil.isNullOrEmpty(saleCouponList)) {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setResultInfo("暂无可售产品！");
                } else {
                    //产品类别名称渲染
                    renderCoupon(saleCouponList);
                    ableCouponInfo.setSaleCouponList(saleCouponList);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setObjData(ableCouponInfo);
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    @ApiOperation(value = "权益券产品购买", notes = "权益券产品购买")
    @RequestMapping(value = "/buybaggagepro", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @Deprecated
    public BaseResp<Map<String,String>> buyBaggagePro(HttpServletRequest request, @RequestBody BaseReq<SaleCouponBuy> req) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_buybaggagepro";
        String ip = this.getClientIP(request);
        try {
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            SaleCouponBuy saleCouponBuy = req.getRequest();
            boolean flag = this.checkKeyInfo(saleCouponBuy.getFfpId(), saleCouponBuy.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }
            //判错条件检验
            resp = checkBuyReq(req, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }
            //使用积分需要验证用户的消费密码
            if (saleCouponBuy.getUseScore() > 0) {
                //消费密码
                String patternStr = PatternCommon.SALE_P_W_D;
                Pattern pattern = Pattern.compile(patternStr);
                Matcher matcher = pattern.matcher(saleCouponBuy.getSalePwd());
                if (!matcher.matches()) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("消费密码为六位数字");
                    return resp;
                }
                VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(Long.valueOf(saleCouponBuy.getFfpId()), saleCouponBuy.getSalePwd(), req.getChannelCode(), getClientPwd(req.getChannelCode()));
                if (!"S000".equals(clientResp.getMessageHeader().getErrorCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(clientResp.getMessageHeader().getDescription());
                    return resp;
                }
            }
            PtSaleCouponBuyRequest ptSaleCouponBuyRequest = createPtSaleCouponBuyRequest(req,resp);
            ptSaleCouponBuyRequest.setOrderRequestIp(ip);
            //Map<String,String> headMap =  HttpUtil.getHeaderMap(ip,"");
            //String result = invokePost(ptSaleCouponBuyRequest, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_SALE_COUPON_BUY,headMap);
            //if (!StringUtil.isNullOrEmpty(result)) {
                //PtSaleCouponBuyResponse ptSaleCouponBuyResponse = (PtSaleCouponBuyResponse) JsonUtil.jsonToBean(result, PtSaleCouponBuyResponse.class);
            HttpResult serviceResult = doPost(ptSaleCouponBuyRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_BUY);
            if (null != serviceResult && serviceResult.isResult()) {
                PtSaleCouponBuyResponse ptSaleCouponBuyResponse = (PtSaleCouponBuyResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponBuyResponse.class);
                if("1001".equals(ptSaleCouponBuyResponse.getResultCode())){
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    Map<String, String> map = new HashMap<>();
                    map.put("channelOrderNo", ptSaleCouponBuyResponse.getChannelOrderNo());
                    map.put("orderNo", ptSaleCouponBuyResponse.getOrderNo());
                    resp.setObjData(map);
                    //将最近的联系人放入redis
                    Map<String,String> linkMap = new HashMap<>();
                    linkMap.put("linkName",ptSaleCouponBuyRequest.getLinker());
                    linkMap.put("linkPhone",ptSaleCouponBuyRequest.getLinkerHandphone());
                    //默认存放180天
                    Date saveTime = DateUtils.addOrLessDay(new Date(),30*6);
                    if(YouHuiCouponSourceEnum.HOBG.coupoSource.equals(saleCouponBuy.getCouponSource())){
                        redisService.setMapData(COMMON_LINKINFO_TABLE,COMMON_LINKINFO_CEL,saleCouponBuy.getFfpCardNo(),linkMap,saveTime);
                    }else if(YouHuiCouponSourceEnum.HOLO.coupoSource.equals(saleCouponBuy.getCouponSource())){
                        redisService.setMapData(COMMON_LOUNGE_LINKINFO_TABLE,COMMON_LOUNGE_LINKINFO_CEL,saleCouponBuy.getFfpCardNo(),linkMap,saveTime);
                    }
                    //0元同步支付
                    int usedScore = saleCouponBuy.getUseScore();
                    if (usedScore > 0 && saleCouponBuy.getPayAmount() == 0.00) {
                        String key = getChannelInfo(req.getChannelCode(), "20");
                        String postUrl = HandlerConstants.URL_PAY;
                        Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(), ptSaleCouponBuyResponse.getOrderNo(), ptSaleCouponBuyResponse.getChannelOrderNo(), key, "CouponType", "", "O");
                        parametersMap.put("UseScore", String.valueOf(usedScore));
                        HttpResult payResult = doPayPost(postUrl, parametersMap);
                        PaymentResp paymentResp;
                        if (payResult.isResult()) {
                            String paymentInfo = payResult.getResponse().trim();
                            log.info("请求号:{}，IP地址:{}，0元支付结果：{}", reqId, ip, JsonUtil.objectToJson(paymentInfo));
                            paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                            //虚拟支付成功
                            if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                                resp.setResultCode("P10001");
                                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            } else {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo("权益券购买失败！");
                            }
                            log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                            return resp;
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("支付请求出错");
                            log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                            return resp;
                        }
                    }
                }else{
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptSaleCouponBuyResponse.getErrorInfo());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，服务端异常：", reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    /**
     * @param request
     * @param req
     * @return
     * 该方法已在新的UpgradeCouponController中定义为upgradeList
     */
    @Deprecated
    @ApiOperation(value = "我的权益券列表与详情", notes = "我的权益券列表与详情")
    @RequestMapping(value = "/baggageprolist", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<Map<String,Object>> listBaggagePro(HttpServletRequest request, @RequestBody BaseReq<MySaleCouponOrderRequest> req) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_baggageprolist";
        String ip = this.getClientIP(request);
        try {
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<MySaleCouponOrderRequest>>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            MySaleCouponOrderRequest mySaleCouponOrderRequest = req.getRequest();
            boolean flag = this.checkKeyInfo(mySaleCouponOrderRequest.getFfpId(), mySaleCouponOrderRequest.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }
            if(mySaleCouponOrderRequest.getPageNo()<=0){
                mySaleCouponOrderRequest.setPageNo(1);
            }
            if(mySaleCouponOrderRequest.getPageSize()<=0){
                mySaleCouponOrderRequest.setPageSize(20);
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            //判断有无订单号和渠道订单编号
            // 不存在的情况下调用综合订单查询接口
            //存在的情况下走详情查询接口
            String orderNo = mySaleCouponOrderRequest.getOrderNo();
            String channelOrderNo = mySaleCouponOrderRequest.getChannelOrderNo();
            Map<String,Object> pageMap = new HashMap<>();
            //订单列表
            if(!StringUtil.isNullOrEmpty(orderNo)&&!StringUtil.isNullOrEmpty(channelOrderNo)){
                pageMap = queryOrderDetail(channelCode,userNo,mySaleCouponOrderRequest);
            }else{
                pageMap  = queryOrderList(channelCode,userNo,mySaleCouponOrderRequest);
                String realChannelCode = request.getHeader("channelCode");
                // 手机端暂时不显示登机口升舱订单 2019-8-16 10:47:19
                if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(realChannelCode)) {
                    List<SaleCouponGet> saleOrderList = (List<SaleCouponGet>) pageMap.get("orderList");
                    if (CollectionUtils.isNotEmpty(saleOrderList)){
                        pageMap.put("orderList", saleOrderList.stream()
                                .filter(order -> !"GatewayUpgrade".equals(order.getCouponSource())).collect(Collectors.toList()));
                    }
                }
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(pageMap);
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    @Deprecated
    @ApiOperation(value = "权益券产品申请退款", notes = "权益券产品申请退款")
    @RequestMapping(value = "/refundbaggagepro", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp refundBaggagePro(HttpServletRequest request, @RequestBody BaseReq<SaleCouponRefundRequest> req) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_refundbaggagepro";
        String ip = this.getClientIP(request);
        try {
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<SaleCouponRefundRequest>>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            SaleCouponRefundRequest saleCouponRefundRequest = req.getRequest();
            boolean flag = this.checkKeyInfo(saleCouponRefundRequest.getFfpId(), saleCouponRefundRequest.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }

            String userNo = getChannelInfo(req.getChannelCode(), "10");
            PtSaleCouponRefundRequest.Coupon coupon = null;
            List<PtSaleCouponRefundRequest.Coupon> couponList = new ArrayList<>();
            for (String couponCode : saleCouponRefundRequest.getSaleCouponList()) {
                coupon = new PtSaleCouponRefundRequest().new Coupon();
                coupon.setCouponCode(couponCode);
                couponList.add(coupon);
            }
            PtSaleCouponRefundRequest ptSaleCouponRefundRequest = new PtSaleCouponRefundRequest(
                    HandlerConstants.VERSION,
                    req.getChannelCode(),
                    userNo,
                    saleCouponRefundRequest.getChannelOrderNo(),
                    saleCouponRefundRequest.getOrderNo(),
                    saleCouponRefundRequest.getFfpId(),
                    saleCouponRefundRequest.getFfpCardNo(),
                    couponList
            );
            Map<String,String> headMap =  HttpUtil.getHeaderMap(ip,"");
            String result = invokePost(ptSaleCouponRefundRequest, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_SALE_COUPON_REFUND,headMap);
            if (!StringUtil.isNullOrEmpty(result)) {
                PtSaleCouponRefundResponse ptSaleCouponRefundResponse = (PtSaleCouponRefundResponse) JsonUtil.jsonToBean(result, PtSaleCouponRefundResponse.class);
                if (!ptSaleCouponRefundResponse.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptSaleCouponRefundResponse.getErrorInfo());
                    return resp;
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    @Deprecated
    @ApiOperation(value = "权益券产品取消订单", notes = "权益券产品取消订单")
    @RequestMapping(value = "/cancelaggagepro", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp cancelBaggagePro(HttpServletRequest request, @RequestBody BaseReq<SaleCouponCancelRequest> req) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_cancelaggagepro";
        String ip = this.getClientIP(request);
        try {
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<SaleCouponCancelRequest>>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            SaleCouponCancelRequest saleCouponCancelRequest = req.getRequest();
            boolean flag = this.checkKeyInfo(saleCouponCancelRequest.getFfpId(), saleCouponCancelRequest.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }

            String userNo = getChannelInfo(req.getChannelCode(), "10");
            PtSaleCouponCancelRequest ptSaleCouponCancelRequest = new PtSaleCouponCancelRequest(
                    HandlerConstants.VERSION,
                    req.getChannelCode(),
                    userNo,
                    saleCouponCancelRequest.getOrderNo(),
                    saleCouponCancelRequest.getChannelOrderNo(),
                    saleCouponCancelRequest.getFfpId(),
                    "APP用户取消"
            );
            HttpResult serviceResult = doPost(ptSaleCouponCancelRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_CANCEL);
            if (null != serviceResult && serviceResult.isResult()) {
                PtSaleCouponCancelResponse ptSaleCouponCancelResponse = (PtSaleCouponCancelResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponCancelResponse.class);
                if (!"1001".equals(ptSaleCouponCancelResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptSaleCouponCancelResponse.getErrorInfo());
                    return resp;
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    @ApiOperation(value = "权益券订单删除", notes = "权益券订单删除")
    @RequestMapping(value = "/deleteRightOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp deleteRightOrder(HttpServletRequest request, @RequestBody BaseReq<SaleCouponRemoveRequest> req) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_deleteRightOrder";
        String ip = this.getClientIP(request);
        try {
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<SaleCouponRemoveRequest>>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            SaleCouponRemoveRequest saleCouponRemoveRequest = req.getRequest();
            boolean flag = this.checkKeyInfo(saleCouponRemoveRequest.getFfpId(), saleCouponRemoveRequest.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }

            String userNo = getChannelInfo(req.getChannelCode(), "10");
            PtRemoveOrderRequest ptSaleCouponCancelRequest = new PtRemoveOrderRequest(
                    HandlerConstants.VERSION,
                    req.getChannelCode(),
                    userNo,
                    saleCouponRemoveRequest.getChannelOrderNo(),
                    saleCouponRemoveRequest.getOrderNo(),
                    saleCouponRemoveRequest.getFfpId(),
                    saleCouponRemoveRequest.getFfpCardNo(),
                    "Y"
            );
            HttpResult serviceResult = doPost(ptSaleCouponCancelRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_REMOVE);
            if (null != serviceResult && serviceResult.isResult()) {
                PtSaleCouponCancelResponse ptSaleCouponCancelResponse = (PtSaleCouponCancelResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponCancelResponse.class);
                if (!"1001".equals(ptSaleCouponCancelResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptSaleCouponCancelResponse.getErrorInfo());
                    return resp;
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }


    //购买参数检验
    private BaseResp checkBuyReq(BaseReq<SaleCouponBuy> req, BaseResp resp) {
        SaleCouponBuy saleCouponBuy = req.getRequest();
        Pattern pattern = Pattern.compile(PatternCommon.SALE_P_W_D);
        Matcher matcher = pattern.matcher(saleCouponBuy.getSalePwd()==null?"":saleCouponBuy.getSalePwd());
        if (saleCouponBuy.getUseScore() > 0) {
            if (StringUtil.isNullOrEmpty(saleCouponBuy.getSalePwd())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("请输入消费密码！");
                return resp;
            }
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("消费密码为六位数字！");
                return resp;
            }
            VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(Long.valueOf(saleCouponBuy.getFfpId()), saleCouponBuy.getSalePwd(), req.getChannelCode(), getClientPwd(req.getChannelCode()));
            if (!"S000".equals(clientResp.getMessageHeader().getErrorCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(clientResp.getMessageHeader().getDescription());
                return resp;
            }
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //购买参数
    private PtSaleCouponBuyRequest createPtSaleCouponBuyRequest(BaseReq<SaleCouponBuy> req,BaseResp resp){
        SaleCouponBuy saleCouponBuy = req.getRequest();
        String userNo = getChannelInfo(req.getChannelCode(), "10");
        PtSaleCouponBuyRequest ptSaleCouponBuyRequest = new PtSaleCouponBuyRequest(
                HandlerConstants.VERSION,
                req.getChannelCode(),
                userNo
                );
        BeanUtils.copyProperties(saleCouponBuy,ptSaleCouponBuyRequest);
        List<PtSaleCouponBuyRequest.SaleCouponBuy> ptsaleList = new ArrayList<>();
        for(SaleCouponBuy.BuySaleCoupon buySaleCoupon:saleCouponBuy.getSaleCouponList()){
            PtSaleCouponBuyRequest.SaleCouponBuy ptsale = ptSaleCouponBuyRequest.getSaleCouponBuy();
            ptsale.setActivityNo(buySaleCoupon.getActivityNo());
            ptsale.setCount(buySaleCoupon.getCount());
            ptsaleList.add(ptsale);
        }
        ptSaleCouponBuyRequest.setSaleCouponList(ptsaleList);
        return ptSaleCouponBuyRequest;
    }

    //构建列表详情请求参数
    private PtSaleCouponOrderGetRequest createPtSaleCouponOrderGetRequest(String channelCode,String userNo,MySaleCouponOrderRequest mySaleCouponOrderRequest){
        PtSaleCouponOrderGetRequest ptSaleCouponGetRequest = new PtSaleCouponOrderGetRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                mySaleCouponOrderRequest.getChannelOrderNo(),
                mySaleCouponOrderRequest.getOrderNo(),
                mySaleCouponOrderRequest.getFfpId(),
                mySaleCouponOrderRequest.getFfpCardNo(),
                mySaleCouponOrderRequest.getPageNo(),
                mySaleCouponOrderRequest.getPageSize()
        );
        return ptSaleCouponGetRequest;
    }

    /**
     * 查询权益订单
     * @param channelCode
     * @param userNo
     * @param mySaleCouponOrderRequest
     * @return
     */
    private   Map<String,Object>  queryOrderList(String channelCode,String userNo,MySaleCouponOrderRequest mySaleCouponOrderRequest){
        List<SaleCouponGet> saleOrderList  = new LinkedList<>();
        Map<String,Object> pageMap = new HashMap<>();
        PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, channelCode, userNo);
        BeanUtils.copyProperties(mySaleCouponOrderRequest,ptBrief);
        ptBrief.setCustomerNo(mySaleCouponOrderRequest.getFfpId());
        if(mySaleCouponOrderRequest.getPageNo()<=0){
            ptBrief.setPageNo(1);
        }
        if(mySaleCouponOrderRequest.getPageSize()<=0){
            ptBrief.setPageSize(20);
        }
        Date curDate = new Date();
        //正向筛选权益券订单
        if(StringUtil.isNullOrEmpty(mySaleCouponOrderRequest.getDateBegin())){
            Date beginDate = DateUtils.addOrLessMonth(curDate,-3);
            ptBrief.setCreateDateBegin(DateUtils.convertDateToString(beginDate,"yyyy-MM-dd"));
        }else {
            ptBrief.setCreateDateBegin(mySaleCouponOrderRequest.getDateBegin());
        }
        if(StringUtil.isNullOrEmpty(mySaleCouponOrderRequest.getDateEnd())){
            ptBrief.setCreateDateEnd(DateUtils.convertDateToString(curDate,"yyyy-MM-dd"));
        }else{
            ptBrief.setCreateDateEnd(mySaleCouponOrderRequest.getDateEnd());
        }
        ptBrief.setSubOrderTypeFilterIndex(1);
        ptBrief.setSubOrderTypeFilterContent("CouponOrder");
        ptBrief.setFfpId(mySaleCouponOrderRequest.getFfpId());
        ptBrief.setFfpCardNo(mySaleCouponOrderRequest.getFfpCardNo());
        HttpResult serviceResult = doPost(ptBrief, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_ORDER_BRIEF);
        //String result = invokePost(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_SALE_COUPON_LISTORDER);
        if (null != serviceResult && serviceResult.isResult()) {
            OrderTotalBriefResp resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(serviceResult.getResponse(), OrderTotalBriefResp.class);
            if("1001".equals(resp.getResultCode())){
                //为保持与原有数据结构保持一致
                saleOrderList = orderToSaleOrder(resp.getOrderTotalBriefInfoList());
                pageMap.put("pageCount",resp.getPageCount());
            }
        }
        pageMap.put("pageNo", mySaleCouponOrderRequest.getPageNo());
        pageMap.put("pageSize", mySaleCouponOrderRequest.getPageSize());
        pageMap.put("orderList",saleOrderList);
        return pageMap;
    }

    /**
     * 订单详情查询
     * @param channelCode
     * @param userNo
     * @param mySaleCouponOrderRequest
     * @return
     */
    private  Map<String,Object> queryOrderDetail(String channelCode,String userNo,MySaleCouponOrderRequest mySaleCouponOrderRequest){
        PtSaleCouponOrderGetRequest ptSaleCouponGetRequest = createPtSaleCouponOrderGetRequest(channelCode,userNo,mySaleCouponOrderRequest);
        List<SaleCouponGet> saleOrderList  = new LinkedList<>();
        Map<String,Object> pageMap = new HashMap<>();
        HttpResult serviceResult = doPost(ptSaleCouponGetRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_LISTORDER);
        if (null != serviceResult && serviceResult.isResult()) {
            PtSaleCouponOrderGetResponse ptSaleCouponGetResponse = (PtSaleCouponOrderGetResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponOrderGetResponse.class);
            if ("1001".equals(ptSaleCouponGetResponse.getResultCode())&&!StringUtil.isNullOrEmpty(ptSaleCouponGetResponse.getCouponOrderList())) {
                List<PtSaleCouponOrderGetResponse.CouponOrder> ptCouponOrderList = ptSaleCouponGetResponse.getCouponOrderList();
                //订单处理
                //当前时间  单位是毫秒
                long curTime = System.currentTimeMillis();
                for (PtSaleCouponOrderGetResponse.CouponOrder couponOrder : ptCouponOrderList) {
                    SaleCouponGet saleCouponGet = new SaleCouponGet();
                    BeanUtils.copyProperties(couponOrder, saleCouponGet);
                    List<CouponShow> showCouponList = new LinkedList<>();
                    //优惠券处理
                    for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon : couponOrder.getSaleCouponList()) {
                        CouponShow couponShow = new CouponShow();
                        BeanUtils.copyProperties(saleCoupon, couponShow);
                        couponShow.setActivityName(showOrderNameBySource(couponShow.getCouponSource(),couponShow.getCouponPrice(),couponShow.getActivityName()));
                        OrderCouponStateEnum orderState = OrderCouponStateEnum.getEnum(couponShow.getCouponState());
                        couponShow.setCouponStateName(orderState==null?"":orderState.getDesc());
                        showCouponList.add(couponShow);
                    }
                    saleCouponGet.setCouponList(showCouponList);
                    //支付时限
                    String bookDateStr = saleCouponGet.getOrderDatetime();
                    Date bookDate = DateUtils.toDate(bookDateStr, "yyyy-MM-dd HH:mm:ss");
                    //下单时间
                    long bookTime = bookDate.getTime();
                    //支付时限  小于0代表已过期
                    long endTime = DateUtils.dateAddOrLessSecond(bookDate, handConfig.getTimeLimit() * 60).getTime();
                    //处理订单基本信息
                    dealSaleCouponGet(saleCouponGet, couponOrder.getOrderState(), couponOrder.getOrderStateName(), couponOrder.getPayState(), bookTime, endTime, curTime);
                    saleOrderList.add(saleCouponGet);
                }
            }
        }
        pageMap.put("pageNo", mySaleCouponOrderRequest.getPageNo());
        pageMap.put("pageSize", mySaleCouponOrderRequest.getPageSize());
        pageMap.put("orderList",saleOrderList);
        return pageMap;
    }


    /**
     * 综合订单转换为权益订单
     * @param orderTotalBriefInfoList
     * @return
     */
    private List<SaleCouponGet> orderToSaleOrder(List<OrderTotalBriefInfo> orderTotalBriefInfoList) {
        List<SaleCouponGet> saleOrderList = new LinkedList<>();
        if (StringUtil.isNullOrEmpty(orderTotalBriefInfoList)) {
            return saleOrderList;
        }
        //当前时间  单位是毫秒
        long curTime = System.currentTimeMillis();
        //订单信息
        for(OrderTotalBriefInfo orderTotalBriefInfo:orderTotalBriefInfoList){
            SaleCouponGet saleCouponOrder = new SaleCouponGet();
            BeanUtils.copyProperties(orderTotalBriefInfo,saleCouponOrder);
            saleCouponOrder.setSaleCouponCount(orderTotalBriefInfo.getOtherOrderInfoList().size());
            saleCouponOrder.setOrderDatetime(orderTotalBriefInfo.getCreateDatetime());
            saleCouponOrder.setOrderTotalAmount(orderTotalBriefInfo.getAmount());
            saleCouponOrder.setFfpUseTotalScore((int)orderTotalBriefInfo.getUseScore().doubleValue());
            //优惠券处理
            boolean flag  = false;
            List<CouponShow> couponShowList = new LinkedList<>();
            for(OtherOrderInfo otherOrderInfo:orderTotalBriefInfo.getOtherOrderInfoList()){
                if(otherOrderInfo.getSaleCouponInfo()!=null){
                    CouponShow couponShow = new CouponShow();
                    BeanUtils.copyProperties(otherOrderInfo.getSaleCouponInfo(),couponShow);
                    couponShowList.add(couponShow);
                }else{
                    //错误的历史数据
                    flag = true;
                    break;
                }
            }
            //优惠券集合无值舍弃当前订单
           if(flag){
               continue;
           }
            saleCouponOrder.setCouponList(couponShowList);
            //支付状态判断
            String bookDateStr = saleCouponOrder.getOrderDatetime();
            Date bookDate = DateUtils.toDate(bookDateStr, "yyyy-MM-dd HH:mm:ss");
            //下单时间
            long bookTime = bookDate.getTime();
            //支付时限  小于0代表已过期
            long endTime = DateUtils.dateAddOrLessSecond(bookDate, handConfig.getTimeLimit() * 60).getTime();
            //处理订单基本信息
            dealSaleCouponGet(saleCouponOrder,orderTotalBriefInfo.getOrderState(), null, orderTotalBriefInfo.getPayState(), bookTime, endTime, curTime);
            saleOrderList.add(saleCouponOrder);
        }
        return saleOrderList;
    }


    /**
     * 行李券展示排序
     * @param saleCouponList
     */
    private void sortCouponPro(List<SaleCoupon> saleCouponList){
        Collections.sort(saleCouponList, new Comparator<SaleCoupon>(){
            @Override
            public int compare(SaleCoupon o1, SaleCoupon o2) {
                return (int) (o1.getCouponPrice()-o2.getCouponPrice());
            }}
        );
    }

    /**
     * 产品购买重置名称
     * @param saleCouponList
     */
    private void renderCoupon(List<SaleCoupon> saleCouponList){
        for(SaleCoupon saleCoupon:saleCouponList){
            String type = showTypeBySource(saleCoupon.getCouponSource());
            saleCoupon.setCouponType(type);
            String activityName = showOrderNameBySource(saleCoupon.getCouponSource(),saleCoupon.getCouponPrice(),saleCoupon.getActivityName());
            saleCoupon.setActivityName(activityName);
        }
    }

    /**
     * 根据source获取名称
     * @param couponSource
     * @return
     */
    private String showTypeBySource(String couponSource){
        String type;
        switch (couponSource){
            case "HOBG":{
                type = "行李额度兑换券";
                break;
            }
            case "HOLO":{
                type = "贵宾休息室券";
                break;
            }
            default:{
                type="";break;
            }
        }
        return type;
    }

    /**
     * 可售券
     * @param saleCouponGet
     * @param orderState
     * @param payState
     * @param bookTime
     * @param endTime
     * @param curTime
     */
    private void dealSaleCouponGet(SaleCouponGet saleCouponGet, String orderState, String orderStateName, String payState, long bookTime, long endTime, long curTime){
        List<CouponShow> couponList = saleCouponGet.getCouponList();
        Set<String> couponSourceSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(couponList)) {
            couponList.forEach(couponShow -> couponSourceSet.add(couponShow.getCouponSource()));
        }
        // 包含接送机
        if (couponSourceSet.contains(VoucherTypesEnum.HOCAR.getCode())) {
            saleCouponGet.setOrderState(orderState);
            saleCouponGet.setOrderStateName(orderStateName);
        } else {
            //预订
            if (OrderStateEnum.Init.getStateCode().equals(orderState) || OrderStateEnum.Booking.getStateCode().equals(orderState)) {
                if (PayEnum.UnPay.getStateCode().equals(payState)) {
                    if (curTime <= endTime) {
                        //支付时限
                        saleCouponGet.setTimeLimit(endTime - curTime);
                        saleCouponGet.setOrderState(PayEnum.UnPay.getStateCode());
                        saleCouponGet.setOrderStateName("等待付款");
                    } else {
                        saleCouponGet.setOrderState(OrderStateEnum.Cancel.getStateCode());
                        saleCouponGet.setOrderStateName("已取消");
                    }
                } else if (PayEnum.Pay.getStateCode().equals(payState)) {
                    saleCouponGet.setOrderState(PayEnum.Pay.getStateCode());
                    saleCouponGet.setOrderStateName("已支付");
                }
            }
            //取消
            else if (OrderStateEnum.Cancel.getStateCode().equals(orderState)) {
                saleCouponGet.setOrderState(OrderStateEnum.Cancel.getStateCode());
                saleCouponGet.setOrderStateName("已取消");
            }
            //已完成
            else if (OrderStateEnum.Finish.getStateCode().equals(orderState)) {
                saleCouponGet.setOrderState(PayEnum.Pay.getStateCode());
                saleCouponGet.setOrderStateName("已支付");
            } else {
                saleCouponGet.setOrderState(orderState);
                saleCouponGet.setOrderStateName("");
            }
        }
        //名称处理
        if(!StringUtil.isNullOrEmpty(saleCouponGet.getCouponList())){
            //产品名称都是一致的，取第一个即可
            CouponShow saleCoupon = saleCouponGet.getCouponList().get(0);
            saleCouponGet.setStartDate(saleCoupon.getStartDate());
            saleCouponGet.setEndDate(saleCoupon.getEndDate());
            saleCouponGet.setPrice(saleCoupon.getPrice());
            saleCouponGet.setOrderName(showOrderNameBySource(saleCoupon.getCouponSource(),saleCoupon.getCouponPrice(),saleCoupon.getActivityName()));
        }
        //总金额
        saleCouponGet.setOrderAmoutPayable(saleCouponGet.getOrderTotalAmount()+saleCouponGet.getFfpUseTotalScore());
    }

    /**
     * 订单名称处理
     * @param couponSource
     * @param couponPrice
     * @param actName  原有的活动名称
     * @return
     */
    private String showOrderNameBySource(String couponSource,double couponPrice,String actName){
        String type;
        switch (couponSource){
            case "HOBG":{
                type = "行李额度兑换券-"+(int)couponPrice+"KG";
                break;
            }
            case "HOLO":{
                type = "贵宾休息室券";
                break;
            }
            default:{
                type=actName;break;
            }
        }
        return type;
    }

    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService redisService;
    @Autowired
    private CrmWSClient crmClient;
    @Autowired
    private HandConfig handConfig;
}
