package com.juneyaoair.mobile.handler.controller;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.member.MemberLevelEnum;
import com.juneyaoair.appenum.member.VerifyStatusEnum;
import com.juneyaoair.appenum.order.OrderPayStateEnum;
import com.juneyaoair.appenum.order.OrderStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.coupon.response.v2.CreateOrderResponseDto;
import com.juneyaoair.baseclass.memberprolong.MemberProlongCreateOrderRequestDto;
import com.juneyaoair.baseclass.memberprolong.MemberProlongResponseDto;
import com.juneyaoair.baseclass.memberprolong.order.PtMemberLevelActivityInfo;
import com.juneyaoair.baseclass.newcoupon.req.protocol.CreateOrderRequestDto;
import com.juneyaoair.baseclass.newcoupon.resp.protocol.BaseCouponOrderIdentity;
import com.juneyaoair.baseclass.response.payment.PaymentResp;
import com.juneyaoair.baseclass.transferaccommodation.request.BaseCouponOrderRequestDto;
import com.juneyaoair.baseclass.transferaccommodation.resonse.BaseCouponOrderResponseDto;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.VirtualPaymentConvert;
import com.juneyaoair.mobile.handler.service.ICouponOrderService;
import com.juneyaoair.mobile.handler.service.IOrderService;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.PtRealNameReq;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.member.response.PtRealNameResp;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponOrderGetResponse;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mortbay.util.SingletonList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 会员延期活动
 * <AUTHOR>
 * @Description
 * @create 2020-11-03 9:44
 */
@RestController
@RequestMapping("member/prolong/")
public class MemberProlongController extends BassController{

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private ICouponOrderService couponOrderService;

    @RequestMapping(value = "queryProlongLevel", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询延长等级活动", notes = "查询延长等级活动")
    @InterfaceLog
    public BaseResp queryProlongLevel(@RequestBody BaseReq<UserInfoMust> req, HttpServletRequest request){
        BaseResp<MemberProlongResponseDto> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            // 1. 查询会员是否可延期
            BaseCouponOrderRequestDto requestDto = new BaseCouponOrderRequestDto();
            requestDto.setVersion(HandlerConstants.VERSION);
            requestDto.setChannelCode(req.getChannelCode());
            requestDto.setUserNo(this.getChannelInfo(req.getChannelCode(), "10"));
            requestDto.setFfpId(req.getRequest().getFfpId());
            requestDto.setFfpCardNo(req.getRequest().getFfpCardNo());
            requestDto.setRequestIp(ip);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult httpResult = this.doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_MEMBER_LEVEL_PROLONG_QUERY, headMap);
            if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
                throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
            }
            BaseCouponOrderResponseDto<PtMemberLevelActivityInfo> responseDto = JsonUtil.fromJson(httpResult.getResponse(),
                    new TypeToken<BaseCouponOrderResponseDto<PtMemberLevelActivityInfo>>(){}.getType());
            if (UnifiedOrderResultEnum.CHECK_85001.getResultCode().equals(responseDto.getResultCode())) {
                throw new OperationFailedException(responseDto.getResultCode());
            } else if (UnifiedOrderResultEnum.CHECK_2041.getResultCode().equals(responseDto.getResultCode())) {
                resp.setResultCode(WSEnum.ACTIVITY_LNELIGIBLE.getResultCode());
                resp.setResultInfo("抱歉，您不符合会员续级的参与条件");
                return resp;
            } else if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(responseDto.getResultCode())) {
                throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
            }
            if (null == responseDto.getResult()) {
                throw new OperationFailedException("系统异常，未查询到续级活动");
            }
            // 成功的情况
            // 2. 校验会员是否已购买延期活动产品
            List<PtSaleCouponOrderGetResponse.CouponOrder> payedOrderList = couponOrderService.queryCouponOrderList( req.getRequest().getFfpId(), req.getRequest().getFfpCardNo(),ip,
                    req.getChannelCode(), false, OrderPayStateEnum.Pay, null, SingletonList.newSingletonList(VoucherTypesEnum.MemberLevelDelay), 10, 1);
            if (CollectionUtils.isNotEmpty(payedOrderList)) {
                resp.setResultCode(WSEnum.ACTIVITY_ATTENDED.getResultCode());
                resp.setResultInfo("您已参与过此会员延期活动\n不要贪心哦");
                return resp;
            }
            MemberProlongResponseDto memberProlongResponseDto = new MemberProlongResponseDto();
            MemberLevelEnum memberLevelEnum = MemberLevelEnum.findByLevelCode(responseDto.getResult().getLevel() + "");
            if (null == memberLevelEnum) {
                resp.setResultCode(WSEnum.ACTIVITY_LNELIGIBLE.getResultCode());
                resp.setResultInfo("抱歉，您不符合会员续级的参与条件");
                return resp;
            }
            BeanUtils.copyNotNullProperties(responseDto.getResult(), memberProlongResponseDto);
            BigDecimal price = BigDecimal.valueOf(responseDto.getResult().getPrice());
            //您的X卡会员已到期，可使用XXX积分/现金购买续级服务，恢复一年X卡会员权益
            memberProlongResponseDto.setActivityMessage("您的" + memberLevelEnum.getLevelName() + "会员已到期，可使用" +
                    price.setScale(0, BigDecimal.ROUND_HALF_UP) + "积分/现金购买续级服务，恢复一年" + memberLevelEnum.getLevelName() + "会员权益");
            memberProlongResponseDto.setRightMessage(memberLevelEnum.getLevelName() + "会员尊享以下权益");
            memberProlongResponseDto.setMemberLevelDesc(memberLevelEnum.getLevelName());
            String cardPictureUrl;
            switch (memberLevelEnum) {
                case Golden: cardPictureUrl = HandlerConstants.MEMBER_CARD_GOLDEN_URL; break;
                case Silver:cardPictureUrl = HandlerConstants.MEMBER_CARD_SILVER_URL; break;
                case Platinum: cardPictureUrl = HandlerConstants.MEMBER_CARD_BLACK_URL; break;
                default:cardPictureUrl = HandlerConstants.MEMBER_CARD_COMMON_URL;
            }
            memberProlongResponseDto.setCardPictureUrl(cardPictureUrl);
            resp.setObjData(memberProlongResponseDto);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    @RequestMapping(value = "createMemberProlongOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "创建会员延期订单", notes = "创建会员延期订单")
    @InterfaceLog
    @NotDuplicate
    public BaseResp recordQuery(@RequestBody BaseReq<MemberProlongCreateOrderRequestDto> req, HttpServletRequest request){
        BaseResp<CreateOrderResponseDto> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            // 1. 使用积分校验
            if (req.getRequest().getUseScore() > 0){
                // 1.1 实名认证信息
                boolean accmflag = false;
                //查询实名认证状态
                PtRealNameReq ptApiRequest = new PtRealNameReq();
                ptApiRequest.setID(req.getRequest().getFfpId());
                ptApiRequest.setClientCode(req.getChannelCode());
                ptApiRequest.setSignature(EncoderHandler.encodeByMD5(req.getChannelCode() + req.getRequest().getFfpId() + this.getChannelInfo(req.getChannelCode(), "40")));
                PtRealNameResp realNameResp = memberService.realNameState(ptApiRequest);
                if ("000".equals(realNameResp.getStatusCode())) {
                    VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(realNameResp.getVerifyStatus());
                    if (verifyStatusEnum != null && VerifyStatusEnum.PASS.code.equals(verifyStatusEnum.code)) {
                        accmflag = true;
                    }
                }
                String[] items = { MemberDetailRequestItemsEnum.STATEINFO.eName};
                PtApiCRMRequest<PtMemberDetailRequest> ptMemberReq = CRMReqUtil.buildMemberDetailReq(req.getRequest().getFfpCardNo(),
                        req.getRequest().getFfpId(), request, ChannelCodeEnum.MOBILE.getChannelCode(), items);
                PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptMemberReq);
                if (ptCRMResponse.getCode() != 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(ptCRMResponse.getMsg());
                    return resp;
                }
                // 验证密码
                BaseResp checkResp = orderService.checkFreeScoreLimit(req.getRequest().getFfpId(), req.getChannelCode(),
                        getChannelInfo(req.getChannelCode(), "40"), req.getRequest().getUseScore(),
                        req.getRequest().getUseScorePwd(), accmflag, "Y".equals(ptCRMResponse.getData().getStateInfo().getIsSmallExemptPwd()));
                if (!WSEnum.SUCCESS.getResultCode().equals(checkResp.getResultCode())) {
                    resp.setResultCode(checkResp.getResultCode());
                    resp.setErrorInfo(checkResp.getResultInfo());
                    return resp;
                }
            }
            // 2.校验重复下单
            List<PtSaleCouponOrderGetResponse.CouponOrder> payedOrderList = couponOrderService.queryCouponOrderList(req.getRequest().getFfpId(), req.getRequest().getFfpCardNo(),ip,
                    req.getChannelCode(), false, OrderPayStateEnum.Pay, null, SingletonList.newSingletonList(VoucherTypesEnum.MemberLevelDelay), 10 ,1);
            if (CollectionUtils.isNotEmpty(payedOrderList)) {
                throw new OperationFailedException("您已参与过此会员延期活动\n不要贪心哦");
            }
            payedOrderList = couponOrderService.queryCouponOrderList(req.getRequest().getFfpId(), req.getRequest().getFfpCardNo(),ip,
                    req.getChannelCode(), false, OrderPayStateEnum.UnPay,OrderStateEnum.Booking, SingletonList.newSingletonList(VoucherTypesEnum.MemberLevelDelay), 10, 1);
            if (CollectionUtils.isNotEmpty(payedOrderList)) {
                throw new OperationFailedException("您已有待支付的订单了，请勿重复下单");
            }
            // 3.下单
            BaseCouponOrderRequestDto<CreateOrderRequestDto> couponOrderRequestDto = this.genCreateOrderRequestDto(ip, req.getChannelCode(), req.getRequest());
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip,"");
            HttpResult httpResult = this.doPostClient(couponOrderRequestDto,
                    HandlerConstants.URL_FARE_API + HandlerConstants.SUB_MEMBER_LEVEL_PROLONG_BOOK, headMap);
            if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
                throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
            }
            BaseCouponOrderResponseDto<BaseCouponOrderIdentity> responseDto = JsonUtil.fromJson(httpResult.getResponse(),
                    new TypeToken<BaseCouponOrderResponseDto<BaseCouponOrderIdentity>>(){}.getType());
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(responseDto.getResultCode())) {
                CreateOrderResponseDto createOrderResponseDto = new CreateOrderResponseDto();
                createOrderResponseDto.setOrderNo(responseDto.getResult().getOrderNo());
                createOrderResponseDto.setChannelOrderNo(responseDto.getResult().getOrderChannelOrderNo());
                createOrderResponseDto.setPayState(BigDecimal.ZERO.equals(new BigDecimal(couponOrderRequestDto.getRequest().getTotalAmount())));
                //0元同步支付
                if (req.getRequest().getUseScore() > 0 && new BigDecimal(req.getRequest().getUseScore()).compareTo(new BigDecimal(req.getRequest().getPrice())) == 0) {
                    String key = getChannelInfo(req.getChannelCode(), "20");
                    String postUrl = HandlerConstants.URL_PAY;
                    Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(), responseDto.getResult().getOrderNo(), responseDto.getResult().getOrderChannelOrderNo(), key, "CouponType", "", "O");
                    parametersMap.put("UseScore", String.valueOf(req.getRequest().getUseScore()));
                    HttpResult payResult = doPayPost(postUrl, parametersMap);
                    PaymentResp paymentResp;
                    if (payResult.isResult()) {
                        String paymentInfo = payResult.getResponse().trim();
                        log.info("请求号:{}，IP地址:{}，0元支付结果：{}", reqId, ip, paymentInfo);
                        paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                        //虚拟支付成功
                        if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                            createOrderResponseDto.setPayState(true);
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("权益券购买支付失败！");
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("支付请求出错");
                    }
                }
                resp.setObjData(createOrderResponseDto);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(responseDto.getErrorInfo());
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    /**
     * 封装创建订单参数
     * @return
     */
    private BaseCouponOrderRequestDto<CreateOrderRequestDto> genCreateOrderRequestDto(String ip, String channelCode, MemberProlongCreateOrderRequestDto requestDto) {
        BaseCouponOrderRequestDto<CreateOrderRequestDto> couponOrderRequestDto = new BaseCouponOrderRequestDto<>();
        couponOrderRequestDto.setRequestIp(ip);
        couponOrderRequestDto.setFfpCardNo(requestDto.getFfpCardNo());
        couponOrderRequestDto.setFfpId(requestDto.getFfpId());
        couponOrderRequestDto.setChannelCode(channelCode);
        couponOrderRequestDto.setVersion(HandlerConstants.VERSION);
        couponOrderRequestDto.setUserNo(this.getChannelInfo(channelCode, "10"));
        CreateOrderRequestDto createOrderRequestDto = new CreateOrderRequestDto();
        createOrderRequestDto.setFfpId(requestDto.getFfpId());
        createOrderRequestDto.setFfpCardNo(requestDto.getFfpCardNo());
        createOrderRequestDto.setChannelOrderNo(requestDto.getChannelOrderNo());
        if (requestDto.getUseScore() > 0) {
            createOrderRequestDto.setTotalAmount(new BigDecimal(requestDto.getPrice()).subtract(BigDecimal.valueOf(requestDto.getUseScore())).toString());
        } else {
            createOrderRequestDto.setTotalAmount(requestDto.getPrice());
        }
        createOrderRequestDto.setPhoneNo(requestDto.getPhoneNo());
        createOrderRequestDto.setUseScore(requestDto.getUseScore() + "");
        createOrderRequestDto.setCurrency(HandlerConstants.CURRENCY_CODE);
        createOrderRequestDto.setRefundRule("产品一经购买，不可退款");
        MemberLevelEnum memberLevelEnum = MemberLevelEnum.findByLevelCode(requestDto.getLevel() + "");
        if (null != memberLevelEnum) {
            createOrderRequestDto.setProductName(memberLevelEnum.getLevelName() + "会员");
        } else {
            createOrderRequestDto.setProductName("会员延期");
        }
        couponOrderRequestDto.setRequest(createOrderRequestDto);
        return couponOrderRequestDto;
    }


}
