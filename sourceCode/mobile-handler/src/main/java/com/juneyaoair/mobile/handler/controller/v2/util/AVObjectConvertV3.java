package com.juneyaoair.mobile.handler.controller.v2.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.*;
import com.juneyaoair.appenum.common.AirCompanyEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.av.common.*;
import com.juneyaoair.baseclass.baggagestandard.common.BaggageFilter;
import com.juneyaoair.baseclass.baggagestandard.common.BaggageStandardConfig;
import com.juneyaoair.baseclass.baggagestandard.response.BaggageStandardResp;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.PictureDto;
import com.juneyaoair.baseclass.change.ChangeAndRefundRule;
import com.juneyaoair.baseclass.request.av.AirCompany;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.response.av.*;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.AVObjectConvert;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.service.bean.country.TCountryDTO;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.av.comm.*;
import com.juneyaoair.thirdentity.av.response.FlightInfoCombApi;
import com.juneyaoair.thirdentity.av.response.PtQueryFlightFareResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description ADDON/SPA/ONEWAY 联程航班处理
 * @date 2019/7/11  8:48.
 */
@Slf4j
public class AVObjectConvertV3 {

    public static ChangeFlightFareResp toFlightFareAddOnResponse(PtQueryFlightFareResponse res, HandConfig handConfig, List<FlightAdvertisementDto> advertisementDtos, LocalCacheService localCacheService, String flightQueryType, ClientInfo userAppVer) {
        //处理航班各类信息
        String dateStr = DateUtils.getDateStringAll(new Date());
        ChangeFlightFareResp resp = new ChangeFlightFareResp(res.getRouteType(), res.getCurrencyCode(), dateStr, res.getInterFlag(), res.getResultCode(), res.getErrorInfo(), new ArrayList<>());
        resp.setUseFareV30(true);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(res.getResultCode()) || CollectionUtils.isEmpty(res.getFlightInfoCombList())) {
            return resp;
        }
        //公务商旅免票过滤，保留商务舱
        if (FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(flightQueryType)) {
            for (FlightInfoCombApi flightInfoCombApiList : res.getFlightInfoCombList()) {
                List<V2CabinFareApi> v2CabinFareApiList = flightInfoCombApiList.getCabinFareCombList().stream().filter(v2CabinFareApi -> handConfig.getBusinessFlyFreeTicketCabin().equals(v2CabinFareApi.getCabinComb()) || (handConfig.getBusinessFlyFreeTicketCabin() + "/" + handConfig.getBusinessFlyFreeTicketCabin()).equals(v2CabinFareApi.getCabinComb())).collect(Collectors.toList());
                flightInfoCombApiList.setCabinFareCombList(v2CabinFareApiList);
            }
        }
        String routeType = res.getRouteType();
        //联程主航班信息处理
        List<FlightInfoComb> flightInfoCombList = toFlightInfoCombList(res, handConfig, localCacheService);

        // 根据flightQueryType 处理一单多券场景退改规则
        updateChangeAndRefundRuleByFlightQueryType(flightQueryType, flightInfoCombList);

        //航班组合按照去程航班分组,返回给前端
        Map<String, List<FlightInfoComb>> flightInfoCombMap = flightInfoCombList.stream().collect(Collectors.groupingBy(flightInfoComb -> flightInfoComb.getFlightKey().split("/")[0]));
        //返回前端的航班数据// Revalidation 非通程改通程情况 ChangeType 上报为 "NEW"
        List<FlightInfo> flightInfoList = new ArrayList<>();
        String curDate = DateUtils.getCurrentDateTimeStr();
        for (Map.Entry<String, List<FlightInfoComb>> entry : flightInfoCombMap.entrySet()) {
            List<FlightInfoComb> groupFlightInfoCombList = entry.getValue();
            List<FlightInfo> flightInfoReturnList = new ArrayList<>();
            FlightInfo flightInfoUniqueG = new FlightInfo();
            //航班低价处理
            CabinFare minCabinFare = setGFlightMinPrice(groupFlightInfoCombList, routeType);
            //对组合航班进行整合处理
            boolean hasgo = false;
            for (FlightInfoComb flightInfoComb : groupFlightInfoCombList) {
                List<FlightInfo> flightInfoListG = flightInfoComb.getCombFlightInfoList().stream().filter(flightInfo -> FlightDirection.GO.getCode().equals(flightInfo.getFlightDirection())).collect(Collectors.toList());
                FlightInfo flightInfoG = new FlightInfo();
                setMainFlightInfo(flightInfoG, flightInfoComb.getFareType(), flightInfoListG, localCacheService,handConfig);
                flightInfoG.setFlightNo(flightInfoComb.getFlightNoComb().split("/")[0]);
                flightInfoG.setFlightDirection(FlightDirection.GO.getCode());
                //航空ICON
                flightInfoG.setFlightNoIconList(setFlightNoIconList(flightInfoG.getFlightNo(), handConfig.getAirCompany()));
                flightInfoG.setMinPrice(minCabinFare == null ? null : minCabinFare.getPriceValue());
                flightInfoG.setMinCabinClassName(minCabinFare == null ? null : minCabinFare.getCabinClass());
                flightInfoG.setOnewayMinPrice(Optional.ofNullable(minCabinFare).map(i -> i.flightPriceComb).map(i -> i.goPriceValue).map(i -> i.doubleValue()).orElse(null));
                flightInfoG.setTotalTax(minCabinFare == null ? null : minCabinFare.getTotalTax());
                flightInfoG.setFareType(flightInfoComb.getFareType());
                // 行李直挂，联程值机 标签
                flightInfoG.setBaggageThroughFlag(flightInfoComb.getBaggageThroughFlag());
                flightInfoG.setThroughCheckFlag(flightInfoComb.getThroughCheckFlag());
                flightInfoG.setPriority(FareTypeEnum.SIMPLE.getFare().equalsIgnoreCase(flightInfoComb.getFareType()) ? 0 : 100);
                if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType)) {
                    List<FlightInfo> flightInfoListB = flightInfoComb.getCombFlightInfoList().stream().filter(flightInfo -> FlightDirection.BACK.getCode().equals(flightInfo.getFlightDirection())).collect(Collectors.toList());
                    FlightInfo flightInfoB = new FlightInfo();
                    setMainFlightInfo(flightInfoB, flightInfoComb.getFareType(), flightInfoListB, localCacheService,handConfig);
                    flightInfoB.setFlightNo(flightInfoComb.getFlightNoComb().split("/")[1]);
                    flightInfoB.setFlightDirection(FlightDirection.BACK.getCode());
                    //航空ICON
                    flightInfoB.setFlightNoIconList(setFlightNoIconList(flightInfoComb.getFlightNoComb().split("/")[1], handConfig.getAirCompany()));
                    flightInfoB.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), flightInfoB.getCarrierNo()));
                    CabinFare minBackCabinFare = getTotalWayBackFlightMinPrice(flightInfoComb.getAdtCabinFareList());//总程最低价
                    if (minBackCabinFare != null) {
                        flightInfoB.setMinPrice(minBackCabinFare.getPriceValue());
                        flightInfoB.setTotalTax(minBackCabinFare.getTotalTax());
                        flightInfoB.setMinCabinClassName(minBackCabinFare.getCabinClass());
                    }
                    CabinFare backMinCabinFare = flightInfoComb.getAdtCabinFareList().stream().min(
                            (o1, o2) -> {
                                if (o1 == null || o1.flightPriceComb == null || o1.flightPriceComb.backPriceValue == null) {
                                    return 0;
                                }
                                return o1.flightPriceComb.backPriceValue.compareTo(o2.flightPriceComb.backPriceValue);
                            }).orElse(null);
                    flightInfoB.setOnewayMinPrice(Optional.ofNullable(backMinCabinFare).map(i -> i.flightPriceComb).map(i -> i.backPriceValue).map(i -> i.doubleValue()).orElse(null));
                    flightInfoB.setFareType(flightInfoComb.getFareType());
                    flightInfoB.setBaggageThroughFlag(flightInfoComb.getBaggageThroughFlag());
                    flightInfoB.setThroughCheckFlag(flightInfoComb.getThroughCheckFlag());
                    flightInfoB.setPriority(FareTypeEnum.SIMPLE.getFare().equalsIgnoreCase(flightInfoComb.getFareType()) ? 0 : 100);
                    flightInfoReturnList.add(flightInfoB);
                }
                if (!hasgo) {
                    flightInfoUniqueG = flightInfoG;
                    hasgo = true;
                } else {
                    if (CollectionUtils.isNotEmpty(flightInfoG.getCabinFareList()) && CollectionUtils.isEmpty(flightInfoUniqueG.getCabinFareList())) {
                        flightInfoUniqueG = flightInfoG;
                    }
                }
            }
            flightInfoUniqueG.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), flightInfoUniqueG.getCarrierNo()));
            //出发时间排序
            flightInfoReturnList.sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDepDateTime).thenComparing(FlightInfo::getArrDateTime));
            //设置返程航班
            flightInfoUniqueG.setFlightInfoReturnList(flightInfoReturnList);
            //如果返程航班的都没舱位的情况下，去程舱位设置售罄
            if (CollectionUtils.isNotEmpty(groupFlightInfoCombList)) {
                if (groupFlightInfoCombList.stream().allMatch(flightInfo -> CollectionUtils.isEmpty(flightInfo.getAdtCabinFareList()))) {
                    flightInfoUniqueG.setCabinFareList(Lists.newArrayList());
                }
            }
            flightInfoList.add(flightInfoUniqueG);
        }
        // OneWay的筛选出飞行时长最短的4个航班
        if (flightInfoList.stream().allMatch(flightInfo -> FareTypeEnum.ONEWAY.getFare().equals(flightInfo.getFareType()))) {
            // 取飞行时长最小的4个航班
            if (flightInfoList.size() > handConfig.getOneWayFareFlightNumber()) {
                flightInfoList.sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDuration).thenComparing(FlightInfo::getArrDateTime));
                flightInfoList = flightInfoList.subList(0, handConfig.getOneWayFareFlightNumber());
            }
            if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType)) {
                flightInfoList.forEach(flightInfo -> {
                    if (CollectionUtils.isNotEmpty(flightInfo.getFlightInfoReturnList()) && flightInfo.getFlightInfoReturnList().size() > handConfig.getOneWayFareFlightNumber()) {
                        flightInfo.getFlightInfoReturnList().sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDuration).thenComparing(FlightInfo::getArrDateTime));
                        flightInfo.setFlightInfoReturnList(flightInfo.getFlightInfoReturnList().subList(0, handConfig.getOneWayFareFlightNumber()));
                    }
                });
            }
        }
        //①优先直飞航班  ②按照起飞时间从早到晚排序
        flightInfoList.sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDepDateTime).thenComparing(FlightInfo::getArrDateTime));
        resp.setFareSource(res.getFareSource());
        resp.setFlightInfoList(flightInfoList);
        setFlightAdvertisement(flightInfoCombList, advertisementDtos, res.getRouteType());
        //  设置cabinLabelList2标签
        setCabinLabelList2(flightInfoCombList, handConfig);
        resp.setTransferFlightInfoList(flightInfoCombList);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setUuId(res.getUuId());
        return resp;
    }


    public static ChangeFlightFareResp toFlightFareAddOnChengResponse(PtQueryFlightFareResponse res, HandConfig handConfig, List<FlightAdvertisementDto> advertisementDtos, LocalCacheService localCacheService, String fareType) {
        //处理航班各类信息
        String dateStr = DateUtils.getDateStringAll(new Date());
        ChangeFlightFareResp resp = new ChangeFlightFareResp(res.getRouteType(), res.getCurrencyCode(), dateStr, res.getInterFlag(), res.getResultCode(), res.getErrorInfo(), new ArrayList<>());
        resp.setUseFareV30(true);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(res.getResultCode()) || CollectionUtils.isEmpty(res.getFlightInfoCombList())) {
            return resp;
        }
        String routeType = res.getRouteType();
        //联程主航班信息处理
        List<FlightInfoComb> flightInfoCombList = toFlightInfoCombList(res, handConfig, localCacheService);
        //航班组合按照去程航班分组,返回给前端
        Map<String, List<FlightInfoComb>> flightInfoCombMap = flightInfoCombList.stream().collect(Collectors.groupingBy(flightInfoComb -> flightInfoComb.getFlightKey().split("/")[0]));
        //返回前端的航班数据// Revalidation 非通程改通程情况 ChangeType 上报为 "NEW"
        List<FlightInfo> flightInfoList = new ArrayList<>();
        for (Map.Entry<String, List<FlightInfoComb>> entry : flightInfoCombMap.entrySet()) {
            List<FlightInfoComb> groupFlightInfoCombList = entry.getValue();
            List<FlightInfo> flightInfoReturnList = new ArrayList<>();
            FlightInfo flightInfoUniqueG = new FlightInfo();
            //航班低价处理
            CabinFare minCabinFare = setGFlightMinPrice(groupFlightInfoCombList, routeType);
            boolean hasgo = false;
            //对组合航班进行整合处理
            for (FlightInfoComb flightInfoComb : groupFlightInfoCombList) {
                List<FlightInfo> flightInfoListG = flightInfoComb.getCombFlightInfoList().stream().filter(flightInfo -> FlightDirection.GO.getCode().equals(flightInfo.getFlightDirection())).collect(Collectors.toList());
                FlightInfo flightInfoG = new FlightInfo();
                setMainFlightInfo(flightInfoG, flightInfoComb.getFareType(), flightInfoListG, localCacheService,handConfig);
                flightInfoG.setFlightNo(flightInfoComb.getFlightNoComb().split("/")[0]);
                flightInfoG.setFlightDirection(FlightDirection.GO.getCode());
                //航空ICON
                flightInfoG.setFlightNoIconList(setFlightNoIconList(flightInfoG.getFlightNo(), handConfig.getAirCompany()));
                flightInfoG.setMinPrice(minCabinFare == null ? null : minCabinFare.getPriceValue());
                flightInfoG.setMinCabinClassName(minCabinFare == null ? null : minCabinFare.getCabinClass());
                flightInfoG.setOnewayMinPrice(Optional.ofNullable(minCabinFare).map(i -> i.flightPriceComb).map(i -> i.goPriceValue).map(i -> i.doubleValue()).orElse(null));
                flightInfoG.setTotalTax(minCabinFare == null ? null : minCabinFare.getTotalTax());
                flightInfoG.setFareType(flightInfoComb.getFareType());
                // 行李直挂，联程值机 标签
                flightInfoG.setBaggageThroughFlag(flightInfoComb.getBaggageThroughFlag());
                flightInfoG.setThroughCheckFlag(flightInfoComb.getThroughCheckFlag());

                flightInfoG.setPriority(FareTypeEnum.SIMPLE.getFare().equalsIgnoreCase(flightInfoComb.getFareType()) ? 0 : 100);
                if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType)) {
                    List<FlightInfo> flightInfoListB = flightInfoComb.getCombFlightInfoList().stream().filter(flightInfo -> FlightDirection.BACK.getCode().equals(flightInfo.getFlightDirection())).collect(Collectors.toList());
                    FlightInfo flightInfoB = new FlightInfo();
                    setMainFlightInfo(flightInfoB, flightInfoComb.getFareType(), flightInfoListB, localCacheService,handConfig);
                    flightInfoB.setFlightNo(flightInfoComb.getFlightNoComb().split("/")[1]);
                    flightInfoB.setFlightDirection(FlightDirection.BACK.getCode());
                    //航空ICON
                    flightInfoB.setFlightNoIconList(setFlightNoIconList(flightInfoComb.getFlightNoComb().split("/")[1], handConfig.getAirCompany()));
                    flightInfoB.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), flightInfoB.getCarrierNo()));
                    CabinFare minBackCabinFare = getTotalWayBackFlightMinPrice(flightInfoComb.getAdtCabinFareList());//总程最低价
                    if (minBackCabinFare != null) {
                        flightInfoB.setMinPrice(minBackCabinFare.getPriceValue());
                        flightInfoB.setTotalTax(minBackCabinFare.getTotalTax());
                        flightInfoB.setMinCabinClassName(minBackCabinFare.getCabinClass());
                    }
                    CabinFare backMinCabinFare = flightInfoComb.getAdtCabinFareList().stream().min(
                            (o1, o2) -> {
                                if (o1 == null || o1.flightPriceComb == null || o1.flightPriceComb.backPriceValue == null) {
                                    return 0;
                                }
                                return o1.flightPriceComb.backPriceValue.compareTo(o2.flightPriceComb.backPriceValue);
                            }).orElse(null);
                    flightInfoB.setOnewayMinPrice(Optional.ofNullable(backMinCabinFare).map(i -> i.flightPriceComb).map(i -> i.backPriceValue).map(i -> i.doubleValue()).orElse(null));
                    flightInfoB.setFareType(flightInfoComb.getFareType());
                    flightInfoB.setBaggageThroughFlag(flightInfoComb.getBaggageThroughFlag());
                    flightInfoB.setThroughCheckFlag(flightInfoComb.getThroughCheckFlag());
                    flightInfoB.setPriority(FareTypeEnum.SIMPLE.getFare().equalsIgnoreCase(flightInfoComb.getFareType()) ? 0 : 100);
                    flightInfoReturnList.add(flightInfoB);
                }
                if (flightInfoComb.getFareType().equals(fareType)) {
                    if (!hasgo) {
                        flightInfoUniqueG = flightInfoG;
                        hasgo = true;
                    } else {
                        if (CollectionUtils.isNotEmpty(flightInfoG.getCabinFareList()) && CollectionUtils.isEmpty(flightInfoUniqueG.getCabinFareList())) {
                            flightInfoUniqueG = flightInfoG;
                        }
                    }
                }
            }
            flightInfoUniqueG.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), flightInfoUniqueG.getCarrierNo()));
            //出发时间排序
            flightInfoReturnList.sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDepDateTime).thenComparing(FlightInfo::getArrDateTime));
            //设置返程航班
            flightInfoUniqueG.setFlightInfoReturnList(flightInfoReturnList);
            //如果返程航班的都没舱位的情况下，去程舱位设置售罄
            if (CollectionUtils.isNotEmpty(groupFlightInfoCombList)) {
                if (groupFlightInfoCombList.stream().allMatch(flightInfo -> CollectionUtils.isEmpty(flightInfo.getAdtCabinFareList()))) {
                    flightInfoUniqueG.setCabinFareList(Lists.newArrayList());
                }
            }
            if (StringUtils.isNotBlank(flightInfoUniqueG.getFlightNo())) {
                flightInfoList.add(flightInfoUniqueG);
            }
        }
        // OneWay的筛选出飞行时长最短的4个航班
        if (flightInfoList.stream().allMatch(flightInfo -> FareTypeEnum.ONEWAY.getFare().equals(flightInfo.getFareType()))) {
            // 取飞行时长最小的4个航班
            if (flightInfoList.size() > handConfig.getOneWayFareFlightNumber()) {
                flightInfoList.sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDuration).thenComparing(FlightInfo::getArrDateTime));
                flightInfoList = flightInfoList.subList(0, handConfig.getOneWayFareFlightNumber());
            }
            if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType)) {
                flightInfoList.forEach(flightInfo -> {
                    if (CollectionUtils.isNotEmpty(flightInfo.getFlightInfoReturnList()) && flightInfo.getFlightInfoReturnList().size() > handConfig.getOneWayFareFlightNumber()) {
                        flightInfo.getFlightInfoReturnList().sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDuration).thenComparing(FlightInfo::getArrDateTime));
                        flightInfo.setFlightInfoReturnList(flightInfo.getFlightInfoReturnList().subList(0, handConfig.getOneWayFareFlightNumber()));
                    }
                });
            }
        }
        //①优先直飞航班  ②按照起飞时间从早到晚排序
        flightInfoList.sort(Comparator.comparingInt(FlightInfo::getPriority).thenComparing(FlightInfo::getDepDateTime).thenComparing(FlightInfo::getArrDateTime));
        resp.setFareSource(res.getFareSource());
        resp.setFlightInfoList(flightInfoList);
        setFlightAdvertisement(flightInfoCombList, advertisementDtos, res.getRouteType());
        setCabinLabelList2(flightInfoCombList, handConfig);
        resp.setTransferFlightInfoList(flightInfoCombList);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setUuId(res.getUuId());
        return resp;
    }


    /**
     * 根据flightQueryType 处理一单多券场景退改规则
     * <p>
     * 一单多券场景 退改规则、退票费 文案由 “按规定执行” 更改为 “以兑换卡券规则为准”
     * <p>
     * 退改规则由 {@link AVObjectConvertV2(List, String,Double) } 处理
     * <p>
     * 由 {@link FlightUtil#completeRule(List, List) 填充
     *
     * @param flightQueryType
     * @param flightInfoCombList
     */
    private static void updateChangeAndRefundRuleByFlightQueryType(String flightQueryType, List<FlightInfoComb> flightInfoCombList) {

        flightInfoCombList.forEach(v -> {
            if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(flightQueryType)
                    || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(flightQueryType)
                    || FlightQueryTypeEnum.OPEN_FREE_TICKET.getType().equals(flightQueryType)) {

                v.getAdtCabinFareList().forEach(cabinFare -> {

                    // 处理父层
                    if (CollectionUtils.isNotEmpty(cabinFare.getRefundRuleList())) {
                        cabinFare.getRefundRuleList().forEach(refundRule -> {
                            if ("按规定执行".equals(refundRule.getRuleDesc())) {
                                refundRule.setRuleDesc("以兑换卡券规则为准");
                            }
                        });
                    }
                    if (cabinFare.getChildRule() != null) {
                        cabinFare.getChildRule().getRefundRuleList().forEach(childRule -> {
                            if ("按规定执行".equals(childRule.getRuleDesc())) {
                                childRule.setRuleDesc("以兑换卡券规则为准");
                            }
                        });
                    }

                    // 处理SegmentCabinInfos下规则
                    cabinFare.getSegmentCabinInfos().forEach(segmentCabinInfo -> {
                        segmentCabinInfo.getRefundRuleList().forEach(segRefundRule -> {
                            if ("按规定执行".equals(segRefundRule.getRuleDesc())) {
                                segRefundRule.setRuleDesc("以兑换卡券规则为准");
                            }
                        });
                        if (segmentCabinInfo.getChildRule() != null) {
                            segmentCabinInfo.getChildRule().getRefundRuleList().forEach(segChildRule -> {
                                if ("按规定执行".equals(segChildRule.getRuleDesc())) {
                                    segChildRule.setRuleDesc("以兑换卡券规则为准");
                                }
                            });
                        }
                        if (segmentCabinInfo.getInfRule() != null) {
                            segmentCabinInfo.getInfRule().getRefundRuleList().forEach(segInfRule -> {
                                if ("按规定执行".equals(segInfRule.getRuleDesc())) {
                                    segInfRule.setRuleDesc("以兑换卡券规则为准");
                                }
                            });
                        }

                    });
                });
            }
        });
    }

    /**
     * 设置cabinLabelList2标签
     *
     * @param flightInfoCombList
     * @param handConfig
     */
    private static void setCabinLabelList2(List<FlightInfoComb> flightInfoCombList, HandConfig handConfig) {
        if (CollectionUtils.isEmpty(flightInfoCombList)) {
            return;
        }
        for (FlightInfoComb flightInfoComb : flightInfoCombList) {
            if (null == flightInfoComb || CollectionUtils.isEmpty(flightInfoComb.getAdtCabinFareList())) {
                continue;
            }
            for (CabinFare cabinFare : flightInfoComb.getAdtCabinFareList()) {
                setCabinLabelList2(cabinFare, handConfig);
            }
        }
    }

    /**
     * 设置cabinLabelList2标签
     *
     * @param cabinFare
     */
    private static void setCabinLabelList2(CabinFare cabinFare, HandConfig handConfig) {
        // 舱位运价为null 直接返回
        if (null == cabinFare) {
            return;
        }
        if (HandlerConstants.BUS_PLUS.equals(cabinFare.getTourCode())) {
            cabinFare.setCabinLabelList(handConfig.getLabelInfoConfig().getBusPlusLabelInfos());
        }
        // 舱位已存在cabinLabelList2标签直接返回
        if (CollectionUtils.isNotEmpty(cabinFare.getCabinLabelList2())) {
            return;
        }

        // 低碳特惠
        if (CollectionUtils.isNotEmpty(cabinFare.getShippingRulesLabel()) && cabinFare.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
            cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getNoFreeBaggageLabelInfos());
            cabinFare.putActivityLabelMap(ShippingRulesLabelEnum.LCPREFERENCE.name(), handConfig.getNoFreeBaggageFareLabel());
            return;
        }
        // 品牌运价
        if (cabinFare.getFareBasis() != null && cabinFare.getFareBasis().endsWith(FareBasisEnum.BRAND_FARE.getFareBasisCode())) {
            cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getBrandLabelInfos());
            return;
        }
        // 高舱高返
        if (cabinFare.getFareBasis() != null && cabinFare.getFareBasis().endsWith(FareBasisEnum.HIGH_REBATE.getFareBasisCode())) {
            cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getHighRebateLabelInfos());
            return;
        }
        // 公务舱
        if ("J".equals(cabinFare.getCabinClass())) {
            cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getBusinessClassLabelInfoList());
            return;
        }
        // 候补标识
        if (SystemConstants.WAIT.equals(cabinFare.getFlightFareType()) && Boolean.TRUE.equals(cabinFare.getAlternateButton())) {
            cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getWaitLabelInfos());
            AVObjectConvertV2.formatLabelName(cabinFare);
            return;
        }
        // 经济舱
        // 区分国内外,国内根据运价显示托运行李额xxKG
        handleCabinLabelByInterFlag(cabinFare, handConfig);

        baggeageCabinLabelByFlag(cabinFare, cabinFare.getCabinLabelList2());
    }


    static void baggeageCabinLabelByFlag(CabinFare cabinFare, List<LabelInfo> commonDomesticLabel) {
        // 取运价行李公斤数
        String checkKg = cabinFare.getCheckBaggeage();
        String handKg = cabinFare.getHandBaggeage();
        List<LabelInfo> commonDomesticList = new ArrayList<>();
        for (LabelInfo labelInfo : commonDomesticLabel) {
            LabelInfo labelInfoNew = new LabelInfo();
            BeanUtils.copyProperties(labelInfo, labelInfoNew);
            commonDomesticList.add(labelInfoNew);
        }
        // 处理名为"托运行李“的标签
        commonDomesticList.forEach(labelInfo -> {
            if ("CHECK".equals(labelInfo.getLabelFlag())) {
                labelInfo.setLabelName("托运行李" + checkKg);
            } else if ("HAND".equals(labelInfo.getLabelFlag())) {
                labelInfo.setLabelName("非托运行李" + handKg);
            }
        });
        cabinFare.setCabinLabelList2(commonDomesticList);
    }

    /**
     * 根据国内/外和运价 处理舱位标签标签
     * <p>
     * 1、国外运价显示”手提+托运行李“
     * <p>
     * 2、国内根据运价显示”托运行李xxKG"
     *
     * @param cabinFare
     * @param handConfig
     */
    private static void handleCabinLabelByInterFlag(CabinFare cabinFare, HandConfig handConfig) {

        if ("I".equals(cabinFare.getInterFlag())) {
            cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getCommonInternationalLabelInfos());
        } else {
            List<LabelInfo> commonDomesticLabel = new ArrayList<>();
            for (LabelInfo labelInfo : handConfig.getLabelInfoConfig().getCommonDomesticLabelInfos()) {
                LabelInfo labelInfoNew = new LabelInfo();
                BeanUtils.copyProperties(labelInfo, labelInfoNew);
                commonDomesticLabel.add(labelInfoNew);
            }
//            // 取运价行李公斤数
//            String kg = cabinFare.getCheckBaggeage();
//            // 处理名为"托运行李“的标签
//            commonDomesticLabel.forEach(labelInfo -> {
//                if (labelInfo.getLabelName().equals("托运行李")) {
//                    labelInfo.setLabelName("托运行李" + kg);
//                }
//            });
            cabinFare.setCabinLabelList2(commonDomesticLabel);
        }
    }

    public static void setFlightAdvertisement(List<FlightInfoComb> flightInfoCombList, List<FlightAdvertisementDto> advertisementDtos, String routeType) {
        if (CollectionUtils.isNotEmpty(flightInfoCombList) && CollectionUtils.isNotEmpty(advertisementDtos)) {
            advertisementDtos.forEach(advertisement -> {
                List<FlightInfoComb> filteredFlightInfo = flightInfoCombList.stream().filter(flightInfoComb -> {
                    boolean success = false;
                    if (CollectionUtils.isNotEmpty(flightInfoComb.getCombFlightInfoList())
                            && (flightInfoComb.getCombFlightInfoList().size() == 1
                            || (HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && (flightInfoComb.getCombFlightInfoList().size() == 2)))) {
                        for (FlightInfo flightInfo : flightInfoComb.getCombFlightInfoList()) {
                            if (advertisement.getAirlineList().contains(flightInfo.getDepAirport() + "-" + flightInfo.getArrAirport())) {
                                success = true;
                            }
                        }
                    }
                    return success;
                }).collect(Collectors.toList());
                filteredFlightInfo.forEach(flightInfoComb ->
                        flightInfoComb.getAdtCabinFareList().stream().filter(cabinFare -> {
                                    boolean result = false;
                                    String[] cabinCodes = cabinFare.getCabinComb().replace("-", "/").split("/");
                                    for (String cabinCode : cabinCodes) {
                                        if (advertisement.getCabinList().contains(cabinCode)) {
                                            result = true;
                                        }
                                        if ((advertisement.getCabinList().contains("K3+") || advertisement.getCabinList().contains("K3-")) && "K".equals(cabinCode)) {
                                            result = true;
                                        }
                                    }
                                    return result;
                                }
                        ).forEach(cabinFare -> {
                            if (cabinFare.getAdvertisements() == null) {
                                cabinFare.setAdvertisements(Lists.newArrayList());
                            }
                            cabinFare.getAdvertisements().add(advertisement.getPictureDto());
                        }));
            });
        }
        flightInfoCombList.forEach(flightInfoComb -> flightInfoComb.getAdtCabinFareList().forEach(cabinFare -> {
            // 若舱位广告大于1，则随机展示一个广告
            if (CollectionUtils.isNotEmpty(cabinFare.getAdvertisements()) && cabinFare.getAdvertisements().size() > 1) {
                PictureDto pictureDto = cabinFare.getAdvertisements().get(new Random().nextInt(cabinFare.getAdvertisements().size()));
                cabinFare.setAdvertisements(Lists.newArrayList(pictureDto));
            }
        }));

    }

    //联程主航班信息处理
    private static List<FlightInfoComb> toFlightInfoCombList(PtQueryFlightFareResponse res, HandConfig handConfig, LocalCacheService localCacheService) {
        List<FlightInfoComb> flightInfoCombList = new ArrayList<>();
        //航班主信息处理 暂时只取addon 和 simple运价
        String[] array = {FareTypeEnum.ADDON.getFare(), FareTypeEnum.SIMPLE.getFare(), FareTypeEnum.SPA.getFare(), FareTypeEnum.ONEWAY.getFare()};
        List<String> fareList = new ArrayList<>(Arrays.asList(array));
        List<FlightInfoCombApi> flightInfoCombApiList = res.getFlightInfoCombList().stream()
                //过滤支持的运价
                .filter(flightInfoCombApi -> fareList.contains(flightInfoCombApi.getFareType()))
                //2023-06-23 上海名古屋暂时去除组合运价
                .filter(flightInfoCombApi -> {
                    if (StringUtils.isBlank(handConfig.getIgnoreAirline())) {
                        return true;
                    } else {
                        String[] ignoreAirlines = handConfig.getIgnoreAirline().split(",");
                        List<String> ignoreAirlineList = Arrays.asList(ignoreAirlines);
                        return !ignoreAirlineList.contains(flightInfoCombApi.getFlightRouteComb());
                    }
                })
                .collect(Collectors.toList());
        flightInfoCombApiList.forEach(flightInfoCombApi -> {
            FlightInfoComb flightInfoComb = dealFlightInfoCombo(flightInfoCombApi, localCacheService, handConfig,
                    res.getInterFlag(), res.getFareDic(), res.getRouteType(), res.getFareSource());
            flightInfoCombList.add(flightInfoComb);
        });
        //同一航班组期的情合存在多个航班日况，暂时剔除多余的航班
        removeSameFlightNoComb(flightInfoCombList);
        return flightInfoCombList;
    }

    /**
     * 删除同一航班号组合的高价
     * 2023.2.2之前原始逻辑取低价原则
     * 2023.2.2调整为中转时间较短优先
     *
     * @param flightInfoCombList
     */
    private static void removeSameFlightNoComb(List<FlightInfoComb> flightInfoCombList) {
        Map<String, List<FlightInfoComb>> flightInfoMap = flightInfoCombList.stream().collect(Collectors.groupingBy(FlightInfoComb::getFlightNoComb));
        if (flightInfoMap != null && !flightInfoMap.isEmpty()) {
            //重置集合对象
            flightInfoCombList.clear();
            for (Map.Entry<String, List<FlightInfoComb>> entry : flightInfoMap.entrySet()) {
                List<FlightInfoComb> childFlightInfoCombList = entry.getValue();
                FlightInfoComb minFlightInfoComb;
                if (childFlightInfoCombList.size() == 1) {
                    minFlightInfoComb = childFlightInfoCombList.get(0);
                } else {
                    List<FlightInfoComb> childFlightInfoCombListOfHasCabin = childFlightInfoCombList.stream().filter(flightInfoComb -> CollectionUtils.isNotEmpty(flightInfoComb.getAdtCabinFareList())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(childFlightInfoCombListOfHasCabin)) {
                        //minFlightInfoComb = childFlightInfoCombListOfHasCabin.stream().min(Comparator.comparing(flightInfoComb -> flightInfoComb.getAdtCabinFareList().stream().min(Comparator.comparing(CabinFare::getPriceValue)).get().getPriceValue())).orElse(null);
                        //计算中转航班的中转时间
                        minFlightInfoComb = childFlightInfoCombListOfHasCabin.stream().min(Comparator.comparing(flightInfoComb -> {
                            List<FlightInfo> GoFlightInfoCombList = flightInfoComb.getCombFlightInfoList().stream().filter(flightInfo -> FlightDirection.GO.getCode().equals(flightInfo.getFlightDirection())).collect(Collectors.toList());
                            List<TransferInfo> goTransferInfoList = setTransferInfoList(GoFlightInfoCombList);
                            List<FlightInfo> BackFlightInfoCombList = flightInfoComb.getCombFlightInfoList().stream().filter(flightInfo -> FlightDirection.BACK.getCode().equals(flightInfo.getFlightDirection())).collect(Collectors.toList());
                            List<TransferInfo> backTransferInfoList = setTransferInfoList(BackFlightInfoCombList);
                            long goTransTime = 0L;
                            long backTransTime = 0L;
                            if (CollectionUtils.isNotEmpty(goTransferInfoList)) {
                                goTransTime = goTransferInfoList.stream().mapToLong(TransferInfo::getTransferTime).sum();
                            }
                            if (CollectionUtils.isNotEmpty(backTransferInfoList)) {
                                backTransTime = backTransferInfoList.stream().mapToLong(TransferInfo::getTransferTime).sum();
                            }
                            return goTransTime + backTransTime;
                        })).orElse(null);
                    } else {
                        minFlightInfoComb = childFlightInfoCombList.get(0);
                    }
                }
                if (minFlightInfoComb != null) {
                    flightInfoCombList.add(minFlightInfoComb);
                }
            }
        }
    }


    public static FlightInfoComb dealFlightInfoCombo(FlightInfoCombApi flightInfoCombApi, LocalCacheService localCacheService,
                                                     HandConfig handConfig, String interFlag, Map<String, Fare> fareMap, String routeType, String fareSource) {
        FlightInfoComb flightInfoComb = new FlightInfoComb();
        flightInfoComb.setFlightRouteComb(flightInfoCombApi.getFlightRouteComb());
        flightInfoComb.setFlightNoComb(flightInfoCombApi.getFlightNoComb());
        flightInfoComb.setFareType(flightInfoCombApi.getFareType());
        flightInfoComb.setBaggageThroughFlag(flightInfoCombApi.getBaggageThroughFlag());
        flightInfoComb.setThroughCheckFlag(flightInfoCombApi.getThroughCheckFlag());
        flightInfoComb.setInterFlag(interFlag);
        flightInfoComb.setFareSource(fareSource);
        // Addon OneWay 需展示中转预订须知 SPA不展示
        if (FareTypeEnum.ADDON.getFare().equals(flightInfoComb.getFareType())
                || FareTypeEnum.ONEWAY.getFare().equals(flightInfoComb.getFareType())) {

            //是否为深港通航班
            boolean bUSFlight = flightInfoCombApi.getFlightInfoList().stream().anyMatch(v2FlightInfo -> FareBasisEnum.BUS_FARE.getFareBasisCode().equals(v2FlightInfo.getFType()));
            // 业务测现以国内行李直挂判断通程
            boolean baggageThroughFlag = Optional.ofNullable(flightInfoComb.getBaggageThroughFlag())
                    .map(flag -> flag.contains("1"))
                    .orElse(false);

            LabelDetail transferTips = new LabelDetail();
            if (bUSFlight) {
                transferTips = handConfig.getBusTransferTips();
            } else {
                if (HandlerConstants.TRIP_TYPE_D.equals(interFlag)) {
                    // 国内行李直挂展示通程
                    if (baggageThroughFlag) {
                        transferTips = handConfig.getThroughCheckTransferTips();
                    } else {
                        transferTips = handConfig.getTransferTips();
                    }
                } else {
                    if (baggageThroughFlag) {
                        transferTips = handConfig.getBaggageThroughTransferTips();
                    } else {
                        transferTips = handConfig.getTransferITips();
                    }
                }
            }
            flightInfoComb.setTransferTips(transferTips);

        }
        //联程航班明细处理
        List<FlightInfo> combFlightInfoList = toCombFlightInfoList(flightInfoCombApi, fareMap, localCacheService, handConfig);
        flightInfoComb.setCombFlightInfoList(combFlightInfoList);
        flightInfoComb.setFlightKey(createKey(combFlightInfoList));
        //组合航班舱位价格处理处理
        flightInfoComb.setAdtCabinFareList(toCabinFareList(HandlerConstants.PASSENGER_TYPE_ADT, fareMap,
                routeType, flightInfoCombApi, localCacheService, handConfig, interFlag));
        flightInfoComb.setChdCabinFareList(toCabinFareList(HandlerConstants.PASSENGER_TYPE_CHD, fareMap,
                routeType, flightInfoCombApi, localCacheService, handConfig, interFlag));
        flightInfoComb.setInfCabinFareList(toCabinFareList(HandlerConstants.PASSENGER_TYPE_INF, fareMap,
                routeType, flightInfoCombApi, localCacheService, handConfig, interFlag));
        //将儿童，婴儿的退改规则统一至成人运价中
        setChildInfRule(flightInfoComb);
        return flightInfoComb;
    }

    private static String createKey(List<FlightInfo> combFlightInfoList) {
        StringBuilder key = new StringBuilder();
        for (FlightInfo flightInfo : combFlightInfoList) {
            if (FlightDirection.GO.getCode().equals(flightInfo.getFlightDirection())) {
                key.append(flightInfo.getFlightNo()).append(flightInfo.getFlightDate());
            } else {
                key.append("/").append(flightInfo.getFlightNo()).append(flightInfo.getFlightDate());
            }
        }
        return key.toString();
    }

    /**
     * 航班主信息设置
     *
     * @param flightInfo     组合航班信息，如果是中转航班显示的是A-C段总结信息
     * @param flightInfoList 按照飞行方向过滤后的航班信息
     * @return
     */
    public static FlightInfo setMainFlightInfo(FlightInfo flightInfo, String fareType, List<FlightInfo> flightInfoList,
                                               LocalCacheService localCacheService,HandConfig handConfig) {
        int size = flightInfoList.size();
        FlightInfo firstFlight = flightInfoList.get(0);
        FlightInfo lastFlight = flightInfoList.get(size - 1);
        flightInfo.setDepDateTime(firstFlight.getDepDateTime());
        flightInfo.setArrDateTime(lastFlight.getArrDateTime());
        if (CollectionUtils.isEmpty(flightInfo.getCabinFareList())) {
            flightInfo.setCabinFareList(firstFlight.getCabinFareList());
        }
        if (FareTypeEnum.SIMPLE.getFare().equals(fareType)) {//直达航线可直接返回航班信息
            BeanUtils.copyNotNullProperties(firstFlight, flightInfo);
            return flightInfo;
        }
        if (flightInfoList.size() == 2 && flightInfoList.get(1).getFType().equals(FareBasisEnum.BUS_FARE.getFareBasisCode())) {
            flightInfo.setThemeFlight(flightInfoList.get(1).getFType());
        } else {
            flightInfo.setThemeFlight(firstFlight.getFType());
        }
        //机场城市处理
        AirPortInfoDto depAirport = localCacheService.getLocalAirport(firstFlight.getDepAirport());
        AirPortInfoDto arrAirport = localCacheService.getLocalAirport(lastFlight.getArrAirport());
        flightInfo.setFlightDate(firstFlight.getFlightDate());
        flightInfo.setDepAirport(firstFlight.getDepAirport());
        flightInfo.setArrAirport(lastFlight.getArrAirport());

        flightInfo.setDepAirportName(depAirport.getAirPortName());
        flightInfo.setArrAirportName(arrAirport.getAirPortName());
        flightInfo.setDepCity(depAirport.getCityCode());
        flightInfo.setArrCity(arrAirport.getCityCode());
        flightInfo.setDepCityName(depAirport.getCityName());
        flightInfo.setArrCityName(arrAirport.getCityName());
        flightInfo.setDeptCountryNo(depAirport.getCountryNo());
        flightInfo.setCountryNo(arrAirport.getCountryNo());
        flightInfo.setDepTerm(firstFlight.getDepTerm());
        flightInfo.setArrTerm(lastFlight.getArrTerm());
        flightInfo.setCarrierNo(StringUtils.join(toCarryNoList(flightInfoList), "-"));
        //飞行时间计算
        //到达时区
        String arrZone = arrAirport.getCityTimeZone();
        //出发时区
        String depZone = depAirport.getCityTimeZone();
        //出发时间(当地时间)
        String depTime = firstFlight.getDepDateTime();
        //到达时间(当地时间)
        String arrTime = lastFlight.getArrDateTime();
        //飞行时长
        if ((!StringUtil.isNullOrEmpty(arrZone)) &&
                (!StringUtil.isNullOrEmpty(depZone)) &&
                (!StringUtil.isNullOrEmpty(depTime)) &&
                (!StringUtil.isNullOrEmpty(arrTime))) {
            //添加夏、冬令时处理
            if (!depZone.equals(arrZone)) {
                depZone = FlightUtil.convertSummerOrWinterTime(depZone, depTime, depAirport);
                arrZone = FlightUtil.convertSummerOrWinterTime(arrZone, arrTime, arrAirport);
            }
            long diff = DateUtils.calDuration(depTime, depZone, arrTime, arrZone);
            flightInfo.setDuration(diff < 0 ? 0 : diff);
        }
        int days = DateUtils.diffDays(depTime.substring(0, 10), arrTime.substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
        flightInfo.setDays(days < 0 ? 0 : days);
        //展示机型
        flightInfo.setAircraftModel(setPlaneType(flightInfoList));
        //餐食处理
        flightInfo.setMealName(setMealName(flightInfoList));
        if (null == flightInfo.getTotalTax()) {
            flightInfo.setTotalTax(0d);
        }
        //中转信息
        List<TransferInfo> transferInfoList = setTransferInfoList(flightInfoList);
        flightInfo.setTransferInfoList(transferInfoList);
        if (CollectionUtils.isNotEmpty(transferInfoList)) { //此处默认存放第一个中转信息\
            if (transferInfoList.size() > 1) {
                TransferInfo transferInfo = new TransferInfo();
                BeanUtils.copyNotNullProperties(transferInfoList.get(0), transferInfo);
                transferInfo.setTransferCityName(transferInfoList.size() + "站");
                flightInfo.setTransferInfo(transferInfo);
            } else {
                TransferInfo transferInfo = transferInfoList.get(0);
                flightInfo.setTransferInfo(transferInfo);
            }
            transferProcess(flightInfo, handConfig, localCacheService);
        }
        return flightInfo;
    }


    /**
     * 提取航班的实际承运航班号
     *
     * @param flightInfoList
     * @return
     */
    private static List<String> toCarryNoList(List<FlightInfo> flightInfoList) {
        List<String> list = new ArrayList<>();
        flightInfoList.stream().forEach(flightInfo -> {
            list.add(flightInfo.getCarrierNo());
        });
        return list;
    }

    /**
     * 设置儿童，婴儿的规则和行李额说明
     * 设置成人行李额说明
     */
    public static void setChildInfRule(FlightInfoComb flightInfoComb) {
        if (StringUtil.isNullOrEmpty(flightInfoComb.getAdtCabinFareList())) {
            return;
        }
        // 子航线儿童、婴儿运价信息
        Map<String, List<CabinFare>> chdCabinFareMap = getSegmentCabinFareMap(flightInfoComb.getChdCabinFareList());
        Map<String, List<CabinFare>> infCabinFareMap = getSegmentCabinFareMap(flightInfoComb.getInfCabinFareList());
        flightInfoComb.getAdtCabinFareList().forEach(adt -> {
            // 处理航班行李信息
            setBaggage(adt, flightInfoComb.getChdCabinFareList(), flightInfoComb.getInfCabinFareList());
            // 子航信不为空 处理子航线行李信息
            if (CollectionUtils.isNotEmpty(adt.getSegmentCabinInfos())) {
                adt.getSegmentCabinInfos().forEach(segmentCabinInfo -> {
                    String keySegment = segmentCabinInfo.getDepCity() + "_" + segmentCabinInfo.getArrCity();
                    setBaggage(segmentCabinInfo, chdCabinFareMap.get(keySegment), infCabinFareMap.get(keySegment));
                });
            }
        });

    }

    /**
     * 获取指定航段的全部子行程舱位信息
     *
     * @param cabinFareList
     * @return
     */
    private static Map<String, List<CabinFare>> getSegmentCabinFareMap(List<CabinFare> cabinFareList) {
        Map<String, List<CabinFare>> cabinFareMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(cabinFareList)) {
            return cabinFareMap;
        }
        cabinFareList.forEach(cabinFare -> {
            cabinFare.getSegmentCabinInfos().forEach(segmentCabinInfo -> {
                if (StringUtils.isNoneBlank(segmentCabinInfo.getDepCity(), segmentCabinInfo.getArrCity())) {
                    String keySegment = segmentCabinInfo.getDepCity() + "_" + segmentCabinInfo.getArrCity();
                    List<CabinFare> segmentCabinInfoList = cabinFareMap.get(keySegment);
                    if (null == segmentCabinInfoList) {
                        segmentCabinInfoList = Lists.newArrayList();
                    }
                    segmentCabinInfoList.add(segmentCabinInfo);
                    cabinFareMap.put(keySegment, segmentCabinInfoList);
                }
            });
        });
        return cabinFareMap;
    }

    /**
     * 设置行李信息
     *
     * @param cabinFare        仓位信息
     * @param chdCabinFareList 同航段儿童舱位信息
     * @param infCabinFareList 同行段婴儿舱位信息
     */
    public static void setBaggage(CabinFare cabinFare, List<CabinFare> chdCabinFareList, List<CabinFare> infCabinFareList) {
        // 设置成人行李额规则
        cabinFare.setAdtBaggage(AVObjectConvertV2.genBaggageRuleFromCabinFare(cabinFare, PassengerTypeEnum.ADT));
        // 优先找Code一样的，不存在时找class一样的
        CabinFare chdCabinFare = CollectionUtils.isEmpty(chdCabinFareList) ? null : chdCabinFareList.stream().filter(chd -> cabinFare.getCabinComb().equals(chd.getCabinComb())).findFirst().orElse(chdCabinFareList.stream().filter(chd -> cabinFare.getCabinClass().equals(chd.getCabinComb())).findFirst().orElse(null));
        CabinFare infCabinFare = null;
        if (CollectionUtils.isNotEmpty(infCabinFareList)) {
            infCabinFare = infCabinFareList.stream().filter(inf -> cabinFare.getCabinComb().equals(inf.getCabinComb())).findFirst()
                    .orElse(infCabinFareList.stream().filter(inf -> cabinFare.getCabinClass().equals(inf.getCabinComb())).findFirst()
                            .orElse(infCabinFareList.stream().filter(inf -> cabinFare.getCabinClass().equals(inf.getCabinCode())).findFirst()
                                    .orElse(infCabinFareList.stream().filter(inf -> cabinFare.getCabinClass().equals(inf.getCabinClass())).findFirst()
                                            .orElse(null))));
        }
        if (chdCabinFare != null) {
            ChildRule childRule = new ChildRule();
            childRule.setRefundRuleList(chdCabinFare.getRefundRuleList());
            childRule.setChangeRuleList(chdCabinFare.getChangeRuleList());
            cabinFare.setChildRule(childRule);
            cabinFare.setChdBaggage(AVObjectConvertV2.genBaggageRuleFromCabinFare(chdCabinFare, PassengerTypeEnum.CHD));
        } else {
            // 未获取到儿童行李信息使用成人行李额信息
            cabinFare.setChdBaggage(AVObjectConvertV2.getChdBaggageFromAtd(cabinFare.getAdtBaggage()));
        }
        if (infCabinFare != null) {
            InfRule infRule = new InfRule();
            infRule.setRefundRuleList(infCabinFare.getRefundRuleList());
            infRule.setChangeRuleList(infCabinFare.getChangeRuleList());
            cabinFare.setInfRule(infRule);
            cabinFare.setInfFreeBaggage(AVObjectConvertV2.genBaggageRuleFromCabinFare(infCabinFare, PassengerTypeEnum.ADT));
        }
    }

    /**
     * 去程航班低价处理
     *
     * @param flightInfoCombList 已按照航班进行分组
     * @param routeType
     * @return
     */
    public static CabinFare setGFlightMinPrice(List<FlightInfoComb> flightInfoCombList, String routeType) {
        //先整合所有的去程运价信息
        List<CabinFare> cabinFareList = new ArrayList<>();
        flightInfoCombList.forEach(flightInfoComb -> {
            flightInfoComb.getAdtCabinFareList().forEach(cabinFare -> {
                CabinFare c = new CabinFare();
                BeanUtils.copyProperties(cabinFare, c);
                c.setPriceValue(cabinFare.getPriceValue());
                cabinFareList.add(c);
            });
        });
        if (!StringUtil.isNullOrEmpty(cabinFareList)) {
            CabinFare cabinFare = cabinFareList.stream().min(
                    Comparator.comparingDouble(CabinFare::getPriceValue)).orElse(null);
            return cabinFare;
        }
        return null;
    }


    /**
     * @param flightInfoComb
     * @return
     */
    public static CabinFare filterMinPrice(FlightInfoComb flightInfoComb) {
        //先整合所有的去程运价信息
        List<CabinFare> cabinFareList = new ArrayList<>();
        flightInfoComb.getAdtCabinFareList().forEach(cabinFare -> {
            CabinFare c = new CabinFare();
            BeanUtils.copyProperties(cabinFare, c);
            c.setPriceValue(cabinFare.getPriceValue());
            cabinFareList.add(c);
        });
        if (!StringUtil.isNullOrEmpty(cabinFareList)) {
            CabinFare cabinFare = cabinFareList.stream().min(Comparator.comparingDouble(CabinFare::getPriceValue)).orElse(null);
            return cabinFare;
        }
        return null;
    }

    /**
     * 返程航班低价处理
     *
     * @param adtCabinFareList
     * @return
     */
    private static CabinFare getTotalWayBackFlightMinPrice(List<CabinFare> adtCabinFareList) {
        return adtCabinFareList.stream().min(Comparator.comparingDouble(CabinFare::getPriceValue)).orElse(null);
    }

    /**
     * @param flightNo 航班号  联程航班是以"-"分割
     * @param airComp
     * @return
     */
    public static List<FlightNoIcon> setFlightNoIconList(String flightNo, String airComp) {
        List<FlightNoIcon> flightNoIconList = new ArrayList<>();
        Map<String, AirCompany> airCompanyMap = FlightUtil.toAirCompanyMap(airComp);
        String[] flightNos = flightNo.split("-");
        for (int i = 0; i < flightNos.length; i++) {
            AirCompany airCompany = FlightUtil.convertCompany(airCompanyMap, flightNos[i]);
            FlightNoIcon flightNoIcon = new FlightNoIcon(flightNos[i]);
            if (airCompany != null) {
                flightNoIcon.setAirIcon(airCompany.getAirIcon());
                flightNoIcon.setAirComName(airCompany.getAirComName());
                flightNoIcon.setAirComText(airCompany.getAirComText());
            }
            flightNoIconList.add(flightNoIcon);
        }
        return flightNoIconList;
    }

    /**
     * 中转信息列表
     *
     * @param flightInfoList
     * @return
     */
    private static List<TransferInfo> setTransferInfoList(List<FlightInfo> flightInfoList) {
        if (flightInfoList.size() > 1) {
            List<TransferInfo> transferInfoList = new ArrayList<>();
            for (int i = 1; i < flightInfoList.size(); i++) {
                TransferInfo transferInfo = new TransferInfo("中转");
                FlightInfo preFlightInfo = flightInfoList.get(i - 1);
                FlightInfo curFlightInfo = flightInfoList.get(i);
                transferInfo.setTransferCityName(preFlightInfo.getArrCityName());
                transferInfo.setTransferAirPort(preFlightInfo.getArrAirport());
                transferInfo.setTransferAirPortFrom(curFlightInfo.getDepAirport());
                //中转一般都为同一时区中转，可不考虑时区问题
                Date arrTime = DateUtils.toDate(preFlightInfo.getArrDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                Date depTime = DateUtils.toDate(curFlightInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                if (depTime != null && arrTime != null) {
                    transferInfo.setTransferTime(depTime.getTime() - arrTime.getTime());
                    transferInfo.setCrossDays(DateUtils.durDays(arrTime, depTime));
                }
                if (!preFlightInfo.getArrAirport().equals(curFlightInfo.getDepAirport())) {
                    transferInfo.setTransferDesc("不同机场中转");
                    transferInfo.setSameAirport(false);
                } else {
                    transferInfo.setTransferDesc("相同机场中转");
                    transferInfo.setSameAirport(true);
                }
                FlightSimpleInfo preFlightSimpleInfo = intiFlightSimpleInfo(preFlightInfo);
                FlightSimpleInfo nextFlightSimpleInfo = intiFlightSimpleInfo(curFlightInfo);
                transferInfo.setPreFlightInfo(preFlightSimpleInfo);
                transferInfo.setNextFlightInfo(nextFlightSimpleInfo);
                preFlightInfo.setTransferInfo(transferInfo);
                transferInfoList.add(transferInfo);
            }
            return transferInfoList;
        }
        return new ArrayList<>();
    }

    private static FlightSimpleInfo intiFlightSimpleInfo(FlightInfo flightInfo) {
        FlightSimpleInfo flightSimpleInfo = new FlightSimpleInfo();
        flightSimpleInfo.setFlightNo(flightInfo.getFlightNo());
        flightSimpleInfo.setFlightDate(flightInfo.getFlightDate());
        flightSimpleInfo.setDepDateTime(flightInfo.getDepDateTime());
        flightSimpleInfo.setArrDateTime(flightInfo.getArrDateTime());
        flightSimpleInfo.setCarryNo(flightInfo.getCarrierNo());
        flightSimpleInfo.setDays(flightInfo.getDays());
        return flightSimpleInfo;
    }


    //联程航班列表处理
    public static List<FlightInfo> toCombFlightInfoList(FlightInfoCombApi flightInfoCombApi, Map<String, Fare> fareMap, LocalCacheService localCacheService, HandConfig handConfig) {
        List<FlightInfo> combFlightInfoList = new ArrayList<>();
        flightInfoCombApi.getFlightInfoList().stream().forEach(v2FlightInfo -> {
            //航班信息处理
            FlightInfo combFlightInfo = toCombFlightInfo(v2FlightInfo, fareMap, localCacheService, handConfig);
            combFlightInfoList.add(combFlightInfo);
        });
        return combFlightInfoList;
    }

    //航班详细信息处理
    private static FlightInfo toCombFlightInfo(V2FlightInfo v2FlightInfo, Map<String, Fare> fareMap, LocalCacheService localCacheService, HandConfig handConfig) {
        FlightInfo flightInfo = new FlightInfo();
        BeanUtils.copyProperties(v2FlightInfo, flightInfo);
        //机场城市处理
        AirPortInfoDto depAirport = localCacheService.getLocalAirport(v2FlightInfo.getDepAirport());
        AirPortInfoDto arrAirport = localCacheService.getLocalAirport(v2FlightInfo.getArrAirport());
        flightInfo.setDepAirportName(depAirport == null ? v2FlightInfo.getDepAirport() : depAirport.getAirPortName());
        flightInfo.setArrAirportName(arrAirport == null ? v2FlightInfo.getArrAirport() : arrAirport.getAirPortName());
        flightInfo.setDepCity(depAirport == null ? v2FlightInfo.getDepCity() : depAirport.getCityCode());
        flightInfo.setArrCity(arrAirport == null ? v2FlightInfo.getArrCity() : arrAirport.getCityCode());
        flightInfo.setDepCityName(depAirport == null ? v2FlightInfo.getDepCity() : depAirport.getCityName());
        flightInfo.setArrCityName(arrAirport == null ? v2FlightInfo.getArrCity() : arrAirport.getCityName());
        flightInfo.setDeptCountryNo(depAirport == null ? "" : depAirport.getCountryNo());
        flightInfo.setCountryNo(arrAirport == null ? "" : arrAirport.getCountryNo());
        //航司机型处理
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, v2FlightInfo.getFType());
        flightInfo.setAircraftModel("机型" + v2FlightInfo.getFType());
        if (aircraftModel != null) {
            flightInfo.setAircraftModel(aircraftModel.getRemark());
        }
        if (FareBasisEnum.BUS_FARE.getFareBasisCode().equals(flightInfo.getFType())) {
            if ("HKG".equals(flightInfo.getArrCity())) {
                flightInfo.setArrCityName("中国香港全境");
            }
            if ("HKG".equals(flightInfo.getDepCity())) {
                flightInfo.setDepCityName("中国香港全境");
            }
        }

        //承运航班处理
        flightInfo.setCarrierNoName("");
        Map<String, AirCompany> airCompanyMap = FlightUtil.toAirCompanyMap(handConfig.getAirCompany());
        AirCompany airCompany = FlightUtil.convertCompany(airCompanyMap, v2FlightInfo.getCarrierNo());
        if (airCompany != null) {
            flightInfo.setCarrierNoName(airCompany.getAirComName() + flightInfo.getCarrierNo());
        }
        //机上WIFI
        flightInfo.setWifiFlag("I".equals(v2FlightInfo.getNwst()) || "W".equals(v2FlightInfo.getNwst()) || "V".equals(v2FlightInfo.getNwst()));
        if (flightInfo.isWifiFlag()) {
            flightInfo.setWifiName("机上WIFI");
        }
        //出发时间(当地时间)
        String depTime = v2FlightInfo.getDepDateTime();
        //到达时间(当地时间)
        String arrTime = v2FlightInfo.getArrDateTime();
        if (arrAirport != null && depAirport != null) {
            //飞行时间计算
            //到达时区
            String arrZone = arrAirport.getCityTimeZone();
            //出发时区
            String depZone = depAirport.getCityTimeZone();
            if ((!StringUtil.isNullOrEmpty(arrZone)) &&
                    (!StringUtil.isNullOrEmpty(depZone)) &&
                    (!StringUtil.isNullOrEmpty(depTime)) &&
                    (!StringUtil.isNullOrEmpty(arrTime))) {
                //添加夏、冬令时处理
                if (!depZone.equals(arrZone)) {
                    depZone = FlightUtil.convertSummerOrWinterTime(depZone, v2FlightInfo.getDepDateTime(), depAirport);
                    arrZone = FlightUtil.convertSummerOrWinterTime(arrZone, v2FlightInfo.getArrDateTime(), arrAirport);
                }
                long diff = DateUtils.calDuration(depTime, depZone, arrTime, arrZone);
                flightInfo.setDuration(diff < 0 ? 0 : diff);
            }
        }
        //航班是否跨天
        int days = DateUtils.diffDays(depTime.substring(0, 10), arrTime.substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
        flightInfo.setDays(days < 0 ? 0 : days);
        //经停城市处理
        if (!StringUtil.isNullOrEmpty(v2FlightInfo.getStopAirport())) {
            AirPortInfoDto stopAirPort = localCacheService.getLocalAirport(v2FlightInfo.getStopAirport());
            flightInfo.setStopAirportName(stopAirPort == null ? v2FlightInfo.getStopAirport() : stopAirPort.getCityName());
        }
        //航班图标
        flightInfo.setFlightNoIconList(setFlightNoIconList(v2FlightInfo.getFlightNo(), handConfig.getAirCompany()));
        flightInfo.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), flightInfo.getCarrierNo()));
        //简单舱位处理  成人 儿童 婴儿
        List<CabinFare> adtCabinFareList = toSimpleCabinFareList(v2FlightInfo.getCabinFareList(), fareMap, handConfig, v2FlightInfo.getCodeShare(), v2FlightInfo.getCarrierNo());
        List<CabinFare> chdInfCabinFareList = toSimpleCabinFareList(v2FlightInfo.getCabinCHDINFFareList(), fareMap, handConfig, v2FlightInfo.getCodeShare(), v2FlightInfo.getCarrierNo());
        flightInfo.setCabinFareList(adtCabinFareList);
        CabinFare[] chdinfFare = new CabinFare[chdInfCabinFareList.size()];
        flightInfo.setCabinCHDINFFareList(chdInfCabinFareList.toArray(chdinfFare));
        return flightInfo;
    }

    /**
     * 简单航班舱位处理
     *
     * @return
     */
    private static List<CabinFare> toSimpleCabinFareList(List<V2CabinFare> ptV2CabinFareList, Map<String, Fare> fareMap, HandConfig handConfig, Boolean codeShare, String carrierNo) {
        List<CabinFare> cabinFareList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(ptV2CabinFareList)) {
            return cabinFareList;
        } else {
            ptV2CabinFareList.stream().forEach(v2CabinFare -> {
                CabinFare cabinFare = new CabinFare();
                BeanUtils.copyProperties(v2CabinFare, cabinFare);
                if ((codeShare != null && !codeShare) || (carrierNo != null && carrierNo.startsWith(AirCompanyEnum.HO.getAirCompanyCode()))) {
                    cabinFare.setScoreGive(v2CabinFare.getReturnScore() == null ? 0 : v2CabinFare.getReturnScore().intValue());
                    //2021-07-09  品牌运价积分返回配置
                    if (StringUtils.isNotBlank(cabinFare.getBrandCode()) && null != cabinFare.getScaleScoreList() && !cabinFare.getScaleScoreList().isEmpty()) {
                        cabinFare.setScoreGive(cabinFare.getScaleScoreList().get(cabinFare.getBrandCode()) == null ? 0 : cabinFare.getScaleScoreList().get(cabinFare.getBrandCode()).intValue());
                    }
                }
                Fare fare = fareMap.get(v2CabinFare.getFareKey());
                if (null != fare) {
                    cabinFare.setShippingRulesLabel(fare.getShippingRulesLabel());
                }
                // 低碳特惠
                if (CollectionUtils.isNotEmpty(cabinFare.getShippingRulesLabel()) && cabinFare.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
                    cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getNoFreeBaggageLabelInfos());
                    cabinFare.putActivityLabelMap(ShippingRulesLabelEnum.LCPREFERENCE.name(), handConfig.getNoFreeBaggageFareLabel());
                }
                cabinFareList.add(cabinFare);
            });
        }
        return cabinFareList;
    }

    /**
     * 航班舱位运价信息处理
     *
     * @param passType          乘客类型
     * @param flightInfoCombApi 明细航班
     * @return
     */
    private static List<CabinFare> toCabinFareList(String passType, Map<String, Fare> fareMap, String routeType, FlightInfoCombApi flightInfoCombApi,
                                                   LocalCacheService localCacheService, HandConfig handConfig, String interFlag) {
        List<V2CabinFareApi> cabinFareCombList = flightInfoCombApi.getCabinFareCombList();
        if (StringUtil.isNullOrEmpty(cabinFareCombList)) {
            return new ArrayList<>();
        }
        boolean isROUTE_TYPE_RT = HandlerConstants.ROUTE_TYPE_RT.equals(routeType);//是否是往返
        //乘客类型过滤
        List<V2FlightInfo> combFlightInfoList = flightInfoCombApi.getFlightInfoList();
        List<V2CabinFareApi> filterCabinFareCombList = cabinFareCombList.stream()
                .filter(c -> c.getPassengerType().equals(passType))
                .collect(Collectors.toList());
        List<CabinFare> cabinFareList = new LinkedList<>();
        for (V2CabinFareApi v2CabinFare : filterCabinFareCombList) {
            if (HandlerConstants.TRIP_TYPE_I.equals(interFlag)) {
                setCabinNumAndClass(v2CabinFare, flightInfoCombApi, passType);
            }
            //过滤无可售座位的舱位
            String cabinNumber = v2CabinFare.getCabinNumber();
            if (StringUtils.isNotBlank(cabinNumber) && (cabinNumber.contains("0") || cabinNumber.contains("S"))) {
                continue;
            }
            //运价处理
            Fare fare = fareMap.get(v2CabinFare.getFareKey());
            if (fare == null) {
                continue;
            }
            //奖励飞免票退改规则，修改为不能退改
            String[] awardFlyFreeTicketCabinList = handConfig.getAwardFlyFreeTicketCabin().split(",");
            if (fare.getFares() != null) {
                for (String cabin : awardFlyFreeTicketCabinList) {
                    if (v2CabinFare.getCabinComb().contains(cabin)) {
                        for (Fare fareFare : fare.getFares()) {
                            fareFare.setRefundedRules(null);
                        }
                    }
                }
            }
            CabinFare cabinFare = toCabinFare(v2CabinFare, fare, interFlag, flightInfoCombApi.getFlightNoComb(), isROUTE_TYPE_RT);
            //候补按钮

            cabinFare.setSegmentCabinInfos(genChildrenSegmentCabinInfo(fare, v2CabinFare, combFlightInfoList, interFlag, localCacheService));
            // 成人运价要拼对应儿童和婴儿的退改规则
            if (PassengerTypeEnum.ADT.getPassType().equals(passType)) {
                /**
                 * 儿童运价获取逻辑
                 * 获取与成人同等舱位子代码的运价，如成人是W-W,则首先获取W-W的儿童价，如果没有则获取Y-Y的价格
                 */
                Optional<V2CabinFareApi> chdCabinFare = cabinFareCombList.stream()
                        .filter(c -> c.getPassengerType().equals(PassengerTypeEnum.CHD.getPassType()) && v2CabinFare.getCabinComb().equals(c.getCabinComb()))
                        .findFirst();
                if (!chdCabinFare.isPresent()) {
                    chdCabinFare = cabinFareCombList.stream()
                            .filter(c -> c.getPassengerType().equals(PassengerTypeEnum.CHD.getPassType()) && v2CabinFare.getCabinClass().equals(c.getCabinComb()))
                            .findFirst();
                }
                if (chdCabinFare.isPresent() && null != fareMap.get(chdCabinFare.get().getFareKey())) {
                    if (CollectionUtils.isNotEmpty(fareMap.get(chdCabinFare.get().getFareKey()).getFares())) {
                        List<Fare> fares = fareMap.get(chdCabinFare.get().getFareKey()).getFares();
                        for (int i = 0; i < cabinFare.getSegmentCabinInfos().size(); i++) {
                            ChildRule childRule = new ChildRule();
                            if (fares.size() >= i + 1) {
                                childRule.setRefundRuleList(AVObjectConvertV2.toRefundRules(fares.get(i).getRefundedRules(), interFlag));
                                childRule.setChangeRuleList(AVObjectConvertV2.toChangeRules(fares.get(i).getChangeRules(), interFlag));
                            }
                            cabinFare.getSegmentCabinInfos().get(i).setChildRule(childRule);
                        }
                    } else {
                        Fare fareRule = fareMap.get(chdCabinFare.get().getFareKey());
                        ChildRule childRule = new ChildRule();
                        childRule.setRefundRuleList(AVObjectConvertV2.toRefundRules(fareRule.getRefundedRules(), interFlag));
                        childRule.setChangeRuleList(AVObjectConvertV2.toChangeRules(fareRule.getChangeRules(), interFlag));
                        cabinFare.getSegmentCabinInfos().get(0).setChildRule(childRule);
                    }
                }
                /**
                 * 婴儿运价获取逻辑
                 * 获取与成人同等舱位子代码的运价，如成人是W-W,则首先获取W-W的婴儿价，如果没有则获取Y-Y的价格
                 */
                Optional<V2CabinFareApi> infCabinFare = cabinFareCombList.stream()
                        .filter(c -> c.getPassengerType().equals(PassengerTypeEnum.INF.getPassType()) && v2CabinFare.getCabinComb().equals(c.getCabinComb()))
                        .findFirst();
                if (!infCabinFare.isPresent()) {
                    infCabinFare = cabinFareCombList.stream()
                            .filter(c -> c.getPassengerType().equals(PassengerTypeEnum.INF.getPassType()) && v2CabinFare.getCabinClass().equals(c.getCabinComb()))
                            .findFirst();
                }
                if (infCabinFare.isPresent() && null != fareMap.get(infCabinFare.get().getFareKey())) {
                    if (CollectionUtils.isNotEmpty(fareMap.get(infCabinFare.get().getFareKey()).getFares())) {
                        List<Fare> fares = fareMap.get(infCabinFare.get().getFareKey()).getFares();
                        for (int i = 0; i < cabinFare.getSegmentCabinInfos().size(); i++) {
                            InfRule infRule = new InfRule();
                            if (fares.size() >= i + 1) {
                                infRule.setRefundRuleList(AVObjectConvertV2.toRefundRules(fares.get(i).getRefundedRules(), interFlag));
                                infRule.setChangeRuleList(AVObjectConvertV2.toChangeRules(fares.get(i).getChangeRules(), interFlag));
                            }
                            cabinFare.getSegmentCabinInfos().get(i).setInfRule(infRule);
                        }
                    } else {
                        Fare fareRule = fareMap.get(infCabinFare.get().getFareKey());
                        InfRule infRule = new InfRule();
                        infRule.setRefundRuleList(AVObjectConvertV2.toRefundRules(fareRule.getRefundedRules(), interFlag));
                        infRule.setChangeRuleList(AVObjectConvertV2.toChangeRules(fareRule.getChangeRules(), interFlag));
                        cabinFare.getSegmentCabinInfos().get(0).setInfRule(infRule);
                    }
                }
            }
            //可赠送积分处理
            cabinFare.setScoreGive(setScoreGive(v2CabinFare.getCabinComb(), combFlightInfoList, v2CabinFare.getBrandCode()));
            //行李重量上线处理  国际处理
            int weightLimit = setWeightLimitFunc(flightInfoCombApi.getFareType(), v2CabinFare.getCabinComb(), combFlightInfoList, localCacheService, handConfig);
            cabinFare.setWeightLimit(weightLimit == 0 ? "" : "每件" + weightLimit + "KG");
            //品牌运价处理
            if (PassengerTypeEnum.ADT.getPassType().endsWith(passType)) {
                String route;
                if (isROUTE_TYPE_RT) {
                    route = flightInfoCombApi.getFlightRouteComb().substring(0, flightInfoCombApi.getFlightRouteComb().length() / 2);
                } else {
                    route = flightInfoCombApi.getFlightRouteComb();
                }
                brandFare(cabinFare, flightInfoCombApi.getFareType(), route, handConfig, localCacheService);
            }
            //2021-08-05 国内直达 searchOne 会员专享     2021-10-26 wifi专享
            if (StringUtils.isNotBlank(v2CabinFare.getPriceProductSign())
                    && !v2CabinFare.getPriceProductSign().endsWith(FareBasisEnum.MEMBER_FARE.getFareBasisCode())
                    && !v2CabinFare.getPriceProductSign().endsWith(FareBasisEnum.MEMBER_FARE_YJ.getFareBasisCode())
                    && (!v2CabinFare.getPriceProductSign().endsWith(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode()))) {
                continue;
            }
            cabinFare.setPriceProductSign(v2CabinFare.getPriceProductSign());
            // 2021-10-19 是否会员秒杀舱位
            boolean memberSecKill = AVObjectConvert.memberSecKillCabinFare(cabinFare.getCabinComb(), cabinFare.getTourCode(), handConfig);
            if (StringUtils.isNotBlank(v2CabinFare.getPriceProductSign())
                    && (v2CabinFare.getPriceProductSign().endsWith(FareBasisEnum.MEMBER_FARE.getFareBasisCode())
                    || v2CabinFare.getPriceProductSign().endsWith(FareBasisEnum.MEMBER_FARE_YJ.getFareBasisCode()))) {
                if (!"J".equalsIgnoreCase(cabinFare.getCabinClass().substring(0, 1))) {
                    if (cabinFare.getPriceValue() < cabinFare.getYPrice()) {
                        cabinFare.setCabinType(PackageTypeEnum.MEMBER_FARE.getPackType());
                        cabinFare.setActivityLabel(handConfig.getMemberFareLabel());
                    } else {
                        continue;
                    }
                } else {
                    cabinFare.setCabinType(PackageTypeEnum.MEMBER_FARE.getPackType());
                    cabinFare.setActivityLabel(handConfig.getMemberFareLabel());
                }
            }
            if (StringUtils.isNotBlank(v2CabinFare.getPriceProductSign())
                    && v2CabinFare.getPriceProductSign().endsWith(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode())) {
                //获取机票赠送的券的信息
                cabinFare.setCabinType(PackageTypeEnum.WIFI_FARE.getPackType());

                //设置wifi运价图标和详情
                LabelAndRuleInfo labelAndRuleInfo = handConfig.getWifiLabelAndRuleInfo();
                cabinFare.setActivityLabel(labelAndRuleInfo);
                //设置订单页详情页wifi信息展示
                TravelPrivilege travelPrivilege = handConfig.getWifiTravelPrivilege();
                List<TravelPrivilege> wifiTravelPrivilege = new ArrayList<>();
                wifiTravelPrivilege.add(travelPrivilege);
                cabinFare.setWifiTravelPrivilege(wifiTravelPrivilege);

            }
            if (memberSecKill) {
                cabinFare.setCabinType(PackageTypeEnum.MEMBER_SEC_KILL.getPackType());
                cabinFare.setActivityLabel(handConfig.getMemberSecKillFareLabel());
            }
            // 低碳特惠
            if (CollectionUtils.isNotEmpty(cabinFare.getShippingRulesLabel()) && cabinFare.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
                cabinFare.setCabinLabelList2(handConfig.getLabelInfoConfig().getNoFreeBaggageLabelInfos());
                cabinFare.putActivityLabelMap(ShippingRulesLabelEnum.LCPREFERENCE.name(), handConfig.getNoFreeBaggageFareLabel());
            }

            cabinFare.setAlternateButton(false);
            if (CollectionUtils.isNotEmpty(combFlightInfoList)) {
                combFlightInfoList.stream().forEach(flightInfo -> {
                    int minute = DateUtils.dateminuteDiff(DateUtils.toAllDate(flightInfo.getDepDateTime()), new Date());
                    flightInfo.getCabinFareList().stream().forEach(combCabinFare -> {
                        //机票候补标识,小程序
                        if (SystemConstants.WAIT.equals(combCabinFare.getFlightFareType()) && cabinFare.getCabinComb().equals(combCabinFare.getCabinCode())) {
                            cabinFare.setFlightFareType(combCabinFare.getFlightFareType());
                            if (minute > handConfig.getWaitFareMinute()) {
                                cabinFare.setAlternateButton(true);
                            }
                        }

                    });

                });
            }

            cabinFareList.add(cabinFare);
        }
        //单程排序
        cabinFareList.sort(Comparator.comparing(i -> i.flightPriceComb.goPriceValue));
        List<CabinFare> notSignCabinList = cabinFareList.stream()
                .filter(cabinFare -> StringUtils.isBlank(cabinFare.getPriceProductSign()))
                .collect(Collectors.toList());
        Map<String, List<CabinFare>> listMap = cabinFareList.stream()
                .filter(cabinFare -> StringUtils.isNotBlank(cabinFare.getPriceProductSign())
                        && !cabinFare.getPriceProductSign().endsWith(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode()))
                .collect(Collectors.groupingBy(CabinFare::getCabinClass));
        if (listMap != null && !listMap.isEmpty()) {
            for (Map.Entry<String, List<CabinFare>> entry : listMap.entrySet()) {
                Optional<CabinFare> min = entry.getValue().stream()
                        .min(Comparator.comparing(CabinFare::getPriceValue));
                if (min.isPresent()) {
                    CabinFare fare = min.get();
                    notSignCabinList.add(fare);
                }
            }
        }
        //处理wifi 选取最低价
        Map<String, List<CabinFare>> wifiCabinFareMap = cabinFareList.stream()
                .filter(cabinFare -> StringUtils.isNotBlank(cabinFare.getPriceProductSign())
                        && cabinFare.getPriceProductSign().endsWith(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode()))
                .collect(Collectors.groupingBy(CabinFare::getCabinClass));
        //只有两个航班都是787才会展示
        boolean isWifiFlight = flightInfoCombApi.getFlightInfoList().stream()
                .allMatch(v2FlightInfo -> "789".equals(v2FlightInfo.getFType()));
        //wifi每个舱等添加一个会员价,必须是wifi航线
        if (isWifiFlight) {
            for (List<CabinFare> cabinFares : wifiCabinFareMap.values()) {
                Optional<CabinFare> min = cabinFares.stream()
                        .min(Comparator.comparing(CabinFare::getPriceValue));
                if (min.isPresent()) {
                    notSignCabinList.add(min.get());
                }
            }
        }

        notSignCabinList.sort(Comparator.comparing(CabinFare::getPriceValue));
        return notSignCabinList;
    }

    /**
     * 生成子运价的退改规则等信息
     *
     * @param fare               当前舱位对应的运价信息
     * @param v2CabinFare        舱位信息
     * @param combFlightInfoList 组合航班明细
     * @param interFlag
     * @return
     */
    private static List<SegmentCabinInfo> genChildrenSegmentCabinInfo(Fare fare, V2CabinFareApi v2CabinFare,
                                                                      List<V2FlightInfo> combFlightInfoList, String interFlag, LocalCacheService localCacheService) {
        List<SegmentCabinInfo> segmentCabinInfoList = Lists.newArrayList();
        boolean isRouteType_RT = combFlightInfoList.get(0).getDepCity().equals(combFlightInfoList.get(combFlightInfoList.size() - 1).getArrCity());
        if (CollectionUtils.isNotEmpty(fare.getFares())) {
            String[] cabinCodes = v2CabinFare.getCabinComb().replaceAll("/", "-").split("-");
            for (int i = 0; i < fare.getFares().size(); i++) {
                // 联程运价的子运价，用于展示(不是儿童运价)
                CabinFare childFare = toCabinFare(v2CabinFare, fare.getFares().get(i), interFlag, combFlightInfoList.get(i).getFlightNo(), isRouteType_RT);
                SegmentCabinInfo segmentCabinInfo = new SegmentCabinInfo();
                BeanUtils.copyNotNullProperties(childFare, segmentCabinInfo);
                V2FlightInfo flightInfo = combFlightInfoList.get(i);
                segmentCabinInfo.setArrCity(flightInfo.getArrCity());
                segmentCabinInfo.setArrCityName(localCacheService.getLocalAirport(flightInfo.getArrAirport()).getCityName());
                segmentCabinInfo.setDepCity(flightInfo.getDepCity());
                segmentCabinInfo.setDepCityName(localCacheService.getLocalAirport(flightInfo.getDepAirport()).getCityName());
                if (cabinCodes.length >= i + 1) {
                    segmentCabinInfo.setCabinCode(cabinCodes[i]);
                }
                segmentCabinInfo.setCabinClass(getCabinClassByCabinCodeFromFlight(flightInfo, cabinCodes[i]));
                segmentCabinInfo.setCabinClassName(CommonUtil.showCabinClassName(segmentCabinInfo.getCabinClass(), interFlag));
                if (isRouteType_RT && i >= fare.getFares().size() / 2) {
                    segmentCabinInfo.setFlightDirection(FlightDirection.BACK.getCode());
                } else {
                    segmentCabinInfo.setFlightDirection(FlightDirection.GO.getCode());
                }
                segmentCabinInfo.setInterFlag(interFlag);
                segmentCabinInfoList.add(segmentCabinInfo);
            }
        } else {
            SegmentCabinInfo segmentCabinInfo = new SegmentCabinInfo();
            CabinFare toCabinFare = toCabinFare(v2CabinFare, fare, interFlag, combFlightInfoList.get(0).getFlightNo(), isRouteType_RT);
            BeanUtils.copyNotNullProperties(toCabinFare, segmentCabinInfo);
            V2FlightInfo flightInfo = combFlightInfoList.get(0);
            segmentCabinInfo.setArrCity(flightInfo.getArrCity());
            if (localCacheService.getLocalAirport(flightInfo.getArrAirport()) != null) {
                segmentCabinInfo.setArrCityName(localCacheService.getLocalAirport(flightInfo.getArrAirport()).getCityName());
            }
            if (localCacheService.getLocalAirport(flightInfo.getDepAirport()) != null) {
                segmentCabinInfo.setDepCityName(localCacheService.getLocalAirport(flightInfo.getDepAirport()).getCityName());
            }
            segmentCabinInfo.setDepCity(flightInfo.getDepCity());
            segmentCabinInfo.setCabinCode(v2CabinFare.getCabinComb());
            segmentCabinInfo.setCabinClass(v2CabinFare.getCabinClass());
            segmentCabinInfo.setCabinClassName(CommonUtil.showCabinClassName(segmentCabinInfo.getCabinClass(), interFlag));
            segmentCabinInfo.setFlightDirection(FlightDirection.GO.getCode());
            segmentCabinInfo.setInterFlag(interFlag);
            segmentCabinInfoList.add(segmentCabinInfo);
            // 解决国际机票前端问题无实际意义
            if (HandlerConstants.TRIP_TYPE_I.equals(interFlag)) {
                SegmentCabinInfo segmentCabinInfo2 = new SegmentCabinInfo();
                segmentCabinInfo2.setShippingRulesLabel(Sets.newHashSet());
                segmentCabinInfoList.add(segmentCabinInfo2);
                SegmentCabinInfo segmentCabinInfo3 = new SegmentCabinInfo();
                segmentCabinInfo3.setShippingRulesLabel(Sets.newHashSet());
                segmentCabinInfo3.setFlightDirection(FlightDirection.BACK.getCode());
                segmentCabinInfoList.add(segmentCabinInfo3);
                SegmentCabinInfo segmentCabinInfo4 = new SegmentCabinInfo();
                segmentCabinInfo4.setShippingRulesLabel(Sets.newHashSet());
                segmentCabinInfo4.setFlightDirection(FlightDirection.BACK.getCode());
                segmentCabinInfoList.add(segmentCabinInfo4);
            }
        }
        return segmentCabinInfoList;
    }

    /**
     * 根据舱位等级
     *
     * @param flightInfo
     * @param cabinCode
     * @return
     */
    private static String getCabinClassByCabinCodeFromFlight(V2FlightInfo flightInfo, String cabinCode) {
        V2CabinFare cabinFare = flightInfo.getCabinFareList().stream().filter(V2CabinFare -> cabinCode.equals(V2CabinFare.getCabinCode())).findFirst().orElse(null);
        //部分舱位信息存在于打包舱位中
        if (cabinFare == null && CollectionUtils.isNotEmpty(flightInfo.getCabinFarePackageList())) {
            cabinFare = flightInfo.getCabinFarePackageList().stream().filter(V2CabinFare -> cabinCode.equals(V2CabinFare.getCabinCode())).findFirst().orElse(null);
        }
        if (cabinFare != null) {
            return cabinFare.getCabinClass();
        }
        log.info("当前航班日期:{},当前航班号:{},舱位:{}未找到舱位等级代码", flightInfo.getFlightDate(), flightInfo.getFlightNo(), cabinCode);
        return "";
    }

    //行李重量上线处理
    private static int setWeightLimitFunc(String fareType, String cabinComb, List<V2FlightInfo> combFlightInfoList, LocalCacheService localCacheService, HandConfig handConfig) {
        String[] cabingoAndBack = cabinComb.split("/");//往返拆分
        List<BaggageStandardConfig> baggageStandardConfigList = handConfig.getBaggageStandardConfigList();
        //重量上线 ，按照最低的标准执行
        int weightLimit = 0;
        for (int i = 0; i < cabingoAndBack.length; i++) {
            List<V2FlightInfo> direcFlightInfoList;
            if (i == 0) {//去程
                direcFlightInfoList = combFlightInfoList.stream().filter(v2FlightInfo -> FlightDirection.GO.getCode().equals(v2FlightInfo.getFlightDirection())).collect(Collectors.toList());
            } else {//返程
                direcFlightInfoList = combFlightInfoList.stream().filter(v2FlightInfo -> FlightDirection.BACK.getCode().equals(v2FlightInfo.getFlightDirection())).collect(Collectors.toList());
            }
            String[] cabins = cabingoAndBack[i].split("-");//具体舱位明细
            for (int j = 0; j < cabins.length; j++) {
                String cabinCode = cabins[j];
                V2FlightInfo v2FlightInfo = direcFlightInfoList.get(j);//舱位对应的航班
                if (!v2FlightInfo.getFlightNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode())) {//非吉祥的不做舱位行李额查询
                    continue;
                }
                V2CabinFare cabinFare = v2FlightInfo.getCabinFareList().stream().filter(v2Cabin -> cabinCode.equals(v2Cabin.getCabinCode())).findFirst().orElse(null);
                AirPortInfoDto depAirport = localCacheService.getLocalAirport(v2FlightInfo.getDepAirport());
                AirPortInfoDto arrAirport = localCacheService.getLocalAirport(v2FlightInfo.getArrAirport());
                if (cabinFare != null &&
                        depAirport != null && arrAirport != null && (HandlerConstants.TRIP_TYPE_I.equals(depAirport.getIsInternational()) || HandlerConstants.TRIP_TYPE_I.equals(arrAirport.getIsInternational()))) {
                    BaggageFilter[] baggageFilter = new BaggageFilter[1];
                    baggageFilter[0] = new BaggageFilter();
                    baggageFilter[0].setCabinCode(cabinCode);
                    if (HandlerConstants.TRIP_TYPE_I.equals(depAirport.getIsInternational())) {
                        baggageFilter[0].setCity(depAirport.getCityCode());
                        baggageFilter[0].setCountryNo(depAirport.getCountryNo());
                    }
                    if (HandlerConstants.TRIP_TYPE_I.equals(arrAirport.getIsInternational())) {
                        baggageFilter[0].setCity(arrAirport.getCityCode());
                        baggageFilter[0].setCountryNo(arrAirport.getCountryNo());
                    }
                    //addon 目前只取国际段
                    if (FareTypeEnum.ADDON.getFare().equals(fareType)) {
                        if (HandlerConstants.TRIP_TYPE_I.equals(depAirport.getIsInternational())) {
                            baggageFilter[0].setCity(depAirport.getCityCode());
                            baggageFilter[0].setCountryNo(depAirport.getCountryNo());
                        }
                        if (HandlerConstants.TRIP_TYPE_I.equals(arrAirport.getIsInternational())) {
                            baggageFilter[0].setCity(arrAirport.getCityCode());
                            baggageFilter[0].setCountryNo(arrAirport.getCountryNo());
                        }
                    } else if (FareTypeEnum.SPA.getFare().equals(fareType)) {
                        //取HO的航班
                        if (v2FlightInfo.getFlightNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode())) {
                            if (HandlerConstants.TRIP_TYPE_I.equals(depAirport.getIsInternational())) {
                                baggageFilter[0].setCity(depAirport.getCityCode());
                                baggageFilter[0].setCountryNo(depAirport.getCountryNo());
                            }
                            if (HandlerConstants.TRIP_TYPE_I.equals(arrAirport.getIsInternational())) {
                                baggageFilter[0].setCity(arrAirport.getCityCode());
                                baggageFilter[0].setCountryNo(arrAirport.getCountryNo());
                            }
                        }
                    }
                    if (baggageFilter[0] != null) {
                        BaggageStandardConfig curBaggageStandardConfig = baggageStandardConfigList.stream()
                                .filter(baggageStandardConfig -> StringUtil.doStringListFilter(baggageStandardConfig.getCityList(), baggageFilter[0].getCity()))
                                .filter(baggageStandardConfig -> StringUtil.doStringListFilter(baggageStandardConfig.getCountryNoList(), baggageFilter[0].getCountryNo()))
                                .filter(baggageStandardConfig -> StringUtil.doStringListFilter(baggageStandardConfig.getCabinList(), baggageFilter[0].getCabinCode()))
                                //排除项
                                .filter(baggageStandardConfig -> {
                                    if (StringUtil.isNullOrEmpty(baggageStandardConfig.getExcludeCountryNoList())) {
                                        return true;
                                    } else {
                                        return !baggageStandardConfig.getExcludeCountryNoList().contains(baggageFilter[0].getCountryNo());
                                    }
                                })
                                .filter(baggageStandardConfig -> {
                                    if (StringUtil.isNullOrEmpty(baggageStandardConfig.getExcludeCityList())) {
                                        return true;
                                    } else {
                                        return !baggageStandardConfig.getExcludeCityList().contains(baggageFilter[0].getCity());
                                    }
                                })
                                .filter(baggageStandardConfig -> {
                                    if (StringUtil.isNullOrEmpty(baggageStandardConfig.getExcludeCabinList())) {
                                        return true;
                                    } else {
                                        return !baggageStandardConfig.getExcludeCabinList().contains(baggageFilter[0].getCabinCode());
                                    }
                                })
                                .findFirst().orElse(null);
                        if (curBaggageStandardConfig != null && !StringUtil.isNullOrEmpty(curBaggageStandardConfig.getBaggageStandardList())) {
                            BaggageStandardResp.BaggageStandard baggageStandard = curBaggageStandardConfig.getBaggageStandardList().stream()
                                    .filter(baggage -> "2".equals(baggage.getLuggageType()))
                                    .findFirst().orElse(null);
                            if (baggageStandard != null) {
                                int weight = NumberUtil.stringToInt(baggageStandard.getPerWeightLimit());
                                if (weightLimit == 0) {
                                    weightLimit = weight;
                                } else {
                                    if (weight < weightLimit && weight > 0) {
                                        weightLimit = weight;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return weightLimit;
    }

    /**
     * 品牌运价舱位处理
     *
     * @param cabinFare
     */
    private static void brandFare(CabinFare cabinFare, String fareType, String route, HandConfig config, LocalCacheService localCacheService) {
        boolean isBrandPrice = false;
        if (FareTypeEnum.SIMPLE.getFare().equals(fareType)) {
            AirPortInfoDto depAirport = localCacheService.getLocalAirport(route.substring(0, 3));
            AirPortInfoDto arrAirport = localCacheService.getLocalAirport(route.substring(route.length() - 3));
            Map<String, List<String>> routeMap = config.getBrandRouteCabinConfig().getInternationalRoute();

            String nation = HandlerConstants.TRIP_TYPE_I.equals(depAirport.getIsInternational()) ? depAirport.getCountryNo() : arrAirport != null ? arrAirport.getCountryNo() : "";
            boolean routeMatch = false;  //航线是否匹配
            if (routeMap.containsKey(nation)) {
                List<String> nationRoute = routeMap.get(nation);
                if (CollectionUtils.isNotEmpty(nationRoute)) {
                    routeMatch = nationRoute.contains(depAirport.getCityCode() + arrAirport.getCityCode());
                } else {
                    // 国家内没配置航线时，默认到该国家的航线均满足品牌运价
                    routeMatch = true;
                }
            }
            if (!routeMatch) {
                return;
            }
            List<String> brandPriceCabin = config.getBrandRouteCabinConfig().getBrandPriceCabins();
            if (!StringUtil.isNullOrEmpty(brandPriceCabin)) {
                List<String> cabinCodeList = new ArrayList<>();
                String[] cabinCodeRT = cabinFare.getCabinComb().split("/");
                for (int i = 0; i < cabinCodeRT.length; i++) {
                    String[] cabinCodes = cabinCodeRT[i].split("-");
                    cabinCodeList.addAll(Arrays.asList(cabinCodes));
                }
                //全部舱位都是K舱时，显示品牌运价
                isBrandPrice = brandPriceCabin.containsAll(cabinCodeList);
                if (isBrandPrice) {
                    cabinFare.setCabinLabelBrand(config.getLabelInfoConfig().getBrandLabel());
                    cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getBrandLabelInfos());
                    BrandPriceRule brandPriceRule = JsonUtil.fromJson(config.getBrandPriceRule(), BrandPriceRule.class);
                    if (brandPriceRule != null && brandPriceRule.getApplicableCabin() != null && brandPriceRule.getApplicableCabin().getDescription() != null) {
                        String cableCabin = brandPriceRule.getApplicableCabin().getDescription().replace("{cabin}", cabinCodeList.get(0));
                        brandPriceRule.getApplicableCabin().setDescription(cableCabin);
                    }
                    cabinFare.setBrandPriceRule(brandPriceRule);
                }
            }
        }
    }

    /**
     * 联合航班舱位数量以及舱位等级处理
     *
     * @param v2CabinFare
     * @param flightInfoCombApi
     */
    private static void setCabinNumAndClass(V2CabinFareApi v2CabinFare, FlightInfoCombApi flightInfoCombApi, String passType) {
        StringBuffer cabinClassBuffer = new StringBuffer("");
        String[] flightNos = flightInfoCombApi.getFlightNoComb().replace("-", "#").replace("/", "#").split("#");
        //计算各舱位舱位等级
        if (CollectionUtils.isNotEmpty(flightInfoCombApi.getFlightInfoList())) {
            int cabinIndex = 0;
            //中台返回的可能有问题，这边为了兼容自行从舱位列表里面获取
            String cabinComb = v2CabinFare.getCabinComb();
            for (int i = 0; i < cabinComb.length(); i++) {
                String _char = String.valueOf(cabinComb.charAt(i));
                if ("-".equals(_char) || "/".equals(_char)) {
                    cabinClassBuffer.append(cabinComb.charAt(i));
                } else {
                    String flightNo = flightNos[cabinIndex];
                    Optional<V2FlightInfo> optional = flightInfoCombApi.getFlightInfoList().stream().filter(v2FlightInfo -> v2FlightInfo.getFlightNo().equals(flightNo)).findFirst();
                    if (optional.isPresent()) {
                        Optional<V2CabinFare> optionalV2CabinFareApi = optional.get().getCabinFareList().stream().filter(cabinFare -> cabinFare.getCabinCode().equals(_char)).findFirst();
                        if (!optionalV2CabinFareApi.isPresent() && CollectionUtils.isNotEmpty(optional.get().getCabinFarePackageList())) {
                            optionalV2CabinFareApi = optional.get().getCabinFarePackageList().stream().filter(cabinFare -> cabinFare.getCabinCode().equals(_char)).findFirst();
                        }
                        if (!optionalV2CabinFareApi.isPresent() && CollectionUtils.isNotEmpty(optional.get().getCabinCHDINFFareList())) {
                            optionalV2CabinFareApi = optional.get().getCabinCHDINFFareList().stream().filter(cabinFare -> cabinFare.getCabinCode().equals(_char)).findFirst();
                        }
                        if (optionalV2CabinFareApi.isPresent()) {
                            //获取cabinCode对应的cabinClass
                            cabinClassBuffer.append(optionalV2CabinFareApi.get().getCabinClass());
                        }
                    }
                    cabinIndex++;
                }
            }
        }
        //从组合航班里面获取不到舱位等级的，直接获取v2CabinFare的等级
        if (StringUtils.isBlank(cabinClassBuffer.toString()) || cabinClassBuffer.toString().length() != v2CabinFare.getCabinComb().length()) {
            cabinClassBuffer = new StringBuffer(v2CabinFare.getCabinClass());
        }
        v2CabinFare.setCabinClass(cabinClassBuffer.toString());
    }

    /**
     * 可赠送积分处理
     *
     * @param cabinComb          组合舱位
     * @param combFlightInfoList 组合航班
     * @param brandCode          品牌代码
     * @return
     */
    private static int setScoreGive(String cabinComb, List<V2FlightInfo> combFlightInfoList, String brandCode) {
        String[] cabingoAndBack = cabinComb.split("/");//往返拆分
        int totalScore = 0;
        int brandScore = 0;
        boolean useBrandScore = false;
        for (int i = 0; i < cabingoAndBack.length; i++) {
            List<V2FlightInfo> direcFlightInfoList;
            if (i == 0) {//去程
                direcFlightInfoList = combFlightInfoList.stream().filter(v2FlightInfo -> FlightDirection.GO.getCode().equals(v2FlightInfo.getFlightDirection())).collect(Collectors.toList());
            } else {//返程
                direcFlightInfoList = combFlightInfoList.stream().filter(v2FlightInfo -> FlightDirection.BACK.getCode().equals(v2FlightInfo.getFlightDirection())).collect(Collectors.toList());
            }
            String[] cabins = cabingoAndBack[i].split("-");//具体舱位明细
            for (int j = 0; j < cabins.length; j++) {
                String cabinCode = cabins[j];
                V2CabinFare cabinFare = null;
                V2FlightInfo direcFlightInfo = direcFlightInfoList.get(j);
                if (StringUtils.isNotBlank(brandCode)) {
                    if ((direcFlightInfo.getCodeShare() != null && !direcFlightInfo.getCodeShare())
                            || (direcFlightInfo.getCarrierNo() != null && direcFlightInfo.getCarrierNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode()))) {
                        cabinFare = direcFlightInfo.getCabinFareList().stream().filter(v2Cabin -> cabinCode.equals(v2Cabin.getCabinCode()) && brandCode.equals(v2Cabin.getBrandCode())).findFirst().orElse(null);
                    }

                } else {
                    if ((direcFlightInfo.getCodeShare() != null && !direcFlightInfo.getCodeShare())
                            || (direcFlightInfo.getCarrierNo() != null && direcFlightInfo.getCarrierNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode()))) {
                        cabinFare = direcFlightInfo.getCabinFareList().stream().filter(v2Cabin -> cabinCode.equals(v2Cabin.getCabinCode())).findFirst().orElse(null);
                    }
                }
                if (cabinFare != null) {
                    totalScore += cabinFare.getReturnScore() == null ? 0 : cabinFare.getReturnScore().intValue();
                    //2021-07-09  品牌运价积分返回配置
                    if (StringUtils.isNotBlank(cabinFare.getBrandCode()) && null != cabinFare.getScaleScoreList() && !cabinFare.getScaleScoreList().isEmpty()) {
                        brandScore += cabinFare.getScaleScoreList().get(cabinFare.getBrandCode()) == null ? 0 : cabinFare.getScaleScoreList().get(cabinFare.getBrandCode()).intValue();
                        useBrandScore = true;
                    }
                }
            }
        }
        if (useBrandScore) {
            return brandScore;
        }
        return totalScore;
    }

    /**
     * 航班舱位信息处理
     *
     * @param v2CabinFare
     * @param fare
     * @param interFlag
     * @param isROUTE_TYPE_RT
     * @return
     */
    private static CabinFare toCabinFare(V2CabinFareApi v2CabinFare, Fare fare, String interFlag, String flightNo, boolean isROUTE_TYPE_RT) {
        CabinFare cabinFare = new CabinFare();
        BeanUtils.copyProperties(v2CabinFare, cabinFare);
        BeanUtils.copyProperties(fare, cabinFare);
        cabinFare.setFareID(fare.getFareID());
        cabinFare.setInterFlag(interFlag);
        cabinFare.setFlightNo(flightNo);
        cabinFare.setPrivilegeList(fare.getPrivilegeList());
        //舱位退改规则处理  国内国际处理
        List<ChangeAndRefundRule> refundRuleList = Lists.newArrayList();
        List<ChangeAndRefundRule> changeRuleList = Lists.newArrayList();
        // 国内中转
        if (CollectionUtils.isNotEmpty(fare.getFares())) {
            for (Fare fareFare : fare.getFares()) {
                refundRuleList.addAll(AVObjectConvertV2.toRefundRules(fareFare.getRefundedRules(), interFlag));
                changeRuleList.addAll(AVObjectConvertV2.toChangeRules(fareFare.getChangeRules(), interFlag));
            }
        } else {
            refundRuleList = AVObjectConvertV2.toRefundRules(fare.getRefundedRules(), interFlag);
            changeRuleList = AVObjectConvertV2.toChangeRules(fare.getChangeRules(), interFlag);
        }
        //规则补全
        FlightUtil.completeRule(refundRuleList, changeRuleList);
        cabinFare.setRefundRuleList(refundRuleList);
        cabinFare.setChangeRuleList(changeRuleList);
        //舱位数量处理
        cabinFare.setCabinNumber(v2CabinFare.getCabinNumber());
        //舱位等级名称
        cabinFare.setCabinClassName(combCabinClassName(v2CabinFare.getCabinClass(), v2CabinFare.getCabinComb()));
        cabinFare.setCabinCombClassName(combCabinClassNameDetail(v2CabinFare.getCabinClass()));
        // 以下字段空字符串可能引起APP崩溃  2019-11-20
        if (StringUtils.isBlank(cabinFare.getMinStay())) {
            cabinFare.setMinStay(null);
        }
        if (StringUtils.isBlank(cabinFare.getValidityPeriod())) {
            cabinFare.setValidityPeriod(null);
        }
        if (null != cabinFare.getTaxInfo() && null == cabinFare.getCNTax()) {
            cabinFare.setCNTax(null == cabinFare.getTaxInfo().getCNTax() ? 0 : cabinFare.getTaxInfo().getCNTax());
            cabinFare.setYQTax(null == cabinFare.getTaxInfo().getYQTax() ? 0 : cabinFare.getTaxInfo().getYQTax());
        }
        cabinFare.setFares(fare.getFares());
        cabinFare.flightPriceComb = FlightPriceComb.priceValueComb2FlightPriceComb(fare.getPriceValueComb(), isROUTE_TYPE_RT);
        return cabinFare;
    }

    /**
     * 只使用国际往返，国内中转（）
     *
     * @param cabinClassStr
     * @return 组合舱位名称 公务舱(R-R/R-R)
     */
    private static String combCabinClassName(String cabinClassStr, String cabinCodeStr) {
        if (StringUtils.isNotBlank(cabinClassStr)) {
            String[] cabinClassRT = cabinClassStr.split("/");
            String[] cabinClassNames = new String[cabinClassRT.length];
            //往返遍历
            for (int i = 0; i < cabinClassRT.length; i++) {
                String[] cabinClasses = cabinClassRT[i].split("-");
                String className = CommonUtil.showCabinClassName(cabinClasses[0]);
                //联程遍历
                for (int j = 0; j < cabinClasses.length; j++) {
                    if (!cabinClasses[j].equals(cabinClasses[0])) {
                        className = "多舱位";
                        break;
                    }
                }
                cabinClassNames[i] = className;
            }
            String cabinClassName = "";
            String cabinClass = cabinClassRT[0];
            for (int i = 0; i < cabinClassRT.length; i++) {
                if (!cabinClassRT[i].equals(cabinClass)) {
                    cabinClassName = "多舱位(" + cabinCodeStr + ")";
                    return cabinClassName;
                }
            }
            cabinClassName = cabinClassNames[0];
            return cabinClassName + "(" + cabinCodeStr + ")";
        }
        return "";
    }

    /**
     * @return 组合舱位具体信息 公务舱-公务舱/公务舱-公务舱
     */
    private static String combCabinClassNameDetail(String cabinClassStr) {
        if (StringUtils.isNotBlank(cabinClassStr)) {
            StringBuffer str = new StringBuffer();
            int length = cabinClassStr.length();
            for (int i = 0; i < length; i++) {
                char c = cabinClassStr.charAt(i);
                if ("-".equals(String.valueOf(c))) {
                    str.append("-");
                } else if ("/".equals(String.valueOf(c))) {
                    str.append("/");
                } else {
                    str.append(CommonUtil.showCabinClassName(String.valueOf(c)));
                }
            }
            return str.toString();
        }
        return "";
    }

    /**
     * 机型展示设置
     *
     * @param flightInfoList
     * @return
     */
    private static String setPlaneType(List<FlightInfo> flightInfoList) {
        String planeType = flightInfoList.get(0).getAircraftModel();
        if (flightInfoList.size() > 1 && (FareBasisEnum.BUS_FARE.getFareBasisCode().equals(flightInfoList.get(1).getFType())
                || FareBasisEnum.BUS_FARE.getFareBasisCode().equals(flightInfoList.get(0).getFType()))) {
            return flightInfoList.get(0).getAircraftModel() + "+" + flightInfoList.get(1).getAircraftModel();
        }
        for (int i = 1; i < flightInfoList.size(); i++) {
            FlightInfo flightInfo = flightInfoList.get(i);
            if (!planeType.equals(flightInfo.getAircraftModel())) {
                planeType = "多种机型";
                break;
            }
        }
        return planeType;
    }

    /**
     * 餐食处理
     *
     * @param flightInfoList
     * @return
     */
    private static String setMealName(List<FlightInfo> flightInfoList) {
        if (flightInfoList.stream().allMatch(flightInfo -> StringUtil.isNullOrEmpty(flightInfo.getMealCode()))) {
            return "无餐食";
        }
        //餐食分组
        Map<String, List<FlightInfo>> mealMap = flightInfoList.stream().collect(Collectors.groupingBy(FlightInfo::getMealCodeNotNull));
        if (mealMap != null && !mealMap.isEmpty()) {
            if (mealMap.size() > 1) {
                return "多餐食";
            } else {
                return flightInfoList.get(0).getMealName();
            }
        } else {
            return "无餐食";
        }
    }

    /**
     * @return
     */
    public static void transferProcess(FlightInfo flightInfo, HandConfig handConfig, LocalCacheService localCacheService) {
        TransferProcess transferProcess = handConfig.getTransferProcessConfig();
        TransferInfo transferInfo = flightInfo.getTransferInfo();
        if (transferProcess != null && transferInfo != null) {
            AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(flightInfo.getDepAirport());
            List<TCountryDTO> depAirportCountry = localCacheService.getLocalCountry(Collections.singletonList(depAirportInfo.getCountryNo()));
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(flightInfo.getArrAirport());
            List<TCountryDTO> arrAirportCountry = localCacheService.getLocalCountry(Collections.singletonList(arrAirportInfo.getCountryNo()));
            AirPortInfoDto transferAirportInfo = localCacheService.getLocalAirport(transferInfo.getTransferAirPort());
            List<TransferRules> rules =transferProcess.getRules();
            if (CollectionUtils.isNotEmpty(rules)) {
                rules.forEach(rule -> {
                    if (isRegionValid(rule.getDepRegionList(), depAirportCountry)
                            && isRegionValid(rule.getArrRegionList(), arrAirportCountry)
                            && isCountryValid(rule.getDepCountryList(), depAirportInfo)
                            && isCountryValid(rule.getArrCountryList(), arrAirportInfo)
                            &&isAirportValid(rule.getDepAirports(),depAirportInfo)
                            &&isAirportValid(rule.getArrAirports(),arrAirportInfo)
                            &&isTransAirportValid(rule.getTransAirportList(),transferAirportInfo)
                            &&isSameAirportValid(rule.getTransSameAirport(),transferInfo.isSameAirport())) {
                        transferInfo.setTransferProcessUrl(transferProcess.getTransferProcessUrl());
                        transferInfo.setTransferProcessDesc(transferProcess.getTransferProcessDesc());
                        transferInfo.setInterTransferProcessUrl(transferProcess.getInterTransferProcessUrl());
                        transferInfo.setTransferProcessPageUrl(transferProcess.getTransferProcessPageUrl());
                        flightInfo.setTransferInfo(transferInfo);
                    }
                });
            }
        }
    }

    private static boolean isRegionValid(List<String> regionList, List<TCountryDTO> airportCountry) {
        return CollectionUtils.isEmpty(regionList) || airportCountry.stream().anyMatch(v -> regionList.contains(v.getRegionCode()));
    }

    private static boolean isCountryValid(List<String> countryList, AirPortInfoDto arrAirportInfo) {
        return CollectionUtils.isEmpty(countryList) || countryList.contains(arrAirportInfo.getCountryNo());
    }

    private static boolean isAirportValid(List<String> depAirports, AirPortInfoDto arrAirportInfo) {
        return  CollectionUtils.isEmpty(depAirports) || depAirports.contains(arrAirportInfo.getAirPortCode());
    }

    private static boolean isTransAirportValid(List<String> transAirportList, AirPortInfoDto transferAirportInfo) {
        return CollectionUtils.isEmpty(transAirportList) || transAirportList.contains(transferAirportInfo.getAirPortCode());
    }

    /**
     * 判断是否满足规则中对同机场转机的要求。
     *
     * @param sameAirport 表示是否在同一机场转机。
     * @return 如果满足规则中的要求，返回 true；否则返回 false。
     */
    private static boolean isSameAirportValid(String transSameAirport, boolean sameAirport) {
        if (StringUtils.isBlank(transSameAirport)) {
            return true;
        }
        if("A".equals(transSameAirport)){
            return true;
        }
        return "Y".equals(transSameAirport) == sameAirport;
    }

}
