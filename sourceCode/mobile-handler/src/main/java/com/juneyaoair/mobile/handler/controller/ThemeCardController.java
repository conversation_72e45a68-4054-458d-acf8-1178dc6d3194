package com.juneyaoair.mobile.handler.controller;

import com.google.common.collect.Lists;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.basicsys.response.AirLineInfoDepCityDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.theme.CouponFlightInfoDto;
import com.juneyaoair.baseclass.theme.CouponFlightRequest;
import com.juneyaoair.baseclass.theme.QueryActivityInfoReq;
import com.juneyaoair.baseclass.theme.ThemeAirLine;
import com.juneyaoair.mobile.handler.service.IThemeCardService;
import com.juneyaoair.thirdentity.comm.request.BindingSearchReq;
import com.juneyaoair.thirdentity.comm.request.BindingSearchRequest;
import com.juneyaoair.thirdentity.comm.request.ThemeCardBaseRequest;
import com.juneyaoair.thirdentity.comm.response.ThemeCardBaseResponse;
import com.juneyaoair.thirdentity.comm.response.ThemeCouponBoundDetail;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.util.ObjCheckUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 主题卡查询相关接口
 * @date 2021/12/30  15:34.
 */
@RequestMapping("/theme")
@RestController
public class ThemeCardController extends BassController {

    @Autowired
    private IThemeCardService themeCardService;

    @ApiOperation(value = "主题卡航线查询")
    @RequestMapping(value = "/queryThemeAirLine",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    public BaseResp queryThemeAirLine(@RequestBody @Validated  BaseReq<ThemeAirLine> req, BindingResult bindingResult, HttpServletRequest request){
        BaseResp baseResp = new BaseResp();
        if(bindingResult.hasErrors()){
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        ThemeAirLine themeAirLine = req.getRequest() ;
        ObjCheckUtil.notNull(themeAirLine,WSEnum.ERROR_REQUEST_PARAMS_925_2.getResultInfo());
        String ip = this.getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        List<AirLineInfoDepCityDto> list = themeCardService.queryThemeAirLine(themeAirLine,headMap,req.getChannelCode());
        baseResp.success(list,WSEnum.SUCCESS.getResultCode(),WSEnum.SUCCESS.getResultInfo());
        return  baseResp;
    }



    @ApiOperation(value = "")
    @RequestMapping(value = "/queryActivityAirLine",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    public BaseResp queryActivityAirLine(@RequestBody @Validated  BaseReq<QueryActivityInfoReq> req, BindingResult bindingResult, HttpServletRequest request){
        BaseResp baseResp = new BaseResp();
        if(bindingResult.hasErrors()){
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        QueryActivityInfoReq queryActivityInfoReq = req.getRequest();
        String ip = this.getClientIP(request);
        List<AirLineInfoDepCityDto> list = themeCardService.queryActivityAirLine(queryActivityInfoReq,ip);
        baseResp.success(list,WSEnum.SUCCESS.getResultCode(),WSEnum.SUCCESS.getResultInfo());
        return  baseResp;
    }

    @ApiOperation(value = "open票可用航线查询")
    @RequestMapping(value = "/queryOpenProductAirLine",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    public BaseResp queryOpenProductAirLine(@RequestBody @Validated  BaseReq<CouponFlightRequest> req, BindingResult bindingResult, HttpServletRequest request){
        BaseResp baseResp = new BaseResp();
        if(bindingResult.hasErrors()){
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        Map<String,List<CouponFlightInfoDto>> list = themeCardService.queryOpenProductAirLine(req);
        baseResp.success(list,WSEnum.SUCCESS.getResultCode(),WSEnum.SUCCESS.getResultInfo());
        return  baseResp;
    }




    @RequestMapping(value = "/redeemBindingList",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    public BaseResp redeemBindingList(@RequestBody @Validated BaseReq<BindingSearchReq> themeCardBaseReq, BindingResult bindingResult, HttpServletRequest request){
        BaseResp baseResp = new BaseResp();
        if(bindingResult.hasErrors()){
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        ThemeCardBaseRequest  themeCardBaseRequest =new ThemeCardBaseRequest();
        themeCardBaseRequest.setFfpCardNo(themeCardBaseReq.getRequest().getFfpCardNo());
        themeCardBaseRequest.setFfpId(themeCardBaseReq.getRequest().getFfpId());
        themeCardBaseRequest.setVersion("10");
        themeCardBaseRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        BindingSearchRequest bindingSearchRequest =new BindingSearchRequest();
        bindingSearchRequest.setResourceType(themeCardBaseReq.getRequest().getResourceType());
        themeCardBaseRequest.setData(bindingSearchRequest);
        ThemeCardBaseResponse<List<ThemeCouponBoundDetail>> bindingSearchResponse=themeCardService.redeemBindingList(themeCardBaseRequest);
        if (bindingSearchResponse!=null){
            if ("1001".equals(bindingSearchResponse.getResultCode())){
                if (CollectionUtils.isNotEmpty(bindingSearchResponse.getData())){
                    baseResp.setObjData(bindingSearchResponse.getData());
                }else {
                    baseResp.setObjData(Lists.newArrayList());
                }
            }else {
                baseResp.setObjData(Lists.newArrayList());
            }

        }
        return baseResp;
    }

}
