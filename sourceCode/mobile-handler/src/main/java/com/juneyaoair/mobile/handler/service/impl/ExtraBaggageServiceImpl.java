package com.juneyaoair.mobile.handler.service.impl;

import com.google.common.base.Strings;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.member.response.TicketInfoQueryResponse;
import com.juneyaoair.baseclass.newcoupon.bean.SingleBookCondition;
import com.juneyaoair.baseclass.prepayment.common.BaseRequestDTO;
import com.juneyaoair.baseclass.prepayment.common.BaseResultDTO;
import com.juneyaoair.baseclass.prepayment.request.CheckCanPayBaggageReq;
import com.juneyaoair.baseclass.prepayment.response.CanPayBaggageResp;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.prepaymentBaggage.QueryPrepaymentBaggageSegmentReq;
import com.juneyaoair.baseclass.response.prepaymentBaggage.BaggagePassengerInfo;
import com.juneyaoair.baseclass.response.prepaymentBaggage.BaggageSegmentInfo;
import com.juneyaoair.baseclass.response.prepaymentBaggage.Pricing;
import com.juneyaoair.baseclass.response.prepaymentBaggage.QueryPrepaymentBaggageSegmentResp;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.NewCouponController;
import com.juneyaoair.mobile.handler.controller.util.AVObjectConvert;
import com.juneyaoair.mobile.handler.service.IExtraBaggageService;
import com.juneyaoair.mobile.handler.service.IMemberTravelService;
import com.juneyaoair.mobile.handler.service.ITicketService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.ChannelUtils;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.util.ControllerUtil;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.exception.BusinessException;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExtraBaggageServiceImpl implements IExtraBaggageService {

    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private HandConfig handConfig;

    @Autowired
    private IMemberTravelService memberTravelService;
    @Autowired
    private ITicketService ticketService;

    /**
     * 根据航距匹配额外行李的阶梯定价
     *
     * @param mileage 航距
     * @return 额外行李的阶梯定价
     */
    @Override
    public Pricing matchExtraBaggagePricingByMileage(int mileage) {
        List<Pricing> pricingList = getExtraBaggagePricing();
        return pricingList.stream()
                .filter(pricing -> (mileage > pricing.getScopeMin() && mileage < pricing.getScopeMax()))
                .findFirst().orElse(null);
    }

    /**
     * 根据线上销售价价格匹配额外行李的阶梯定价
     *
     * @param price 线上销售价单价
     * @return
     */
    @Override
    public Pricing matchExtraBaggagePricingByOnLinePrice(Double price) {
        List<Pricing> pricingList = getExtraBaggagePricing();
        return pricingList.stream().filter(pricing -> (pricing.getOnlinePrice() == price)).findFirst().orElse(null);
    }

    @Override
    public List<Pricing> getExtraBaggagePricing() {
        return handConfig.getExtraBaggagePricing();
    }

    @Override
    public boolean whetherUesNewBaggageProduct() {
        Date enableDate = DateUtils.toAllDate(handConfig.getNewBaggageProductEnableTime());
        return enableDate.before(new Date());
    }


    /**
     * 查询行程
     * 优先以证件号列表查询
     * 无证件号以票号查询
     *
     * @param request
     * @param ffpCardNo
     * @param ffpId
     * @param certNOs   Option 证件号列表
     * @param ticketNo  Option 票号
     * @return
     */
    @Override
    public List<TicketInfoQueryResponse> queryFlightSegment(HttpServletRequest request, String ffpCardNo, String ffpId, List<String> certNOs, String ticketNo) {
        List<TicketInfoQueryResponse> ticketList = new ArrayList<>();
        // 优先多证件查询 结果合并
        if (certNOs != null && !certNOs.isEmpty()) {
            certNOs.forEach(certNO -> {
                List<TicketInfoQueryResponse> ticketInfoQueryResponseList = memberTravelService.fetchItineraryInfo(ticketNo, certNO, IPUtil.getIpAddr(request));
                if (CollectionUtils.isNotEmpty(ticketInfoQueryResponseList)) {
                    ticketList.addAll(ticketInfoQueryResponseList);
                }});
            } else{
                // 无证件 以票号查询
                if (StringUtils.isNotBlank(ticketNo)) {
                    return memberTravelService.fetchItineraryInfo(ticketNo, null, IPUtil.getIpAddr(request));
                }
            }
        return ticketList;
        }

        /**
         * 以票号基准处理额外行李行程航段信息
         * <p>
         * TicketInfoQueryResponse 转换为 QueryPrepaymentBaggageSegmentResp
         *
         * @param info
         * @param req
         * @param isItineraryEntry
         * @return
         */
        @Override
        public List<QueryPrepaymentBaggageSegmentResp> convertToQueryPrepaymentBaggageSegmentResp
        (List<TicketInfoQueryResponse> info, BaseReq<QueryPrepaymentBaggageSegmentReq> req, boolean isItineraryEntry){
            // 使用会员卡号比对行程信息标记本人
            String ffpNo = req.getRequest().getFfpCardNo();
            //
            List<QueryPrepaymentBaggageSegmentResp> resp;
            // 过滤 取消(废票、退票、换开)
            info = info.stream().filter(ticketInfoQueryResponse -> ticketInfoQueryResponse.getTraveller_trfd_flag().equals("0")).collect(Collectors.toList());
            // 票号去重list
            List<String> ticketNums = info.stream().map(TicketInfoQueryResponse::getTicket_ticketnumber).distinct().collect(Collectors.toList());

            // 处理为前端BO,及原始状态导致的不可购 (值机、中转、G舱、婴儿、非018票号)
            List<TicketInfoQueryResponse> finalInfo = info;
            resp = ticketNums.stream().map(ticketNum -> QueryPrepaymentBaggageSegmentResp.builder()
                    .useNewBaggageProduct(true)
                    .ticketNumber(ticketNum)
                    .baggageSegmentInfoList(this.convertTicketInfoQueryResponseToBaggageSegmentInfo(req, finalInfo, ticketNum, isItineraryEntry))
                    .baggagePassengerInfo(this.extractBaggagePassengerInfo(finalInfo, ticketNum, ffpNo))
                    .build()).collect(Collectors.toList());

            // 校验产品端judgeExtraPro Api 判断是否能购买产品 及 产品可购起飞时限
            resp.forEach(qPBS -> qPBS.setBaggageSegmentInfoList(
                    qPBS.getBaggageSegmentInfoList().stream().map(baggageSegmentInfo -> this.handleProductCanPurchase(req, baggageSegmentInfo)).collect(Collectors.toList()))
            );

            // 提取子层routeType、interFlag、CanPurchase、ErrorDesc
            resp.forEach(qPBS -> {
                if (!qPBS.getBaggageSegmentInfoList().isEmpty()) {
                    qPBS.setRouteType(qPBS.getBaggageSegmentInfoList().stream().findFirst().get().getFlightType());
                    qPBS.setInterFlag(qPBS.getBaggageSegmentInfoList().stream().findFirst().get().getInterFlag());
                    qPBS.setCanPurchase(qPBS.getBaggageSegmentInfoList().stream().allMatch(BaggageSegmentInfo::getCanPurchase));
                    qPBS.setSegErrorDesc(qPBS.getBaggageSegmentInfoList().stream().filter(v -> !Strings.isNullOrEmpty(v.getErrorDesc())).findFirst().map(BaggageSegmentInfo::getErrorDesc).orElse(null));
                }
            });

            // 航线日期排序,相同日期按起飞时间排序
            resp = resp.stream()
                    .sorted(Comparator.<QueryPrepaymentBaggageSegmentResp, Date>comparing(
                            v -> DateUtils.toDate(v.getBaggageSegmentInfoList().get(0).getFlightDate()))
                            .thenComparing(v -> v.getBaggageSegmentInfoList().get(0).getDepTime()))
                    .collect(Collectors.toList());
            return resp;
        }


        /**
         * 校验产品端 是否能购买产品 及 产品起飞时限
         * @param req 实际仅需 UserInfoMust
         * @param segmentInfo
         * @return
         */
        private BaggageSegmentInfo handleProductCanPurchase
        (BaseReq < QueryPrepaymentBaggageSegmentReq > req, BaggageSegmentInfo segmentInfo){
            BaseResultDTO<CanPayBaggageResp> judgeResp = this.fetchJudgeExtraPro(genCheckCanPayBaggageReq(req.getChannelCode(), segmentInfo, req.getRequest().getFfpCardNo(), req.getRequest().getFfpId()));
            if (judgeResp != null) {
                // 校验产品能否购买
                if (!judgeResp.getResult().getCanBuy()) {
                    segmentInfo.setCanPurchase(false);
                    segmentInfo.setErrorDesc("您的航班暂不支持产品购买，请至机场柜台缴纳行李费");
                    return segmentInfo;
                }
                // 校验产品起飞可购时间时限
                Date depDateTime = DateUtils.toDate(segmentInfo.getDepDate() + " " + segmentInfo.getDepTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                Date dateOutTime = DateUtils.dateAddOrLessSecond(depDateTime, -60 * judgeResp.getResult().getTimeLimit());
                if (dateOutTime.before(new Date())) {
                    segmentInfo.setErrorDesc("航班起飞时间小于" + judgeResp.getResult().getTimeLimit() + "分钟后不支持购买");
                    segmentInfo.setCanPurchase(false);
                }
            }
            return segmentInfo;
        }


        /**
         * 渠道用户编号
         *
         * @param channelCode
         * @param segmentInfo
         * @param ffpCardNo
         * @param ffpId
         * @return
         */
        private BaseRequestDTO<CheckCanPayBaggageReq> genCheckCanPayBaggageReq (String channelCode, BaggageSegmentInfo
        segmentInfo, String ffpCardNo, String ffpId){
            return BaseRequestDTO.<CheckCanPayBaggageReq>builder()
                    .Version("10")
                    .ChannelCode(channelCode)
                    .UserNo(ControllerUtil.getChannelInfo(channelCode, "10"))
                    .FfpId(ffpId)
                    .FfpCardNo(ffpCardNo)
                    .Request(CheckCanPayBaggageReq.builder()
                            .SearchTypes(VoucherTypesEnum.EXTRABAGGAGE.getCode())
                            .SingleBookCondition(SingleBookCondition.builder()
                                    .DepAirportCode(segmentInfo.getDepAirport())
                                    .ArrAirportCode(segmentInfo.getArrAirport())
                                    .DepCity(segmentInfo.getDepCity())
                                    .ArrCity(segmentInfo.getArrCity())
                                    .FlightDate(segmentInfo.getDepDate() +  " " + segmentInfo.getDepTime() + ":00")
                                    .build())
                            .build())
                    .build();
        }

        /**
         * 封装/v2/smngAvailableP/judgeExtraPro通用请求
         *
         * @return
         */
        public BaseResultDTO<CanPayBaggageResp> fetchJudgeExtraPro (BaseRequestDTO < CheckCanPayBaggageReq > requestDTO)
        {

            String requestUrl = HandlerConstants.URL_COUPON_API + HandlerConstants.PRODUCT_CHECK_CANPAY_BAGGAGE;

            HttpResult result = HttpUtil.doPostClient(requestDTO, requestUrl);
            if (!result.isResult()) {
                throw new NetworkException("查询是否购买预付费行李异常");
            }
            BaseResultDTO<CanPayBaggageResp> resultDTO = (BaseResultDTO<CanPayBaggageResp>)
                    JsonUtil.jsonToBean(result.getResponse(), new TypeToken<BaseResultDTO<CanPayBaggageResp>>() {
                    }.getType());
            if ("10001".equals(resultDTO.getResultCode())) {
                return resultDTO;
            }
            return null;
        }

        /**
         * 提取乘客信息
         *
         * @param info
         * @param ticketNum
         * @param ffpNo
         * @return
         */
        private BaggagePassengerInfo extractBaggagePassengerInfo(List<TicketInfoQueryResponse> info, String ticketNum, String ffpNo) {
            BaggagePassengerInfo b = new BaggagePassengerInfo();
            List<TicketInfoQueryResponse> tickets = info.stream().filter(ticketInfoQueryResponse -> ticketInfoQueryResponse.getTicket_ticketnumber().equals(ticketNum))
                    .collect(Collectors.toList());
            TicketInfoQueryResponse ticketInfo = tickets.get(0);
            b.setPassengerName(ticketInfo.getTraveller_name());
            b.setIdNo(ticketInfo.getTraveller_number());
            b.setIdType(ticketInfo.getTraveller_document_type());
            b.setIsOwn(ffpNo.equals(ticketInfo.getMember_card_no()));
            return b;
        }

        /**
         * 判断是否中转航班
         *
         * @param segmentInfos
         * @return
         */
        private boolean whetherConnectingFlight (List < BaggageSegmentInfo > segmentInfos) {
            return segmentInfos.size() > 1 && (!(segmentInfos.get(0).getDepCity().equals(segmentInfos.get(1).getArrCity())));
        }

        /**
         * 判断是否往返航班
         *
         * @param segmentInfos
         * @return
         */
        private boolean whetherRoundTripFlight (List < BaggageSegmentInfo > segmentInfos) {
            return (segmentInfos.size() > 1 && (segmentInfos.get(0).getDepCity().equals(segmentInfos.get(segmentInfos.size() - 1).getArrCity())));
        }

        /**
         * @param req
         * @param info
         * @param ticketNum
         * @param isItineraryEntry
         * @return
         */
        private List<BaggageSegmentInfo> convertTicketInfoQueryResponseToBaggageSegmentInfo
        (BaseReq<QueryPrepaymentBaggageSegmentReq> req, List < TicketInfoQueryResponse > info, String ticketNum, boolean isItineraryEntry){
            try {
                List<BaggageSegmentInfo> segmentInfos = new ArrayList<>();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-ddHH:mm");
                // 同票号按日期时间排序以区分顺序
                List<TicketInfoQueryResponse> sameTicketInfo = info.stream().filter(v -> v.getTicket_ticketnumber().equals(ticketNum))
                        .sorted((v1, v2) -> {
                            try {
                                return sdf.parse(v1.getSegment_depa_date() + v1.getSegment_depa_time())
                                        .compareTo(sdf.parse(v2.getSegment_depa_date() + v2.getSegment_depa_time()));
                            } catch (ParseException e) {
                                throw new RuntimeException(e);
                            }
                        }).collect(Collectors.toList());

                for (int i = 0; i < sameTicketInfo.size(); i++) {
                    TicketInfoQueryResponse ticketInfo = sameTicketInfo.get(i);
                    Date depDate = DateUtils.toDate(ticketInfo.getSegment_depa_date() + " " + ticketInfo.getSegment_depa_time(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                    Date arrDate = DateUtils.toDate(ticketInfo.getSegment_arrival_date() + " " + ticketInfo.getSegment_arrival_time(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                    // tips getLocalAirport方法未处理Date
                    AirPortInfoDto depLocalAirport = localCacheService.getLocalAirport(ticketInfo.getSegment_depa_airportcode(), depDate);
                    AirPortInfoDto arrLocalAirport = localCacheService.getLocalAirport(ticketInfo.getSegment_arrival_airportcode(), arrDate);
                    String depWeek = DateUtils.getWeek(depDate);
                    String arrWeek = DateUtils.getWeek(arrDate);
                    //
                    long flightTime = 0;
                    int durDay = 0;
                    if (depLocalAirport != null && arrLocalAirport != null) {
                        String depCityTimeZone = depLocalAirport.getCityTimeZone();
                        String arrCityTimeZone = arrLocalAirport.getCityTimeZone();
                        //飞行时长
                        if ((!StringUtil.isNullOrEmpty(arrCityTimeZone)) &&
                                (!StringUtil.isNullOrEmpty(depCityTimeZone))) {
                            //添加夏、冬令时处理
                            if (!depCityTimeZone.equals(arrCityTimeZone)) {
                                depCityTimeZone = FlightUtil.convertSummerOrWinterTime(depCityTimeZone, ticketInfo.getSegment_depa_date() + " " + ticketInfo.getSegment_depa_time(), depLocalAirport);
                                arrCityTimeZone = FlightUtil.convertSummerOrWinterTime(arrCityTimeZone, ticketInfo.getSegment_arrival_date() + " " + ticketInfo.getSegment_arrival_time(), arrLocalAirport);
                            }
                            //飞行时长
                            flightTime = DateUtils.calDuration(
                                    ticketInfo.getSegment_depa_date() + " " + ticketInfo.getSegment_depa_time()
                                    , depCityTimeZone,
                                    ticketInfo.getSegment_arrival_date() + " " + ticketInfo.getSegment_arrival_time()
                                    , arrCityTimeZone);
                            //
                            durDay = DateUtils.diffDays(ticketInfo.getSegment_depa_date(), ticketInfo.getSegment_arrival_date(), DateUtils.YYYY_MM_DD_PATTERN);
                        }
                    }
                    // 值机校验
                    String errorDesc = "";
                    if (ticketInfo.getTraveller_checkin_flag().equals("1")) {
                        //数据部门值机状态存在问题，需要重新查询客票 临时方案
                        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), ChannelUtils.getChannelInfo(ChannelCodeEnum.MOBILE.getChannelCode(), "10"));
                        ticketInfoRequest.setTicketNo(ticketInfo.getTicket_ticketnumber());
                        if (isItineraryEntry) {
                            ticketInfoRequest.setPassengerName(ticketInfo.getTraveller_name());
                        } else {
                            ticketInfoRequest.setPassengerName(req.getRequest().getName());
                        }
                        TicketListInfoResponse ticketListInfoResponse = ticketService.listTicketInfo(ticketInfoRequest, null);
                        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                            throw new BusinessException("信息查询异常，请稍候再试");
                        }
                        PtSegmentInfo ptSegmentInfo = ticketListInfoResponse.getIBETicketInfoList().get(0).getSegmentInfoList().stream().filter(segmentInfo ->
                                        segmentInfo.getDepAirportCode().equals(ticketInfo.getSegment_depa_airportcode()) && segmentInfo.getArrAirportCode().equals(ticketInfo.getSegment_arrival_airportcode()))
                                .findFirst().orElse(null);
                        if (ptSegmentInfo != null) {
                            if (ptSegmentInfo.getTicketStatus().equals(HandlerConstants.CHECKED_IN)) {
                                errorDesc = NewCouponController.ERR_CONTENT_PREPAYMENT_BAGGAGE_CHECKED_IN;
                            } else {
                                ticketInfo.setTraveller_checkin_flag("0");
                            }
                        }
                    }
                    if (!ticketInfo.getSegment_opt_airlinecode().equalsIgnoreCase("HO")) {
                        errorDesc = NewCouponController.ERR_CONTENT_PREPAYMENT_BAGGAGE_CODESHARES;
                    }
                    if (ticketInfo.getTraveller_type().equalsIgnoreCase("INF")) {
                        errorDesc = NewCouponController.ERR_CONTENT_PREPAYMENT_BAGGAGE_IS_INF;
                    }
                    if (ticketInfo.getSegment_clazz().equalsIgnoreCase("G")) {
                        errorDesc = NewCouponController.ERR_CONTENT_PREPAYMENT_BAGGAGE_CONNECTING_FLIGHT;
                    }
                    if (!ticketInfo.getTicket_ticketnumber().startsWith("018")) {
                        errorDesc = NewCouponController.ERR_CONTENT_PREPAYMENT_BAGGAGE_NOT_018;
                    }

                    // format机型
                    Map<String, AircraftModel> aircraftModelMap = AVObjectConvert.toAircraftModelMap(handConfig.getAircraftModel());
                    AircraftModel aircraftModel = AVObjectConvert.convertAircraftModel(aircraftModelMap, ticketInfo.getAircraft_type());

                    BaggageSegmentInfo baggageSegmentInfo = BaggageSegmentInfo.builder()
                            .ticketNumber(ticketNum)
                            .arrAirportTerminal(ticketInfo.getSegment_arr_terminal())
                            .depAirportTerminal(ticketInfo.getSegment_depa_terminal())
                            .depAirport(ticketInfo.getSegment_depa_airportcode())
                            .depDate(ticketInfo.getSegment_depa_date())
                            .depTime(ticketInfo.getSegment_depa_time())
                            .arrAirport(ticketInfo.getSegment_arrival_airportcode())
                            .arrDate(ticketInfo.getSegment_arrival_date())
                            .arrTime(ticketInfo.getSegment_arrival_time())
                            // 取了市场航班
                            .airLineCode(ticketInfo.getSegment_car_airlinecode() + ticketInfo.getSegment_car_flightnumber())
                            // 额外处理了机型
                            // see com.juneyaoair.mobile.handler.controller.NewCouponController.copyBaggageSegmentColumn
                            .airLineName(aircraftModel != null ? aircraftModel.getRemark() : null)
                            .travellerName(ticketInfo.getTraveller_name())
                            .travellerNumber(ticketInfo.getTraveller_number())
                            .cabin(ticketInfo.getSegment_clazz())
                            .cabinClass(ticketInfo.getSegment_compartment())
                            .pnrNo(ticketInfo.getBooking_recordlocator())
                            .flightDate(ticketInfo.getSegment_depa_date())
                            // 计算信息
                            .interFlag(this.getInterFlag(depLocalAirport, arrLocalAirport))
                            .cabinClassName(("C".equals(ticketInfo.getSegment_compartment()) || "J".equals(ticketInfo.getSegment_compartment())) ? "公务舱" : "经济舱")
                            .flightTime(formatTime(flightTime))
                            .durDay(Math.max(durDay, 0))
                            .errorDesc(errorDesc)
                            .canPurchase(errorDesc.isEmpty())
                            // 机场信息
                            .depCity(Optional.ofNullable(depLocalAirport.getCityCode()).orElse(""))
                            .depCityName(Optional.ofNullable(depLocalAirport.getCityName()).orElse(""))
                            .depAirportName(Optional.ofNullable(depLocalAirport.getAirPortName()).orElse(""))
                            .depWeekNum(depWeek)
                            .arrCity(arrLocalAirport.getCityCode())
                            .arrCityName(arrLocalAirport.getCityName())
                            .arrAirportName(arrLocalAirport.getAirPortName())
                            .arrWeekNum(arrWeek)
                            // 无效/兼容字段:.planeType().ticketType().ticketStatus()
                            .build();
                    segmentInfos.add(baggageSegmentInfo);
                }
                // 处理航段逻辑信息及不可购情况
                segmentInfos = this.handleMultiSegmentLogicInfo(segmentInfos);
                return segmentInfos;

            } catch (Exception e) {
                log.error("[TicketInfoQueryResponse转换发生错误]", e);
            }
            return new ArrayList<>();
        }

        /**
         * 行程数据ticket_tickettype 实际为客票类型AIRLINE_INTERNATIONAL_ETICKET 且会存在数据为空的情况 ,使用机场判断
         * @param depAirport
         * @param arrAirport
         * @return
         */
        private String getInterFlag(AirPortInfoDto depAirport, AirPortInfoDto arrAirport) {
            String interFlag = HandlerConstants.TRIP_TYPE_D;
            if (HandlerConstants.TRIP_TYPE_I.equals(depAirport.getIsInternational()) || HandlerConstants.TRIP_TYPE_I.equals(arrAirport.getIsInternational())) {
                interFlag = HandlerConstants.TRIP_TYPE_I;
            }
            return interFlag;
        }

        /**
         * 处理多个航段产生的逻辑信息
         * <p>
         * 往返、方向、中转不可购情况
         */
        private List<BaggageSegmentInfo> handleMultiSegmentLogicInfo (List < BaggageSegmentInfo > segmentInfos) {
            //flightDirection 航段方向 G/B
            //flightType 单程/往返  OW/RT
            int segmentQuantity = segmentInfos.size();
            boolean isOWRoute = !this.whetherRoundTripFlight(segmentInfos);
            for (int i = 0; i < segmentInfos.size(); i++) {
                BaggageSegmentInfo b = segmentInfos.get(i);
                b.setFlightDirection(!isOWRoute && (i + 1) > (segmentQuantity / 2) ? FlightDirection.BACK.getCode() : FlightDirection.GO.getCode());
                b.setFlightType(isOWRoute ? "OW" : "RT");
                //中转航班
                if (this.whetherConnectingFlight(segmentInfos)) {
                    b.setCanPurchase(false);
                    b.setErrorDesc(NewCouponController.ERR_CONTENT_PREPAYMENT_BAGGAGE_CONNECTING_FLIGHT);
                }
            }
            return segmentInfos;
        }


        private String formatTime (Long ms){
            Integer ss = 1000;
            Integer mi = ss * 60;
            Integer hh = mi * 60;
            Integer dd = hh * 24;

            Long day = ms / dd;
            Long hour = (ms - day * dd) / hh;
            Long minute = (ms - day * dd - hour * hh) / mi;
            StringBuilder sb = new StringBuilder();
            if (day > 0) {
                sb.append(day + "d");
            }
            if (hour > 0) {
                sb.append(hour + "h");
            }
            if (minute > 0) {
                sb.append(minute + "m");
            }

            return sb.toString();
        }
    }
