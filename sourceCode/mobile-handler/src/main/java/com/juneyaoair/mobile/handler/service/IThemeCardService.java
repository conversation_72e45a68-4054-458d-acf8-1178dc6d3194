package com.juneyaoair.mobile.handler.service;

import com.juneyaoair.baseclass.basicsys.response.AirLineInfoDepCityDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.theme.*;
import com.juneyaoair.baseclass.common.request.RequestData;
import com.juneyaoair.baseclass.common.response.ResponseData;
import com.juneyaoair.baseclass.newcoupon.req.protocol.OrderCouponDto;
import com.juneyaoair.baseclass.theme.QueryActivityInfoReq;
import com.juneyaoair.baseclass.theme.QueryActivityInfoResp;
import com.juneyaoair.baseclass.theme.ThemeAirLine;
import com.juneyaoair.thirdentity.comm.request.BindingSearchRequest;
import com.juneyaoair.thirdentity.comm.request.ThemeCardBaseRequest;
import com.juneyaoair.thirdentity.comm.response.ThemeCardBaseResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/30  17:46.
 */
public interface IThemeCardService {

    /**
     * 查询主题卡支持的航线对
     * @return
     */
    List<AirLineInfoDepCityDto> queryThemeAirLine(ThemeAirLine themeAirLine, Map<String, String> headMap, String channelCode);

    /**
     * 直播航线对配对
     * @return
     */
    List<AirLineInfoDepCityDto> queryActivityAirLine(QueryActivityInfoReq queryActivityInfoReq, String ip);

    /**
     * 次飞卡查询绑定人的信息
     * @param themeCardBaseRequest
     * @return
     */
    ThemeCardBaseResponse redeemBindingList(ThemeCardBaseRequest<BindingSearchRequest> themeCardBaseRequest);


    ResponseData<QueryActivityInfoResp> queryActivityInfo(RequestData<QueryActivityInfoReq> ptRequest);

    /**
     * open票可用航线查询
     * @param baseReq
     * @return
     */
    Map<String,List<CouponFlightInfoDto>> queryOpenProductAirLine(BaseReq<CouponFlightRequest> baseReq);

    List<OrderCouponDto> queryTicketRedeem(String ticketNo, String depCity, String arrCity);
}
