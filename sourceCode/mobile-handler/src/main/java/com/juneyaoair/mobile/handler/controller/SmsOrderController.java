package com.juneyaoair.mobile.handler.controller;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.order.OrderPayStateEnum;
import com.juneyaoair.appenum.order.OrderSortEnum;
import com.juneyaoair.appenum.order.SubOrderTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.AirportWarnDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.flightDynamic.NewFlightStatusRequest;
import com.juneyaoair.baseclass.request.order.query.SmsTicketOutOrderDetailReq;
import com.juneyaoair.baseclass.response.airport.AirPortInfoRespDTO;
import com.juneyaoair.baseclass.response.flightdynamic.FlightInfoResponse;
import com.juneyaoair.baseclass.response.flightdynamic.FlightWeatherInfoEnum;
import com.juneyaoair.baseclass.response.flightdynamic.NewFlightInfo;
import com.juneyaoair.baseclass.response.order.detail.OrderDetailResp;
import com.juneyaoair.baseclass.response.order.detail.SmsOrderDetailSegment;
import com.juneyaoair.baseclass.response.order.detail.SmsTicketOutOrderDetailResp;
import com.juneyaoair.baseclass.response.order.query.OrderBase;
import com.juneyaoair.baseclass.response.order.query.SubOrderResp;
import com.juneyaoair.baseclass.smsorder.req.SmsOrderDetailReq;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.OrderDetailService;
import com.juneyaoair.mobile.handler.controller.service.bean.OrderDetailRequest;
import com.juneyaoair.mobile.handler.controller.util.OrderPayStateConvert;
import com.juneyaoair.mobile.handler.controller.util.TicketOrderConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.sdk.TravellerHttpApi;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.bigdata.request.PtFlightStatusReq;
import com.juneyaoair.thirdentity.request.order.query.PtSubOrderReq;
import com.juneyaoair.utils.DES3;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.SensitiveInfoHider;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> by jiangmingming
 * @date 2019/6/17 14:22
 */
@RequestMapping("/smsOrderService")
@RestController
public class SmsOrderController extends BassController {
    private static final String LOG_INFO_ONE = "查询结果:";
    private static final String LOG_INFO_TWO = "查询网络出错";
    @Autowired
    private IBasicService basicService;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private TravellerHttpApi travellerHttpApi;
    @Autowired
    private OrderManage orderManage;

    private static final String SUCCESS = "000000";

    @InterfaceLog
    @ApiOperation(value = "短信查询订单详情", notes = "短信查询订单详情")
    @RequestMapping(value = "/queryOrderDetailM", method = RequestMethod.POST)
    public BaseResp queryOrderDetailM(@RequestBody @Validated BaseReq<SmsOrderDetailReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            SmsOrderDetailReq detailReq = req.getRequest();
            //校验请求参数
            String channelCode = req.getChannelCode();
            checkOrderDetailReq(detailReq, resp, channelCode);
            if (!resp.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                return resp;
            }
            if (channelCode.equals(ChannelCodeEnum.MWEB.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            //解密customer
            String customerNoSource = "";
            String desKey = EncoderHandler.encodeByMD5("juneyaoair");
            desKey = EncoderHandler.encodeByMD5(desKey);
            //原始明文
            customerNoSource = DES3.des3DecodeECB(desKey, detailReq.getCustomerNo());
            detailReq.setCardNo(detailReq.getCustomerNo());
            detailReq.setCustomerNo(customerNoSource);
            //构建订单概要请求参数
            PtSubOrderReq ptSubOrderReq = createSubOrderReq(detailReq, channelCode);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult serviceResult = doPostClient(ptSubOrderReq, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_SUB_ORDER, headMap);
            if (serviceResult.isResult() && !StringUtil.isNullOrEmpty(serviceResult.getResponse())) {
                SubOrderResp subOrderResp = (SubOrderResp) JsonUtil.jsonToBean(serviceResult.getResponse(), SubOrderResp.class);
                if (!subOrderResp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_INFO_ONE + subOrderResp.getErrorInfo());
                    return resp;
                }
                if (StringUtil.isNullOrEmpty(subOrderResp.getSubtOrderBaseInfoList())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_INFO_ONE + "查无此单!");
                    return resp;
                }
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                String infoIdKey = EncoderHandler.encode("MD5", customerNoSource + key).toUpperCase();
                //此处分类订单类型
                List<OrderBase> subTiccetList = TicketOrderConvert.getSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
                if (CollectionUtils.isNotEmpty(subTiccetList)) {//处理结果
                    //获取订单结果
                    OrderDetailRequest orderDetailRequest = new OrderDetailRequest();
                    BeanUtils.copyNotNullProperties(detailReq, orderDetailRequest);
                    String userNo = getChannelInfo(channelCode, "10");
                    OrderDetailResp orderResult = orderDetailService.getOrderResult(subOrderResp, subTiccetList, ip,
                            channelCode, userNo, orderDetailRequest,"");
                    orderResult.setCustomerNo(customerNoSource);
                    orderResult.setLoginKeyInfo(infoIdKey);
                    orderResult.getOrderPassengerInfoList().parallelStream().forEach(orderPassengerInfo -> orderPassengerInfo.setCertNo(SensitiveInfoHider.hideSensitiveInfo(orderPassengerInfo.getCertNo())));
                    orderResult.getPassengerList().parallelStream().forEach(passenger -> passenger.setCertNo(SensitiveInfoHider.hideSensitiveInfo(passenger.getCertNo())));
                    resp.setObjData(orderResult);
                } else {
                    //处理辅助订单类型
                    OrderDetailResp orderResult = formatAssistOrder(subOrderResp);
                    orderResult.setCustomerNo(customerNoSource);
                    orderResult.setLoginKeyInfo(infoIdKey);
                    resp.setObjData(orderResult);
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(LOG_INFO_TWO);
                return resp;
            }
        } catch (Exception e) {
            log.error("{}请求异常:{}",reqId,e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    /**
     * 处理辅助订单信息
     *
     * @param subOrderResp
     * @return
     */
    private OrderDetailResp formatAssistOrder(SubOrderResp subOrderResp) {
        OrderDetailResp orderDetailResp = new OrderDetailResp();
        orderDetailResp.setOrderNO(subOrderResp.getOrderNo());
        orderDetailResp.setChannelOrderNo(subOrderResp.getChannelOrderNo());
        orderDetailResp.setOrderAmount(subOrderResp.getAmount());
        orderDetailResp.setActualAmount(subOrderResp.getAmount());
        orderDetailResp.setTotalAmount(subOrderResp.getAmount());
        orderDetailResp.setOrderState(subOrderResp.getOrderState());
        OrderPayStateEnum orderPayState = OrderPayStateConvert.convertState(subOrderResp.getOrderState(), subOrderResp.getPayState(), "", null, null);
        orderDetailResp.setOrderPayState(orderPayState.getStateCode());
        orderDetailResp.setOrderPayStateName(orderPayState.getStateDesc());
        orderDetailResp.setBookingDatetime(subOrderResp.getCreateDatetime());
        orderDetailResp.setOrderPassengerInfoList(new ArrayList<>());
        orderDetailResp.setPassengerList(new ArrayList<>());
        orderDetailResp.setOrderSort(OrderSortEnum.AssistOrder.getOrderSort());
        return orderDetailResp;
    }

    /**
     * 构建查询订单概要请求参数
     *
     * @param detailReq
     * @param channelCode
     * @return
     */
    private PtSubOrderReq createSubOrderReq(SmsOrderDetailReq detailReq, String channelCode) {
        PtSubOrderReq req = new PtSubOrderReq(HandlerConstants.VERSION, channelCode, getChannelInfo(channelCode, "10"));
        req.setChannelCode(channelCode);
        req.setChannelOrderNo(detailReq.getChannelOrderNo());
        req.setOrderNo(detailReq.getOrderNo());
        req.setCustomerNo(detailReq.getCustomerNo());
        return req;
    }

    /**
     * 校验参数
     *
     * @param detailReq
     * @param resp
     * @return
     */
    private BaseResp checkOrderDetailReq(SmsOrderDetailReq detailReq, BaseResp resp, String channelCode) {
        String customerNo = detailReq.getCustomerNo();
        String timeStamp = detailReq.getTimeStamp();
        String channelOrderNo = detailReq.getChannelOrderNo();
        String orderNo = detailReq.getOrderNo();
        //校验Key
        String keyStr = "OrderNo=" + orderNo + "&ChannelOrderNo=" + channelOrderNo + "&CustomerNo=" + customerNo + "&Timestamp=" + timeStamp;
        keyStr = EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(keyStr)) + "juneyaoair");
        if (!keyStr.equals(detailReq.getKey())) {
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            return resp;
        }
        //校验20min时长
        Date curTime = new Date();
        Date orderDate = DateUtils.toDate(timeStamp, "yyyyMMddHHmmss");
        if (orderDate == null) {
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            return resp;
        } else {
            Date maxDate = DateUtils.dateAddOrLessSecond(orderDate, 60 * 60);
            if (maxDate.getTime() < curTime.getTime()) {
                resp.setResultInfo(WSEnum.PAYMENT_OVER_TIME.getResultInfo());
                resp.setResultCode(WSEnum.PAYMENT_OVER_TIME.getResultCode());
                return resp;
            }
        }
        if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(channelCode)) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultCode());
            return resp;
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "短信查询出票订单详情", notes = "短信查询出票订单详情")
    @RequestMapping(value = "/queryTicketOutOrderDetail", method = RequestMethod.POST)
    public BaseResp<SmsTicketOutOrderDetailResp> queryTicketOutOrderDetail(@RequestBody @Validated BaseReq<SmsTicketOutOrderDetailReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<SmsTicketOutOrderDetailResp> resp = new BaseResp();
        String ip = getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            String channelCode = req.getChannelCode();
            SmsTicketOutOrderDetailReq detailReq = req.getRequest();
            //校验请求参数
            checkOrderDetailReq(detailReq, resp, channelCode);
            if (!resp.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                return resp;
            }
            if (channelCode.equals(ChannelCodeEnum.MWEB.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }

            //解密customer
            String customerNoSource = "";
            String desKey = EncoderHandler.encodeByMD5("juneyaoair");
            desKey = EncoderHandler.encodeByMD5(desKey);
            //原始明文
            // 由于loginFilter覆盖customerNo,使用EncCustomerNo
            customerNoSource = DES3.des3DecodeECB(desKey, detailReq.getEncCustomerNo());
            detailReq.setCustomerNo(customerNoSource);

            // 验证是否是本人
            if (!detailReq.getCustomerNo().equals(detailReq.getFfpId())) {
                // 不是本人,返回用户订单列表页面
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo("请查看您的订单列表");
                return resp;
            }

            SmsTicketOutOrderDetailResp detailResp = new SmsTicketOutOrderDetailResp();
            //构建订单概要请求参数
            PtSubOrderReq ptSubOrderReq = createSubOrderReq(detailReq, channelCode);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            SubOrderResp subOrderResp = orderManage.getSubOrder(ptSubOrderReq, headMap);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(subOrderResp.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_INFO_ONE + subOrderResp.getErrorInfo());
                return resp;
            }
            if (StringUtil.isNullOrEmpty(subOrderResp.getSubtOrderBaseInfoList())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_INFO_ONE + "查无此单!");
                return resp;
            }
            String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
            String infoIdKey = EncoderHandler.encode("MD5", customerNoSource + key).toUpperCase();
            //此处分类订单类型
            List<OrderBase> subTiccetList = TicketOrderConvert.getSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
            if (CollectionUtils.isNotEmpty(subTiccetList)) {//处理结果
                //获取订单结果
                OrderDetailRequest orderDetailRequest = new OrderDetailRequest();
                BeanUtils.copyNotNullProperties(detailReq, orderDetailRequest);
                String userNo = getChannelInfo(channelCode, "10");
                OrderDetailResp orderResult = orderDetailService.getOrderResult(subOrderResp, subTiccetList, ip,
                        channelCode, userNo, orderDetailRequest,"smsLink");
                orderResult.setCustomerNo(customerNoSource);
                orderResult.setLoginKeyInfo(infoIdKey);
                BeanUtils.copyProperties(orderResult, detailResp);
                detailResp.setSegmentList(BeanUtils.convertList2List(detailResp.getSegmentList(), SmsOrderDetailSegment.class));
                //处理航班信息和机场重要提示信息
                processFlightInfo(detailResp, ip);
                // 处理乘机提醒信息、值机柜台信息
                handleCheckinRemind(detailResp, channelCode, ip);
                detailResp.getOrderPassengerInfoList().parallelStream().forEach(orderPassengerInfo -> orderPassengerInfo.setCertNo(SensitiveInfoHider.hideSensitiveInfo(orderPassengerInfo.getCertNo())));
                detailResp.getPassengerList().parallelStream().forEach(passenger -> passenger.setCertNo(SensitiveInfoHider.hideSensitiveInfo(passenger.getCertNo())));
                resp.setObjData(detailResp);
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            this.logError(resp, reqId, ip, req, e);
            return resp;
        }
    }

    /**
     * 校验参数
     *
     * @param detailReq
     * @param resp
     * @return
     */
    private BaseResp checkOrderDetailReq(SmsTicketOutOrderDetailReq detailReq, BaseResp resp, String channelCode) {
        // 由于loginFilter覆盖customerNo,使用EncCustomerNo
        String customerNo = detailReq.getEncCustomerNo();
        String timeStamp = detailReq.getTimeStamp();
        String channelOrderNo = detailReq.getChannelOrderNo();
        String orderNo = detailReq.getOrderNo();
        //校验Key
        String keyStr = "OrderNo=" + orderNo + "&ChannelOrderNo=" + channelOrderNo + "&CustomerNo=" + customerNo + "&Timestamp=" + timeStamp;
        keyStr = EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(keyStr)) + "juneyaoair");
        if (!keyStr.equals(detailReq.getKey())) {
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            return resp;
        }

        if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(channelCode)) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("请使用浏览器打开此网页");
            return resp;
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    /**
     * 构建查询订单概要请求参数
     *
     * @param detailReq
     * @param channelCode
     * @return
     */
    private PtSubOrderReq createSubOrderReq(SmsTicketOutOrderDetailReq detailReq, String channelCode) {
        PtSubOrderReq req = new PtSubOrderReq(HandlerConstants.VERSION, channelCode, getChannelInfo(channelCode, "10"));
        req.setChannelCode(channelCode);
        req.setChannelOrderNo(detailReq.getChannelOrderNo());
        req.setOrderNo(detailReq.getOrderNo());
        req.setCustomerNo(detailReq.getCustomerNo());
        return req;
    }

    /**
     * 处理航班信息和机场重要提示信息
     *
     * @param detailResp
     */
    private void processFlightInfo(SmsTicketOutOrderDetailResp detailResp, String ip) {
        List<SmsOrderDetailSegment> segmentList = detailResp.getSegmentList();
        List<String> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(segmentList)) {
            for (int i = 0; i < segmentList.size(); i++) {
                SmsOrderDetailSegment segment = segmentList.get(i);
                //处理机场重要提示信息
                if (i == 0) {
                    //出发机场
                    AirPortInfoDto depAirPortInfoDto = localCacheService.getLocalAirport(segment.getDepAirport());
                    List<AirportWarnDto> depAirPortWarnList = depAirPortInfoDto.getAirportWarnList();
                    setWarnList("dep", depAirPortWarnList, list);
                    //到达机场
                    AirPortInfoDto arrAirPortInfoDto = localCacheService.getLocalAirport(segment.getArrAirport());
                    List<AirportWarnDto> arrAirPortWarnList = arrAirPortInfoDto.getAirportWarnList();
                    setWarnList("arr", arrAirPortWarnList, list);
                    detailResp.setWarnList(list);
                }
                //处理航班动态信息，仅可查询本公司航班
                if (segment.realFlightNo().startsWith("HO")) {
                    NewFlightStatusRequest flightStatusRequest = new NewFlightStatusRequest();
                    flightStatusRequest.setFlightDate(segment.getDepDateTime());
                    flightStatusRequest.setDepartureAirport(segment.getDepAirport());
                    flightStatusRequest.setArrivalAirport(segment.getArrAirport());
                    flightStatusRequest.setDepartureCity(segment.getDepCity());
                    flightStatusRequest.setArrivalCity(segment.getArrCity());
                    flightStatusRequest.setFlightNo(segment.realFlightNo());
                    flightStatusRequest.setQueryType("1");
                    flightStatusRequest.setTravelMark(true);
                    setFlightOtherInfo(segment, flightStatusRequest, ip);
                }else if (segment.realFlightNo().startsWith("MU")) {
                    //值机柜
                    segment.setCheckinTable("请以中国东方航空现场安排为准");
                }else if (segment.realFlightNo().startsWith("3U")) {
                    //值机柜
                    segment.setCheckinTable("请以四川航空现场安排为准");
                }
                
            }

        }
    }

    private void setWarnList(String airPortType, List<AirportWarnDto> airPortWarnList, List<String> list) {
        if (CollectionUtils.isNotEmpty(airPortWarnList)) {
            //排序
            airPortWarnList.sort(Comparator.comparing(AirportWarnDto::getSortOrder));
            for (AirportWarnDto airportWarnDto : airPortWarnList) {

                if ("dep".equals(airPortType) && "0".equals(airportWarnDto.getDepArrFlat()) && "N".equals(airportWarnDto.getDeleteFlat())) {
                    list.add(airportWarnDto.getWarnContent());
                }

                if ("arr".equals(airPortType) && "1".equals(airportWarnDto.getDepArrFlat()) && "N".equals(airportWarnDto.getDeleteFlat())) {
                    list.add(airportWarnDto.getWarnContent());
                }

            }
        }
    }

    /**
     * 处理 航班登机提醒/值机柜台
     * <p>
     * flightbase查询机场信息处理
     *
     * @param detailResp
     * @param channelCode
     * @param ip
     */
    private void handleCheckinRemind(SmsTicketOutOrderDetailResp detailResp, String channelCode, String ip) {
        List<SmsOrderDetailSegment> segmentList = detailResp.getSegmentList();
        if (CollectionUtils.isNotEmpty(segmentList)) {
            for (int i = 0; i < segmentList.size(); i++) {
                SmsOrderDetailSegment segment = segmentList.get(i);
                List<AirPortInfoRespDTO> airports = basicService.fetchAirPortInfo(channelCode, ip, segment.getDepAirport(), segment.getDepCity(), "", "1");
                if (!StringUtil.isNullOrEmpty(segment.getCarrierFlightNo())&&segment.getCarrierFlightNo().startsWith("MU")) {
                    segment.setCheckinRemindStr("本次航班停止办理登机牌时间及起飞时间，请以中国东方航空集团有限公司地面服务现场规则为准，建议您提前2.5小时到达机场办理乘机手续");
                    break;
                }
                if (!StringUtil.isNullOrEmpty(segment.getCarrierFlightNo())&&segment.getCarrierFlightNo().startsWith("3U")) {
                    segment.setCheckinRemindStr("本次航班停止办理登机牌时间及起飞时间，请以四川航空地面服务现场规则为准，建议您提前2.5小时到达机场办理乘机手续");
                    break;
                }
                if (airports != null && !airports.isEmpty()) {
                    AirPortInfoRespDTO airport = airports.get(0);
                    //  乘机文案
                    segment.setCheckinRemindStr(this.spliceRemindStr(airport.getCheckinendtime(), airport.getGateCloseDate()));
                    //  值机柜台
                    //segment.setCheckinTable(airport.getIsInternational().equals("D") ? airport.getCheckincounter() : airport.getICheckincounter());
                }
            }
        }
    }


    /**
     * 拼接登机提醒信息
     *
     * e.g. :本次航班将于航班起飞前xx分钟停止办理登机牌，起飞前xx分钟关闭登机口
     * default :本次航班将于航班起飞前60分钟停止办理登机牌，起飞前20分钟关闭登机口
     * @param checkinTime 登机牌停止办理时间
     * @param gateCloseTime 关闭登机口时间
     * @return
     */
    private String spliceRemindStr(String checkinTime, String gateCloseTime) {
        StringBuilder sb = new StringBuilder();
        // 登机牌停止办理时间
        sb.append("本次航班将于航班");
        if (!Strings.isNullOrEmpty(checkinTime)) {
            sb.append(checkinTime + "停止办理登机牌，");
        } else {
            sb.append("起飞前60分钟停止办理登机牌，");
        }
        // 关闭登机口时间
        if (!Strings.isNullOrEmpty(gateCloseTime)) {
            sb.append(gateCloseTime + "关闭登机口");
        } else {
            sb.append("起飞前20分钟关闭登机口");
        }
        return sb.toString();
    }


    private void setFlightOtherInfo(SmsOrderDetailSegment segment, NewFlightStatusRequest
            flightStatusRequest, String ip) {
        List<NewFlightInfo> flightInfos = queryNewFlightInfo(flightStatusRequest, ip);
        //处理航段值机柜、登机口、行李转盘、到达口等参数
        if (CollectionUtils.isNotEmpty(flightInfos)) {
            for (NewFlightInfo flightInfo : flightInfos) {
                if (flightInfo.getDeparture_airport().equalsIgnoreCase(segment.getDepAirport()) &&
                        flightInfo.getArrival_airport().equalsIgnoreCase(segment.getArrAirport())) {
                    //到达机位  进港靠桥
                    segment.setArrBridge(flightInfo.getArr_bridge());
                    //到达口
                    segment.setReachexit(StringUtils.isBlank(flightInfo.getReachexit()) ? "--" : flightInfo.getReachexit());
                    //到达行李转盘
                    segment.setBaggageID(StringUtils.isBlank(flightInfo.getBaggageID()) ? "--" : flightInfo.getBaggageID());
                    //经停行李转盘
                    segment.setStopovertBaggageID(StringUtils.isBlank(flightInfo.getStopovert_baggageID()) ? "--" : flightInfo.getStopovert_baggageID());
                    //登机口
                    segment.setBoardGate(StringUtils.isBlank(flightInfo.getBoardGate()) ? "--" : flightInfo.getBoardGate());
                    //值机柜
                    segment.setCheckinTable(StringUtils.isBlank(flightInfo.getCheckinTable()) ? "--" : flightInfo.getCheckinTable());
                }

            }
        } else {
            //到达口
            segment.setReachexit("--");
            //到达行李转盘
            segment.setBaggageID("--");
            //经停行李转盘
            segment.setStopovertBaggageID("--");
            //登机口
            segment.setBoardGate("--");
            //值机柜
//            segment.setCheckinTable("--");
        }
    }

    /**
     * 获取航班动态信息
     */
    public List<NewFlightInfo> queryNewFlightInfo(NewFlightStatusRequest req, String ip) {
        try {
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(req.getFlightNo());
            ptFlightStatusReq.setFlightDateLocal(DateUtils.dateToString(DateUtils.toDate(req.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN), DateUtils.YYYY_MM_DD_PATTERN));
            ptFlightStatusReq.setDepartureCityCode(req.getDepartureCity());
            ptFlightStatusReq.setArrivalCityCode(req.getArrivalCity());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse resp = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (SUCCESS.equals(resp.getCode())) {
                List<NewFlightInfo> data = resp.getData();
                List<NewFlightInfo> newList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (NewFlightInfo flightInfoResponse : data) {
                        if (getCompareTo(flightInfoResponse.getOntimeRate())) {
                            flightInfoResponse.setOntimeRate("--");
                        }
                        NewFlightInfo info = this.bigdataFlightInfo(flightInfoResponse, ip, flightInfoResponse.getFlight_no(), flightInfoResponse.getFlight_date());
                        newList.add(info);
                    }
                }
                //两段行程合并展示
                /*if (req.isTravelMark()) {
                    List<NewFlightInfo> collect = newList.stream()
                            .filter(s -> (s.getDeparture_airport().equalsIgnoreCase(req.getDepartureAirport()) && s.getArrival_airport().equalsIgnoreCase(req.getArrivalAirport())))
                            .collect(Collectors.toList());
                    if (collect.isEmpty()) {
                        NewFlightInfo info = new NewFlightInfo();
                        for (NewFlightInfo objDatum : newList) {
                            if (objDatum.getDeparture_airport().equalsIgnoreCase(req.getDepartureAirport())) {
                                info.setFlight_no(objDatum.getFlight_no());
                                info.setFlight_date(objDatum.getFlight_date());
                                if (!getCompareTo(objDatum.getOntimeRate())) {
                                    info.setOntimeRate(objDatum.getOntimeRate());
                                } else {
                                    info.setOntimeRate("--");
                                }
                                info.setCheckinTable(objDatum.getCheckinTable());
                                info.setBoardGate(objDatum.getBoardGate());
                                info.setDeptWeatherPic(objDatum.getDeptWeatherPic());
                                info.setDepAirPortName(objDatum.getDepAirPortName());
                                info.setDeparture_airport(objDatum.getDeparture_airport());
                                info.setDeparture_city(objDatum.getDeparture_city());
                                info.setFlightHTerminal(objDatum.getFlightHTerminal());
                                info.setDepStandGate(objDatum.getDepStandGate());
                                info.setDepTimeZone(objDatum.getDepTimeZone());
                                info.setStd(objDatum.getStd());
                                info.setAtd(objDatum.getAtd());
                                info.setEtd(org.apache.commons.lang.StringUtils.isNotBlank(objDatum.getEtd()) ? objDatum.getEtd() : objDatum.getStd());
                                info.setDeptWeather(objDatum.getDeptWeather());
                                info.setDeptTemp(objDatum.getDeptTemp());
                                info.setDeptpm(objDatum.getDeptpm());
                            }
                            if (objDatum.getArrival_airport().equalsIgnoreCase(req.getArrivalAirport())) {
                                info.setArr_bridge(objDatum.getArr_bridge());
                                info.setArrAirPortName(objDatum.getArrAirPortName());
                                info.setArrival_airport(objDatum.getArrival_airport());
                                info.setArrival_city(objDatum.getArrival_city());
                                info.setFlightTerminal(objDatum.getFlightTerminal());
                                info.setArrStandGate(objDatum.getArrStandGate());
                                info.setArrTimeZone(objDatum.getArrTimeZone());
                                info.setSta(objDatum.getSta());
                                info.setAta(objDatum.getAta());
                                info.setEta(org.apache.commons.lang.StringUtils.isNotBlank(objDatum.getEta()) ? objDatum.getEta() : objDatum.getSta());
                                info.setDestWeatherPic(objDatum.getDestWeatherPic());
                                info.setDestWeather(objDatum.getDestWeather());
                                info.setDestTemp(objDatum.getDestTemp());
                                info.setDestpm(objDatum.getDestpm());
                                info.setBaggageID(objDatum.getBaggageID());
                                info.setReachexit(objDatum.getReachexit());
                            }
                        }
                        List<NewFlightInfo> newFlightInfos = new ArrayList<>();
                        if (!org.apache.commons.lang.StringUtils.isBlank(info.getStd()) && !org.apache.commons.lang.StringUtils.isBlank(info.getSta()) && !org.apache.commons.lang.StringUtils.isBlank(String.valueOf(info.getDepTimeZone())) && !org.apache.commons.lang.StringUtils.isBlank(String.valueOf(info.getArrTimeZone()))) {
                            String flyTime = DateUtils.getFlyTime(info.getStd(), String.valueOf(info.getDepTimeZone()), info.getSta(), String.valueOf(info.getArrTimeZone()));
                            info.setFlyTimeLength(flyTime);
                        }
                        //将到达城市的时区转化为出发城市的时区获取预计跨天数
                        int etTimeOver = getEtTimeOver(info.getStd(), info.getSta(), info.getEtd(), info.getEta(), info.getDepTimeZone(), info.getArrTimeZone());
                        info.setEtTimeOver(etTimeOver);
                        //经停航班状态处理
                        info.setFlight_status(getFlightStatus(newList, req));
                        newFlightInfos.add(info);
                        return newFlightInfos;
                    }
                    int etTimeOver = getEtTimeOver(collect.get(0).getStd(), collect.get(0).getSta(), collect.get(0).getEtd(), collect.get(0).getEta(), collect.get(0).getDepTimeZone(), collect.get(0).getArrTimeZone());
                    collect.get(0).setEtTimeOver(etTimeOver);
                    return collect;
                }*/

                Collections.sort(newList);
                return newList;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.info("查询航班动态信息异常，错误信息为：{}", e.getMessage(), e);
            return null;
        }
    }


    /**
     * @Description:
     * @Param:
     * @return: 接口拆解  大数据查询
     * @Author: shenmengyue
     * @Date: 2020/7/01
     */
    private NewFlightInfo bigdataFlightInfo(NewFlightInfo flightInfoResponse, String ip, String
            argFlightNo, String argFlightDate) {
        //大数据参数对象
        //航班延误时间换算
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
        AirPortInfoDto deptAirPoty = localCacheService.getLocalAirport(flightInfoResponse.getDeparture_airport());
        AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(flightInfoResponse.getArrival_airport());
        //前置起飞机场 城市
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getPre_departure_airport())) {
            AirPortInfoDto preDeptAirPoty = localCacheService.getLocalAirport(flightInfoResponse.getPre_departure_airport());
            flightInfoResponse.setPre_depAirPortName(preDeptAirPoty.getAirPortName());
            flightInfoResponse.setPre_departure_city(preDeptAirPoty.getCityName());
        }
        //航班状态
        if ("0".equals(flightInfoResponse.getAdjust_type())) {
            flightInfoResponse.setFlight_status("取消");
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getPre_arrival_airport())) {
            AirPortInfoDto preArrAirPort = localCacheService.getLocalAirport(flightInfoResponse.getPre_arrival_airport());
            flightInfoResponse.setPre_arrAirPortName(preArrAirPort.getAirPortName());
            flightInfoResponse.setPre_arrival_city(preArrAirPort.getCityName());
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_airport())) {
            AirPortInfoDto divertAirPort = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport());
            if (null != divertAirPort) {
                flightInfoResponse.setDivert_airPortName(divertAirPort.getCityName() +
                        divertAirPort.getAirPortName());
            }
        }

        //飞行时长 行程页面
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStd()) && !org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getSta())) {
            String flyTime = DateUtils.getFlyTime(flightInfoResponse.getStd(), deptAirPoty.getCityTimeZone(), flightInfoResponse.getSta(), arrAirPort.getCityTimeZone());
            flightInfoResponse.setFlyTimeLength(flyTime);
        }

        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getSta()) && (flightInfoResponse.getSta().indexOf('-') < 0)) {
            flightInfoResponse.setSta(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getSta());
        }
        String std = flightInfoResponse.getStd().indexOf('-') < 0 ? flightInfoResponse.getFlight_date() + " "
                + flightInfoResponse.getStd() : flightInfoResponse.getStd();
        //计算延误的毫秒值
        try {
            if ("延误".equals(flightInfoResponse.getFlight_status())) {
                Long delayTime = new Date().getTime() - sdf.parse(std).getTime();
                if (delayTime > 0) {
                    flightInfoResponse.setDelayTime(delayTime);
                }
            }
        } catch (ParseException e) {
            log.error("时间转换异常，预飞时间为：{}", std, e);
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStd()) && (flightInfoResponse.getStd().indexOf('-') < 0)) {
            flightInfoResponse.setStd(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getStd());
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getAta()) && (flightInfoResponse.getAta().indexOf('-') < 0)) {
            flightInfoResponse.setAta(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getAta());
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getAtd()) && (flightInfoResponse.getAtd().indexOf('-') < 0)) {
            flightInfoResponse.setAtd(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getAtd());
        }

        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getPre_sta()) && (flightInfoResponse.getPre_sta().indexOf('-') < 0)) {
            flightInfoResponse.setPre_sta(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_sta());
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getPre_std()) && (flightInfoResponse.getPre_std().indexOf('-') < 0)) {
            flightInfoResponse.setPre_std(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_std());
        }

        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getPre_ata()) && (flightInfoResponse.getPre_ata().indexOf('-') < 0)) {
            flightInfoResponse.setPre_ata(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_ata());
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getPre_atd()) && (flightInfoResponse.getPre_atd().indexOf('-') < 0)) {
            flightInfoResponse.setPre_atd(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_atd());
        }
        flightInfoResponse.setDepAirPortName(deptAirPoty.getAirPortName());
        flightInfoResponse.setDeparture_city(deptAirPoty.getCityName());
        flightInfoResponse.setArrAirPortName(arrAirPort.getAirPortName());
        flightInfoResponse.setArrival_city(arrAirPort.getCityName());
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDeptTemp())) {
            flightInfoResponse.setDeptTemp(flightInfoResponse.getDeptTemp().replaceAll("C\\((-)?[0-9]+F\\)", "℃"));
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDestTemp())) {
            flightInfoResponse.setDestTemp(flightInfoResponse.getDestTemp().replaceAll("C\\((-)?[0-9]+F\\)", "℃"));
        }
        //查询机型时 入参对象
        com.juneyaoair.mobile.mongo.entity.FlightInfo flightInfo = new com.juneyaoair.mobile.mongo.entity.FlightInfo();
        flightInfo.setArrAirport(flightInfoResponse.getArrival_airport());
        flightInfo.setDepAirport(flightInfoResponse.getDeparture_airport());
        flightInfo.setFlightDate(flightInfoResponse.getFlight_date());
        flightInfo.setFlightNo(flightInfoResponse.getFlight_no());
        List<com.juneyaoair.mobile.mongo.entity.FlightInfo> queryBaseFlight = basicService.queryFlightInfo(flightInfo);
        if (CollectionUtils.isNotEmpty(queryBaseFlight)) {
            flightInfo = queryBaseFlight.get(0);
            Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
            AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, flightInfo.getPlanType());
            flightInfoResponse.setAirPortInfo("机型" + flightInfo.getPlanType());
            if (aircraftModel != null) {
                flightInfoResponse.setAirPortType(aircraftModel.getRemark());
            }

        }
        FlightWeatherInfoEnum deptWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getDeptWeather());
        FlightWeatherInfoEnum destWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getDestWeather());
        flightInfoResponse.setDeptWeatherPic(deptWeather.getWeatherPictureUrl());
        flightInfoResponse.setDestWeatherPic(destWeather.getWeatherPictureUrl());
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getPre_flight_no())) {
            //存在前序航班，并且前序航班状态为空
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoResponse.getPre_flight_no());
            ptFlightStatusReq.setFlightDateLocal(flightInfoResponse.getFlight_date());
            ptFlightStatusReq.setDepartureAirport(flightInfoResponse.getPre_departure_airport());
            ptFlightStatusReq.setArrivalAirport(flightInfoResponse.getPre_arrival_airport());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse preFlight = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq,null);
            if (SUCCESS.equals(preFlight.getCode())) {
                List<NewFlightInfo> newFlightInfos = preFlight.getData();
                if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                    NewFlightInfo newFlightInfo = newFlightInfos.get(0);
                    if ("0".equals(newFlightInfo.getAdjust_type())) {
                        flightInfoResponse.setPre_flight_status("取消");
                    } else {
                        flightInfoResponse.setPre_flight_status(newFlightInfo.getFlight_status());
                    }
                    flightInfoResponse.setPre_etd(newFlightInfo.getEtd());
                    flightInfoResponse.setPre_eta(newFlightInfo.getEta());
                }
            }
        }
        //经停航班查询
        int cancelAll = 0;
        String flightDate = "";
        if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStopover_station())) {
            AirPortInfoDto stopoverAirPort = localCacheService.getLocalAirport(flightInfoResponse.getStopover_station());
            //机场信息
            if (null != stopoverAirPort) {
                flightInfo.setArrAirport(flightInfoResponse.getStopover_station());
                flightInfo.setDepAirport(flightInfoResponse.getDeparture_airport());
                flightInfo.setFlightDate(argFlightDate);
                flightInfo.setFlightNo(argFlightNo);
                queryBaseFlight = basicService.queryFlightInfo(flightInfo);
                if (CollectionUtils.isNotEmpty(queryBaseFlight)) {
                    flightInfo = queryBaseFlight.get(0);
                    flightInfoResponse.setStopovert_airPortName(stopoverAirPort.getCityName() +
                            stopoverAirPort.getAirPortName() +
                            (org.apache.commons.lang.StringUtils.isBlank(flightInfo.getArrAirportTerminal()) ?
                                    "" : flightInfo.getArrAirportTerminal()));
                } else {
                    flightInfoResponse.setStopovert_airPortName(stopoverAirPort.getCityName() +
                            stopoverAirPort.getAirPortName());
                }
            }
            //A-B段查询有没有备降信息
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoResponse.getFlight_no());
            ptFlightStatusReq.setFlightDateLocal(flightInfoResponse.getFlight_date());
            ptFlightStatusReq.setDepartureAirport(flightInfoResponse.getDeparture_airport());
            ptFlightStatusReq.setArrivalAirport(flightInfoResponse.getStopover_station());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse aToBs = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq,null);
            if (SUCCESS.equals(aToBs.getCode())) {
                List<NewFlightInfo> newFlightInfos = aToBs.getData();
                if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                    NewFlightInfo newFlightInfo = newFlightInfos.get(0);
                    //A-B存在备降
                    if (!org.apache.commons.lang.StringUtils.isBlank(newFlightInfo.getDivert_airport())) {
                        flightInfoResponse.setDivert_airport1(newFlightInfo.getDivert_airport());
                        flightInfoResponse.setDivert_weather1(newFlightInfo.getDivert_weather());
                        flightInfoResponse.setDivert_temp1(newFlightInfo.getDivert_temp());
                        flightInfoResponse.setDivert_pm1(newFlightInfo.getDivert_pm());
                        flightInfoResponse.setDivert_airPortName1(null == localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()) ? "" : localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getCityName() +
                                localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getAirPortName());
                        flightInfoResponse.setDivert_ata1(newFlightInfo.getDivert_ata());
                        flightInfoResponse.setDivert_eta1(newFlightInfo.getDivert_eta());
                        flightInfoResponse.setDivert_etd1(newFlightInfo.getDivert_etd());
                        flightInfoResponse.setDivert_atd1(newFlightInfo.getDivert_atd());
                    }
                    //行李转盘
                    flightInfoResponse.setStopovert_baggageID(newFlightInfo.getBaggageID());
                    flightInfoResponse.setStopovert_Temp(org.apache.commons.lang.StringUtils.isBlank(newFlightInfo.getDestTemp()) ?
                            "" : newFlightInfo.getDestTemp().replaceAll("C\\([0-9]+F\\)", "℃"));
                    flightInfoResponse.setStopovert_weather(newFlightInfo.getDestWeather());
                    flightInfoResponse.setStopovert_pm(newFlightInfo.getDestpm());
                    FlightWeatherInfoEnum stopovertWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getStopovert_weather());
                    flightInfoResponse.setStopovertWeatherPic(stopovertWeather.getWeatherPictureUrl());
                    //经停站的到达时间补充
                    //计划到达
                    if (!org.apache.commons.lang.StringUtils.isBlank(newFlightInfo.getEta())) {
                        //存在预到
                        flightInfoResponse.setStopover_eta(newFlightInfo.getEta());
                    } else {
                        if (org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStopover_eta())) {
                            flightInfoResponse.setStopover_eta(newFlightInfo.getSta());
                        }
                    }
                    //实际到达
                    if (org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStopover_ata())) {
                        flightInfoResponse.setStopover_ata(newFlightInfo.getAta());
                    }
                    if (newFlightInfo.getFlight_status().equals("取消")) {
                        cancelAll++;
                    }
                }
            }
            //B-C段信息不全
            ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoResponse.getFlight_no());
            ptFlightStatusReq.setFlightDateLocal(flightInfoResponse.getFlight_date());
            ptFlightStatusReq.setDepartureAirport(flightInfoResponse.getStopover_station());
            ptFlightStatusReq.setArrivalAirport(flightInfoResponse.getArrival_airport());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse bToCs = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq,null);
            if (SUCCESS.equals(bToCs.getCode())) {
                List<NewFlightInfo> newFlightInfos = bToCs.getData();
                if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                    NewFlightInfo newFlightInfo = newFlightInfos.get(0);
                    //B-C存在备降
                    if (!org.apache.commons.lang.StringUtils.isBlank(newFlightInfo.getDivert_airport())) {
                        flightInfoResponse.setDivert_airport2(newFlightInfo.getDivert_airport());
                        flightInfoResponse.setDivert_weather2(newFlightInfo.getDivert_weather());
                        flightInfoResponse.setDivert_temp2(newFlightInfo.getDivert_temp());
                        flightInfoResponse.setDivert_pm2(newFlightInfo.getDivert_pm());
                        flightInfoResponse.setDivert_airPortName2(null == localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()) ? "" : localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getCityName() +
                                localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getAirPortName());
                        flightInfoResponse.setDivert_ata2(newFlightInfo.getDivert_ata());
                        flightInfoResponse.setDivert_eta2(newFlightInfo.getDivert_eta());
                        flightInfoResponse.setDivert_etd2(newFlightInfo.getDivert_etd());
                        flightInfoResponse.setDivert_atd2(newFlightInfo.getDivert_atd());
                    }
                    //B-C缺少信息
                    flightInfoResponse.setStopovert_boardGate(newFlightInfo.getBoardGate());
                    flightInfoResponse.setStopovert_checkinTable(newFlightInfo.getCheckinTable());
                    flightInfoResponse.setStopovert_Temp(org.apache.commons.lang.StringUtils.isBlank(newFlightInfo.getDeptTemp()) ? "" :
                            newFlightInfo.getDeptTemp().replaceAll("C\\([0-9]+F\\)", "℃"));
                    flightInfoResponse.setStopovert_weather(newFlightInfo.getDeptWeather());
                    flightInfoResponse.setStopovert_pm(newFlightInfo.getDeptpm());
                    FlightWeatherInfoEnum stopovertWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getStopovert_weather());
                    flightInfoResponse.setStopovertWeatherPic(stopovertWeather.getWeatherPictureUrl());
                    //经停站的起飞时间补充
                    if (!org.apache.commons.lang.StringUtils.isBlank(newFlightInfo.getEtd())) {
                        flightInfoResponse.setStopover_etd(newFlightInfo.getEtd());
                    } else {
                        if (org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStopover_etd())) {
                            flightInfoResponse.setStopover_etd(newFlightInfo.getStd());
                        }
                    }
                    if (org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStopover_atd())) {
                        flightInfoResponse.setStopover_atd(newFlightInfo.getAtd());
                    }
                    if (newFlightInfo.getFlight_status().equals("取消")) {
                        cancelAll++;
                    }
                }
            }
            if (cancelAll == 2) {
                flightInfoResponse.setIs_interline("N");
            }
            //有经停，时间转换
            AirPortInfoDto stopAirPort = localCacheService.getLocalAirport(flightInfoResponse.getStopover_station());
            if (!org.apache.commons.lang.StringUtils.isBlank(stopAirPort.getCityTimeZone().trim())) {
                int timeZone = Integer.parseInt(stopAirPort.getCityTimeZone().trim());
                flightInfoResponse.setStopTimeZone(timeZone);
                //不是中国地区需要转换时间
                if (timeZone != 8) {
                    //经停航班的预计/计划起飞时间
                    flightDate = flightInfoResponse.getStopover_etd();
                    flightInfoResponse.setStopover_etd(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStopover_atd())) {
                        //经停航班的实际起飞时间
                        flightDate = flightInfoResponse.getStopover_atd();
                        flightInfoResponse.setStopover_atd(flightDate);
                    }
                    //经停航班的计划/预计降落时间
                    flightDate = flightInfoResponse.getStopover_eta();
                    flightInfoResponse.setStopover_eta(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getStopover_ata())) {
                        //经停航班的实际降落时间
                        flightDate = flightInfoResponse.getStopover_ata();
                        flightInfoResponse.setStopover_ata(flightDate);
                    }
                }
            }
            //第一段备降地时间修改
            AirPortInfoDto divertAirPort1 = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport1());
            if (null != divertAirPort1 && !org.apache.commons.lang.StringUtils.isBlank(divertAirPort1.getCityTimeZone().trim())) {
                int timeZone1 = Integer.parseInt(divertAirPort1.getCityTimeZone().trim());
                //非中国地区需要转换时间
                if (timeZone1 != 8) {
                    //经停航班前半段备降预计/计划起飞时间
                    flightDate = flightInfoResponse.getDivert_etd1();
                    flightInfoResponse.setDivert_etd1(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_atd1())) {
                        //经停航班前半段实际起飞时间
                        flightDate = flightInfoResponse.getDivert_atd1();
                        flightInfoResponse.setDivert_atd1(flightDate);
                    }
                    //经停航班前半段计划/预计降落时间
                    flightDate = flightInfoResponse.getDivert_eta1();
                    flightInfoResponse.setDivert_eta1(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_ata1())) {
                        //经停航班前半段实际降落时间
                        flightDate = flightInfoResponse.getDivert_ata1();
                        flightInfoResponse.setDivert_ata1(flightDate);
                    }
                }
            }
            //第二段备降地时间修改
            AirPortInfoDto divertAirPort2 = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport2());
            if (null != divertAirPort2 && !org.apache.commons.lang.StringUtils.isBlank(divertAirPort2.getCityTimeZone().trim())) {
                int timeZone2 = Integer.parseInt(divertAirPort2.getCityTimeZone().trim());
                if (timeZone2 != 8) {
                    //经停航班后半段备降预计/计划起飞时间
                    flightDate = flightInfoResponse.getDivert_etd2();
                    flightInfoResponse.setDivert_etd2(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_atd2())) {
                        //经停航班后半段备降实际起飞时间
                        flightDate = flightInfoResponse.getDivert_atd2();
                        flightInfoResponse.setDivert_atd2(flightDate);
                    }
                    //经停航班后半段备降预计/计划降落时间
                    flightDate = flightInfoResponse.getDivert_eta2();
                    flightInfoResponse.setDivert_eta2(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_ata2())) {
                        //经停航班后半段备降实际降落时间
                        flightDate = flightInfoResponse.getDivert_ata2();
                        flightInfoResponse.setDivert_ata2(flightDate);
                    }
                }
            }
        } else if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_airport())) {
            //直接的备降，时间转换
            AirPortInfoDto divertAirPort = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport());
            if (!org.apache.commons.lang.StringUtils.isBlank(divertAirPort.getCityTimeZone().trim())) {
                int timeZone = Integer.parseInt(divertAirPort.getCityTimeZone().trim());
                if (timeZone != 8) {
                    //备降航班预计/计划起飞时间
                    flightDate = flightInfoResponse.getDivert_etd();
                    flightInfoResponse.setDivert_etd(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_atd())) {
                        //备降航班实际起飞时间
                        flightDate = flightInfoResponse.getDivert_atd();
                        flightInfoResponse.setDivert_atd(flightDate);
                    }
                    //备降航班预计/计划降落时间
                    flightDate = flightInfoResponse.getDivert_eta();
                    flightInfoResponse.setDivert_eta(flightDate);
                    if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getDivert_ata())) {
                        //备降航班实际降落时间
                        flightDate = flightInfoResponse.getDivert_ata();
                        flightInfoResponse.setDivert_ata(flightDate);
                    }
                }
            }
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(deptAirPoty.getCityTimeZone().trim())) {
            int depTimeZone = Integer.parseInt(deptAirPoty.getCityTimeZone().trim());
            flightInfoResponse.setDepTimeZone(depTimeZone);
            if (depTimeZone != 8) {
                //航班计划起飞时间
                flightDate = flightInfoResponse.getStd();
                flightInfoResponse.setStd(flightDate);
                if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getEtd())) {
                    //航班预计起飞时间
                    flightDate = flightInfoResponse.getEtd();
                    flightInfoResponse.setEtd(flightDate);
                }
                if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getAtd())) {
                    //航班实际起飞时间
                    flightDate = flightInfoResponse.getAtd();
                    flightInfoResponse.setAtd(flightDate);
                }
            }
        }
        if (!org.apache.commons.lang.StringUtils.isBlank(arrAirPort.getCityTimeZone().trim())) {
            int arrTimeZone = Integer.parseInt(arrAirPort.getCityTimeZone().trim());
            flightInfoResponse.setArrTimeZone(arrTimeZone);
            if (arrTimeZone != 8) {
                //计划到达时间
                flightDate = flightInfoResponse.getSta();
                flightInfoResponse.setSta(flightDate);
                if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getEta())) {
                    //预计到达时间
                    flightDate = flightInfoResponse.getEta();
                    flightInfoResponse.setEta(flightDate);
                }
                if (!org.apache.commons.lang.StringUtils.isBlank(flightInfoResponse.getAta())) {
                    //实际到达时间
                    flightDate = flightInfoResponse.getAta();
                    flightInfoResponse.setAta(flightDate);
                }
            }
        }
        return flightInfoResponse;
    }


    private int getEtTimeOver(String std, String sta, String etd, String eta, int deptZone, int arrZone) {
        int etTimeOver = 0;

        String dept = String.valueOf(deptZone);
        String arr = String.valueOf(arrZone);
        if (!StringUtil.isNullOrEmpty(etd) && !StringUtil.isNullOrEmpty(eta)) {
            Date date = DateUtils.toTargetDate(eta, arr, dept);
            String dateString = DateUtils.getDateString(date);
            int etTime = DateUtils.dateDiff(etd, dateString, "yyyy-MM-dd") - 1;
            etTimeOver = etTime < 0 ? 0 : etTime;
        } else if (!StringUtil.isNullOrEmpty(std) && !StringUtil.isNullOrEmpty(sta)) {
            Date date = DateUtils.toTargetDate(sta, arr, dept);
            String dateString = DateUtils.getDateString(date);
            int etTime = DateUtils.dateDiff(std, dateString, "yyyy-MM-dd") - 1;
            etTimeOver = etTime < 0 ? 0 : etTime;
        }

        return etTimeOver;
    }

    /**
     * @Description: 经停航班状态设置
     * @Param: No such property: code for class: Script1
     * @return:
     * @Author: zhangwanli
     * @Date: 2019/11/14
     */
    private String getFlightStatus(List<NewFlightInfo> flightInfo, NewFlightStatusRequest requestBaseResp) {
        String stopFlightStatus = null;
        //参数非null
        if (CollectionUtils.isNotEmpty(flightInfo) && null != requestBaseResp) {
            for (NewFlightInfo objDatum : flightInfo) {
                //找到第一段航班
                if (objDatum.getDeparture_airport().equalsIgnoreCase(requestBaseResp.getDepartureAirport())) {
                    stopFlightStatus = objDatum.getFlight_status();
                    for (NewFlightInfo flightInfos : flightInfo) {
                        //找到第二段航班
                        if (flightInfos.getArrival_airport().equalsIgnoreCase(requestBaseResp.getArrivalAirport())) {
                            if ("到达".equalsIgnoreCase(stopFlightStatus) && "计划".equalsIgnoreCase(objDatum.getFlight_status())) {
                                stopFlightStatus = "经停中";
                                return stopFlightStatus;
                            } else if ("计划".equalsIgnoreCase(objDatum.getFlight_status())) {
                                return stopFlightStatus;
                            } else {
                                stopFlightStatus = objDatum.getFlight_status();
                                return stopFlightStatus;
                            }
                        }
                    }
                }
            }
        }
        return stopFlightStatus;
    }

    private boolean getCompareTo(String param) {
        boolean result = false;
        if (!StringUtil.isNullOrEmpty(param)) {
            if (param.length() > 5 && handConfig.getOnTimeRate().length() > 5) {
                String substring = param.substring(0, 5);
                String substring1 = handConfig.getOnTimeRate().substring(0, 5);
                BigDecimal dataA = new BigDecimal(substring);
                BigDecimal dataB = new BigDecimal(substring1);
                int i = dataA.compareTo(dataB);
                if (i < 0) {
                    result = true;
                }
            } else {
                result = true;
            }
        }
        return result;
    }


}
