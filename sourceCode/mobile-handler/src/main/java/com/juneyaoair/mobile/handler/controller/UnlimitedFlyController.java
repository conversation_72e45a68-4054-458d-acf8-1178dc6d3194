package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.salecoupon.request.ProductQuery;
import com.juneyaoair.baseclass.salecoupon.response.QueryProductAvailCount;
import com.juneyaoair.baseclass.salecoupon.response.QueryProductStock;
import com.juneyaoair.baseclass.unlimit.UnlimitedCard2Config;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.IMemberCacheService;
import com.juneyaoair.mobile.handler.service.IProductCacheService;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ProductInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.request.QueryPurchasableRequestDto;
import com.juneyaoair.thirdentity.salecoupon.v2.response.QueryPurchasableResponseDto;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @ClassName UnlimitedFlyController
 * <AUTHOR>
 * @Description
 * @Date 2020-11-25 17:22
 **/
@RequestMapping("/unlimited/fly")
@RestController
@Api(value = "UnlimitedFlyController", tags = {"无限卡V2服务"})
public class UnlimitedFlyController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private OrderManage orderManage;
    @Autowired
    private IProductCacheService productCacheService;
    @Autowired
    private IMemberCacheService memberCacheService;

    @ApiOperation(value = "查询产品库存信息和实名认证状态", notes = "查询产品库存信息和实名认证状态")
    @RequestMapping(value = "/getProductStockAndRealName", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp getProductStockAndRealName(@RequestBody BaseReq<ProductQuery> req, HttpServletRequest request){
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        this.validateRequest(req);
        ProductQuery productQuery = req.getRequest();
        try {
            if (!StringUtil.isNullOrEmpty(productQuery.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(productQuery.getFfpId(), productQuery.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            String channelCode = req.getChannelCode();
            String userNo = this.getChannelInfo(channelCode, "10");
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
            QueryProductStock queryProductStock = new QueryProductStock();
            if(unlimitedCard2Config.isQueryProductStockFlag()){
                List<ProductInfo> productInfos = productCacheService.obtainProductCache(productQuery.getProductType(), channelCode, ip);
                if(CollectionUtils.isNotEmpty(productInfos)){
                    String  productNum = productInfos.get(0).getProducts().get(0).getProductNum();
                    //查询产品库存
                    PtRequest ptRequest = createProductQuery(channelCode, userNo, productQuery, productNum, productQuery.getProductType());
                    //QueryStock-查剩余库存 QueryPurchasable -查可购数
                    ptRequest.setSearchType("QueryStock");
                    QueryPurchasableResponseDto queryPurchasableResponseDto = queryRemnantQuantity(ptRequest, headMap, resp);
                    if(queryPurchasableResponseDto != null) {
                        if(queryPurchasableResponseDto.getStockStatus() != null){
                            queryProductStock.setAvailPrdStockFlag(queryPurchasableResponseDto.getStockStatus());
                        }else{
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
                            return resp;
                        }
                    }else{
                        return resp;
                    }
                }else{
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setResultInfo("暂无可售产品");
                    return resp;
                }
            }else{
                queryProductStock.setAvailPrdStockFlag(true);
            }
            //获取会员实名认证状态
            Boolean realName = memberCacheService.queryMemberRealName(productQuery.getFfpId(), channelCode);
            queryProductStock.setVerifyStatus(realName);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(queryProductStock);
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            log.error("查询产品库存信息和实名认证状态出错",e);
            return resp;
        }
    }

    @ApiOperation(value = "查询用户可购买数量", notes = "查询用户可购买数量")
    @RequestMapping(value = "/getProductRemaindCount", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp getProductStock(@RequestBody BaseReq<ProductQuery> req, HttpServletRequest request){

        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        this.validateRequest(req);
        ProductQuery productQuery = req.getRequest();
        try {
            if (!StringUtil.isNullOrEmpty(productQuery.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(productQuery.getFfpId(), productQuery.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            String channelCode = req.getChannelCode();
            String userNo = this.getChannelInfo(channelCode, "10");
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            QueryProductAvailCount queryProductAvailCount =new QueryProductAvailCount();
            List<ProductInfo> productInfos = productCacheService.obtainProductCache(productQuery.getProductType(), channelCode, ip);
            if(CollectionUtils.isNotEmpty(productInfos)){
                String  productNum = productInfos.get(0).getProducts().get(0).getProductNum();
                //查询产品库存
                PtRequest ptRequest = createProductQuery(channelCode, userNo, productQuery, productNum, productQuery.getProductType());
                //QueryStock-查剩余库存 QueryPurchasable -查可购数
                ptRequest.setSearchType("QueryPurchasable");
                QueryPurchasableResponseDto queryPurchasableResponseDto = queryRemnantQuantity(ptRequest, headMap, resp);
                if(queryPurchasableResponseDto != null ) {
                    if(queryPurchasableResponseDto.getRemaindCount() != null){
                        queryProductAvailCount.setRemaindCount(queryPurchasableResponseDto.getRemaindCount());
                    }else{
                        queryProductAvailCount.setRemaindCount(10);
                    }
                }else{
                    return resp;
                }
            }else{
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo("暂无可售产品");
                return resp;
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(queryProductAvailCount);
            return resp;

        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            log.error("查询用户可购买数量",e);
            return resp;
        }
    }

    @RequestMapping(value = "clearCache", method = RequestMethod.GET)
    @InterfaceLog
    public BaseResp recordQuery(HttpServletRequest request){
        BaseResp<String> resp = new BaseResp<>();
        productCacheService.clearCache();
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    private PtRequest createProductQuery(String channelCode, String userNo, ProductQuery productQuery, String productNum, String resourceType) {
        PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
        ptRequest.setFfpId(productQuery.getFfpId());
        ptRequest.setFfpCardNo(productQuery.getFfpCardNo());
        QueryPurchasableRequestDto requestDto = new QueryPurchasableRequestDto();
        requestDto.setProductNum(productNum);
        requestDto.setResourceType(resourceType);
        ptRequest.setRequest(requestDto);
        return ptRequest;
    }

    //获取产品库存信息和可购买数量
    private QueryPurchasableResponseDto queryRemnantQuantity(PtRequest ptRequest,  Map<String, String> headMap,BaseResp resp ){
        PtResponse<QueryPurchasableResponseDto> ptResponse = orderManage.queryRemnantQuantity(ptRequest, headMap);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            QueryPurchasableResponseDto responseDto = ptResponse.getResult();
            if (responseDto != null) {
                return responseDto;
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo("暂无可售产品");
            }
        } else {
            if(UnifiedOrderResultEnum.R1005.getResultCode().equals(ptResponse.getResultCode())){
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            }else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("小吉正在努力搬砖中，请您稍后再试");
            }
        }
        return null;
    }

}
