package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.AddressTypeEnum;
import com.juneyaoair.appenum.order.CouponProductTypeEnum;
import com.juneyaoair.appenum.order.VisaApplyPersonTypeEnum;
import com.juneyaoair.baseclass.airportcoach.req.QueryProduct;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.response.CountryInfo;
import com.juneyaoair.baseclass.member.response.ProvinceInfo;
import com.juneyaoair.baseclass.newcoupon.bean.*;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductBuyRequest;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductQueryRequestDto;
import com.juneyaoair.baseclass.newcoupon.resp.CouponProductBuyResponse;
import com.juneyaoair.baseclass.newcoupon.resp.SearchProductResponse;
import com.juneyaoair.baseclass.response.province.CityInfo;
import com.juneyaoair.baseclass.visa.bean.PassengerInfo;
import com.juneyaoair.baseclass.visa.bean.VISAExtInfo;
import com.juneyaoair.baseclass.visa.bean.VisaProduct;
import com.juneyaoair.baseclass.visa.bean.VisaProductInfo;
import com.juneyaoair.baseclass.visa.req.BuyVisaProduct;
import com.juneyaoair.baseclass.visa.req.CouponProductVisaDictionariesRequestDto;
import com.juneyaoair.baseclass.visa.resp.CouponProductVisaDictionariesResponseDto;
import com.juneyaoair.baseclass.visa.resp.QueryVisaProduct;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.controller.v2.util.RightCouponConvert;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.EntryType;
import com.juneyaoair.mobile.webservice.client.crm.GetDataInfoResponseForClient;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.*;


/**
 * <AUTHOR> by jiangmingming
 * @date 2019/4/28 10:40
 */
@RequestMapping("/applyVisa")
@RestController
@Api(value = "签证服务", description = "签证服务")
public class VisaController extends BassController {
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private CrmWSClient crmClient;

    @ApiOperation(value = "签证产品查询", notes = "签证产品查询")
    @RequestMapping(value = "/queryVisaProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryVisaProduct(HttpServletRequest request, @RequestBody BaseReq<QueryProduct> req) {
        BaseResp resp = new BaseResp();
        String ip = getClientIP(request);
        String reqId = StringUtil.newGUID() + "_queryVisaProduct";
        long t1 = System.currentTimeMillis();
        try {
            QueryProduct queryProduct = req.getRequest();
            //构建请求类
            CouponProductQueryRequestDto productRequest = getCouponProductQueryRequestDto(req, queryProduct);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            //发起请求
            HttpResult result = doPostClient(productRequest, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_QUERY_PRODUCT_V2, headMap);
            if (result.isResult()) {
                //处理结果
                getVisaQueryResponse(req, resp, headMap, result);
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常");
            }
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，服务端异常：", reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            log.info("【签证产品查询】IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}", ip, JsonMapper.buildNormalMapper().toJson(req), System.currentTimeMillis() - t1, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    /**
     * 对查询产品结果进行处理
     * @param req
     * @param resp
     * @param headMap
     * @param result
     */
    public void getVisaQueryResponse(@RequestBody BaseReq<QueryProduct> req, BaseResp resp, Map<String, String> headMap, HttpResult result) {
        SearchProductResponse response = (SearchProductResponse) JsonUtil.jsonToBean(result.getResponse(), SearchProductResponse.class);
        if ("1001".equals(response.getResultCode())) {
            SearchProductResponse.ProductInfo[] productList = response.getProductList();
            if (productList.length == 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("暂无相关产品");
            } else {
                //存在产品
                //调用字典值接口，获取签证相关的字典说明，转换给前端
                Map<String, String> visaMaterial = new HashMap<>();
                Map<String, String> visaCustomer = new HashMap<>();
                Map<String, String> visaType = new HashMap<>();
                //构建查询字典值接口请求参数
                CouponProductVisaDictionariesRequestDto requestDto = getCouponProductVisaDictionariesRequestDto(req);
                HttpResult typeResult = doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_VISA_DICT, headMap);
                if (typeResult.isResult()) {
                    //处理响应结果
                    CouponProductVisaDictionariesResponseDto responseDto = (CouponProductVisaDictionariesResponseDto) JsonUtil.jsonToBean(typeResult.getResponse(), CouponProductVisaDictionariesResponseDto.class);
                    Map<String, List<CouponProductVisaDictionariesResponseDto.SimpleTreeNode>> data = responseDto.getData();
                    Map<String, Map<String, String>> stringMapMap = RightCouponConvert.getStringMapMap(data);
                    visaMaterial = stringMapMap.get(CouponProductTypeEnum.DICT_VISA_MATERIAL.getCode());
                    visaCustomer = stringMapMap.get(CouponProductTypeEnum.DICT_VISA_CUSTOMER.getCode());
                    visaType = stringMapMap.get(CouponProductTypeEnum.DICT_VISA_TYPE.getCode());
                }
                QueryVisaProduct queryVisaProduct = new QueryVisaProduct();
                Map<String, VisaProductInfo> map = new HashMap<>();
                //存储产品信息给前端
                saveVisaProductInfo(productList, visaMaterial, visaCustomer, visaType, map);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                queryVisaProduct.setVisaProductInfoMap(map);
                resp.setObjData(queryVisaProduct);
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(response.getErrorInfo());
        }
    }

    /**
     * 存储产品信息
     * @param productList
     * @param visaMaterial
     * @param visaCustomer
     * @param visaType
     * @param map
     */
    public void saveVisaProductInfo(SearchProductResponse.ProductInfo[] productList, Map<String, String> visaMaterial, Map<String, String> visaCustomer, Map<String, String> visaType, Map<String, VisaProductInfo> map) {
        for (int i = 0; i < productList.length; i++) {
            //储存产品信息
            VisaProductInfo visaProductInfo = new VisaProductInfo();
            BeanUtils.copyNotNullProperties(productList[i], visaProductInfo);
            visaProductInfo.setPrice(productList[i].getMinPrice());
            visaProductInfo.setSaleCount(productList[i].getSaledCount());
            long time = System.currentTimeMillis();
            String startDate = DateUtils.timeStampToDateStr(time, "yyyy-MM-dd");
            long endTime = time + (long) Integer.parseInt(productList[i].getVaildityDays()) * 24 * 60 * 60 * 1000;
            String endDate = DateUtils.timeStampToDateStr(endTime, "yyyy-MM-dd");
            visaProductInfo.setUseStartDate(startDate);
            visaProductInfo.setUseEndDate(endDate);
            visaProductInfo.setProductName(productList[i].getProductName() + "个人旅游签证");
            SearchProductResponse.ProductInfo.RescourceInfo[] resourceList = productList[i].getResourceList();
            for (int j = 0; j < resourceList.length; j++) {
                BeanUtils.copyNotNullProperties(resourceList[j], visaProductInfo);
                visaProductInfo.setResourceId(resourceList[j].getResourceId());
                visaProductInfo.setResourceType(resourceList[j].getResourceType());
                visaProductInfo.setOriginPrice(resourceList[j].getStandardPrice());
                SearchProductResponse.ProductInfo.RescourceInfo.VISAMaterialExtInfo[] visaMaterialExt = resourceList[j].getVISAMaterialExt();
                //对人员材料按照类型排序
                List<SearchProductResponse.ProductInfo.RescourceInfo.VISAMaterialExtInfo> visaMaterialExtInfos = Arrays.asList(visaMaterialExt);
                visaMaterialExtInfos.sort(Comparator.comparingInt(a -> Integer.parseInt(a.getCustomerType())));
                SearchProductResponse.ProductInfo.RescourceInfo.VISAMaterialExtInfo[] objects = visaMaterialExtInfos.toArray(new SearchProductResponse.ProductInfo.RescourceInfo.VISAMaterialExtInfo[0]);
                for (int m = 0; m < objects.length; m++) {
                    visaMaterialExt[m].setCustomerTypeName(visaCustomer.get(visaMaterialExt[m].getCustomerType()));
                    MaterialExt[] materialTypeInfo = visaMaterialExt[m].getMaterialTypeInfo();
                    for (int n = 0; n < materialTypeInfo.length; n++) {
                        materialTypeInfo[n].setMaterialTypeName(visaMaterial.get(materialTypeInfo[n].getMaterialType()));
                    }
                    visaMaterialExt[m].setMaterialTypeInfo(materialTypeInfo);
                }
                visaProductInfo.setVisaMaterialExtInfo(visaMaterialExt);
                VISAExtInfo visaExtInfo = new VISAExtInfo();
                BeanUtils.copyNotNullProperties(resourceList[j].getVISAExt(), visaExtInfo);
                String[] split = visaExtInfo.getApplyProcess().split(";");
                visaExtInfo.setProcesses(split);
                visaExtInfo.setVisaType(visaType.get(resourceList[j].getVISAExt().getVisaType()));
                visaProductInfo.setVisaExtInfo(visaExtInfo);
            }
            map.put(productList[i].getProductName(), visaProductInfo);
        }
    }

    /**
     * 构建字典值接口请求参数
     * @param req
     * @return
     */
    public CouponProductVisaDictionariesRequestDto getCouponProductVisaDictionariesRequestDto(@RequestBody BaseReq<QueryProduct> req) {
        CouponProductVisaDictionariesRequestDto requestDto = new CouponProductVisaDictionariesRequestDto();
        requestDto.setChannelCode(req.getChannelCode());
        requestDto.setUserNo(HandlerConstants.B2C_USER_NO);
        requestDto.setVersion(HandlerConstants.VERSION);
        String[] codes = {CouponProductTypeEnum.DICT_VISA_MATERIAL.getCode(), CouponProductTypeEnum.DICT_VISA_CUSTOMER.getCode(), CouponProductTypeEnum.DICT_VISA_TYPE.getCode()};
        requestDto.setDictCodes(codes);
        return requestDto;
    }

    /**
     * 构建查询签证产品请求参数
     * @param req
     * @param queryProduct
     * @return
     */
    public CouponProductQueryRequestDto getCouponProductQueryRequestDto(@RequestBody BaseReq<QueryProduct> req, QueryProduct queryProduct) {
        CouponProductQueryRequestDto productRequest = new CouponProductQueryRequestDto();
        BeanUtils.copyNotNullProperties(queryProduct, productRequest);
        productRequest.setPageNo(1);
        productRequest.setPageSize(20);
        SortCondition condition = new SortCondition();
        condition.setSortDirection("Asc");
        condition.setSortField("Default");
        productRequest.setSortCondition(condition);
        SingleBookCondition singleBookCondition = new SingleBookCondition();
        productRequest.setSingleBookCondition(singleBookCondition);
        productRequest.setSearchTypes("VISA");
        String channelCode = req.getChannelCode();
        productRequest.setVersion(HandlerConstants.VERSION);
        productRequest.setChannelCode(channelCode);
        String userNo = getChannelInfo(channelCode, "10");
        productRequest.setUserNo(userNo);
        return productRequest;
    }

    @ApiOperation(value = "签证产品购买", notes = "签证产品购买")
    @RequestMapping(value = "/buyVisaProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp buyVisaProduct(HttpServletRequest request, @RequestBody BaseReq<BuyVisaProduct> req) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_buyBaggageWifi";
        String ip = getClientIP(request);
        long t1 = System.currentTimeMillis();
        try {
            String reqJson = JsonUtil.objectToJson(req);
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, reqJson);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String reqString = JsonMapper.buildNormalMapper().toJson(req);
                String respString = JsonUtil.objectToJson(resp);
                log.info("【签证产品购买】IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}", ip, reqString, System.currentTimeMillis() - t1,respString );
                return resp;
            }
            //检验登录
            BuyVisaProduct visaProduct = req.getRequest();
            boolean flag = this.checkKeyInfo(visaProduct.getFfpId(), visaProduct.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //封装请求参数
            CouponProductBuyRequest buyRequest = getCouponProductBuyRequest(req, visaProduct);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = doPostClient(buyRequest, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_BUY_PRODUCT_V2, headMap);
            if (result.isResult()) {
                CouponProductBuyResponse couponProductBuyResponse = (CouponProductBuyResponse) JsonUtil.jsonToBean(result.getResponse(), CouponProductBuyResponse.class);
                if ("1001".equals(couponProductBuyResponse.getResultCode())) {
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    Map<String, Object> orderInfo = new HashMap<>();
                    orderInfo.put("orderNo", couponProductBuyResponse.getOrderNo());
                    orderInfo.put("channelOrderNo", couponProductBuyResponse.getChannelOrderNo());
                    orderInfo.put("payState", false);
                    resp.setObjData(orderInfo);
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(couponProductBuyResponse.getErrorInfo());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络超时");
            }
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，服务端异常：", reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            log.info("【签证产品购买】IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}", ip, JsonMapper.buildNormalMapper().toJson(req), System.currentTimeMillis() - t1, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    public CouponProductBuyRequest getCouponProductBuyRequest(@RequestBody BaseReq<BuyVisaProduct> req, BuyVisaProduct visaProduct) {
        CouponProductBuyRequest buyRequest = new CouponProductBuyRequest();
        buyRequest.setFfpCardNo(visaProduct.getFfpCardNo());
        buyRequest.setFfpId(visaProduct.getFfpId());
        buyRequest.setBuyType("VISA");
        buyRequest.setLinkerMobile(visaProduct.getMobilePhone() == null ? "" : visaProduct.getMobilePhone());
        buyRequest.setPayAmount(BigDecimal.valueOf(visaProduct.getPayAmount()));
        buyRequest.setTotalPrice(BigDecimal.valueOf(visaProduct.getTotalPrice()));
        buyRequest.setVersion(HandlerConstants.VERSION);
        buyRequest.setUserNo("10001");
        buyRequest.setChannelCode(req.getChannelCode());
        buyRequest.setChannelOrderNo(visaProduct.getChannelOrderNo());
        //设置省市和详细地址
        buyRequest.setReceiverAddress(visaProduct.getAddress());
        BookProductInfo bookProductInfo = new BookProductInfo();
        VisaProductInfo visaProductInfo = visaProduct.getVisaProductInfo();
        bookProductInfo.setProductId(visaProductInfo.getProductId());
        bookProductInfo.setProductType(visaProductInfo.getProductType());
        bookProductInfo.setProductName(visaProductInfo.getProductName());
        //生成产品数据
        PassengerInfo[] passengerInfos = visaProduct.getPassengerInfos();
        BookResourceInfo[] bookResourceInfos = new BookResourceInfo[passengerInfos.length];
        for (int i = 0; i < bookResourceInfos.length; i++) {
            BookResourceInfo bookResourceInfo = new BookResourceInfo();
            bookResourceInfo.setResourceId(visaProductInfo.getResourceId());
            bookResourceInfo.setResourceType(visaProductInfo.getResourceType());
            String currentTime = DateUtils.timeStampToDateStr(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss");
            bookResourceInfo.setUseStartDate(visaProduct.getFlightDate() + " " + currentTime.substring(11));
            ResourcePriceDetail[] resourcePriceDetails = new ResourcePriceDetail[1];
            ResourcePriceDetail resourcePriceDetail = new ResourcePriceDetail();
            resourcePriceDetail.setPriceType("Unit");
            resourcePriceDetail.setBookingCount(visaProduct.getCount());
            resourcePriceDetail.setPriceUnit("Person");
            resourcePriceDetail.setSalePrice(visaProduct.getTotalPrice() / visaProduct.getCount());
            resourcePriceDetail.setUnitNumber(1.0);
            resourcePriceDetails[0] = resourcePriceDetail;
            bookResourceInfo.setResourcePriceDetails(resourcePriceDetails);
            //存储申请人信息
            VisaProduct product = new VisaProduct();
            product.setLinkerPhone(visaProduct.getMobilePhone());
            product.setLinkerName(visaProduct.getLinker());
            product.setLinkerEmail(visaProduct.getEmail());
            //设置邮寄地址
            List<CountryInfo> countryInfoList = new ArrayList<>();
            List<CountryInfo> countryChinaInfoList = new ArrayList<>();
            //获取国家集合
            countryChinaInfoList = getCountryInfos(req, countryInfoList, countryChinaInfoList);
            //设置详细地址
            setLinkerAddress(visaProduct, product, countryChinaInfoList);
            product.setDeadlineDate(visaProduct.getDeadlineDate());
            product.setFlightDate(visaProduct.getFlightDate());
            //储存申请人信息
            product.setCardNo(passengerInfos[i].getCardNo());
            product.setCardType(VisaApplyPersonTypeEnum.getEnum(passengerInfos[i].getCardType()).getDesc());
            product.setCustomerType(passengerInfos[i].getCustomerType());
            product.setSex(passengerInfos[i].getSex());
            product.setName(passengerInfos[i].getName());
            product.setRefundRules(visaProduct.getVisaProductInfo().getRefundRules());
            bookResourceInfo.setVisaProduct(product);
            bookResourceInfos[i] = bookResourceInfo;
        }
        bookProductInfo.setResources(bookResourceInfos);
        List<BookProductInfo> productsList = new ArrayList<>();
        productsList.add(bookProductInfo);
        buyRequest.setProducts(productsList);
        return buyRequest;
    }

    /**
     * 设置省市代码
     * @param visaProduct
     * @param product
     * @param countryChinaInfoList
     */
    public void setLinkerAddress(BuyVisaProduct visaProduct, VisaProduct product, List<CountryInfo> countryChinaInfoList) {
        String cityNm ="";
        String provinceNm = "";
        for (int a = 0; a < countryChinaInfoList.size(); a++) {
            CountryInfo countryInfo = (CountryInfo) JsonUtil.jsonToBean(JsonUtil.objectToJson(countryChinaInfoList.get(a)), CountryInfo.class);
            if ("CN".equalsIgnoreCase(countryInfo.getCountryCode())){
                List<ProvinceInfo> provinceInfoList = countryInfo.getProvinceInfoList();
                for (int b = 0; b < provinceInfoList.size(); b++) {
                    ProvinceInfo provinceInfo = (ProvinceInfo) JsonUtil.jsonToBean(JsonUtil.objectToJson(provinceInfoList.get(b)), ProvinceInfo.class);
                    if (visaProduct.getProvinceCode().equalsIgnoreCase(provinceInfo.getProvinceCode())){
                        provinceNm = provinceInfo.getProvinceName();
                        List<CityInfo> cityInfoList = provinceInfo.getCityInfoList();
                        for (int c = 0; c < cityInfoList.size(); c++) {
                            CityInfo cityInfo = (CityInfo) JsonUtil.jsonToBean(JsonUtil.objectToJson(cityInfoList.get(c)),CityInfo.class);
                            if (cityInfo.getCityCode().equalsIgnoreCase(visaProduct.getCityCode())){
                                cityNm = cityInfo.getCityNm();
                            }
                        }
                    }
                }
            }
        }
        product.setLinkerAddress(provinceNm + cityNm + visaProduct.getAddress());
    }

    /**
     * 获取国家集合
     * @param req
     * @param countryInfoList
     * @param countryChinaInfoList
     * @return
     */
    public List<CountryInfo> getCountryInfos(@RequestBody BaseReq<BuyVisaProduct> req, List<CountryInfo> countryInfoList, List<CountryInfo> countryChinaInfoList) {
        String result = apiRedisService.getData(RedisKeyConfig.COMMON_CRM_ADDRESS);
        if(!StringUtil.isNullOrEmpty(result)){
            BaseResp<ArrayList<CountryInfo>> baseResp = JsonMapper.buildNormalMapper().fromJson(result,BaseResp.class);
            countryChinaInfoList = baseResp.getObjData();
        }else{
            GetDataInfoResponseForClient countryData = crmClient.getDataInfoResponseForClient(req.getChannelCode(), this.getChannelInfo(req.getChannelCode(), "40"), "", AddressTypeEnum.COUNTRY.getCode());
            if (countryData.getMessageHeader() != null && "S000".equals(countryData.getMessageHeader().getErrorCode())) {
                List<String> countryList = Arrays.asList("CN", "HK", "MO", "TW");
                for (EntryType entryType : countryData.getDataEntry()) {//国家遍历
                    CountryInfo countryInfo = new CountryInfo();
                    countryInfo.setCountryCode(entryType.getCode());
                    countryInfo.setCountryName(entryType.getDesc());
                    //省份信息
                    if (countryList.contains(entryType.getCode())) {//目前只要中国大陆，港澳台地区需要省市信息
                        GetDataInfoResponseForClient provinceData = crmClient.getDataInfoResponseForClient(req.getChannelCode(), this.getChannelInfo(req.getChannelCode(),"40"), entryType.getCode(), AddressTypeEnum.PROVINCE.getCode());
                        List<ProvinceInfo> provinceInfoList = new ArrayList<>();
                        if (provinceData.getMessageHeader() != null && "S000".equals(provinceData.getMessageHeader().getErrorCode())) {
                            for (EntryType province : provinceData.getDataEntry()) {
                                ProvinceInfo provinceInfo = new ProvinceInfo();
                                provinceInfo.setProvinceCode(province.getCode());
                                provinceInfo.setProvinceName(province.getDesc());
                                //城市信息
                                GetDataInfoResponseForClient cityData = crmClient.getDataInfoResponseForClient(req.getChannelCode(), this.getChannelInfo(req.getChannelCode(),"40"), province.getCode(), AddressTypeEnum.ADDRESSCITY.getCode());
                                List<CityInfo> cityInfoList = new ArrayList<>();
                                if (cityData.getMessageHeader() != null && "S000".equals(cityData.getMessageHeader().getErrorCode())) {
                                    for (EntryType city : cityData.getDataEntry()) {
                                        CityInfo cityInfo = new CityInfo();
                                        cityInfo.setCityCode(city.getCode());
                                        cityInfo.setCityNm(city.getDesc());
                                        cityInfoList.add(cityInfo);
                                    }
                                    provinceInfo.setCityInfoList(cityInfoList);
                                }
                                provinceInfoList.add(provinceInfo);
                            }
                            countryInfo.setProvinceInfoList(provinceInfoList);
                        }
                        countryChinaInfoList.add(countryInfo);
                    } else {
                        countryInfoList.add(countryInfo);
                    }
                }
                countryChinaInfoList.addAll(countryInfoList);
            }
        }
        return countryChinaInfoList;
    }

    @ApiOperation(value = "查询类型字典", notes = "查询类型字典")
    @RequestMapping(value = "/queryType", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryType(HttpServletRequest request, @RequestBody BaseReq<String[]> req) {
        BaseResp resp = new BaseResp();
        try {
            //构建请求参数
            String ip = getClientIP(request);
            CouponProductVisaDictionariesRequestDto requestDto = new CouponProductVisaDictionariesRequestDto();
            requestDto.setChannelCode(req.getChannelCode());
            requestDto.setUserNo("10001");
            requestDto.setVersion(HandlerConstants.VERSION);
            String[] codes = req.getRequest();
            requestDto.setDictCodes(codes);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_VISA_DICT, headMap);
            if (result.isResult()) {
                CouponProductVisaDictionariesResponseDto responseDto = (CouponProductVisaDictionariesResponseDto) JsonUtil.jsonToBean(result.getResponse(), CouponProductVisaDictionariesResponseDto.class);
                if ("1001".equals(responseDto.getResultCode())) {
                    Map<String, List<CouponProductVisaDictionariesResponseDto.SimpleTreeNode>> data = responseDto.getData();
                    Map<String, Map<String, String>> mapMap = RightCouponConvert.getStringMapMap(data);
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setObjData(mapMap);
                } else {
                    resp.setResultInfo(responseDto.getErrorInfo());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                }
            } else {
                resp.setResultInfo("网络超时");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            return resp;
        }
    }

}
