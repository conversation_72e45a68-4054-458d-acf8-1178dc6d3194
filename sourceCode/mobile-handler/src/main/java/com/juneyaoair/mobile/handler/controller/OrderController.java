package com.juneyaoair.mobile.handler.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.PayMethodEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FareTypeEnum;
import com.juneyaoair.appenum.coupon.CouponTypeEnum;
import com.juneyaoair.appenum.insurance.InsuranceDeleteStatEnmu;
import com.juneyaoair.appenum.insurance.InsuranceOrderStatEnum;
import com.juneyaoair.appenum.insurance.InsuranceStatEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.member.MileageAccountRequestItemsEnum;
import com.juneyaoair.appenum.member.VerifyStatusEnum;
import com.juneyaoair.appenum.order.OrderPayStateEnum;
import com.juneyaoair.appenum.order.OrderSortEnum;
import com.juneyaoair.appenum.order.OrderStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.activity.MaoTaiConfig;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.BaseResponse;
import com.juneyaoair.baseclass.member.response.MemberBeneficiaryDTO;
import com.juneyaoair.baseclass.prepayment.request.QueryCouponInfoRequest;
import com.juneyaoair.baseclass.request.booking.*;
import com.juneyaoair.baseclass.request.crm.MemberBeneficiaryRequest;
import com.juneyaoair.baseclass.request.flightDynamic.FlightChange;
import com.juneyaoair.baseclass.request.flightDynamic.FlightChangeRequest;
import com.juneyaoair.baseclass.request.flightDynamic.NewFlightStatusRequest;
import com.juneyaoair.baseclass.request.myScore.MyScoreReq;
import com.juneyaoair.baseclass.request.order.cancel.CancelOrderReason;
import com.juneyaoair.baseclass.request.order.cancel.CancelOrderRequest;
import com.juneyaoair.baseclass.request.order.query.OrderBriefRequest;
import com.juneyaoair.baseclass.request.order.query.OrderDetailRequest;
import com.juneyaoair.baseclass.request.order.query.ShareOrderDetailRequest;
import com.juneyaoair.baseclass.request.payment.OrderScroeShareRequest;
import com.juneyaoair.baseclass.request.premium.OrderDetailPremium;
import com.juneyaoair.baseclass.response.booking.Segment;
import com.juneyaoair.baseclass.response.booking.TicketBookingResp;
import com.juneyaoair.baseclass.response.flightdynamic.FlightInfoResponse;
import com.juneyaoair.baseclass.response.flightdynamic.NewFlightInfo;
import com.juneyaoair.baseclass.response.flightdynamic.ShareSign;
import com.juneyaoair.baseclass.response.order.cancel.CancelOrderResp;
import com.juneyaoair.baseclass.response.order.comm.OrderPassengerInfo;
import com.juneyaoair.baseclass.response.order.comm.SegmentPriceInfo;
import com.juneyaoair.baseclass.response.order.detail.*;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;
import com.juneyaoair.baseclass.response.order.query.*;
import com.juneyaoair.baseclass.response.order.refund.insurance.InsuranceRefund;
import com.juneyaoair.baseclass.response.order.refund.insurance.RefundInsureDetailResp;
import com.juneyaoair.baseclass.response.payment.PaymentResp;
import com.juneyaoair.baseclass.response.payment.ScoreControlSwitchResp;
import com.juneyaoair.baseclass.response.score.ScoreUseRuleResp;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.InsuranceService;
import com.juneyaoair.mobile.handler.controller.service.OrderDetailService;
import com.juneyaoair.mobile.handler.controller.service.ScoreControlService;
import com.juneyaoair.mobile.handler.controller.util.*;
import com.juneyaoair.mobile.handler.controller.v2.util.RightCouponConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.sdk.TravellerHttpApi;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.MemberInfoQueryResponseForClient;
import com.juneyaoair.mobile.webservice.client.crm.VerifyConsumeMilesResponseForClient;
import com.juneyaoair.mobile.webservice.client.crm.VerifyConsumePasswdResponseForClient;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.bigdata.request.PtFlightStatusReq;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtCrmMileageRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.MileageAccountQueryRequest;
import com.juneyaoair.thirdentity.member.response.MemberRealNameSummarySoaModel;
import com.juneyaoair.thirdentity.member.response.MileageAccountQueryResponse;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.booking.*;
import com.juneyaoair.thirdentity.request.order.cancel.PtCancelOrderReq;
import com.juneyaoair.thirdentity.request.order.query.*;
import com.juneyaoair.thirdentity.request.order.refund.insurance.PtRefundInsureDetailReq;
import com.juneyaoair.thirdentity.request.score.ScoreUseRuleRequest;
import com.juneyaoair.utils.AESUtil;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * Created by qinxiaoming on 2016-4-26.
 */
@RestController
@Api(value = "订单管理")
@RequestMapping("/orderService")
public class OrderController extends BassController {
    private static final String LOG_RESP_ONE = "该账户暂未实名请先进行实名认证!";
    private static final String LOG_RESP_TWO = "查询网络出错";
    private static final String LOG_RESP_THREE = "查询结果:";
    private static final String LOG_RESP_FIVE = "请求号:{},响应结果:{}";
    private static final String VALUE_TWO = "LoungeType";
    private static final String SERVICE_NAME = "订单服务";
    private static final String INSURANCE_ORDER = "Insurance";
    private static final String SUCCESS = "000000";
    @Autowired
    private IBeneficiaryService beneficiaryService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private OrderManage orderManage;
    @Autowired
    private ScoreControlService scoreControlService;

    //查询积分规则
    @InterfaceLog
    @ApiOperation(value = "查询积分使用规则", notes = "查询积分使用规则")
    @RequestMapping(value = "/queryScoreRule", method = RequestMethod.POST)
    public ScoreUseRuleResp queryScoreRule(@RequestBody @Validated TicketBookingRequestV2 bookReq, BindingResult bindingResult, HttpServletRequest request) {
        ScoreUseRuleResp resp = new ScoreUseRuleResp();
        String ip = this.getClientIP(request);
        String channelCode = bookReq.getChannelCode();
        String headChannelCode = StringUtils.isNotEmpty(request.getHeader(HandlerConstants.TOKEN_CHANNELCODE))
                ? request.getHeader(HandlerConstants.TOKEN_CHANNELCODE) : channelCode;
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        resp.setScoreRiskFlag(false);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        boolean accmflag = false;
        //查询会员当前级别
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,
                MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
        PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(bookReq.getFfpCardNo(), bookReq.getFfpId(), request, bookReq.getChannelCode(), items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        if (ptCRMResponse.getCode() != 0) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo("未查询到会员信息");
            return resp;
        }
        String cacheData = apiRedisService.getData(RedisKeyConfig.createOrderPass(bookReq.getChannelOrderNo()));
        if(StringUtils.isNotBlank(cacheData)){
            List<OrderPassengerInfo> orderPassengerInfoList = JsonUtil.fromJson(cacheData,new TypeToken<List<OrderPassengerInfo>>() {});
            if(CollectionUtils.isNotEmpty(orderPassengerInfoList)){
                for(PassengerInfo passengerInfo:bookReq.getPassengerInfoList()){
                    OrderPassengerInfo orderPassengerInfo = orderPassengerInfoList.stream().filter(param -> passengerInfo.getPassengerID() == param.getPassengerID()).findFirst().orElse(null);
                    if(orderPassengerInfo !=null){
                        passengerInfo.setCertNo(orderPassengerInfo.getCertNo());
                    }
                }
            }
        }
        PtMemberDetail ptMemberDetail = ptCRMResponse.getData();
        List<MemberRealNameSummarySoaModel> memberRealNameSummarySoaModelList = ptMemberDetail.getRealVerifyInfos();
        if (CollectionUtils.isNotEmpty(memberRealNameSummarySoaModelList)) {
            //获取最新的认证记录
            MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = memberRealNameSummarySoaModelList.stream().max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate)).orElse(null);
            if (memberRealNameSummarySoaModel != null && VerifyStatusEnum.PASS.code.equals(memberRealNameSummarySoaModel.getStatus())) {
                accmflag = true;
            }
        }
        if (!accmflag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_ONE);
            return resp;
        }
        bookReq.setOrderRequestIp(ip);
        //是否使用受益人制度
        if (null != DateUtils.toDate(handConfig.getLimitScoreUseDate()) && DateUtils.toDate(handConfig.getLimitScoreUseDate()).before(new Date())) {
            PtCrmMileageRequest ptCrmMileageRequest = buildCommCrmReq(request, bookReq.getChannelCode());
            ptCrmMileageRequest.setData(new MemberBeneficiaryRequest(Integer.parseInt(bookReq.getFfpId()), Collections.singletonList("A"), null));
            List<PassengerInfo> benefitPassengers = Lists.newArrayList();
            try {
                List<MemberBeneficiaryDTO> beneficiaryDTOList = this.beneficiaryService.listEffectiveBeneficiaryInfoRecord(ptCrmMileageRequest, ip);
                beneficiaryDTOList.forEach(memberBeneficiaryDTO -> {
                    List<PassengerInfo> passengerInfos = bookReq.getPassengerInfoList().stream().filter(passengerInfo -> {
                        // 本人或者受益人可用积分
                        return memberBeneficiaryDTO.getCertificate().stream()
                                .anyMatch(certificate -> certificate.getCtype() == CertificateTypeEnum.checkShowCode(passengerInfo.getCertType()).getCode()
                                        && certificate.getCnumber().equals(passengerInfo.getCertNo()));
                    }).collect(Collectors.toList());
                    benefitPassengers.addAll(passengerInfos);
                });
                benefitPassengers.addAll(bookReq.getPassengerInfoList().stream().filter(passengerInfo -> RightCouponConvert.isOwnPass(ptCRMResponse, passengerInfo)).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("查询受益人出现异常", e);
            }
            // 只请求受益人
            bookReq.setPassengerInfoList(benefitPassengers.stream().distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(bookReq.getPassengerInfoList())) {
            //2021-02-22 判断茅台航线不能使用积分
            boolean maotaiCabin = filterMaotaiCabin(handConfig, bookReq.getFlightInfoList());
            if (!maotaiCabin) {
                Boolean useScore = false;
                if ((ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= 72700)
                        || (!ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) > 19000)) {
                    OrderScroeShareRequest orderScroeShareRequest = new OrderScroeShareRequest();
                    orderScroeShareRequest.setChannelCode("B2C");
                    orderScroeShareRequest.setVersion("10");
                    ScoreControlSwitchResp scoreControlSwitchResp = scoreControlService.scoreControlSwitch(orderScroeShareRequest);
                    if (scoreControlSwitchResp != null) {
                        useScore = scoreControlSwitchResp.getUseScore();
                    }
                }
                ScoreUseRuleRequest scoreUseRuleRequest = OrderObjectConvert.ToScoreUseRuleRequest(bookReq, getChannelInfo(bookReq.getChannelCode(), "10"), useScore);
                HttpResult serviceResult = HttpUtil.doPostClient(scoreUseRuleRequest, HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.SUB_SCORE_USE_RULE);
                if (null == serviceResult || !serviceResult.isResult() || StringUtils.isBlank(serviceResult.getResponse())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询可用积分网络错误");
                    return resp;
                }
                resp = (ScoreUseRuleResp) JsonUtil.jsonToBean(serviceResult.getResponse(), ScoreUseRuleResp.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else if ("该航线不享受使用积分".equals(resp.getErrorInfo())) {
                    resp.setResultCode(WSEnum.CANNOT_USE_SCORE.getResultCode());
                }
            } else {
                resp.setResultCode(WSEnum.CANNOT_USE_SCORE.getResultCode());
                resp.setErrorInfo("茅台专享舱位不支持使用积分抵扣票款");
            }
        } else {
            resp.setResultCode(WSEnum.CANNOT_USE_SCORE.getResultCode());
            resp.setErrorInfo("当前乘机人不支持使用积分");
        }

        //返回客户可用积分
        String clientPwd = getClientPwd(channelCode);
        ptApiCRMRequest = CRMReqUtil.buildCommReq(channelCode, clientPwd, bookReq.getFfpId(), "", ip);
        MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
        items = new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName};
        mileageAccountQueryRequest.setMemberCardNo(bookReq.getFfpCardNo());
        mileageAccountQueryRequest.setRequestItems(items);
        ptApiCRMRequest.setData(mileageAccountQueryRequest);
        PtCRMResponse<MileageAccountQueryResponse> mileageAccountQueryResponsePtCRMResponse = memberService.mileageAccountQuery(ptApiCRMRequest);
        if (mileageAccountQueryResponsePtCRMResponse.getCode() == 0) {
            resp.setUserScore(mileageAccountQueryResponsePtCRMResponse.getData().getTotalBill().getAvailableMiles());
        }
        if (CollectionUtils.isNotEmpty(bookReq.getPremiumList()) && resp.getScoreUseMax() > 0) {
            List<OrderDetailPremium> detailPremiums = bookReq.getPremiumList().stream()
                    .filter(p -> !VoucherTypesEnum.DISNEYTICKET.getCode().equals(p.getProductType())).collect(Collectors.toList());
            Double sum = detailPremiums.stream().mapToDouble(OrderDetailPremium::getAllPremiumPrice).sum();
            int max = resp.getScoreUseMax() + sum.intValue();
            resp.setScoreUseMax(max);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        }
        //判断账号可用积分
        int usableScore = 0;
        if (resp.getUserScore() >= resp.getScoreUseMin()) {
            if (resp.getUserScore() >= resp.getScoreUseMax()) {
                usableScore = resp.getScoreUseMax();
            } else {
                usableScore = resp.getUserScore();
            }
        } else {
            usableScore = resp.getScoreUseMin();
        }
        ClientInfo clientInfo = initClientInfo(request, channelCode, bookReq.getFfpId(), bookReq.getFfpCardNo());
        //积分使用规则增加风控
            /*if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode()) && resp.getUserScore() > 0 && usableScore > 0) {
                if (orderService.checkScoreUseRule(clientInfo,channelCode,ptCRMResponse.getData().getRealVerifyInfos())) {
                    resp.setScoreRiskFlag(true);
                    resp.setScoreRiskDesc("非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，是否前往实名认证？");
                }
            }*/
        //查询是否设置消费密码
        resp.setMemberPasswordState(ptMemberDetail.getBasicInfo().getIsSetConsumePwd() ? "Y" : "N");
        return resp;
    }

    /**
     * 过滤茅台特殊舱位,茅台仓位不能使用积分和优惠券
     *
     * @param handConfig
     * @return
     */
    private boolean filterMaotaiCabin(HandConfig handConfig, List<FlightInfo> flightInfoList) {
        MaoTaiConfig outMaoTaiConfig = handConfig.getOutMaoTaiConfig();
        MaoTaiConfig inMaoTaiConfig = handConfig.getInMaoTaiConfig();
        if (outMaoTaiConfig != null) {
            String startDateStr = outMaoTaiConfig.getStartDate();
            String endDateStr = outMaoTaiConfig.getEndDate();
            //活动时间比较
            Date date = new Date();
            Date startDate = DateUtils.toDate(startDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date endDate = DateUtils.toDate(endDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            if (date.getTime() >= startDate.getTime() && date.getTime() < endDate.getTime()) {
                for (FlightInfo flightInfo : flightInfoList) {
                    //航班号匹配
                    if (StringUtils.isNotBlank(outMaoTaiConfig.getFlightNo()) && outMaoTaiConfig.getFlightNo().contains(flightInfo.getFlightNo())) {
                        //航班时间
                        String flightStartDateStr = outMaoTaiConfig.getFlightStartDate();
                        String flightEndDateStr = outMaoTaiConfig.getFlightEndDate();
                        Date flightStartDate = DateUtils.toDate(flightStartDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date flightEndDate = DateUtils.toDate(flightEndDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date curFlightDate = DateUtils.toDate(flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                        if (curFlightDate.getTime() >= flightStartDate.getTime() && curFlightDate.getTime() < flightEndDate.getTime()) {
                            List<CabinFare> cabinFareList = flightInfo.getCabinFareList();
                            //舱位匹配
                            if (StringUtils.isNotBlank(outMaoTaiConfig.getCabins())
                                    && outMaoTaiConfig.getCabins().contains(cabinFareList.get(0).getCabinCode())
                                    && !outMaoTaiConfig.isUseScore()) {
                                return true;
                            }
                        }

                    }
                }
            }
        }
        if (inMaoTaiConfig != null) {
            String startDateStr = inMaoTaiConfig.getStartDate();
            String endDateStr = inMaoTaiConfig.getEndDate();
            //活动时间比较
            Date date = new Date();
            Date startDate = DateUtils.toDate(startDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date endDate = DateUtils.toDate(endDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            if (date.getTime() >= startDate.getTime() && date.getTime() < endDate.getTime()) {
                for (FlightInfo flightInfo : flightInfoList) {
                    //航班号匹配
                    if (StringUtils.isNotBlank(inMaoTaiConfig.getFlightNo()) && inMaoTaiConfig.getFlightNo().contains(flightInfo.getFlightNo())) {
                        //航班时间
                        String flightStartDateStr = inMaoTaiConfig.getFlightStartDate();
                        String flightEndDateStr = inMaoTaiConfig.getFlightEndDate();
                        Date flightStartDate = DateUtils.toDate(flightStartDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date flightEndDate = DateUtils.toDate(flightEndDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date curFlightDate = DateUtils.toDate(flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                        if (curFlightDate.getTime() >= flightStartDate.getTime() && curFlightDate.getTime() < flightEndDate.getTime()) {
                            List<CabinFare> cabinFareList = flightInfo.getCabinFareList();
                            //舱位匹配
                            if (StringUtils.isNotBlank(inMaoTaiConfig.getCabins())
                                    && inMaoTaiConfig.getCabins().contains(cabinFareList.get(0).getCabinCode())
                                    && !inMaoTaiConfig.isUseScore()) {
                                return true;
                            }
                        }

                    }
                }
            }
        }
        return false;
    }

    /**
     * @param orderBriefRequest
     * @param request
     * @return
     * @deprecated {@link com.juneyaoair.mobile.handler.controller.NewOrderController#queryOrderBrief}
     */
    @ApiOperation(value = "QueryOrderBrief", notes = "机票订单列表")
    @RequestMapping(value = "/queryOrderBrief", method = RequestMethod.POST)
    @Deprecated
    public OrderTotalBriefResp queryOrderBrief(@RequestBody OrderBriefRequest orderBriefRequest, HttpServletRequest request) {
        OrderTotalBriefResp resp = new OrderTotalBriefResp();
        String channelCode = orderBriefRequest.getChannelCode();
        String ip = this.getClientIP(request);
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderBriefRequest.getCustomerNo(), orderBriefRequest.getLoginKeyInfo(), orderBriefRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<OrderBriefRequest>> violations = validator.validate(orderBriefRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(orderBriefRequest.getChannelCode(), "10");
        PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, orderBriefRequest.getChannelCode(), userNo);
        BeanUtils.copyProperties(orderBriefRequest, ptBrief);
        ptBrief.setCreateDateBegin(orderBriefRequest.getDateBegin());
        ptBrief.setCreateDateEnd(orderBriefRequest.getDateEnd());
        if ("Finish".equalsIgnoreCase(orderBriefRequest.getState()) || "Cancel".equalsIgnoreCase(orderBriefRequest.getState())) {
            ptBrief.setOrderState(orderBriefRequest.getState());
            ptBrief.setPayState("");
        } else if ("UnPay".equalsIgnoreCase(orderBriefRequest.getState())) {
            ptBrief.setOrderState("Booking");
            ptBrief.setPayState(orderBriefRequest.getState());
            //反向筛选排除优惠券订单
            ptBrief.setSubOrderTypeFilterIndex(0);
            ptBrief.setSubOrderTypeFilterContent("CouponOrder");
        } else {//全部
            ptBrief.setOrderState("");
            ptBrief.setPayState("");
            //反向筛选排除优惠券订单
            ptBrief.setSubOrderTypeFilterIndex(0);
            ptBrief.setSubOrderTypeFilterContent("CouponOrder");
        }
        HttpResult serviceResult = doPost(ptBrief, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_ORDER_BRIEF);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(serviceResult.getResponse(), OrderTotalBriefResp.class);
                if (resp.getResultCode().equals("1001")) {
                    if (!StringUtil.isNullOrEmpty(resp.getOrderTotalBriefInfoList())) {
                        for (OrderTotalBriefInfo orderTotalBriefInfo : resp.getOrderTotalBriefInfoList()) {
                            orderTotalBriefInfo.setPayType(PayMethodEnum.CASH.value);
                            //支付类型处理
                            if (orderTotalBriefInfo.getUseScore() != null && orderTotalBriefInfo.getUseScore() > 0 && orderTotalBriefInfo.getAmount() == 0) {
                                orderTotalBriefInfo.setPayType(PayMethodEnum.SCORE.value);
                            }
                            if (!StringUtil.isNullOrEmpty(orderTotalBriefInfo.getSegmentInfoList())) {
                                for (SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                                    AirPortInfoDto depAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getDepAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), "yyyy-MM-dd HH:mm"));
                                    AirPortInfoDto arrAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getArrAirport(), DateUtils.toDate(segmentInfo.getArrDateTime(), "yyyy-MM-dd HH:mm"));
                                    segmentInfo.setDepCityName(depAirPortInfoDto.getCityName());
                                    segmentInfo.setArrCityName(arrAirPortInfoDto.getCityName());
                                    segmentInfo.setDepAirportName(depAirPortInfoDto.getAirPortName());
                                    segmentInfo.setArrAirportName(arrAirPortInfoDto.getAirPortName());
                                }
                            }
                            //订单支付状态处理
                            OrderPayStateEnum orderPayState = OrderPayStateConvert.convertState(orderTotalBriefInfo.getOrderState(), orderTotalBriefInfo.getPayState(), null, null);
                            orderTotalBriefInfo.setOrderPayState(orderPayState.getStateCode());
                            orderTotalBriefInfo.setOrderPayStateName(orderPayState.getStateDesc());
                            // 已出票和已删除的订单显示删除订单按钮
                            orderTotalBriefInfo.setShowDeleteButton(OrderPayStateEnum.Cancel.equals(orderPayState) || OrderPayStateEnum.TicketOut.equals(orderPayState));
                        }
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_THREE + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询订单列表出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询订单列表返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    /**
     * Deprecated at 2020-06-12
     * new function
     *
     * @param orderBriefRequest
     * @param request
     * @return
     * @see NewOrderController#queryOrderBrief(BaseReq, BindingResult, HttpServletRequest)
     */
    @Deprecated
    //待出行订单
    @RequestMapping(value = "/QueryOrderTrvel", method = RequestMethod.POST)
    public OrderTotalBriefResp queryOrderTrvel(@RequestBody OrderBriefRequest orderBriefRequest, HttpServletRequest request) {
        OrderTotalBriefResp resp = new OrderTotalBriefResp();
        String channelCode = orderBriefRequest.getChannelCode();
        String ip = this.getClientIP(request);
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderBriefRequest.getCustomerNo(), orderBriefRequest.getLoginKeyInfo(), orderBriefRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<OrderBriefRequest>> violations = validator.validate(orderBriefRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(orderBriefRequest.getChannelCode(), "10");
        PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, orderBriefRequest.getChannelCode(), userNo);
        BeanUtils.copyProperties(orderBriefRequest, ptBrief);
        ptBrief.setCreateDateBegin(orderBriefRequest.getDateBegin());
        ptBrief.setCreateDateEnd(orderBriefRequest.getDateEnd());
        HttpResult serviceResult = doPost(ptBrief, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_ORDER_TRVEL);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(serviceResult.getResponse(), OrderTotalBriefResp.class);
                if (resp.getResultCode().equals("1001")) {
                    if (!StringUtil.isNullOrEmpty(resp.getOrderTotalBriefInfoList())) {
                        for (OrderTotalBriefInfo orderTotalBriefInfo : resp.getOrderTotalBriefInfoList()) {
                            if (!StringUtil.isNullOrEmpty(orderTotalBriefInfo.getSegmentInfoList())) {
                                for (SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                                    AirPortInfoDto depAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getDepAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), "yyyy-MM-dd HH:mm"));
                                    AirPortInfoDto arrAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getArrAirport(), DateUtils.toDate(segmentInfo.getArrDateTime(), "yyyy-MM-dd HH:mm"));
                                    segmentInfo.setDepCityName(depAirPortInfoDto.getCityName());
                                    segmentInfo.setArrCityName(arrAirPortInfoDto.getCityName());
                                    segmentInfo.setDepAirportName(depAirPortInfoDto.getCityName());
                                    segmentInfo.setArrAirportName(arrAirPortInfoDto.getCityName());
                                }
                            }
                        }
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_THREE + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询待出行列表出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询待出行订单列表返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    //待评价
    @RequestMapping(value = "/QueryQueryOrderNoComments", method = RequestMethod.POST)
    public OrderTotalBriefResp queryQueryOrderNoComments(@RequestBody OrderBriefRequest orderBriefRequest, HttpServletRequest request) {
        OrderTotalBriefResp resp = new OrderTotalBriefResp();
        String channelCode = orderBriefRequest.getChannelCode();
        String ip = this.getClientIP(request);
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderBriefRequest.getCustomerNo(), orderBriefRequest.getLoginKeyInfo(), orderBriefRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<OrderBriefRequest>> violations = validator.validate(orderBriefRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(orderBriefRequest.getChannelCode(), "10");
        PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, orderBriefRequest.getChannelCode(), userNo);
        BeanUtils.copyProperties(orderBriefRequest, ptBrief);
        ptBrief.setCreateDateBegin(orderBriefRequest.getDateBegin());
        ptBrief.setCreateDateEnd(orderBriefRequest.getDateEnd());
        HttpResult serviceResult = doPost(ptBrief, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_ORDER_COMMENT);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(serviceResult.getResponse(), OrderTotalBriefResp.class);
                if (resp.getResultCode().equals("1001")) {
                    if (!StringUtil.isNullOrEmpty(resp.getOrderTotalBriefInfoList())) {
                        for (OrderTotalBriefInfo orderTotalBriefInfo : resp.getOrderTotalBriefInfoList()) {
                            if (!StringUtil.isNullOrEmpty(orderTotalBriefInfo.getSegmentInfoList())) {
                                for (SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                                    AirPortInfoDto depAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getDepAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), "yyyy-MM-dd HH:mm"));
                                    AirPortInfoDto arrAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getArrAirport(), DateUtils.toDate(segmentInfo.getArrDateTime(), "yyyy-MM-dd HH:mm"));
                                    segmentInfo.setDepCityName(depAirPortInfoDto.getCityName());
                                    segmentInfo.setArrCityName(arrAirPortInfoDto.getCityName());
                                    segmentInfo.setDepAirportName(depAirPortInfoDto.getCityName());
                                    segmentInfo.setArrAirportName(arrAirPortInfoDto.getCityName());
                                }
                            }
                        }
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_THREE + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询待评价订单列表出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询待评价订单列表返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    //订单详情查询
    @InterfaceLog
    @ApiOperation(value = "机票订单详情", notes = "机票订单详情")
    @RequestMapping(value = "/queryOrderDetail", method = RequestMethod.POST)
    public OrderDetailResp queryOrderDetail(@RequestBody @Validated OrderDetailRequest orderDetailRequest, BindingResult bindingResult, HttpServletRequest request) {
        OrderDetailResp resp = new OrderDetailResp();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderDetailRequest.getCustomerNo(), orderDetailRequest.getLoginKeyInfo(), orderDetailRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
            return resp;
        }
        if (StringUtils.isNotBlank(orderDetailRequest.getRequestChannelCode())) {
            orderDetailRequest.setRequestChannelCode(orderDetailRequest.getChannelCode());
        }
        String userNo = getChannelInfo(orderDetailRequest.getChannelCode(), "10");
        PtSubOrderReq ptSubOrderReq = new PtSubOrderReq(HandlerConstants.VERSION, orderDetailRequest.getRequestChannelCode(), userNo);
        BeanUtils.copyProperties(orderDetailRequest, ptSubOrderReq);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        // 查询订单 /Order/QuerySubOrder
        HttpResult serviceResult = doPostClient(ptSubOrderReq, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_SUB_ORDER, headMap);
        if (serviceResult.isResult() && StringUtils.isNotBlank(serviceResult.getResponse())) {
            try {
                SubOrderResp subOrderResp = (SubOrderResp) JsonUtil.jsonToBean(serviceResult.getResponse(), SubOrderResp.class);
                if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(subOrderResp.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_THREE + subOrderResp.getErrorInfo());
                    return resp;
                }
                if (CollectionUtils.isEmpty(subOrderResp.getSubtOrderBaseInfoList())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询结果:查无此单!");
                    return resp;
                }
                List<OrderBase> subTiccetList = TicketOrderConvert.getSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), "TicketOrder");
                // 获取订单结果
                // 构建所需要的参数
                com.juneyaoair.mobile.handler.controller.service.bean.OrderDetailRequest detailRequest =
                        new com.juneyaoair.mobile.handler.controller.service.bean.OrderDetailRequest();
                BeanUtils.copyNotNullProperties(orderDetailRequest, detailRequest);
                OrderDetailResp orderResult = orderDetailService.getOrderResult(subOrderResp, subTiccetList, ip, orderDetailRequest.getChannelCode(), userNo, detailRequest, "");
                // 处理保险
                this.setInsuranceState(orderResult, ptSubOrderReq);
                // 保险订单状态
                this.setInsuranceOrderState(orderResult);
                // 2021-03-09 错购退票使用 设置退款标识
                List<SegmentPriceInfo> segmentPriceInfos = new ArrayList();
                orderResult.getOrderPassengerInfoList().stream()
                        .filter(orderPassengerInfo -> CollectionUtils.isNotEmpty(orderPassengerInfo.getSegmentPriceInfoList()))
                        .forEach(orderPassengerInfo -> orderPassengerInfo.getSegmentPriceInfoList().stream()
                                .forEach(segmentPriceInfo -> {
                                    if (HandlerConstants.OPEN_FOR_USE.equals(segmentPriceInfo.getTicketState()) || (HandlerConstants.EXCHANGED.equals(segmentPriceInfo.getTicketState()) && HandlerConstants.EXCHANGED.equals(segmentPriceInfo.getTKTStatus()))) {
                                        orderPassengerInfo.setRefundable(true);
                                        segmentPriceInfo.setRefundable(true);
                                    }
                                    if ("Y".equals(orderPassengerInfo.getAdtIsConnectedChd())) {
                                        orderPassengerInfo.setCantRefundReason("当前客票已关联同行儿童旅客，请完成成人退票手续后，为儿童客票也办理退票手续；如希望儿童在无成人陪伴的情况下单独出行，请务必提前申请“无成人陪伴儿童”服务，吉祥航空有权拒绝未申请无陪服务的儿童独自出行的要求。");
                                    }
                                    if (HandlerConstants.CHECKED_IN.equals(segmentPriceInfo.getTicketState())) {
                                        segmentPriceInfo.setCantRefundReason("票号：" + segmentPriceInfo.getETicketNo() + "处于已值机状态，请先取消值机再申请退票。");
                                    }
                                    if (FareTypeEnum.ADDON.getFare().equals(orderResult.getFareType()) && !HandlerConstants.OPEN_FOR_USE.equals(segmentPriceInfo.getTicketState())) {
                                        segmentPriceInfo.setCantRefundReason("部分航段已使用或已退改，无法退票申请。");
                                    }
                                    //是否改期或者退票
                                    if (HandlerConstants.REFUNDED.equals(segmentPriceInfo.getTicketState()) || HandlerConstants.EXCHANGED.equals(segmentPriceInfo.getTicketState())) {
                                        segmentPriceInfo.setIsChangeOrRefund(true);
                                    }
                                    segmentPriceInfos.add(segmentPriceInfo);
                                }));

                // 请求 /Refund/QueryRefundDetail
                // 根据机票订单号查询是否存在退款信息 setIsSpeedRefund False
                this.setIsSpeedRefund(orderDetailRequest, orderResult, headMap);
                orderResult.setLoginKeyInfo(orderDetailRequest.getLoginKeyInfo());
                orderResult.setResultCode(WSEnum.SUCCESS.getResultCode());
                orderResult.setServiceLink(handConfig.getServiceLink());
                // 新流程购保的第一次订单详情请求需要通知统一订单
                try {
                    if (OrderStateEnum.Init.getStateCode().equalsIgnoreCase(orderResult.getOrderState())
                            && CollectionUtils.isNotEmpty(orderResult.getPassengerList().get(0).getInsuranceList())) {
                        InsuranceInfoRequestDto requestDto = new InsuranceInfoRequestDto(HandlerConstants.VERSION, orderDetailRequest.getRequestChannelCode(), userNo, orderResult.getOrderNO());
                        // 无需处理响应
                        doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_INSURANCE_STATE, headMap, 5000, 5000);
                    }
                } catch (Exception e) {
                    log.error("通知统一订单刷新保险状态失败", e);
                }
                // 保险状态中文
                this.setInsuranceStateDesc(orderResult);
                // 处理权益券响应信息（预付费行李,选座,已购退票提示）
                this.setPayCouponInfoDesc(orderResult, ip, ptSubOrderReq, userNo);
                // 是否为一单多券订单
                this.setIsMultiCoupon(orderResult);
                Map<String, List<SegmentPriceInfo>> groupMap = segmentPriceInfos.stream().collect(Collectors.groupingBy(SegmentPriceInfo::getFlightNo));
                // 处理额外行李(国内航线且开关打开)
                // 获取全部城市
                Set<String> cityCodeSet = Sets.newHashSet();
                orderResult.getSegmentList().forEach(segment -> {
                    cityCodeSet.add(segment.getDepCity());
                    cityCodeSet.add(segment.getArrCity());
                    List<SegmentPriceInfo> SegmentPriceInfo = groupMap.get(segment.getFlightNo());
                    Boolean isChangeOrRefund = SegmentPriceInfo.stream().allMatch(segmentPriceInfo -> segmentPriceInfo.getIsChangeOrRefund());
                    segment.setIsChangeOrRefund(isChangeOrRefund);
                });
                Segment segment = localCacheService.getSegment(cityCodeSet.toArray(new String[]{}));
                boolean useExtraBaggagePricingFlag = HandlerConstants.TRIP_TYPE_D.equals(segment.getSegmentType()) && iExtraBaggageService.whetherUesNewBaggageProduct();
                orderResult.setUseExtraBaggagePricingFlag(useExtraBaggagePricingFlag);
                if (orderResult.getUseExtraBaggagePricingFlag()) {
                    orderResult.setExtraBaggagePricing(handConfig.getExtraBaggagePricing());
                }
                //1小时处理时间
                apiRedisService.replaceData(orderResult.getOrderSign(), JsonUtil.objectToJson(orderResult), 3600L);
                //缓存乘机人信息
                apiRedisService.putData(RedisKeyConfig.createOrderPass(orderDetailRequest.getChannelOrderNo()),JsonUtil.objectToJson(orderResult.getOrderPassengerInfoList()),3600L);
                orderResult.getOrderPassengerInfoList().parallelStream().forEach(orderPassengerInfo -> orderPassengerInfo.setCertNo(SensitiveInfoHider.hideSensitiveInfo(orderPassengerInfo.getCertNo())));
                orderDetailRequest.setSign(EncoderHandler.encodeByMD5(orderDetailRequest.getChannelOrderNo()+orderDetailRequest.getOrderNo()+orderDetailRequest.getCustomerNo()));
                String   orderDetailString  =JsonUtil.objectToJson(orderDetailRequest);
                String ticketSign = AESUtil.encrypt(orderDetailString,  HandlerConstants.DEFAULT_TOKEN.substring(HandlerConstants.DEFAULT_TOKEN.length() - 16));
                orderResult.setTicketSign(ticketSign);
                return orderResult;
            } catch (Exception e) {
                saveError("查询订单详情", reqId, ip, JsonMapper.buildNormalMapper().toJson(orderDetailRequest), e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询订单详情返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }


    @InterfaceLog
    @ApiOperation(value = "机票分享订单详情", notes = "机票分享订单详情")
    @RequestMapping(value = "/queryShareOrderDetail", method = RequestMethod.POST)
    public BaseResponse  queryShareOrderDetail(@RequestBody @Validated ShareSign req, BindingResult bindingResult, HttpServletRequest request){
        String ip = this.getClientIP(request);
        BaseResponse resp =new BaseResponse();
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            String decryptResult = AESUtil.decrypt(req.getSign(), HandlerConstants.DEFAULT_TOKEN.substring(HandlerConstants.DEFAULT_TOKEN.length() - 16), 16);
            ShareOrderDetailRequest  shareOrderDetailRequest = JsonUtil.fromJson(decryptResult, ShareOrderDetailRequest.class);
            String sign = EncoderHandler.encodeByMD5(shareOrderDetailRequest.getChannelOrderNo() + shareOrderDetailRequest.getOrderNo() + shareOrderDetailRequest.getCustomerNo());
            if (!shareOrderDetailRequest.getSign().equals(sign)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("签名不正确");
                return resp;
            }
            String userNo = getChannelInfo(shareOrderDetailRequest.getChannelCode(), "10");
            PtSubOrderReq ptSubOrderReq = new PtSubOrderReq(HandlerConstants.VERSION, shareOrderDetailRequest.getRequestChannelCode(), userNo);
            BeanUtils.copyProperties(shareOrderDetailRequest, ptSubOrderReq);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult serviceResult = doPostClient(ptSubOrderReq, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_SUB_ORDER, headMap);
            if (serviceResult.isResult() && StringUtils.isNotBlank(serviceResult.getResponse())) {
                SubOrderResp subOrderResp = (SubOrderResp) JsonUtil.jsonToBean(serviceResult.getResponse(), SubOrderResp.class);
                if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(subOrderResp.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_RESP_THREE + subOrderResp.getErrorInfo());
                    return resp;
                }
                if (CollectionUtils.isEmpty(subOrderResp.getSubtOrderBaseInfoList())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("查询结果:查无此单!");
                    return resp;
                }
                List<OrderBase> subTiccetList = TicketOrderConvert.getSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), "TicketOrder");
                // 获取订单结果
                // 构建所需要的参数
                com.juneyaoair.mobile.handler.controller.service.bean.OrderDetailRequest detailRequest =
                        new com.juneyaoair.mobile.handler.controller.service.bean.OrderDetailRequest();
                BeanUtils.copyNotNullProperties(shareOrderDetailRequest, detailRequest);
                ShareOrderResp shareOrderResp = orderDetailService.getShareOrderResult(subOrderResp, subTiccetList, ip, shareOrderDetailRequest.getChannelCode());
                // 处理额外行李(国内航线且开关打开)
                // 获取全部城市
                Set<String> cityCodeSet = Sets.newHashSet();
                shareOrderResp.getSegmentList().forEach(segment -> {
                    cityCodeSet.add(segment.getDepCity());
                    cityCodeSet.add(segment.getArrCity());
                });
                Segment segment = localCacheService.getSegment(cityCodeSet.toArray(new String[]{}));
                boolean useExtraBaggagePricingFlag = HandlerConstants.TRIP_TYPE_D.equals(segment.getSegmentType()) && iExtraBaggageService.whetherUesNewBaggageProduct();
                shareOrderResp.setUseExtraBaggagePricingFlag(useExtraBaggagePricingFlag);
                if (shareOrderResp.isUseExtraBaggagePricingFlag()) {
                    shareOrderResp.setExtraBaggagePricing(handConfig.getExtraBaggagePricing());
                }
                resp.setObjData(shareOrderResp);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("查询结果:查无此单!");
            }
        } catch (Exception e) {
                log.info("查询航班动态信息异常，错误信息为：{}", e.getMessage(), e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络异常");
                return resp;
            }
        return  resp;

    }


    /**
     * orderCouponList 数量大于1，且全部都是抵扣券，且抵扣比例couponRebate为0
     *
     * @param orderResult
     */
    private void setIsMultiCoupon(OrderDetailResp orderResult) {
        // orderCouponList 数量大于1，且全部都是抵扣券，且抵扣比例couponRebate为0
        if (CollectionUtils.isNotEmpty(orderResult.getOrderCouponList())
                && orderResult.getOrderCouponList().size() > 1
                && orderResult.getOrderCouponList().stream().allMatch(orderCouponInfo -> StringUtils.equals(orderCouponInfo.getCouponType(), CouponTypeEnum.D.getType()))
                && orderResult.getOrderCouponList().stream().allMatch(orderCouponInfo -> "0".equals(orderCouponInfo.getCouponRebate()))) {
            orderResult.setMultiCoupons(true);
        }
    }

    private void setIsSpeedRefund(OrderDetailRequest orderDetailRequest, OrderDetailResp orderResult, Map<String, String> headMap) {
        RefundDetailReq refundDetailReq = new RefundDetailReq();
        refundDetailReq.setChannelCode(orderDetailRequest.getRequestChannelCode());
        //
        List<OrderPassengerInfo> orderPassengerInfoList = orderResult.getOrderPassengerInfoList();
        //
        //根据机票订单号查询是否存在退款信息
        for (OrderPassengerInfo orderPassengerInfo : orderPassengerInfoList) {
            refundDetailReq.setTicketOrderNo(orderPassengerInfo.getTicketOrderNo());
            HttpResult result = HttpUtil.doPostClient(refundDetailReq, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_REFUND_DETAILS, headMap);
            if (result.isResult() && StringUtils.isNotBlank(result.getResponse())) {
                SubRefundDetailResp subRefundDetailResp = (SubRefundDetailResp) JsonUtil.jsonToBean(result.getResponse(), SubRefundDetailResp.class);
                //展示查看退单详情按钮
                if (subRefundDetailResp != null && UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(subRefundDetailResp.getResultCode())) {
                    List<RefundDetailResp> ticketRefundList = subRefundDetailResp.getTicketRefundList();
                    if (CollectionUtils.isNotEmpty(orderResult.getPassengerList())) {
                        List<OrderDetailPassenger> passengerList = orderResult.getPassengerList();
                        passengerList.parallelStream().forEach(orderDetailPassenger -> {
                            ticketRefundList.parallelStream().forEach(refundDetailResp -> {
                                if (CollectionUtils.isNotEmpty(refundDetailResp.getRefundDetailList())) {
                                    if (refundDetailResp.getRefundDetailList().stream().anyMatch(subQueryRefundDetail ->
                                            orderDetailPassenger.getTicketNos().contains(subQueryRefundDetail.getTicketNo()) || "候补无票退款".equals(subQueryRefundDetail.getTicketNo()))) {
                                        orderDetailPassenger.setRefundNo(refundDetailResp.getRefundNo());
                                    }

                                }
                            });
                            orderDetailPassenger.setIsSpeedRefund(false);
                        });
                    }
                }
            }
        }
    }


    /**
     * set payCouponInfoDesc by ExtraBaggage/PaySeat
     *
     * @param orderResult
     * @param ip
     * @param ptSubOrderReq
     * @param userNo
     */
    private void setPayCouponInfoDesc(OrderDetailResp orderResult, String ip, PtSubOrderReq ptSubOrderReq, String userNo) {
        /**
         * 2021-8-20
         * 添加购买权益券判断
         */
        Optional.ofNullable(orderResult.getTravelPrivilegeList())
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(privileges -> privileges.stream()
                        .filter(travelPrivilege -> "BusPlusCoupon".equals(travelPrivilege.getType()))
                        .findFirst())
                .ifPresent(travelPrivilege ->
                        orderResult.setPayCouponInfoDesc("此订单为包车机票订单，如您退票，将在退票完成后一同取消包车服务并作废包车券")
                );
        if (!StringUtil.isNullOrEmpty(orderResult.getPassengerList())) {
            QueryCouponInfoRequest queryCouponInfoRequest = new QueryCouponInfoRequest();
            queryCouponInfoRequest.setRequestIp(ip);
            queryCouponInfoRequest.setChannelCode(ptSubOrderReq.getChannelCode());
            queryCouponInfoRequest.setUserNo(userNo);
            queryCouponInfoRequest.setVersion("10");
            //获取客票信息
            List<String> ticketNos = new ArrayList<>();
            for (OrderDetailPassenger orderDetailPassenger : orderResult.getPassengerList()) {
                if (orderDetailPassenger != null && CollectionUtils.isNotEmpty(orderDetailPassenger.getTicketNos())) {
                    ticketNos.addAll(orderDetailPassenger.getTicketNos());
                }
            }
            if (!StringUtil.isNullOrEmpty(ticketNos)) {
                queryCouponInfoRequest.setTicketNoList(ticketNos);
                String payCouponDesc = orderManage.isPayCoupon(queryCouponInfoRequest);
                if (!StringUtil.isNullOrEmpty(payCouponDesc)) {
                    payCouponDesc = payCouponDesc + "，需要单独申请退款，确认继续退票吗？";
                    orderResult.setPayCouponInfoDesc(payCouponDesc);
                }
            }
        }
    }

    //获取我的可用积分
    @RequestMapping(value = "/myAvailScore", method = RequestMethod.POST)
    public ScoreUseRuleResp getMyScore(@RequestBody MyScoreReq myScoreReq, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_myAvailScore";
        ScoreUseRuleResp resp = new ScoreUseRuleResp();
        String ip = this.getClientIP(request);
        String reqJson = JsonUtil.objectToJson(myScoreReq);
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MyScoreReq>> violations = validator.validate(myScoreReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        try {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(myScoreReq.getFfpId(), myScoreReq.getLoginKeyInfo(), myScoreReq.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            boolean accmflag = false;
            MemberInfoQueryResponseForClient memberInfoquery = crmClient.memberInfoquery(Long.valueOf(myScoreReq.getFfpId()), myScoreReq.getChannelCode(), getClientPwd(myScoreReq.getChannelCode()));
            if ("认证成功".equals(memberInfoquery.getMemberQueryInfo().getRealVerifyStatus())) {
                accmflag = true;
            }
            if (!accmflag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                return resp;
            }
            //返回客户可用积分
            String channelCode = myScoreReq.getChannelCode();
            VerifyConsumeMilesResponseForClient accmClient = crmClient.getAccmInfo(Long.parseLong(myScoreReq.getFfpId()), channelCode, getChannelInfo(channelCode, "40"));
            if (accmClient.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setUserScore(Integer.parseInt(accmClient.getMemberRedeemInfo().getAvailableMiles()));
            } else {
                String reqStr = JsonUtil.objectToJson(myScoreReq);
                log.error("请求号:{},查询积分出错:{}", reqId, reqStr);
                resp.setUserScore(0);
            }
        } catch (Exception e) {
            log.error("请求号:{},查询积分出错:{}", reqId, e.getMessage());
            resp.setUserScore(0);
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
        String respJson = JsonUtil.objectToJson(resp);
        log.info("请求号:{}，响应结果:{}", reqId, respJson);
        return resp;
    }

    //<editor-fold desc="增值产品查询">
    //取休息室详细信息（退前查看是否可退）F
    @RequestMapping(value = "/queryLoungeDetail", method = RequestMethod.POST)
    public LoungeGetResp queryOrderLoungeDetail(@RequestBody OrderDetailRequest orderDetailRequest, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_queryLoungeDetail";
        String ip = this.getClientIP(request);
        String reqJson = JsonUtil.objectToJson(orderDetailRequest);
        log.info("请求号:{}，IP地址:{},客户端提交参数:{}", reqId, ip, reqJson);
        LoungeGetResp resp = new LoungeGetResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderDetailRequest.getCustomerNo(), orderDetailRequest.getLoginKeyInfo(), orderDetailRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<OrderDetailRequest>> violations = validator.validate(orderDetailRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(orderDetailRequest.getChannelCode(), "10");
        Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(orderDetailRequest.getChannelCode(), ip);
        resp = queryLoungeInfo(userNo, orderDetailRequest.getChannelCode(), orderDetailRequest.getChannelOrderNo(), orderDetailRequest.getOrderNo(), orderDetailRequest.getCustomerNo(), airportMap);
        //<editor-fold desc="订单详情查询成功，将数据放入缓存">
        resp.setCustomerNo(orderDetailRequest.getCustomerNo());
        resp.setCardNo(orderDetailRequest.getCardNo());
        resp.setLoginKeyInfo(orderDetailRequest.getLoginKeyInfo());
        String key = StringUtil.newGUID() + orderDetailRequest.getCustomerNo();
        resp.setOrderSign(key);
        //1小时处理时间
        apiRedisService.replaceData(key, JsonUtil.objectToJson(resp), 3600);
        //</editor-fold>
        String respJson = JsonUtil.objectToJson(resp);
        log.info(LOG_RESP_FIVE, reqId, respJson);
        return resp;
    }

    //取wifi详细信息（退前查看是否可退）
    @RequestMapping(value = "/queryWifiDetail", method = RequestMethod.POST)
    public WifiGetResp queryOrderWifiDetail(@RequestBody OrderDetailRequest orderDetailRequest, HttpServletRequest request) {
        WifiGetResp resp = new WifiGetResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderDetailRequest.getCustomerNo(), orderDetailRequest.getLoginKeyInfo(), orderDetailRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<OrderDetailRequest>> violations = validator.validate(orderDetailRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(orderDetailRequest.getChannelCode(), "10");
        resp = queryWifiInfo(userNo, orderDetailRequest.getChannelCode(), orderDetailRequest.getChannelOrderNo(), orderDetailRequest.getOrderNo(), orderDetailRequest.getCustomerNo());
        //<editor-fold desc="订单详情查询成功，将数据放入缓存">
        resp.setCustomerNo(orderDetailRequest.getCustomerNo());
        resp.setCardNo(orderDetailRequest.getCardNo());
        resp.setLoginKeyInfo(orderDetailRequest.getLoginKeyInfo());
        String key = StringUtil.newGUID() + orderDetailRequest.getCustomerNo();
        resp.setOrderSign(key);
        //1小时处理时间
        apiRedisService.replaceData(key, JsonUtil.objectToJson(resp), 3600);
        //</editor-fold>
        return resp;
    }

    //取行程单邮寄信息
    @RequestMapping(value = "/queryDeliveryDetail", method = RequestMethod.POST)
    public TicketDeliveryGetResp queryOrderDeliveryDetail(@RequestBody OrderDetailRequest orderDetailRequest, HttpServletRequest request) {
        TicketDeliveryGetResp resp = new TicketDeliveryGetResp();
        String clientIp = this.getClientIP(request);
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderDetailRequest.getCustomerNo(), orderDetailRequest.getLoginKeyInfo(), orderDetailRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<OrderDetailRequest>> violations = validator.validate(orderDetailRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(orderDetailRequest.getChannelCode(), "10");
        resp = queryDeliveryInfo(userNo, orderDetailRequest.getChannelCode(), orderDetailRequest.getChannelOrderNo(), orderDetailRequest.getOrderNo(), orderDetailRequest.getCustomerNo(), clientIp);
        //<editor-fold desc="订单详情查询成功，将数据放入缓存">
        resp.setCustomerNo(orderDetailRequest.getCustomerNo());
        resp.setCardNo(orderDetailRequest.getCardNo());
        resp.setLoginKeyInfo(orderDetailRequest.getLoginKeyInfo());
        String key = StringUtil.newGUID() + orderDetailRequest.getCustomerNo();
        resp.setOrderSign(key);
        //1小时处理时间
        apiRedisService.replaceData(key, JsonUtil.objectToJson(resp), 3600);
        //</editor-fold>
        return resp;
    }

    //wifi订单生成
    @RequestMapping(value = "/wifiBooking", method = RequestMethod.POST)
    public WifiGetResp wifiBook(@RequestBody WifiBookingRequest bookReq, HttpServletRequest request) {
        WifiGetResp resp = new WifiGetResp();
        String ip = StringUtil.isNullOrEmpty(bookReq.getIp()) ? this.getClientIP(request) : bookReq.getIp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WifiBookingRequest>> violations = validator.validate(bookReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(bookReq.getChannelCode(), "10");
        PtWifiBookingReq target = new PtWifiBookingReq(HandlerConstants.VERSION,
                bookReq.getChannelCode(), userNo);
        BeanUtils.copyProperties(bookReq, target);
        target.setOrderRequestIp(ip);
        target.setFfpCardNo(bookReq.getFfpCardNo());
        HttpResult serviceResult = doPost(target, HandlerConstants.URL_FARE + HandlerConstants.SUB_BUY_WIFI);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (WifiGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), WifiGetResp.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("wifi订单生成结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("wifi订单规则出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("生成wifi订单出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }

    }

    //休息室订单生成
    @RequestMapping(value = "/loungeBooking", method = RequestMethod.POST)
    public LoungeGetResp loungeBook(@RequestBody LoungeBookingRequest bookReq, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_loungeBooking";
        String ip = StringUtil.isNullOrEmpty(bookReq.getIp()) ? this.getClientIP(request) : bookReq.getIp();
        String reqJson = JsonUtil.objectToJson(bookReq);
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        LoungeGetResp resp = new LoungeGetResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<LoungeBookingRequest>> violations = validator.validate(bookReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        Double payableAmount = bookReq.getPayAmount();//订单总金额
        int usedScore = bookReq.getUseScore();//积分抵扣
        Double actualAmount = payableAmount - usedScore;//订单应支付金额
        if (usedScore > 0) {//使用积分需要验证用户的消费密码
            String patternStr = PatternCommon.SALE_P_W_D;//消费密码
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(bookReq.getPwd());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("消费密码为六位数字");
                return resp;
            }
            VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(Long.valueOf(bookReq.getFfpId()), bookReq.getPwd(), bookReq.getChannelCode(), getClientPwd(bookReq.getChannelCode()));
            if (!"S000".equals(clientResp.getMessageHeader().getErrorCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResp.getMessageHeader().getDescription());
                return resp;
            }
            //积分分配
            int avgScore = usedScore / bookReq.getLoungeList().size();//平均分配
            int remScore = usedScore % bookReq.getLoungeList().size();//余留积分
            int count = 0;
            for (LoungeInfo loungeQuery : bookReq.getLoungeList()) {
                if (count == 0) {
                    loungeQuery.setUseScore(avgScore + remScore);
                } else {
                    loungeQuery.setUseScore(avgScore);
                }
                count++;
            }
        }
        String userNo = getChannelInfo(bookReq.getChannelCode(), "10");
        PtLoungeBookingReq target = new PtLoungeBookingReq(HandlerConstants.VERSION,
                bookReq.getChannelCode(), userNo);
        BeanUtils.copyProperties(bookReq, target);
        target.setOrderRequestIp(ip);
        target.setFfpCardNo(bookReq.getFfpCardNo());
        target.setIsSingleBuy("Y");
        target.setPayAmount(actualAmount);
        HttpResult serviceResult = doPost(target, HandlerConstants.URL_FARE + HandlerConstants.SUB_BUY_LOUNGE);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (LoungeGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), LoungeGetResp.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                    //0元同步支付
                    if (usedScore > 0 && actualAmount == 0.00) {
                        String key = getChannelInfo(bookReq.getChannelCode(), "20");
                        String postUrl = HandlerConstants.URL_PAY;
                        Map<String, String> parametersMap = VirtualPaymentConvert.payment0(bookReq.getChannelCode(), resp.getOrderNo(), resp.getChannelOrderNo(), key, VALUE_TWO, "", "O");
                        parametersMap.put("UseScore", String.valueOf(usedScore));
                        HttpResult payResult = doPayPost(postUrl, parametersMap);
                        PaymentResp paymentResp;
                        if (payResult.isResult()) {
                            String paymentInfo = payResult.getResponse().trim();
                            log.info("请求号:{},支付结果:{}", reqId, paymentInfo);
                            paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                            if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {//虚拟支付成功
                                resp.setResultCode("P10001");
                                resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                            } else {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setErrorInfo("休息室购买失败！");
                            }
                            String respJson = JsonUtil.objectToJson(resp);
                            log.info(LOG_RESP_FIVE, reqId, respJson);
                            return resp;
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setErrorInfo("支付请求出错");
                            String respJson = JsonUtil.objectToJson(resp);
                            log.info(LOG_RESP_FIVE, reqId, respJson);
                            return resp;
                        }
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("休息室订单生成结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("休息室订单规则出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("生成休息室订单出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    //一元购休息室
    @RequestMapping(value = "/oneLoungeBooking", method = RequestMethod.POST)
    public LoungeGetResp oneLoungeBooking(@RequestBody LoungeBookingRequest bookReq, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_loungeBooking";
        String ip = StringUtil.isNullOrEmpty(bookReq.getIp()) ? this.getClientIP(request) : bookReq.getIp();
        String reqJson = JsonUtil.objectToJson(bookReq);
        log.info("请求号:{}，IP地址:{},客户端提交参数:{}", reqId, ip, reqJson);
        LoungeGetResp resp = new LoungeGetResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<LoungeBookingRequest>> violations = validator.validate(bookReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        Double payableAmount = bookReq.getPayAmount();//订单总金额
        int usedScore = bookReq.getUseScore();//积分抵扣
        Double actualAmount = payableAmount - usedScore;//订单应支付金额
        if (usedScore > 0) {//使用积分需要验证用户的消费密码
            String patternStr = PatternCommon.SALE_P_W_D;//消费密码
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(bookReq.getPwd());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("消费密码为六位数字");
                return resp;
            }
            VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(Long.valueOf(bookReq.getFfpId()), bookReq.getPwd(), bookReq.getChannelCode(), getClientPwd(bookReq.getChannelCode()));
            if (!"S000".equals(clientResp.getMessageHeader().getErrorCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResp.getMessageHeader().getDescription());
                return resp;
            }
            //积分分配
            int avgScore = usedScore / bookReq.getLoungeList().size();//平均分配
            int remScore = usedScore % bookReq.getLoungeList().size();//余留积分
            int count = 0;
            for (LoungeInfo loungeQuery : bookReq.getLoungeList()) {
                if (count == 0) {
                    loungeQuery.setUseScore(avgScore + remScore);
                } else {
                    loungeQuery.setUseScore(avgScore);
                }
                count++;
            }
        }
        String userNo = getChannelInfo(bookReq.getChannelCode(), "10");
        List<LoungeInfo> list = new ArrayList<>();
        LoungeInfo loungeInfo = bookReq.getLoungeList().get(0);
        loungeInfo.setLoungeAmount(1.0);
        loungeInfo.setLoungeOrgAmount(1.0);
        list.add(loungeInfo);
        bookReq.setLoungeList(list);
        PtLoungeBookingReq target = new PtLoungeBookingReq(HandlerConstants.VERSION,
                bookReq.getChannelCode(), userNo);
        BeanUtils.copyProperties(bookReq, target);
        target.setOrderRequestIp(ip);
        target.setFfpCardNo(bookReq.getFfpCardNo());
        target.setIsSingleBuy("Y");
        target.setPayAmount(actualAmount);
        HttpResult serviceResult = doPost(target, HandlerConstants.URL_FARE + HandlerConstants.SUB_BUY_ONE_LOUNGE);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (LoungeGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), LoungeGetResp.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                    //0元同步支付
                    if (usedScore > 0 && actualAmount == 0.00) {
                        String key = getChannelInfo(bookReq.getChannelCode(), "20");
                        String postUrl = HandlerConstants.URL_PAY;
                        Map<String, String> parametersMap = VirtualPaymentConvert.payment0(bookReq.getChannelCode(), resp.getOrderNo(), resp.getChannelOrderNo(), key, VALUE_TWO, "", "O");
                        parametersMap.put("UseScore", String.valueOf(usedScore));
                        HttpResult payResult = doPayPost(postUrl, parametersMap);
                        PaymentResp paymentResp;
                        if (payResult.isResult()) {
                            String paymentInfo = payResult.getResponse().trim();
                            log.info("请求号:{},支付结果:{}", reqId, paymentInfo);
                            paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                            if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {//虚拟支付成功
                                resp.setResultCode("P10001");
                                resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                            } else {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setErrorInfo("休息室购买失败！");
                            }
                            String respJson = JsonUtil.objectToJson(resp);
                            log.info(LOG_RESP_FIVE, reqId, respJson);
                            return resp;
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setErrorInfo("支付请求出错");
                            String respJson = JsonUtil.objectToJson(resp);
                            log.info(LOG_RESP_FIVE, reqId, respJson);
                            return resp;
                        }
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("休息室订单生成结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("休息室订单规则出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("生成休息室订单出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    //单独购买保险
    @InterfaceLog
    @RequestMapping(value = "/insureBooking", method = RequestMethod.POST)
    public TicketBookingResp insureBook(@RequestBody @Validated InsuranceBookingRequest bookReq, BindingResult bindingResult, HttpServletRequest request) {
        TicketBookingResp resp = new TicketBookingResp();
        String ip = StringUtil.isNullOrEmpty(bookReq.getIp()) ? this.getClientIP(request) : bookReq.getIp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String userNo = getChannelInfo(bookReq.getChannelCode(), "10");
        if (CommonBaseConstants.IBE_PASSENGER_TYPE_INF.equals(bookReq.getTicketInfo().getPassengerType())
                && HandlerConstants.FLIGHT_INTER_D.equals(bookReq.getInterFlag())) {
            if (StringUtils.isBlank(bookReq.getInfIdentityNo())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("婴儿购保请输入身份证号");
                return resp;
            }
            //正则表达式
            Pattern pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            Matcher matcher = pattern.matcher(bookReq.getInfIdentityNo());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("身份证号不正确");
                return resp;
            }
            String birthDay = CertUtil.certNoToDate(bookReq.getInfIdentityNo());
            int age = DateUtils.getAgeByBirthIncludeBirthDay(birthDay,
                    bookReq.getTicketInfo().getSegmentInfoList().get(bookReq.getTicketInfo().getSegmentInfoList().size() - 1).getDepTime().substring(0, 10),
                    DateUtils.YYYY_MM_DD_PATTERN);
            if (age >= 2) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("婴儿年龄必须小于2岁");
                return resp;
            }
        }
        PtTicketBookingReq bookRequest;
        try {
            bookRequest = OrderObjectConvert.ToInsurePlatformReq(bookReq, userNo, ip, apiRedisService);
        } catch (CommonException commonException) {
            log.error("生成单独购保信息出错，出错编码为:{},出错信息为:{}", commonException.getResultCode(), commonException.getErrorMsg());
            resp.setResultCode(commonException.getResultCode());
            resp.setErrorInfo(commonException.getErrorMsg());
            return resp;
        }
        bookRequest.setIsNewProcessInsurance("Y".equalsIgnoreCase(handConfig.getIsNewProcessInsurance()) ? "Y" : "N");// M站单独购保都走新流程
        HttpResult insureResult = this.doPostClient(bookRequest, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_BUY_INSURE_APPLY);
        if (null != insureResult && insureResult.isResult()) {
            try {
                resp = (TicketBookingResp) JsonUtil.jsonToBean(insureResult.getResponse(), TicketBookingResp.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("生成保险订单规则出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("生成保险订单出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    //单独邮寄行程单
    @RequestMapping(value = "/deliveryBooking", method = RequestMethod.POST)
    public TicketDeliveryGetResp deliveryBook(@RequestBody DeliveryBookingRequest bookReq, HttpServletRequest request) {
        TicketDeliveryGetResp resp = new TicketDeliveryGetResp();
        String ip = StringUtil.isNullOrEmpty(bookReq.getIp()) ? this.getClientIP(request) : bookReq.getIp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<DeliveryBookingRequest>> violations = validator.validate(bookReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(bookReq.getChannelCode(), "10");
        PtDeliveryBookingReq target = new PtDeliveryBookingReq(HandlerConstants.VERSION,
                bookReq.getChannelCode(), userNo);
        BeanUtils.copyProperties(bookReq, target);
        PtTripCertSendInfo tripCertSendInfo = new PtTripCertSendInfo();
        BeanUtils.copyProperties(bookReq.getTripCertSendInfo(), tripCertSendInfo);
        List<PtTripCertSendInfo> sendInfoList = new ArrayList<>();
        sendInfoList.add(tripCertSendInfo);
        target.setTripCertSendList(sendInfoList);
        target.setPayAmount(Double.parseDouble(String.valueOf(tripCertSendInfo.getDeliveryFee())));
        target.setOrderRequestIp(ip);
        target.setIsSingleBuy("Y");
        HttpResult serviceResult = doPost(target, HandlerConstants.URL_FARE + HandlerConstants.SUB_BUY_DELIVERY);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (TicketDeliveryGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), TicketDeliveryGetResp.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("邮寄行程单订单生成结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("邮寄行程单订单规则出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("邮寄行程单订单出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    //</editor-fold>


    //取消订单
    @ApiOperation(value = "取消机票订单", notes = "取消机票订单")
    @InterfaceLog
    @RequestMapping(value = "/cancelOrder", method = RequestMethod.POST)
    public CancelOrderResp cancelOrder(@RequestBody @Validated CancelOrderRequest cancelOrderRequest, BindingResult bindingResult, HttpServletRequest request) {
        CancelOrderResp resp = new CancelOrderResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(cancelOrderRequest.getCustomerNo(), cancelOrderRequest.getLoginKeyInfo(), cancelOrderRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String userNo = getChannelInfo(cancelOrderRequest.getChannelCode(), "10");
        String path = HandlerConstants.URL_FARE_API + HandlerConstants.SUB_CANCEL_ORDER;
        PtCancelOrderReq ptCancelOrderReq = new PtCancelOrderReq(HandlerConstants.VERSION, cancelOrderRequest.getBookChannelCode(), userNo, cancelOrderRequest.getChannelOrderNo(), cancelOrderRequest.getOrderNo());
        if (VALUE_TWO.equals(cancelOrderRequest.getOrderType())) {//休息室
            path = HandlerConstants.URL_FARE + HandlerConstants.SUB_CANCEL_TOUR_ORDER;
            ptCancelOrderReq.setChannelCustomerNo(cancelOrderRequest.getCustomerNo());
            ptCancelOrderReq.setRemark("客户自行取消");
        }
        //M站过来的取消订单请求，默认改为MOBILE
        if (ptCancelOrderReq.getChannelCode().equalsIgnoreCase(ChannelCodeEnum.MWEB.getChannelCode())) {
            ptCancelOrderReq.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        CancelOrderReason cancelOrderReason;
        if (null == cancelOrderRequest.getCancelOrderReason() && handConfig.getCancelOrderReasonConfig().get(cancelOrderRequest.getOrderType()) != null) {
            List<CancelOrderReason> reasons = handConfig.getCancelOrderReasonConfig().get(cancelOrderRequest.getOrderType());
            cancelOrderReason = reasons.stream().filter(CancelOrderReason::isDefault).findFirst().orElse(null);
        } else {
            cancelOrderReason = cancelOrderRequest.getCancelOrderReason();
        }
        ptCancelOrderReq.setReason(null == cancelOrderReason ? "未选择取消原因" : cancelOrderReason.getReason());
        HttpResult serviceResult = doPostClient(ptCancelOrderReq, path);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (CancelOrderResp) JsonUtil.jsonToBean(serviceResult.getResponse(), CancelOrderResp.class);
                if (!resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("取消结果:" + resp.getErrorInfo());
                    return resp;
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                return resp;
            } catch (Exception e) {
                log.error("取消出错:{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("取消订单返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    @RequestMapping(value = "queryCancelOrderReason", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询取消订单原因选项", notes = "查询取消订单原因选项")
    public BaseResp<List<CancelOrderReason>> recordQuery(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<List<CancelOrderReason>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            List<CancelOrderReason> cancelOrderReasons = handConfig.getCancelOrderReasonConfig().get(req.getRequest());
            if (CollectionUtils.isNotEmpty(cancelOrderReasons)) {
                resp.setObjData(cancelOrderReasons.stream().filter(CancelOrderReason::isShow).collect(Collectors.toList()));
            } else {
                resp.setObjData(Lists.newArrayList());
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }


    /**
     * 航班变动标识
     */
    @RequestMapping(value = "/flightChangeLogo", method = RequestMethod.POST)
    public BaseResp flightChangeLogo(@RequestBody FlightChangeRequest flightChangeRequest, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        List<FlightChange> flightChangeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(flightChangeRequest.getFlightStatusRequestList())) {
            flightChangeRequest.getFlightStatusRequestList().forEach(newFlightStatusRequest -> {
                List<NewFlightInfo> newFlightInfos = queryNewFlightInfo(newFlightStatusRequest, ip);
                if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                    NewFlightInfo newFlightInfo = newFlightInfos.get(0);
                    FlightChange flightChange = new FlightChange();
                    flightChange.setFlightNo(newFlightInfo.getFlight_no());
                    flightChange.setFlightDate(newFlightInfo.getFlight_date());
                    flightChange.setFlightStatus(newFlightInfo.getFlight_status());
                    flightChange.setDepartureAirport(newFlightInfo.getDeparture_airport());
                    flightChange.setArrivalAirport(newFlightInfo.getArrival_airport());
                    flightChange.setDepartureCity(newFlightInfo.getDeparture_city());
                    flightChange.setArrivalCity(newFlightInfo.getArrival_city());
                    flightChange.setFlightStatus(newFlightInfo.getFlight_status());
                    flightChange.setFlightStatusCode(newFlightInfo.getAdjust_type());
                    flightChangeList.add(flightChange);
                }
            });
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(flightChangeList);
        return resp;
    }


    /**
     * 获取航班动态信息
     */
    public List<NewFlightInfo> queryNewFlightInfo(NewFlightStatusRequest req, String ip) {
        try {
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(req.getFlightNo());
            ptFlightStatusReq.setFlightDateLocal(DateUtils.dateToString(DateUtils.toDate(req.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN), DateUtils.YYYY_MM_DD_PATTERN));
            ptFlightStatusReq.setDepartureCityCode(req.getDepartureCity());
            ptFlightStatusReq.setArrivalCityCode(req.getArrivalCity());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse resp = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (SUCCESS.equals(resp.getCode())) {
                List<NewFlightInfo> data = resp.getData();
                return data;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.info("查询航班动态信息异常，错误信息为：{}", e.getMessage(), e);
            return null;
        }
    }


    public static void main(String[] args) {
        List<CancelOrderReason> cancelOrderReasons = new ArrayList<>();
        cancelOrderReasons.add(new CancelOrderReason("0", "未选择取消原因", true, false));
        cancelOrderReasons.add(new CancelOrderReason("1", "信息填写错误，重新订票", false, true));
        cancelOrderReasons.add(new CancelOrderReason("2", "出行计划变动", false, true));
        cancelOrderReasons.add(new CancelOrderReason("3", "机票价格发生变化", false, true));
        cancelOrderReasons.add(new CancelOrderReason("4", "发现更便宜的机票", false, true));
        cancelOrderReasons.add(new CancelOrderReason("5", "改乘其他交通工具", false, true));
        cancelOrderReasons.add(new CancelOrderReason("6", "其他原因", false, true));
        Map<String, List<CancelOrderReason>> map = new HashMap<>();
        map.put(OrderSortEnum.Normal.getOrderSort(), cancelOrderReasons);
        System.out.println(JsonUtil.objectToJson(map));
    }

    //取休息室订单
    private LoungeGetResp queryLoungeInfo(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo, Map<String, AirPortInfoDto> airportMap) {
        LoungeGetResp loungeGetResp = new LoungeGetResp();
        String path = HandlerConstants.URL_FARE + HandlerConstants.SUB_GET_LOUNGE;
        PtLoungeGetReq requObj = createLoungeGetRequest(userNo, channelCode, channelOrderNo, orderNo, customerNo);
        HttpResult serviceResult = doPost(requObj, path);
        if (null != serviceResult && serviceResult.isResult()) {
            loungeGetResp = (LoungeGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), LoungeGetResp.class);
            if (!loungeGetResp.getResultCode().equals("1001")) {
                loungeGetResp.setResultCode(WSEnum.ERROR.getResultCode());
                loungeGetResp.setErrorInfo(LOG_RESP_THREE + loungeGetResp.getErrorInfo());
            } else {
                int loungeBuyCnt = 0;
                Double loungeBuyAmt = 0.0;
                int useScore = 0;//积分抵扣
                for (LoungeBuy loungeBuy : loungeGetResp.getLoungeBuyList()) {
                    loungeBuyCnt += loungeBuy.getLoungeCount();
                    loungeBuyAmt += loungeBuy.getLoungeOrderAmount();
                    useScore += loungeBuy.getUseScore();
                    loungeBuy.setDepAirportNm(airportMap.get(loungeBuy.getDepAirport()).getAirPortName());
                }
                loungeGetResp.setPayType(PayMethodEnum.CASH.value);
                loungeGetResp.setUseScore(useScore);
                loungeGetResp.setLoungeCountTotal(loungeBuyCnt);
                loungeGetResp.setLoungeOrderAmountPayableTotal(loungeBuyAmt);//订单应付金额
                loungeGetResp.setLoungeOrderAmountTotal(loungeBuyAmt - useScore);//订单实付金额
                if (useScore > 0 && useScore == loungeBuyAmt) {
                    loungeGetResp.setPayType(PayMethodEnum.SCORE.value);
                }
                loungeGetResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                loungeGetResp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
            }
        } else {
            loungeGetResp.setResultCode(WSEnum.ERROR.getResultCode());
            loungeGetResp.setErrorInfo(LOG_RESP_TWO);
        }
        return loungeGetResp;
    }

    //取wifi订单
    private WifiGetResp queryWifiInfo(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        WifiGetResp wifiGetResp = new WifiGetResp();
        String path = HandlerConstants.URL_FARE + HandlerConstants.SUB_GET_WIFI;
        PtWifiGetReq requObj = createWifiGetRequest(userNo, channelCode, channelOrderNo, orderNo, customerNo);
        HttpResult serviceResult = doPost(requObj, path);
        if (null != serviceResult && serviceResult.isResult()) {
            wifiGetResp = (WifiGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), WifiGetResp.class);
            if (!wifiGetResp.getResultCode().equals("1001")) {
                wifiGetResp.setResultCode(WSEnum.ERROR.getResultCode());
                wifiGetResp.setErrorInfo(LOG_RESP_THREE + wifiGetResp.getErrorInfo());
            } else {
                Double wifiBuyAmt = 0.0;
                for (WifiBuy wifiBuy : wifiGetResp.getWifiBuyList()) {
                    wifiBuyAmt += wifiBuy.getOrderAmount();
                }
                wifiGetResp.setPayAmount(wifiBuyAmt);
                wifiGetResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                wifiGetResp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
            }
        } else {
            wifiGetResp.setResultCode(WSEnum.ERROR.getResultCode());
            wifiGetResp.setErrorInfo(LOG_RESP_TWO);
        }
        return wifiGetResp;
    }

    //取行程单订单
    private TicketDeliveryGetResp queryDeliveryInfo(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo, String clientIp) {
        TicketDeliveryGetResp deliveryGetResp = new TicketDeliveryGetResp();
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        String path = HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_DELIVERY;
        PtTicketDeliveryGetReq requObj = createDeliveryGetRequest(userNo, channelCode, channelOrderNo, orderNo, customerNo);
        HttpResult serviceResult = doPostClient(requObj, path, headMap);
        if (serviceResult.isResult() && !StringUtil.isNullOrEmpty(serviceResult.getResponse())) {
            deliveryGetResp = (TicketDeliveryGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), TicketDeliveryGetResp.class);
            if (!deliveryGetResp.getResultCode().equals("1001")) {
                deliveryGetResp.setResultCode(WSEnum.ERROR.getResultCode());
                deliveryGetResp.setErrorInfo(LOG_RESP_THREE + deliveryGetResp.getErrorInfo());
            } else {
                deliveryGetResp.setDeliveryFee(deliveryGetResp.getTripCertSendInfo().getDeliveryFee());
                deliveryGetResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                deliveryGetResp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
            }
        } else {
            deliveryGetResp.setResultCode(WSEnum.ERROR.getResultCode());
            deliveryGetResp.setErrorInfo(LOG_RESP_TWO);
        }
        return deliveryGetResp;
    }


    //查询休息室
    private PtLoungeGetReq createLoungeGetRequest(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        return new PtLoungeGetReq(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                channelOrderNo,
                orderNo,
                customerNo
        );
    }

    //查询wifi
    private PtWifiGetReq createWifiGetRequest(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        return new PtWifiGetReq(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                channelOrderNo,
                orderNo,
                customerNo
        );
    }

    //查询行程单
    private PtTicketDeliveryGetReq createDeliveryGetRequest(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        return new PtTicketDeliveryGetReq(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                channelOrderNo,
                orderNo,
                customerNo
        );
    }

    //取crm接口客户端密码
    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }


    //保险状态
    private void setInsuranceStateDesc(OrderDetailResp orderResult) {
        List<InsuranceInfo> insuranceStateas = new ArrayList<InsuranceInfo>();
        List<InsuranceInfo> newinsuranceStateas = new ArrayList<InsuranceInfo>();
        //获取订单中所有保险信息
        if (orderResult.getOrderPassengerInfoList() != null && orderResult.getOrderPassengerInfoList().size() > 0) {
            orderResult.getOrderPassengerInfoList().forEach(orderPassengerInfo -> {
                if (orderPassengerInfo.getInsuranceList() != null && orderPassengerInfo.getInsuranceList().size() > 0) {
                    orderPassengerInfo.getInsuranceList().stream().forEach(insuranceInfo -> {
                        InsuranceInfo insuranceInfo1 = new InsuranceInfo();
                        insuranceInfo1.setInsuranceState(insuranceInfo.getInsuranceState());
                        insuranceInfo1.setInsuranceNumber(insuranceInfo.getInsuranceNumber());
                        insuranceInfo1.setInsuranceAmount(insuranceInfo.getInsuranceAmount() / insuranceInfo.getInsuranceNumber());
                        insuranceInfo1.setInsuranceBillNo(insuranceInfo.getInsuranceBillNo());
                        insuranceInfo1.setInsuranceCode(insuranceInfo.getInsuranceCode());
                        insuranceInfo1.setInsuranceName(insuranceInfo.getInsuranceName());
                        insuranceInfo1.setIsRetire(insuranceInfo.getIsRetire());
                        if (insuranceInfo1.getInsuranceState().equals(InsuranceOrderStatEnum.APPLY.getInsStat())) {
                            insuranceInfo1.setInsuranceStateName(InsuranceOrderStatEnum.APPLY.getInsStatDesc());
                        } else if (insuranceInfo1.getInsuranceState().equals(InsuranceOrderStatEnum.SUCCESS.getInsStat())) {
                            insuranceInfo1.setInsuranceStateName(InsuranceOrderStatEnum.SUCCESS.getInsStatDesc());
                        } else if (insuranceInfo1.getInsuranceState().equals(InsuranceOrderStatEnum.DELETE.getInsStat())) {
                            insuranceInfo1.setInsuranceStateName(InsuranceOrderStatEnum.DELETE.getInsStatDesc());
                        } else if (insuranceInfo1.getInsuranceState().equals(InsuranceOrderStatEnum.FAILED.getInsStat())) {
                            insuranceInfo1.setInsuranceStateName(InsuranceOrderStatEnum.FAILED.getInsStatDesc());
                        } else if (insuranceInfo1.getInsuranceState().equals(InsuranceOrderStatEnum.AWAITDELETE.getInsStat())) {
                            insuranceInfo1.setInsuranceStateName(InsuranceOrderStatEnum.AWAITDELETE.getInsStatDesc());
                        } else if (insuranceInfo1.getInsuranceState().equals(InsuranceOrderStatEnum.DELETEFAILED.getInsStat())) {
                            insuranceInfo1.setInsuranceStateName(InsuranceOrderStatEnum.DELETEFAILED.getInsStatDesc());
                        }
                        insuranceInfo.setInsuranceStateName(insuranceInfo1.getInsuranceStateName());
                        insuranceStateas.add(insuranceInfo1);
                    });
                }
            });
        }
        //按照分组
        Map<String, List<InsuranceInfo>> stringListMap = insuranceStateas.stream().collect(Collectors.groupingBy(InsuranceInfo::getInsuranceCode));
        for (Map.Entry<String, List<InsuranceInfo>> entry : stringListMap.entrySet()) {
            List<InsuranceInfo> newStateas = entry.getValue();
            List<InsuranceInfo> insuranceInfoList = newStateas.stream()
                    .collect(Collectors.toMap(InsuranceInfo::getInsuranceCode, a -> a, (o1, o2) -> {
                        if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.APPLY.getInsStat()) && o2.getInsuranceState().equals(InsuranceOrderStatEnum.APPLY.getInsStat())) {
                            o1.setInsuranceStateName(InsuranceOrderStatEnum.APPLY.getInsStatDesc());
                        } else if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.SUCCESS.getInsStat()) && o2.getInsuranceState().equals(InsuranceOrderStatEnum.SUCCESS.getInsStat())) {
                            o1.setInsuranceStateName(InsuranceOrderStatEnum.SUCCESS.getInsStatDesc());
                        } else if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.DELETE.getInsStat()) && o2.getInsuranceState().equals(InsuranceOrderStatEnum.DELETE.getInsStat())) {
                            o1.setInsuranceStateName(InsuranceOrderStatEnum.DELETE.getInsStatDesc());
                        } else if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.FAILED.getInsStat()) && o2.getInsuranceState().equals(InsuranceOrderStatEnum.FAILED.getInsStat())) {
                            o1.setInsuranceStateName(InsuranceOrderStatEnum.FAILED.getInsStatDesc());
                        } else if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.AWAITDELETE.getInsStat()) && o2.getInsuranceState().equals(InsuranceOrderStatEnum.AWAITDELETE.getInsStat())) {
                            o1.setInsuranceStateName(InsuranceOrderStatEnum.AWAITDELETE.getInsStatDesc());
                        } else if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.DELETEFAILED.getInsStat()) && o2.getInsuranceState().equals(InsuranceOrderStatEnum.DELETEFAILED.getInsStat())) {
                            o1.setInsuranceStateName(InsuranceOrderStatEnum.DELETEFAILED.getInsStatDesc());
                        } else {
                            if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.AWAITDELETE.getInsStat()) || o2.getInsuranceState().equals(InsuranceOrderStatEnum.AWAITDELETE.getInsStat())) {
                                o1.setInsuranceState(InsuranceOrderStatEnum.HALFAWAITDELETE.getInsStat());
                                o1.setInsuranceStateName(InsuranceOrderStatEnum.HALFAWAITDELETE.getInsStatDesc());
                            }
                            if (o1.getInsuranceState().equals(InsuranceOrderStatEnum.DELETE.getInsStat()) || o2.getInsuranceState().equals(InsuranceOrderStatEnum.DELETE.getInsStat())) {
                                o1.setInsuranceStateName(InsuranceOrderStatEnum.HALFDELETE.getInsStatDesc());
                                o1.setInsuranceState(InsuranceOrderStatEnum.HALFDELETE.getInsStat());
                            }
                        }
                        o1.setInsuranceNumber(o1.getInsuranceNumber() + o2.getInsuranceNumber());
                        return o1;
                    })).values().stream().collect(Collectors.toList());

            newinsuranceStateas.addAll(insuranceInfoList);
        }

        orderResult.setInsuranceStateList(newinsuranceStateas);
    }


    //处理订单详情中的保险状态
    private void setInsuranceState(OrderDetailResp orderResult, PtSubOrderReq ptSubOrderReq) {
        PtRefundInsureDetailReq ptBrief = new PtRefundInsureDetailReq();
        BeanUtils.copyProperties(ptSubOrderReq, ptBrief);
        orderResult.getOrderPassengerInfoList().stream()
                .filter(orderPassengerInfo -> CollectionUtils.isNotEmpty(orderPassengerInfo.getSegmentPriceInfoList()))
                .forEach(orderPassengerInfo -> orderPassengerInfo.getSegmentPriceInfoList().stream()
                        .filter(segmentPriceInfo -> CollectionUtils.isNotEmpty(segmentPriceInfo.getInsuranceList()))
                        .forEach(segmentPriceInfo -> segmentPriceInfo.getInsuranceList().forEach(
                                insuranceInfo -> {
                                    List<String> cannotRefundIndIdList = insuranceService.getCannotRefundIndIdList();
                                    if (cannotRefundIndIdList.contains(insuranceInfo.getInsuranceCode())) {
                                        //如果不可退保险id集合中包含此保险id，不可退
                                        insuranceInfo.setIsRetire("N");
                                    } else {
                                        insuranceInfo.setIsRetire("Y");
                                    }
                                }
                        )));
        orderResult.getOrderPassengerInfoList().stream()
                .filter(orderPassengerInfo -> CollectionUtils.isNotEmpty(orderPassengerInfo.getInsuranceList()))
                .forEach(orderPassengerInfo -> orderPassengerInfo.getInsuranceList()
                        .forEach(insuranceInfo -> {
                            List<String> cannotRefundIndIdList = insuranceService.getCannotRefundIndIdList();
                            if (cannotRefundIndIdList.contains(insuranceInfo.getInsuranceCode())) {
                                //如果不可退保险id集合中包含此保险id，不可退
                                insuranceInfo.setIsRetire("N");
                            } else {
                                insuranceInfo.setIsRetire("Y");
                            }
                            if (InsuranceStatEnum.DELETE.getInsStat().equalsIgnoreCase(insuranceInfo.getInsuranceState())) {
                                ptBrief.setInsuranceBillNo(insuranceInfo.getInsuranceBillNo());
                                //查询保险退保详情
                                HttpResult serviceResult = doPost(ptBrief, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_INSURE_DETAIL);
                                if (null != serviceResult && serviceResult.isResult()) {
                                    try {
                                        RefundInsureDetailResp refundInsureDetailResp = (RefundInsureDetailResp) JsonUtil.jsonToBean(serviceResult.getResponse(), RefundInsureDetailResp.class);
                                        if (refundInsureDetailResp.getResultCode().equals("1001") && refundInsureDetailResp.getInsuranceRefund() != null) {
                                            InsuranceRefund insuranceRefund = refundInsureDetailResp.getInsuranceRefund();
                                            //退保还在处理中
                                            if (InsuranceDeleteStatEnmu.APPLY.getDeletsStat().equalsIgnoreCase(insuranceRefund.getSurrenderState())
                                                    || InsuranceDeleteStatEnmu.OCHECK.getDeletsStat().equalsIgnoreCase(insuranceRefund.getSurrenderState())
                                                    || InsuranceDeleteStatEnmu.PROBLEM.getDeletsStat().equalsIgnoreCase(insuranceRefund.getSurrenderState())) {
                                                insuranceInfo.setInsuranceState(InsuranceStatEnum.AWAITDELETE.getInsStat());
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error("【订单详情】保险退保状态查询失败", e);
                                    }
                                }
                            }
                        }));
    }

    private void setInsuranceOrderState(OrderDetailResp orderResult) {
        //只处理保险订单和已支付订单
        if (!INSURANCE_ORDER.equalsIgnoreCase(orderResult.getOrderSort())) {
            return;
        }
        if (!OrderPayStateEnum.Pay.getStateCode().equalsIgnoreCase(orderResult.getOrderPayState())) {
            return;
        }
        //没有保险不处理
        List<InsuranceInfo> insuranceList = orderResult.getOrderPassengerInfoList().get(0).getInsuranceList();
        if (CollectionUtils.isEmpty(insuranceList)) {
            return;
        }
        //处理保险
        //保险个数
        int size = insuranceList.size();
        //已退保险个数
        int refundCount = 0;
        //退款中个数
        int waitDelete = 0;
        for (InsuranceInfo insuranceInfo : insuranceList) {
            //已退保的保险
            if (InsuranceStatEnum.DELETE.getInsStat().equalsIgnoreCase(insuranceInfo.getInsuranceState())) {
                refundCount++;
            }
            if (InsuranceStatEnum.AWAITDELETE.getInsStat().equalsIgnoreCase(insuranceInfo.getInsuranceState())) {
                waitDelete++;
            }
        }
        //如果全部退保
        if (refundCount == size) {
            orderResult.setOrderPayState(OrderPayStateEnum.Refund.getStateCode());
            orderResult.setOrderPayStateName(OrderPayStateEnum.Refund.getStateDesc());
            return;
        }
        //如果全部退款中
        if (waitDelete == size) {
            orderResult.setOrderPayState(OrderPayStateEnum.Refunding.getStateCode());
            orderResult.setOrderPayStateName(OrderPayStateEnum.Refunding.getStateDesc());
            return;
        }
        //部分已退款部分待退款
        if ((refundCount + waitDelete) == size) {
            orderResult.setOrderPayState(OrderPayStateEnum.Refunding.getStateCode());
            orderResult.setOrderPayStateName(OrderPayStateEnum.Refunding.getStateDesc());
        }
    }

    @Autowired
    private CrmWSClient crmClient;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IExtraBaggageService iExtraBaggageService;

    @Autowired
    private TravellerHttpApi travellerHttpApi;
}
