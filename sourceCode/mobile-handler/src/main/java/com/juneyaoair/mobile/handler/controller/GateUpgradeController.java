package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSON;
import com.geetest.geeguard.sdk.GeetestLib;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.gateupgrade.UpgradeOrderStatus;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.member.ContactTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.OrderStateEnum;
import com.juneyaoair.appenum.order.PayEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.gateupgrade.request.*;
import com.juneyaoair.baseclass.gateupgrade.response.*;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.service.GeetestService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberContactSoaModel;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName UpgradeController
 * @Description 登机口升舱
 * <AUTHOR>
 * @Date 2019/8/7 10:01
 **/
@RestController
@RequestMapping("/gateUpgrade/")
@Api(value = "GateUpgradeController")
public class GateUpgradeController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private GeetestService geetestService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    private String networkError = "网络异常，请稍后再试";

    private String noDataResp = "抱歉，您暂无符合活动条件的航班。如有疑问可查看登机口升舱相关协议或咨询客服95520";

    @InterfaceLog
    @RequestMapping(value = "queryUpgradeOrderList", method = RequestMethod.POST)
    @ApiOperation(value = "查询登机口升舱订单", notes = "查询登机口升舱订单")
    public BaseResp queryUpgradeOrderList(@RequestBody BaseReq<UpgTourQueryListRequestDTO> req, HttpServletRequest request) {
        BaseResp<QueryUpgradeOrderResponse> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        String channelCode = req.getChannelCode();
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        try {
            //校验参数
            UpgTourQueryListRequestDTO upgTourQueryListRequestDTO = req.getRequest();
            // 需要验证极验
            boolean jiYanFlag = true;
            // 存在UUID 检查Redis是否存在该数据 匹配不验证极验
            if (StringUtils.isNotBlank(upgTourQueryListRequestDTO.getUuid())) {
                String key = RedisKeyConfig.QUERY_UPGRADE_ORDER + upgTourQueryListRequestDTO.getUuid();
                String data = apiRedisService.getData(key);
                if (upgTourQueryListRequestDTO.getCertNo().equalsIgnoreCase(data)) {
                    jiYanFlag = false;
                }
            }
            //短信进入不验证极验
            if ("Y".equalsIgnoreCase(req.getRequest().getIsSMS())) {
                jiYanFlag = false;
            }
            if (jiYanFlag) {
                //极验验证操作
                GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
                HashMap<String, String> param = new HashMap<>();
                param.put("user_id", ip); //网站用户id  设备号
                param.put("client_type", upgTourQueryListRequestDTO.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
                param.put("ip_address", ip); //传输用户请求验证时所携带的IP
                Geetest geetest = new Geetest(upgTourQueryListRequestDTO.getGeetest_challenge(), upgTourQueryListRequestDTO.getGeetest_validate(), upgTourQueryListRequestDTO.getGeetest_seccode());
                geetestService.validateMd5(gtSdk, geetest, param);
            }
            //此处两值只是为了检验参数通过，无实际意义
            upgTourQueryListRequestDTO.setClient_type("h5");
            upgTourQueryListRequestDTO.setScenario(GeetestTypeEnum.TICKET.getGeetestType());
            validateRequest(req);
            // 增加校验
            if (!"SHA".equalsIgnoreCase(upgTourQueryListRequestDTO.getDepCityCode())) {
                log.error("1.非上海出港的国内航班，暂不支持升舱,请求ID：{} 票号：{} 出发城市：{}", MdcUtils.getRequestId(), upgTourQueryListRequestDTO.getCertNo(), upgTourQueryListRequestDTO.getDepCityCode());
                throw new CommonException(WSEnum.ERROR.getResultCode(), "非上海出港的国内航班，暂不支持升舱");
            }
            CityInfoDto arrCityInfo = localCacheService.getLocalCity(upgTourQueryListRequestDTO.getArrCityCode());
            if (null == arrCityInfo || !HandlerConstants.TRIP_TYPE_D.equals(arrCityInfo.getIsInternational())) {
                log.error("2.非上海出港的国内航班，暂不支持升舱,请求ID：{} 票号：{} 到达城市：{}", MdcUtils.getRequestId(),
                        upgTourQueryListRequestDTO.getCertNo(), null == arrCityInfo ? upgTourQueryListRequestDTO.getArrCityCode() : JSON.toJSONString(arrCityInfo));
                throw new CommonException(WSEnum.ERROR.getResultCode(), "非上海出港的国内航班，暂不支持升舱");
            }
            UpgradeBaseRequest<?> baseRequestDTO = buildRequest(ip, channelCode, req);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult httpResult = this.doPostClient(baseRequestDTO, HandlerConstants.URL_FARE_API + HandlerConstants.GATE_UPGRADE_QUERY_UPG_TOURS, headMap);
            if (null == httpResult || !httpResult.isResult()) {
                throw new OperationFailedException(networkError);
            }
            String resultStr = httpResult.getResponse();
            BaseResultDTO<List<UpgTourQueryResponseDTO>> resultDTO = (BaseResultDTO<List<UpgTourQueryResponseDTO>>)
                    JsonUtil.jsonToBean(resultStr, new TypeToken<BaseResultDTO<List<UpgTourQueryResponseDTO>>>() {
                    }.getType());
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resultDTO.getResultCode())) {
                if (resultDTO.getErrorMsg().contains("null") || resultDTO.getErrorMsg().contains("Exception")) {
                    throw new CommonException(WSEnum.ERROR.getResultCode(), "系统异常，请稍后再试");
                } else if ("未获取到旅客符合条件的行程信息".equals(resultDTO.getErrorMsg())) {
                    log.error("1.升舱未获取到旅客符合条件的行程信息,请求ID：{} 票号：{}", MdcUtils.getRequestId(), upgTourQueryListRequestDTO.getCertNo());
                    throw new CommonException(WSEnum.ERROR.getResultCode(), noDataResp);
                }
                throw new CommonException(WSEnum.ERROR.getResultCode(), resultDTO.getErrorMsg());
            }
            QueryUpgradeOrderResponse queryUpgradeOrderResponse = new QueryUpgradeOrderResponse();
            queryUpgradeOrderResponse.setFinishedList(new ArrayList<>());
            queryUpgradeOrderResponse.setToBeProceedList(new ArrayList<>());
            queryUpgradeOrderResponse.setToPayList(new ArrayList<>());
            queryUpgradeOrderResponse.setAll(new ArrayList<>());
            List<UpgTourQueryResponseDTO> upgTourQueryResponseDTOS = resultDTO.getResult();
            UpgTourQueryListRequestDTO upgTourQueryRequest = req.getRequest();
            upgTourQueryResponseDTOS = upgTourQueryResponseDTOS.stream()
                    .filter(dto ->
                            upgTourQueryRequest.getFlightNo().equals(dto.getFlight().getFlightNo())
                    )
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(upgTourQueryResponseDTOS)) {
                queryUpgradeOrderResponse.setAll(upgTourQueryResponseDTOS);
                upgTourQueryResponseDTOS.forEach(item -> {
                    handleResponseInfo(item);
                    switch (item.getStatus()) {
                        case 6:
                            item.setSortOrder(3);/* 已退款 */
                            queryUpgradeOrderResponse.getFinishedList().add(item);
                            break;
                        case 7:
                            item.setSortOrder(1);/* 确认中 */
                            queryUpgradeOrderResponse.getFinishedList().add(item);
                            break;
                        case 3:
                            item.setSortOrder(2);/* 已完成 */
                            queryUpgradeOrderResponse.getFinishedList().add(item);
                            break;
                        case 2:
                            queryUpgradeOrderResponse.getToPayList().add(item);
                            break;
                        case 1:
                            queryUpgradeOrderResponse.getToBeProceedList().add(item);
                            break;
                        default:
                    }
                });
            }
            if (CollectionUtils.isEmpty(queryUpgradeOrderResponse.getAll())) {
                response.setResultCode(WSEnum.NO_DATA.getResultCode());
                log.error("2.升舱未获取到旅客符合条件的行程信息,请求ID：{} 票号：{}", MdcUtils.getRequestId(), upgTourQueryListRequestDTO.getCertNo());
                response.setResultInfo(noDataResp);
                return response;
            }
            if (CollectionUtils.isNotEmpty(queryUpgradeOrderResponse.getFinishedList())) {
                queryUpgradeOrderResponse.getFinishedList().sort(
                        Comparator.comparing(UpgTourQueryResponseDTO::getSortOrder).thenComparing(e -> e.getFlight().getFlightDate()));
            }
            if (!"Y".equalsIgnoreCase(req.getRequest().getIsSMS()) && CollectionUtils.isEmpty(queryUpgradeOrderResponse.getFinishedList())
                    && CollectionUtils.isEmpty(queryUpgradeOrderResponse.getToBeProceedList())
                    && CollectionUtils.isEmpty(queryUpgradeOrderResponse.getToPayList())) {
                // 只有一个行程 且 存在描述返回描述给前端
                boolean flag = upgTourQueryResponseDTOS.size() == 1 && null != upgTourQueryResponseDTOS.get(0) && StringUtils.isNotBlank(upgTourQueryResponseDTOS.get(0).getUnmanageableReason());
                if (flag) {
                    response.setResultCode(WSEnum.NO_DATA.getResultCode());
                    response.setResultInfo(upgTourQueryResponseDTOS.get(0).getUnmanageableReason());
                    return response;
                }
                log.error("3.升舱未获取到旅客符合条件的行程信息,请求ID：{} 票号：{}", MdcUtils.getRequestId(), upgTourQueryListRequestDTO.getCertNo());
                response.setResultCode(WSEnum.NO_DATA.getResultCode());
                response.setResultInfo(noDataResp);
                return response;
            }
            if (CollectionUtils.isNotEmpty(queryUpgradeOrderResponse.getToPayList())) {
                queryUpgradeOrderResponse.getToBeProceedList()
                        .sort((e1, e2) -> (int) (e1.getPay().getCountdownTimestamp() - e2.getPay().getCountdownTimestamp()));
            }
            // 生成UUID 用于前端第二次访问无法获取极验绕过极验
            if (jiYanFlag) {
                String uuid = UUID.randomUUID().toString();
                String key = RedisKeyConfig.QUERY_UPGRADE_ORDER + uuid;
                apiRedisService.putData(key, upgTourQueryListRequestDTO.getCertNo(), 5 * 60L);
                queryUpgradeOrderResponse.setUuid(uuid);
            }
            response.setObjData(queryUpgradeOrderResponse);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (CommonException ce) {
            response.setResultCode(ce.getResultCode());
            response.setResultInfo(ce.getErrorMsg());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    /**
     * 基于机场三字码获取城市三字码
     *
     * @param airportCode
     * @return
     */
    private String getCityCode(String airportCode) {
        if (StringUtils.isBlank(airportCode)) {
            return null;
        }
        AirPortInfoDto airPortInfo = localCacheService.getLocalAirport(airportCode);
        return null == airPortInfo ? null : airPortInfo.getCityCode();
    }

    @RequestMapping(value = "queryUpgradeOrderDetail", method = RequestMethod.POST)
    @ApiOperation(value = "查询登机口升舱订单详情", notes = "查询登机口升舱订单详情")
    public BaseResp queryUpgradeOrderDetail(HttpServletRequest request, @RequestBody BaseReq<UpgTourQueryDetailRequestDTO> req) {
        BaseResp<UpgTourQueryResponseDTO> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Upgrade_QueryUpgradeOrderDetail";
        String channelCode = req.getChannelCode();
        try {
            //校验参数
            validateRequest(req);
            UpgradeBaseRequest baseRequestDTO = buildRequest(ip, channelCode);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            baseRequestDTO.setRequest(req.getRequest());
            HttpResult httpResult = this.doPostClient(baseRequestDTO, HandlerConstants.URL_FARE_API + HandlerConstants.GATE_UPGRADE_GET_ORDER_INFO, headMap);
            if (null == httpResult || !httpResult.isResult()) {
                throw new OperationFailedException(networkError);
            }
            BaseResultDTO<UpgTourQueryResponseDTO> resultDTO = (BaseResultDTO<UpgTourQueryResponseDTO>)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<BaseResultDTO<UpgTourQueryResponseDTO>>() {
                    }.getType());
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resultDTO.getResultCode())) {
                if (resultDTO.getErrorMsg().contains("null") || resultDTO.getErrorMsg().contains("Exception")) {
                    throw new OperationFailedException("系统异常，请稍后再试");
                } else if ("未获取到旅客符合条件的行程信息".equals(resultDTO.getErrorMsg())) {
                    throw new OperationFailedException(noDataResp);
                }
                throw new OperationFailedException(resultDTO.getErrorMsg());
            }
            UpgTourQueryResponseDTO queryUpgradeOrderResponse = resultDTO.getResult();
            if (null != queryUpgradeOrderResponse) {
                handleResponseInfo(queryUpgradeOrderResponse);
                response.setObjData(queryUpgradeOrderResponse);
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                response.setResultInfo(noDataResp);
            }
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    @RequestMapping(value = "queryUpgradeSeatMap", method = RequestMethod.POST)
    @ApiOperation(value = "查询登机口升舱座位图", notes = "查询登机口升舱座位图")
    public BaseResp queryUpgradeSeatMap(HttpServletRequest request, @RequestBody BaseReq<UpgSeatMapRequestDTO> req) {
        BaseResp<QueryUpgradeSeatMapResponse> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Upgrade_QueryUpgradeSeatMap";
        try {
            //校验参数
            validateRequest(req);
            checkKeyInfo(req);
            UpgradeBaseRequest baseRequestDTO = buildRequest(ip, req.getChannelCode());
            baseRequestDTO.setUserNo(req.getRequest().getFfpId());
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            req.getRequest().setCurrency("CNY");
            baseRequestDTO.setRequest(req.getRequest());
            HttpResult httpResult = this.doPostClient(baseRequestDTO, HandlerConstants.URL_FARE_API + HandlerConstants.GATE_UPGRADE_QUERY_UPG_SEAT_MAP, headMap);
            if (null == httpResult || !httpResult.isResult()) {
                throw new OperationFailedException(networkError);
            }
            BaseResultDTO<QueryUpgradeSeatMapResponse> resultDTO = (BaseResultDTO<QueryUpgradeSeatMapResponse>)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<BaseResultDTO<QueryUpgradeSeatMapResponse>>() {
                    }.getType());
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resultDTO.getResultCode())) {
                throw new OperationFailedException(resultDTO.getErrorMsg());
            }
            QueryUpgradeSeatMapResponse queryUpgradeSeatMapResponse = resultDTO.getResult();
            if (CollectionUtils.isEmpty(queryUpgradeSeatMapResponse.getSeatMaplist())) {
                String logStr = JsonUtil.objectToJson(req);
                this.log.error("查询座位图失败!请求参数{}，订单中心响应{}", logStr, httpResult.getResponse());
                throw new OperationFailedException("查询座位图失败");
            }
            queryUpgradeSeatMapResponse.setInterFlag(req.getRequest().getInterFlag());
            queryUpgradeSeatMapResponse.setNewSeatMap(FlightUtil.gateUpgradeNewSeatMap(queryUpgradeSeatMapResponse));
            Map<String, String> seatMapConfig = JsonUtil.fromJson(handConfig.getGateUpgradeSeatMapCfg(),
                    new TypeToken<Map<String, String>>() {
                    }.getType());
            String planeType = convertCommonPlaneType(queryUpgradeSeatMapResponse.getPlaneType());
            if (null != seatMapConfig && seatMapConfig.containsKey(planeType)) {
                switch (seatMapConfig.get(planeType)) {
                    case "neither":
                        throw new OperationFailedException("暂未开放在线值机选座，请至柜台办理");
                    case "old":
                        if (queryUpgradeSeatMapResponse.isNewSeatMap()) {
                            throw new OperationFailedException("暂未开放在线值机选座，请至柜台办理");
                        }
                        break;
                    case "new":
                        if (!queryUpgradeSeatMapResponse.isNewSeatMap()) {
                            throw new OperationFailedException("暂未开放在线值机选座，请至柜台办理");
                        }
                        break;
                    case "both":
                        break;
                    default:
                        throw new OperationFailedException("登机口升舱座位图配置不正确");
                }
            }
            setDefaultMobile(queryUpgradeSeatMapResponse, request, req);
            handleSeatMapResponse(queryUpgradeSeatMapResponse);
            response.setObjData(queryUpgradeSeatMapResponse);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    private void setDefaultMobile(QueryUpgradeSeatMapResponse queryUpgradeSeatMapResponse, HttpServletRequest request, BaseReq<UpgSeatMapRequestDTO> req) {
        String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildMemberDetailReq(req.getRequest().getFfpCardNo(),
                req.getRequest().getFfpId(), request, req.getChannelCode(), items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
        if (ptCRMResponse.getCode() == 0) {
            List<MemberContactSoaModel> contactInfos = ptCRMResponse.getData().getContactInfo();
            if (CollectionUtils.isNotEmpty(contactInfos)) {
                for (MemberContactSoaModel memberContactSoaModel : contactInfos) {
                    if (ContactTypeEnum.MOBILE.getCode() == memberContactSoaModel.getContactType()
                            && StringUtils.isNotBlank(memberContactSoaModel.getContactNumber())) {
                        queryUpgradeSeatMapResponse.setDefaultMobile(memberContactSoaModel.getContactNumber());
                        break;
                    }
                }
            }
        }
    }

    private void handleSeatMapResponse(QueryUpgradeSeatMapResponse queryUpgradeSeatMapResponse) {
        queryUpgradeSeatMapResponse.setPlaneType(convertCommonPlaneType(queryUpgradeSeatMapResponse.getPlaneType()));
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, queryUpgradeSeatMapResponse.getPlaneType());
        if (aircraftModel != null) {
            queryUpgradeSeatMapResponse.setPlaneTypeRemark(aircraftModel.getRemark());
        }
        //获取选座321具体机型
        if (queryUpgradeSeatMapResponse.getPlaneType().equals("321")) {
            //头等舱
            int firstCabinCnt = 0;
            if (queryUpgradeSeatMapResponse.getCabin().equals("F")
                    || queryUpgradeSeatMapResponse.getCabin().equals("J")
                    || queryUpgradeSeatMapResponse.getCabin().equals("C")) {
                for (SeatChartDto seatChart : queryUpgradeSeatMapResponse.getSeatMaplist()) {
                    if (seatChart.getSeatColumn() != ' ' && !seatChart.getSeatStatus().equals("=")) {
                        firstCabinCnt++;
                    }
                }
                queryUpgradeSeatMapResponse.setFlightType(firstCabinCnt == 12 ? "190" : "198");
            } else {
                //经济舱
                queryUpgradeSeatMapResponse.setFlightType(queryUpgradeSeatMapResponse.getSeatMaplist().get(0).getSeatRow() == 4 ? "190" : "198");
            }
        }
        queryUpgradeSeatMapResponse.getSeatMaplist().forEach(item -> {
            //单位从分转化成元
            item.setPrice(item.getPrice().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        });
    }

    private String convertCommonPlaneType(String planeType) {
        String result = planeType;
        if (planeType.contains("787")) {
            result = "787";
        } else if (planeType.contains("789")) {
            result = "789";
        } else if (planeType.contains("320")) {
            result = "320";
        } else if (planeType.contains("321")) {
            result = "321";
        }
        return result;
    }

    @RequestMapping(value = "createUpgradeOrder", method = RequestMethod.POST)
    @ApiOperation(value = "创建登机口升舱订单", notes = "创建登机口升舱订单")
    @NotDuplicate
    public BaseResp createUpgradeOrder(HttpServletRequest request, @RequestBody BaseReq<UpgradeCreateOrderRequest> req) {
        BaseResp<UpgradeOrderInfo> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Upgrade_CreateUpgradeOrder";
        try {
            //校验参数
            validateRequest(req);
            //判断是否登录
            checkKeyInfo(req);
            UpgradeBaseRequest baseRequestDTO = buildRequest(ip, req.getChannelCode());
            baseRequestDTO.setUserNo(req.getRequest().getFfpId());
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            UpgradeCreateOrderRequest reqParam = req.getRequest();
            UpgCreateOrderRequestDTO requestDTO = new UpgCreateOrderRequestDTO();
            requestDTO.setFlightNo(reqParam.getFlight().getFlightNo());
            requestDTO.setFlightDate(reqParam.getFlight().getFlightDate());
            requestDTO.setDepAirportCode(reqParam.getFlight().getDepart().getAirportCode());
            requestDTO.setArrAirportCode(reqParam.getFlight().getArrive().getAirportCode());
            requestDTO.setDepTerminal(reqParam.getFlight().getDepart().getTerminal());
            requestDTO.setArrTerminal(reqParam.getFlight().getArrive().getTerminal());
            requestDTO.setDepTime(reqParam.getFlight().getDepart().getTime().replace(":", ""));
            requestDTO.setArrTime(reqParam.getFlight().getArrive().getTime().replace(":", ""));
            requestDTO.setPlaneType(reqParam.getFlight().getPlaneType());
            requestDTO.setPsgName(reqParam.getPassenger().getPsgName());
            requestDTO.setPhoneNo(reqParam.getPhoneNo());
            requestDTO.setFfpId(reqParam.getFfpId());
            requestDTO.setFfpCardNo(reqParam.getFfpCardNo());
            requestDTO.setCertNo(StringUtils.isBlank(reqParam.getPassenger().getCertNo())
                    ? reqParam.getPassenger().getTktNo() : reqParam.getPassenger().getCertNo());
            requestDTO.setTktNo(reqParam.getTicketNo());
            requestDTO.setPnrNo(reqParam.getPassenger().getPnrNo());
            requestDTO.setCabin(reqParam.getWantedSeat().getCabin());
            requestDTO.setSeatNo(reqParam.getWantedSeat().getSeatNo());
            requestDTO.setOriginCabin(reqParam.getOriginSeat().getCabin());
            requestDTO.setOriginSeatNo(reqParam.getOriginSeat().getSeatNo());
            requestDTO.setChannelOrderNo(reqParam.getChannelOrderNo());
            requestDTO.setPrice(BigDecimal.valueOf(100).multiply(reqParam.getPrice()));
            requestDTO.setInterFlag(reqParam.getInterFlag());
            requestDTO.setCurrency("CNY");
            baseRequestDTO.setRequest(requestDTO);
            HttpResult httpResult = this.doPostClient(baseRequestDTO, HandlerConstants.URL_FARE_API + HandlerConstants.GATE_UPGRADE_CREATE_ORDER, headMap);
            if (null == httpResult || !httpResult.isResult()) {
                throw new OperationFailedException(networkError);
            }
            CreateOrderResponseDTO resultDTO = (CreateOrderResponseDTO)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<CreateOrderResponseDTO>() {
                    }.getType());
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resultDTO.getResultCode())) {
                if (resultDTO.getErrorInfo().contains("记录已经处于冻结状态")) {
                    throw new OperationFailedException("该机票已经下过订单了，请勿重复下单");
                }
                throw new OperationFailedException(resultDTO.getErrorInfo());
            }
            Date date = new Date();
            UpgradeOrderInfo orderInfo = new UpgradeOrderInfo();
            orderInfo.setCreateTime(DateUtils.dateToString(date, DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
            if (null != resultDTO.getPayAmount()) {
                //单位从分转化成元
                orderInfo.setPrice(resultDTO.getPayAmount().divide(BigDecimal.valueOf(100), BigDecimal.ROUND_HALF_UP));
            }
            orderInfo.setCreateDatetime(date.getTime());
            orderInfo.setChannelOrderNo(resultDTO.getChannelOrderNo());
            orderInfo.setOrderNo(resultDTO.getOrderNo());
            orderInfo.setOrderType("GatewayUpgrade");
            orderInfo.setStatus(UpgradeOrderStatus.TO_PAY.getStatusCode() + "");
            response.setObjData(orderInfo);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    @RequestMapping(value = "queryUpgradeRules", method = RequestMethod.POST)
    @ApiOperation(value = "查询登机口升舱产品规则", notes = "查询登机口升舱产品规则")
    public BaseResp queryUpgradeRules(HttpServletRequest request, @RequestBody BaseReq req) {
        BaseResp<List<UpgradeRules>> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Upgrade_QueryUpgradeRules";
        try {
            //校验参数
            validateRequest(req);
            UpgQueryRuleRequestDTO upgQueryRuleRequestDTO = new UpgQueryRuleRequestDTO("1");
            HttpResult httpResult = this.doPostClient(upgQueryRuleRequestDTO, HandlerConstants.BASIC_INFO_URL + HandlerConstants.GATE_UPGRADE_QUERY_RULES);
            if (null == httpResult || !httpResult.isResult()) {
                throw new OperationFailedException(networkError);
            }
            QueryUpgRulesResponseDTO resultDTO = JSON.parseObject(httpResult.getResponse(), new TypeToken<QueryUpgRulesResponseDTO>() {
            }.getType());
            if ("10001".equals(resultDTO.getResultCode())) {
                response.setObjData(resultDTO.getTUpgRuleDTOList());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                this.log.error("登机口升舱规则查询失败，后台系统响应结果：{}", httpResult.getResponse());
                throw new OperationFailedException(resultDTO.getErrMsg());
            }
            List<UpgradeRules> upgradeRules = resultDTO.getTUpgRuleDTOList();
            if (CollectionUtils.isNotEmpty(upgradeRules)) {
                upgradeRules.forEach(rule -> {
                    String detail = rule.getUpgRuleDetail();
                    String[] detailItems = detail.split("#");
                    StringBuilder detailHtml = new StringBuilder("<p>");
                    for (int i = 0; i < detailItems.length; i++) {
                        detailHtml.append(detailItems[i]);
                        if (i != detailItems.length - 1) {
                            detailHtml.append("</p><p>");
                        }
                    }
                    detailHtml.append("</p>");
                    rule.setUpgRuleDetail(detailHtml.toString());
                });
            }
            response.setObjData(resultDTO.getTUpgRuleDTOList());
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    @RequestMapping(value = "decryptTktNoAndPsgName", method = RequestMethod.POST)
    @ApiOperation(value = "解密票号及旅客姓名", notes = "解密票号及旅客姓名")
    public BaseResp decryptTktNoAndPsgName(HttpServletRequest request, @RequestBody BaseReq<String> req) {
        BaseResp<UpgradeDecryptResponse> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Upgrade_DecryptTktNoAndPsgName";
        try {
            //校验参数
            validateRequest(req);
            String message = req.getRequest();//内容：票号|姓名|出发机场|航班号|航班日期|出发城市三字码|到达城市三字码
            String key = EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5("juneyaoair"));
            String result = SecurityUtil.decryptAES(message, key);
            String[] content = result.split("\\|");
            UpgradeDecryptResponse upgradeDecryptResponse = new UpgradeDecryptResponse();
            upgradeDecryptResponse.setTktNo(content[0]);
            upgradeDecryptResponse.setPsgName(content[1]);
            upgradeDecryptResponse.setDepAirportCode(content[2]);
            if (content.length > 3) {
                upgradeDecryptResponse.setFlightNo(content[3]);
                upgradeDecryptResponse.setFlightDate(content[4]);
                upgradeDecryptResponse.setDepCityCode(content[5]);
                upgradeDecryptResponse.setArrCityCode(content[6]);
            }
            response.setObjData(upgradeDecryptResponse);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    @RequestMapping(value = "cancelOrder", method = RequestMethod.POST)
    @ApiOperation(value = "取消订单", notes = "取消订单")
    public BaseResp cancelOrder(HttpServletRequest request, @RequestBody BaseReq<UpgCancelOrderRequestDTO> req) {
        BaseResp response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Upgrade_CancelOrder";
        try {
            //校验参数
            validateRequest(req);
            checkKeyInfo(req);
            UpgradeBaseRequest baseRequestDTO = buildRequest(ip, req.getChannelCode());
            baseRequestDTO.setUserNo(req.getRequest().getFfpId());
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            baseRequestDTO.setRequest(req.getRequest());
            HttpResult httpResult = this.doPostClient(baseRequestDTO, HandlerConstants.URL_FARE_API + HandlerConstants.GATE_UPGRADE_CANCEL_ORDER, headMap);
            if (null == httpResult || !httpResult.isResult()) {
                throw new OperationFailedException(networkError);
            }
            BaseResultDTO<UpgTourQueryResponseDTO> resultDTO = (BaseResultDTO<UpgTourQueryResponseDTO>)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<BaseResultDTO<UpgTourQueryResponseDTO>>() {
                    }.getType());
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resultDTO.getResultCode())) {
                throw new OperationFailedException(resultDTO.getErrorMsg());
            }
            response.setResultInfo("订单取消成功");
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    /**
     * 处理返回信息
     *
     * @param item
     * @return
     */
    private void handleResponseInfo(UpgTourQueryResponseDTO item) {
        AirPortInfoDto depAirport = localCacheService.getLocalAirport(item.getFlight().getDepart().getAirportCode());
        AirPortInfoDto arrAirport = localCacheService.getLocalAirport(item.getFlight().getArrive().getAirportCode());
        item.getFlight().getDepart().setAirportName(depAirport.getAirPortName() + "机场");
        item.getFlight().getDepart().setCity(depAirport.getCityName());
        item.getFlight().getArrive().setAirportName(arrAirport.getAirPortName() + "机场");
        item.getFlight().getArrive().setCity(arrAirport.getCityName());
        item.getFlight().setWeek(DateUtils.getWeekStr(DateUtils.toDate(item.getFlight().getFlightDate())));
        Date depTime = DateUtils.toDate(item.getFlight().getFlightDate() + " " + item.getFlight().getDepart().getTime(), "yyyy-MM-dd HHmm");
        Date arrTime = DateUtils.toDate(item.getFlight().getFlightDate() + " " + item.getFlight().getArrive().getTime(), "yyyy-MM-dd HHmm");
        if (depTime != null) {
            item.getFlight().getArrive().setTime(DateUtils.convertDateToString(arrTime, "HH:mm"));
            item.getFlight().getDepart().setTime(DateUtils.convertDateToString(depTime, "HH:mm"));
        }
        long duration = 0;
        if (null != depTime && null != arrTime) {
            duration = DateUtils.calDuration(
                    DateUtils.dateToString(depTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN), arrAirport.getCityTimeZone(),
                    DateUtils.dateToString(arrTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN), arrAirport.getCityTimeZone());
        }
        if (duration < 0) {//起飞到达不在一天的情况
            duration += 24 * 3600 * 1000;
            item.getFlight().setDurDay(1);
        }
        UpgOrderInfo upgOrderInfo = new UpgOrderInfo();
        upgOrderInfo.setDepTime(item.getFlight().getDepart().getTime());
        upgOrderInfo.setDepAirportName(item.getFlight().getDepart().getAirportName());
        upgOrderInfo.setDepCity(item.getFlight().getDepart().getCity());
        upgOrderInfo.setDepTerminal(item.getFlight().getDepart().getTerminal() == null ? "" : item.getFlight().getDepart().getTerminal());
        upgOrderInfo.setArrTime(item.getFlight().getArrive().getTime());
        upgOrderInfo.setArrAirportName(item.getFlight().getArrive().getAirportName());
        upgOrderInfo.setArrCity(item.getFlight().getArrive().getCity());
        upgOrderInfo.setArrTerminal(item.getFlight().getArrive().getTerminal() == null ? "" : item.getFlight().getArrive().getTerminal());
        upgOrderInfo.setFlightDate(item.getFlight().getFlightDate());
        upgOrderInfo.setFlightNo(item.getFlight().getFlightNo());
        upgOrderInfo.setPlaneType(item.getFlight().getPlaneType());
        upgOrderInfo.setDuration(duration);
        upgOrderInfo.setWeek(item.getFlight().getWeek());
        upgOrderInfo.setDurDay(item.getFlight().getDurDay());
        upgOrderInfo.setPhoneNo(item.getPassenger().getPhoneNo());
        upgOrderInfo.setCouponSource(VoucherTypesEnum.GATEWAYUPGRADE.getCode());
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, item.getFlight().getPlaneType());
        if (aircraftModel != null) {
            item.getFlight().setPlaneType(aircraftModel.getRemark());
        }
        if (item.getOrder() != null) {
            Date date = new Date(item.getOrder().getCreateDatetime());
            item.getOrder().setCreateTime(DateUtils.dateToString(date, DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
            if (null != item.getOrder().getPrice()) {
                //单位从分转化成元
                item.getOrder().setPrice(item.getOrder().getPrice().divide(BigDecimal.valueOf(100), 0, BigDecimal.ROUND_HALF_UP));
                upgOrderInfo.setPayPrice(item.getOrder().getPrice());
            }
            upgOrderInfo.setOrderNo(item.getOrder().getOrderNo());
            upgOrderInfo.setCreateTime(item.getOrder().getCreateTime());
            upgOrderInfo.setChannelOrderNo(item.getOrder().getChannelOrderNo());
            upgOrderInfo.setPrice(item.getOrder().getPrice());
        }
        if (null != item.getSeat().getOrigin()) {
            item.getSeat().getOrigin().setCabinClass(
                    CommonUtil.getCabinClassByCabinCode(item.getSeat().getOrigin().getCabin(), handConfig.getCabinClass()));
            item.getSeat().getOrigin().setCabinClassName(CommonUtil.showCabinClassName(item.getSeat().getOrigin().getCabinClass()));
            upgOrderInfo.setOriginCabin(item.getSeat().getOrigin().getCabin());
            upgOrderInfo.setOriginCabinClassName(item.getSeat().getOrigin().getCabinClassName());
            upgOrderInfo.setOriginSeatNo(item.getSeat().getOrigin().getSeatNo());
        }
        if (null != item.getSeat().getWanted() && StringUtils.isNotBlank(item.getSeat().getWanted().getCabin())) {
            item.getSeat().getWanted().setCabinClass(
                    CommonUtil.getCabinClassByCabinCode(item.getSeat().getWanted().getCabin(), handConfig.getCabinClass()));
            item.getSeat().getWanted().setCabinClassName(CommonUtil.showCabinClassName(item.getSeat().getWanted().getCabinClass()));
            upgOrderInfo.setWantedCabin(item.getSeat().getWanted().getCabin());
            upgOrderInfo.setWantedCabinClassName(item.getSeat().getWanted().getCabinClassName());
            upgOrderInfo.setWantedSeatNo(item.getSeat().getWanted().getSeatNo());
        }
        UpgradeOrderStatus status;
        if (item.getSeat().isCanUpgrade()) {
            status = UpgradeOrderStatus.TO_UPGRADE;
            item.getSeat().setCurrent(item.getSeat().getOrigin());
        } else {
            status = UpgradeOrderStatus.UNAVAILABLE;
            item.getSeat().setCurrent(item.getSeat().getOrigin());

            if (StringUtils.isNotBlank(item.getUnmanageableReason())) {
                if (item.getUnmanageableReason().contains("没有足够的公务舱座位") ||
                    item.getUnmanageableReason().contains("无可升舱座位") ||
                    item.getUnmanageableReason().contains("已售罄")) {
                    item.setUnmanageableReason("抱歉，当前航班已无可升舱座位，如有疑问请致电客服95520。");
                } /* 2023非售罄使用原始错误文案 else {
                    item.setUnmanageableReason("抱歉，暂时无法参加该活动，如有疑问，请联系客服~");
                } */
            }
        }
        if (null != item.getPay()) {
            if (PayEnum.UnPay.getStateCode().equals(item.getPay().getPayed()) || "N".equals(item.getPay().getPayed())) {
                status = UpgradeOrderStatus.TO_PAY;
                item.setShowNew(true);
                item.getSeat().setCurrent(item.getSeat().getWanted());
            }
            if (PayEnum.Pay.getStateCode().equals(item.getPay().getPayed())
                    || OrderStateEnum.Finish.getStateCode().equals(item.getOrder().getStatus())
                    || "Y".equals(item.getPay().getPayed())) {
                status = UpgradeOrderStatus.FINISHED;
                item.setShowNew(true);
                item.getSeat().setCurrent(item.getSeat().getWanted());
            }
            item.getPay().setCountdownTimestamp(item.getPay().getCountdownTimestamp() * 1000);//从秒改为毫秒
            upgOrderInfo.setMethod(item.getPay().getMethod());
            upgOrderInfo.setPayAt(item.getPay().getPayAt());
            // 倒计时如果小于0，则设置为0
            if (item.getPay().getCountdownTimestamp() < 0) {
                item.getPay().setCountdownTimestamp(0);
            }
            upgOrderInfo.setCountdownTimestamp(item.getPay().getCountdownTimestamp());
            if (item.getPay().getPayAt() > 0) {
                Date payTime = new Date(item.getPay().getPayAt());
                upgOrderInfo.setPayTime(DateUtils.dateToString(payTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
            }
        }
        if (item.getOrder() != null) {
            if (OrderStateEnum.Cancel.getStateCode().equals(item.getOrder().getStatus())) {
                status = UpgradeOrderStatus.CANCEL;
            }
            if (OrderStateEnum.Refund.getStateCode().equals(item.getOrder().getStatus())) {
                status = UpgradeOrderStatus.REFUND;
            }
            // 座位不是已确定时，订单状态改为确认中
            if (status == UpgradeOrderStatus.FINISHED && !"T".equals(item.getSeat().getWanted().getStatus())) {
                status = UpgradeOrderStatus.CONFIRMING;
            }
        }
        item.setStatus(status.getStatusCode());
        item.setStatusRemark(status.getStatusRemark());
        if (status.equals(UpgradeOrderStatus.TO_UPGRADE) && StringUtils.isNotBlank(item.getDiscountPrice())) {
            item.setUnmanageableReason(new BigDecimal(item.getDiscountPrice()).divide(BigDecimal.valueOf(100), BigDecimal.ROUND_HALF_UP).toString()
                    + "元优惠价升至公务舱，名额有限先到先得。");
        }
        item.getFlight().setDuration(duration);
        item.setOrderName("登机口升舱");
        upgOrderInfo.setOrderName(item.getOrderName());
        upgOrderInfo.setStatus(item.getStatus());
        upgOrderInfo.setStatusRemark(item.getStatusRemark());
        item.setOrderInfo(upgOrderInfo);
    }

    private UpgradeBaseRequest<? extends UpgTourQueryListRequestDTO> buildRequest(
            String ip, String channelCode, BaseReq<? extends UpgTourQueryListRequestDTO> req) {
        UpgradeBaseRequest request = this.buildRequest(ip, channelCode);
        req.getRequest().setCertNo(req.getRequest().getCertNo().replace("-", ""));
        request.setRequest(req.getRequest());
        return request;
    }

    private UpgradeBaseRequest buildRequest(String ip, String channelCode) {
        UpgradeBaseRequest request = new UpgradeBaseRequest<>();
        request.setRequestIp(ip);
        request.setChannelCode(channelCode);
        request.setUserNo(getChannelInfo(channelCode, "10"));
        request.setVersion("30");
        return request;
    }

    private PtApiCRMRequest<PtMemberDetailRequest> buildMemberDetailReq(String cardNo, String ffpId, HttpServletRequest request, String channelCode, String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(cardNo);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(request, ffpId, "");
        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getChannelInfo(channelCode, "40"));
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        return ptApiCRMRequest;
    }

    @RequestMapping(value = "encryptTktNoAndPsgName", method = RequestMethod.POST)
    @ApiOperation(value = "加密票号及旅客姓名（测试用）", notes = "加密票号及旅客姓名（测试用）")
    public BaseResp encryptTktNoAndPsgName(HttpServletRequest request, @RequestBody BaseReq<String> req) {
        BaseResp<String> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Upgrade_DecryptTktNoAndPsgName";
        try {
            //校验参数
            validateRequest(req);
            String message = req.getRequest();//内容：票号|姓名|起飞地三字码
            String key = EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5("juneyaoair"));
            String result = SecurityUtil.encryptAES(message, key);
            response.setObjData(result);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

}