package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.coupon.BoardingPassStateEnum;
import com.juneyaoair.appenum.order.OrderPayStateEnum;
import com.juneyaoair.appenum.order.PayEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherStateEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.airbookstore.bean.SupportFlightInfo;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.boardingpass.bean.BoardingPassFlightInfo;
import com.juneyaoair.baseclass.boardingpass.req.BoardingPassBook;
import com.juneyaoair.baseclass.boardingpass.req.QueryBoardingProductReq;
import com.juneyaoair.baseclass.boardingpass.resp.BoardingPassResp;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.BaseRequestDTO;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.newcoupon.bean.*;
import com.juneyaoair.baseclass.newcoupon.req.CouponOrderReq;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductQueryRequestDto;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.response.order.comm.BoardingPass;
import com.juneyaoair.baseclass.salecoupon.response.SaleCouponGet;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.IPaymentService;
import com.juneyaoair.mobile.handler.controller.util.RightCouponObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.VirtualPaymentConvert;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.VerifyConsumePasswdResponseForClient;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.boardingpass.common.BaseAirport;
import com.juneyaoair.thirdentity.boardingpass.common.BaseCouponOrderIdentity;
import com.juneyaoair.thirdentity.boardingpass.common.BasePassenger;
import com.juneyaoair.thirdentity.boardingpass.common.BasePay;
import com.juneyaoair.thirdentity.boardingpass.req.BoardingCheckBuyRequestDto;
import com.juneyaoair.thirdentity.boardingpass.req.QueryProductLimitRequest;
import com.juneyaoair.thirdentity.boardingpass.resp.CouponOrderInfo;
import com.juneyaoair.thirdentity.boardingpass.resp.QueryProductLimitResponse;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.request.checkin.QueryTourRequestDTO;
import com.juneyaoair.thirdentity.response.checkin.FlightTourDTO;
import com.juneyaoair.thirdentity.response.checkin.QueryTourResponseDTO;
import com.juneyaoair.thirdentity.salecoupon.common.ProductInfo;
import com.juneyaoair.thirdentity.salecoupon.common.ResourceInfo;
import com.juneyaoair.thirdentity.salecoupon.response.PtProductQueryResponseDto;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
@RequestMapping("boardingPass")
@Api(value = "预留登机牌服务", description = "预留登机牌服务")
public class BoardingPassController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private CrmWSClient crmClient;
    @Autowired
    private IPaymentService paymentService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private LocalCacheService localCacheService;


    private final static int READ_TIMEOUT = 40000;
    private final static int CONNECT_TIMEOUT = 40000;
    private static String SERVICE_NAME = "预留登机牌";

    @InterfaceLog
    @ApiOperation(value = "预留登机牌产品查询", notes = "预留登机牌产品查询")
    @RequestMapping(value = "queryBoardingPassProduct", method = RequestMethod.POST)
    public BaseResp<BoardingPassResp> queryBoardingPassProduct(@RequestBody BaseReq<QueryBoardingProductReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            QueryBoardingProductReq productReq = req.getRequest();
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            //验证用户查询是否正常
            if (!StringUtil.isNullOrEmpty(productReq.getFfpId()) || !StringUtil.isNullOrEmpty(productReq.getLoginKeyInfo())) {
                boolean flag = this.checkKeyInfo(productReq.getFfpId(), productReq.getLoginKeyInfo(), channelCode);
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            checkQueryReq(productReq, resp);
            if (!resp.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                return resp;
            }
            //查询值机选座获取旅客航班行程
            JSONObject tags = new JSONObject();
            tags.put("IP",ip);
            tags.put("FfpCardNo",productReq.getFfpCardNo());
            tags.put("ChannelCode",headChannelCode);
            MetricLogUtil.saveMetricLog("预留登机牌-客票提取",tags,new BigDecimal(1));
            List<FlightTourDTO> flightTourDTOS = queryTours(request, req, resp, productReq);
            if (!resp.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                return resp;
            }
            BoardingPassResp boardingPassResp = new BoardingPassResp();
            //查询预留登机牌前端所需信息
            Date curDate = new Date();
            List<BoardingPassFlightInfo> boardingPassFlightInfoList = flightTourDTOS.stream().map(flightTourDTO -> queryBoardingProductInfo(flightTourDTO, channelCode, userNo, curDate, request, productReq)).collect(Collectors.toList());
            //行程信息分组处理
            List<BoardingPassFlightInfo> onBoardingPassFlightInfoList = new ArrayList<>();
            List<BoardingPassFlightInfo> notBoardingPassFlightInfoList = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(boardingPassFlightInfoList)) {
                boardingPassFlightInfoList.forEach(boardingPassFlightInfo -> {
                    if (BoardingPassStateEnum.Use.getState() == boardingPassFlightInfo.getState()) {
                        onBoardingPassFlightInfoList.add(boardingPassFlightInfo);
                    } else {
                        notBoardingPassFlightInfoList.add(boardingPassFlightInfo);
                    }
                });
            }
            //列表排序处理
            if (!StringUtil.isNullOrEmpty(onBoardingPassFlightInfoList)) {
                onBoardingPassFlightInfoList.sort(Comparator.comparing(BoardingPassFlightInfo::getFlightDepDate));
            }
            if (!StringUtil.isNullOrEmpty(notBoardingPassFlightInfoList)) {
                notBoardingPassFlightInfoList.sort(Comparator.comparing(BoardingPassFlightInfo::getFlightDepDate));
            }
            boardingPassResp.setBoardingPassFlightInfoList(onBoardingPassFlightInfoList);
            boardingPassResp.setNotBoardingPassFlightInfoList(notBoardingPassFlightInfoList);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(boardingPassResp);
            return resp;
        } catch (Exception e) {
            String paramStr = JsonUtil.objectToJson(req);
            saveError(SERVICE_NAME, reqId, ip, paramStr, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    @ApiOperation(value = "预留登机牌产品购买", notes = "预留登机牌产品购买")
    @RequestMapping(value = "bookBoardingPass", method = RequestMethod.POST)
    public BaseResp bookBoardingPass(@RequestBody BaseReq<BoardingPassBook> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = getClientIP(request);
        long t1 = System.currentTimeMillis();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String respJson = JsonUtil.objectToJson(resp);
                saveRespInfo(SERVICE_NAME, reqId, ip, System.currentTimeMillis() - t1, reqJson, respJson);
                return resp;
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            BoardingPassBook boardingPassBook = req.getRequest();
            if (boardingPassBook == null) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("缺少必要业务参数");
                return resp;
            }
            //入参检验
            boolean check = checkBoardingPassBook(boardingPassBook, resp, channelCode);
            if (!check) {
                return resp;
            }
            boolean flag = this.checkKeyInfo(boardingPassBook.getFfpId(), boardingPassBook.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //封装下单请求参数
            PtRequest<BoardingCheckBuyRequestDto> couponProductBuyRequest = createBoardingBuyReq(boardingPassBook, channelCode, userNo, ip);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            String url = HandlerConstants.URL_FARE_API + HandlerConstants.BOARDING_PRODUCT_BUY;
            HttpResult httpResult = this.doPostClient(couponProductBuyRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (httpResult.isResult()) {
                if (!StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                    PtResponse<BaseCouponOrderIdentity> response = (PtResponse) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<PtResponse<BaseCouponOrderIdentity>>() {
                    }.getType());
                    if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
                        BaseCouponOrderIdentity baseCouponOrderIdentity = response.getResult();
                        Map<String, Object> orderMap = new HashMap<>();
                        orderMap.put("payState", false);
                        orderMap.put("orderNo", baseCouponOrderIdentity.getOrderNo());
                        orderMap.put("channelOrderNo", baseCouponOrderIdentity.getOrderChannelOrderNo());
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        //全积分抵扣处理 同步执行0元支付
                        if (boardingPassBook.getUseScore() > 0 && new BigDecimal(boardingPassBook.getUseScore()).compareTo(BigDecimal.valueOf(boardingPassBook.getProductInfo().getMinPrice())) == 0) {
                            String key = getChannelInfo(req.getChannelCode(), "20");
                            Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(), baseCouponOrderIdentity.getOrderNo(), baseCouponOrderIdentity.getOrderChannelOrderNo(), key, "CouponType", "", "O");
                            parametersMap.put("UseScore", String.valueOf(boardingPassBook.getUseScore()));
                            BaseResp payResp = paymentService.doPay(parametersMap);
                            if (!WSEnum.SUCCESS.getResultCode().equals(payResp.getResultCode()) || payResp.getObjData() == null) {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo("创单成功，支付失败！"+payResp.getErrorInfo());
                                saveErrorInfo(SERVICE_NAME, reqId,ip,reqJson);
                                return resp;
                            }
                            Map<String, String> payMap = (Map<String, String>) payResp.getObjData();
                            if (payMap.containsKey("RespCode") && !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(payMap.get("RespCode"))) {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo(payMap.get("ErrorMsg") == null ? "支付异常！":payMap.get("ErrorMsg"));
                                saveErrorInfo(SERVICE_NAME, reqId,ip,reqJson);
                                return resp;
                            }else{
                                orderMap.put("payState", true);
                            }
                        }
                        resp.setObjData(orderMap);
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(response.getErrorInfo());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("下单结果返回数据空！");
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(httpResult.getResponse());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("下单发生异常");
            saveError(SERVICE_NAME, reqId, ip, reqJson, e);
        }
        return resp;
    }

    @ApiOperation(value = "预留登机牌订单详情", notes = "预留登机牌订单详情")
    @RequestMapping(value = "queryOrderDetail", method = RequestMethod.POST)
    public BaseResp<SaleCouponGet> queryOrderDetail(@RequestBody BaseReq<CouponOrderReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        String jsonReq = JsonUtil.objectToJson(req);
        saveReqInfo(SERVICE_NAME, reqId, ip, jsonReq);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //检验登录
            CouponOrderReq couponOrderReq = req.getRequest();
            String channelCode = req.getChannelCode();
            boolean flag = this.checkKeyInfo(couponOrderReq.getFfpId(), couponOrderReq.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
                return resp;
            }
            String userNo = getChannelInfo(channelCode, "10");
            PtRequest<BaseCouponOrderIdentity> ptRequest = createBaseCouponOrderDetailRequest(couponOrderReq, channelCode, userNo, ip);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult httpResult = doPostClient(ptRequest, HandlerConstants.URL_FARE_API + HandlerConstants.BOARDING_PRODUCT_DETAIL, headMap);
            if (httpResult.isResult()) {
                PtResponse<CouponOrderInfo> ptResponse = (PtResponse) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<PtResponse<CouponOrderInfo>>() {
                }.getType());
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                    CouponOrderInfo couponOrderInfo = ptResponse.getResult();
                    //订单信息转换处理
                    SaleCouponGet saleCouponGet = doCouponOrderInfoToSaleCouponGet(couponOrderInfo,channelCode,ip);
                    resp.setObjData(saleCouponGet);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptResponse.getErrorInfo());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(httpResult.getResponse());
                return resp;
            }
        } catch (Exception e) {
            saveError(SERVICE_NAME, reqId, ip, jsonReq, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据处理异常！");
            return resp;
        }
        return resp;
    }

    /**
     * 查询对应的机场服务信息
     *
     * @param flightTourDTO
     */
    private BoardingPassFlightInfo queryBoardingProductInfo(FlightTourDTO flightTourDTO, String channelCode, String userNo, Date curDate, HttpServletRequest request, QueryBoardingProductReq productReq) {
        String ip = getClientIP(request);
        BoardingPassFlightInfo boardingPassFlightInfo = new BoardingPassFlightInfo(BoardingPassStateEnum.NO_SERVICE.getState(), BoardingPassStateEnum.NO_SERVICE.getDesc());
        //航班信息转换
        SupportFlightInfo supportFlightInfo = getFlightInfo(flightTourDTO,channelCode,ip);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        //限额查询
        //查询当日航班预留登机牌限额,限额已满的情况下不支持预留
        PtRequest baseCouponOrderRequest = createBaseCouponOrderLimitRequest(flightTourDTO, productReq.getFfpId(), productReq.getFfpCardNo(), channelCode, userNo, ip);
        if (!checkLimit(baseCouponOrderRequest, headMap)) {
            boardingPassFlightInfo.setState(BoardingPassStateEnum.FULLMAN.getState());
            boardingPassFlightInfo.setStateName(BoardingPassStateEnum.FULLMAN.getDesc());
        } else {
            //构建请求参数
            CouponProductQueryRequestDto productRequest = createCouponProductQueryRequestDto(flightTourDTO, channelCode, userNo, productReq.getFfpId(), productReq.getFfpCardNo());
            //发起请求
            HttpResult result = doPostClient(productRequest, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_QUERY_PRODUCT_V2, headMap);
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                PtProductQueryResponseDto response = (PtProductQueryResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtProductQueryResponseDto.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
                    List<ProductInfo> productList = response.getProductList();
                    if (!StringUtil.isNullOrEmpty(productList)) {
                        productList.sort(Comparator.comparing(ProductInfo::getMinPrice));//如果有多个产品时按照价格从低到高取第一个
                        ProductInfo productInfo = productList.get(0);
                        productInfo.setOriginalPrice(productInfo.getResourceList().stream().mapToDouble(ResourceInfo::getStandardPrice).sum());
                        productInfo.setProductSign(EncoderHandler.encodeByMD5(productInfo.signProductField() + HandlerConstants.DEFAULT_TOKEN));
                        boardingPassFlightInfo.setProductInfo(productInfo);
                        //50分钟时间判断
                        Date depDate = DateUtils.toDate(supportFlightInfo.getFlightDate() + " " + supportFlightInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        if (!checkBuyDate(curDate, depDate)) {
                            boardingPassFlightInfo.setState(BoardingPassStateEnum.OVERTIME.getState());
                            boardingPassFlightInfo.setStateName(BoardingPassStateEnum.OVERTIME.getDesc());
                        } else {
                            boardingPassFlightInfo.setState(BoardingPassStateEnum.Use.getState());
                            boardingPassFlightInfo.setStateName(BoardingPassStateEnum.Use.getDesc());
                        }
                    }
                }
            }
        }
        if (boardingPassFlightInfo.getProductInfo() != null) {
            supportFlightInfo.setFlightSign(EncoderHandler.encodeByMD5(supportFlightInfo.createSignParam() + boardingPassFlightInfo.getProductInfo().getProductId() + HandlerConstants.DEFAULT_TOKEN));
        } else {
            supportFlightInfo.setFlightSign(EncoderHandler.encodeByMD5(supportFlightInfo.createSignParam() + HandlerConstants.DEFAULT_TOKEN));
        }
        boardingPassFlightInfo.setFlightInfo(supportFlightInfo);
        return boardingPassFlightInfo;
    }

    /**
     * 请求参数构造
     *
     * @return
     */
    private CouponProductQueryRequestDto createCouponProductQueryRequestDto(FlightTourDTO flightTourDTO, String channelCode, String userNo, String ffpId, String ffpCardNo) {
        CouponProductQueryRequestDto productRequest = new CouponProductQueryRequestDto();
        productRequest.setPageNo(1);
        productRequest.setPageSize(20);
        SortCondition condition = new SortCondition();
        condition.setSortDirection("Asc");
        condition.setSortField("Default");
        productRequest.setSortCondition(condition);
        SingleBookCondition singleBookCondition = new SingleBookCondition();
        singleBookCondition.setDepAirportCode(flightTourDTO.getDepAirport());
        productRequest.setSingleBookCondition(singleBookCondition);
        productRequest.setSearchTypes(VoucherTypesEnum.CHECKINSUBSTITUTION.getCode());
        productRequest.setVersion(HandlerConstants.VERSION);
        productRequest.setChannelCode(channelCode);
        productRequest.setUserNo(userNo);
        productRequest.setFfpId(ffpId == null ? "" : ffpId);
        productRequest.setFfpCardNo(ffpCardNo == null ? "" : ffpCardNo);
        return productRequest;
    }

    /**
     * 创建基础的限额请求参数
     *
     * @param flightTourDTO
     * @param ffpId
     * @param ffpCardNo
     * @param channelCode
     * @param userNo
     * @param ip
     * @return
     */
    private PtRequest createBaseCouponOrderLimitRequest(FlightTourDTO flightTourDTO, String ffpId, String ffpCardNo, String channelCode, String userNo, String ip) {
        PtRequest baseCouponOrderRequest = RightCouponObjectConvert.createBaseRequest(channelCode, ffpId, ffpCardNo, userNo, ip);
        QueryProductLimitRequest queryProductLimitRequest = new QueryProductLimitRequest();
        queryProductLimitRequest.setDepAirportCode(flightTourDTO.getDepAirport());
        queryProductLimitRequest.setFlightDate(flightTourDTO.getFlightDate());
        queryProductLimitRequest.setFlightNo(flightTourDTO.getFlightNo());
        baseCouponOrderRequest.setRequest(queryProductLimitRequest);
        return baseCouponOrderRequest;
    }

    /**
     * 创建订单详情请求类
     *
     * @param channelCode
     * @param userNo
     * @param ip
     * @return
     */
    private PtRequest createBaseCouponOrderDetailRequest(CouponOrderReq couponOrderReq, String channelCode, String userNo, String ip) {
        PtRequest baseCouponOrderRequest = RightCouponObjectConvert.createBaseRequest(channelCode, couponOrderReq.getFfpId(), couponOrderReq.getFfpCardNo(), userNo, ip);
        BaseCouponOrderIdentity baseCouponOrderIdentity = new BaseCouponOrderIdentity();
        baseCouponOrderIdentity.setOrderNo(couponOrderReq.getOrderNo());
        baseCouponOrderIdentity.setOrderChannelOrderNo(couponOrderReq.getChannelOrderNo());
        baseCouponOrderRequest.setRequest(baseCouponOrderIdentity);
        return baseCouponOrderRequest;
    }

    /**
     * 获取航班信息
     *
     * @param flightTourDTO
     */
    private SupportFlightInfo getFlightInfo(FlightTourDTO flightTourDTO,String channelCode,String ip) {
        SupportFlightInfo supportFlightInfo = new SupportFlightInfo();
        supportFlightInfo.setFlightNo(flightTourDTO.getFlightNo());
        supportFlightInfo.setFlightDate(flightTourDTO.getFlightDate());
        supportFlightInfo.setSchArrFlightDate(flightTourDTO.getSchArrDate());
        supportFlightInfo.setDepDateTime(transfer(flightTourDTO.getSchDeptTime()));
        supportFlightInfo.setArrDateTime(transfer(flightTourDTO.getSchArrTime()));
        supportFlightInfo.setDepAirportCode(flightTourDTO.getDepAirport());
        supportFlightInfo.setArrAirportCode(flightTourDTO.getArrAirport());
        supportFlightInfo.setDepAirportName(flightTourDTO.getDepAirport());
        supportFlightInfo.setArrAirportName(flightTourDTO.getArrAirport());
        supportFlightInfo.setDepCityName(flightTourDTO.getDepAirport());
        supportFlightInfo.setArrCityName(flightTourDTO.getArrAirport());
        supportFlightInfo.setDepAirportTerminal(FlightUtil.formatTerminal(flightTourDTO.getDepTerminal()));
        supportFlightInfo.setArrAirportTerminal(FlightUtil.formatTerminal(flightTourDTO.getArrTerminal()));
        supportFlightInfo.setPassengerName(flightTourDTO.getPsrName());
        supportFlightInfo.setEticketNo(flightTourDTO.getEtCode().replace("-", ""));
        supportFlightInfo.setCabin(flightTourDTO.getCabin());
        supportFlightInfo.setSeatNo(StringUtil.isNullOrEmpty(flightTourDTO.getCheckInSeatNo()) ? flightTourDTO.getAsrSeatNo() : flightTourDTO.getCheckInSeatNo());
        //跨天数
        int days = DateUtils.diffDays(flightTourDTO.getFlightDate(), flightTourDTO.getSchArrDate(), DateUtils.YYYY_MM_DD_PATTERN);
        supportFlightInfo.setDay(days);
        //处理航班时间
        String weekStr = DateUtils.getWeekStr(DateUtils.toDate(flightTourDTO.getFlightDate()));
        supportFlightInfo.setWeekDay(weekStr);
        supportFlightInfo.setWifiFlag(FlightUtil.supportWifi(flightTourDTO.getPlaneType()));
        //处理机型转换
        Map<String, AircraftModel> stringAircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        if (stringAircraftModelMap != null) {
            AircraftModel aircraftModel = stringAircraftModelMap.get(flightTourDTO.getPlaneType());
            supportFlightInfo.setAircraftCode(aircraftModel.getAircraftCode());
            supportFlightInfo.setRemark(aircraftModel.getRemark());
        }
        //出发时间(当地时间)
        String depTime = supportFlightInfo.getFlightDate() + " " + supportFlightInfo.getDepDateTime();
        //到达时间(当地时间)
        String arrTime = flightTourDTO.getSchArrDate() + " " + supportFlightInfo.getArrDateTime();
        AirPortInfoDto depInfo = localCacheService.getLocalAirport(flightTourDTO.getDepAirport(),DateUtils.toDate(flightTourDTO.getFlightDate(),DateUtils.YYYY_MM_DD_PATTERN));
        AirPortInfoDto arrInfo = localCacheService.getLocalAirport(flightTourDTO.getArrAirport(),DateUtils.toDate(flightTourDTO.getSchArrDate(),DateUtils.YYYY_MM_DD_PATTERN));
        if (depInfo != null && arrInfo != null) {
            supportFlightInfo.setDepAirportName(depInfo.getAirPortName());
            supportFlightInfo.setArrAirportName(arrInfo.getAirPortName());
            supportFlightInfo.setDepCityName(depInfo.getCityName());
            supportFlightInfo.setArrCityName(arrInfo.getCityName());
            //飞行时长
            //到达时区
            String arrZone = arrInfo.getCityTimeZone();
            //出发时区
            String depZone = depInfo.getCityTimeZone();
            if ((!StringUtil.isNullOrEmpty(arrZone)) &&
                    (!StringUtil.isNullOrEmpty(depZone)) &&
                    (!StringUtil.isNullOrEmpty(depTime)) &&
                    (!StringUtil.isNullOrEmpty(arrTime))) {
                //添加夏、冬令时处理
                if (!depZone.equals(arrZone)) {
                    depZone = FlightUtil.convertSummerOrWinterTime(depZone, depTime, depInfo);
                    arrZone = FlightUtil.convertSummerOrWinterTime(arrZone, arrTime, arrInfo);
                }
                long diff = DateUtils.calDuration(depTime, depZone, arrTime, arrZone);
                supportFlightInfo.setFlightTime(diff);
            }
        }
        return supportFlightInfo;
    }

    /**
     * 查询航班信息，值机，选座信息
     *
     * @param request
     * @param req
     * @param resp
     * @param productReq
     * @return
     */
    private List<FlightTourDTO> queryTours(HttpServletRequest request, BaseReq<QueryBoardingProductReq> req, BaseResp resp, QueryBoardingProductReq productReq) {
        BaseRequestDTO<QueryTourRequestDTO> baseRequestDTO = new BaseRequestDTO<>();
        QueryTourRequestDTO queryTourRequestDTO = new QueryTourRequestDTO();
        queryTourRequestDTO.setCertificateNo(productReq.getPassengerCard());
        queryTourRequestDTO.setPassengerName(productReq.getPassengerName());
        //queryTourRequestDTO.setQueryType("CHECKIN");
        baseRequestDTO.setRequest(queryTourRequestDTO);
        baseRequestDTO.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        baseRequestDTO.setIp(getClientIP(request));
        baseRequestDTO.setChannelCode(getChannelInfo(req.getChannelCode(), "50"));
        HttpResult result = doPostClient(baseRequestDTO, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.QUERY_TOUR, null, READ_TIMEOUT, CONNECT_TIMEOUT);
        if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
            BaseResultDTO<QueryTourResponseDTO> response = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), BaseResultDTO.class, QueryTourResponseDTO.class);
            if (response.getResultCode().equals("1001")) {
                QueryTourResponseDTO responseDTO = response.getResult();
                if (responseDTO != null) {
                    List<FlightTourDTO> tours = responseDTO.getTours();
                    if (CollectionUtils.isNotEmpty(tours)) {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        return tours;
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("无有效行程");
                        return new ArrayList<>();
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("无有效行程");
                    return new ArrayList<>();
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(response.getErrorMsg());
                return new ArrayList<>();
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询网络出错");
            return new ArrayList<>();
        }
    }

    /**
     * 校验请求参数
     *
     * @param productReq
     * @param resp
     */
    private void checkQueryReq(QueryBoardingProductReq productReq, BaseResp resp) {
        //身份证校验
        Pattern patternOne = Pattern.compile(PatternCommon.ID_NUMBER);
        Matcher matcherOne = patternOne.matcher(productReq.getPassengerCard() == null ? "" : productReq.getPassengerCard());
        //票号校验
        Pattern patternTwo = Pattern.compile(PatternCommon.TICKET_NO);
        Matcher matcherTwo = patternTwo.matcher(productReq.getPassengerCard() == null ? "" : productReq.getPassengerCard());
        if (!matcherOne.matches() && !matcherTwo.matches()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("证件号或票号格式有误");
        } else {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        }
    }

    /**
     * 检验航班限额
     *
     * @param baseCouponOrderRequest
     * @return
     */
    private boolean checkLimit(PtRequest baseCouponOrderRequest, Map<String, String> headMap) {
        HttpResult limitResult = doPostClient(baseCouponOrderRequest, HandlerConstants.URL_FARE_API + HandlerConstants.BOARDING_PRODUCT_LIMIT, headMap);
        if (limitResult.isResult() && !StringUtil.isNullOrEmpty(limitResult.getResponse())) {
            PtResponse<QueryProductLimitResponse> response = (PtResponse) JsonUtil.jsonToBean(limitResult.getResponse(), new TypeToken<PtResponse<QueryProductLimitResponse>>() {
            }.getType());
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
                return false;
            } else {
                QueryProductLimitResponse queryProductLimitResponse = response.getResult();
                if (queryProductLimitResponse == null || "N".equals(queryProductLimitResponse.getCanBuy())) {
                    return false;
                }
                return true;
            }
        } else {
            return false;
        }
    }

    private String transfer(String s) {
        String regex = "(.{2})";
        s = s.replaceAll(regex, "$1:");
        return s.substring(0, 5);
    }

    /**
     * 预留登机牌参数检验
     *
     * @param boardingPassBook
     * @param resp
     * @return
     */
    private boolean checkBoardingPassBook(BoardingPassBook boardingPassBook, BaseResp resp, String channelCode) {
        ProductInfo productInfo = boardingPassBook.getProductInfo();
        SupportFlightInfo supportFlightInfo = boardingPassBook.getFlightInfo();
        if (!EncoderHandler.encodeByMD5(supportFlightInfo.createSignParam() + productInfo.getProductId() + HandlerConstants.DEFAULT_TOKEN).equals(supportFlightInfo.getFlightSign())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("航班信息发生变动，请重新查询");
            return false;
        }
        if (!EncoderHandler.encodeByMD5(productInfo.signProductField() + HandlerConstants.DEFAULT_TOKEN).equals(productInfo.getProductSign())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("产品信息发生变动，请重新查询");
            return false;
        }
        Date depDate = DateUtils.toDate(supportFlightInfo.getFlightDate() + " " + supportFlightInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
        if (!checkBuyDate(new Date(), depDate)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(BoardingPassStateEnum.OVERTIME.getDesc());
            return false;
        }
        if (boardingPassBook.getUseScore() > 0) {
            if (boardingPassBook.getUseScore() > productInfo.getMinPrice()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("积分金额有误！");
                return false;
            }
            //消费密码
            String patternStr = PatternCommon.SALE_P_W_D;
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(boardingPassBook.getUseScorePwd());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("消费密码为六位数字");
                return false;
            }
            //验证消费密码
            VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(Long.valueOf(boardingPassBook.getFfpId()), boardingPassBook.getUseScorePwd(), channelCode, getClientPwd(channelCode));
            if (!"S000".equals(clientResp.getMessageHeader().getErrorCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(clientResp.getMessageHeader().getDescription());
                return false;
            }
        }
        return true;
    }

    //购买时间检验  当前时间限制为50分钟
    private boolean checkBuyDate(Date curDate, Date depDate) {
        if (DateUtils.dateAddOrLessSecond(curDate, 50 * 60).after(depDate)) {
            return false;
        } else {
            return true;
        }
    }

    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    //封装预留登机牌第三方请求参数
    private PtRequest createBoardingBuyReq(BoardingPassBook boardingPassBook, String channelCode, String userNo, String ip) {
        PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
        ProductInfo productInfo = boardingPassBook.getProductInfo();
        SupportFlightInfo supportFlightInfo = boardingPassBook.getFlightInfo();
        ptRequest.setFfpId(boardingPassBook.getFfpId());
        ptRequest.setFfpCardNo(boardingPassBook.getFfpCardNo());
        ptRequest.setRequestIp(ip);
        BoardingCheckBuyRequestDto baseCreateCouponOrder = new BoardingCheckBuyRequestDto();
        baseCreateCouponOrder.setChannelOrderNo(boardingPassBook.getChannelOrderNo());
        //乘客信息
        baseCreateCouponOrder.setCertNo("");
        baseCreateCouponOrder.setCertType("");
        baseCreateCouponOrder.setPassengerName(supportFlightInfo.getPassengerName());
        baseCreateCouponOrder.setDepAirportTerminal(supportFlightInfo.getDepAirportTerminal());
        baseCreateCouponOrder.setArrAirportTerminal(supportFlightInfo.getArrAirportTerminal());
        //订单其余信息
        baseCreateCouponOrder.setPhoneNo(boardingPassBook.getLinkPhone());
        baseCreateCouponOrder.setContact(boardingPassBook.getLinkName());
        baseCreateCouponOrder.setTktNo(supportFlightInfo.getEticketNo());
        baseCreateCouponOrder.setPnrNo("");
        baseCreateCouponOrder.setUseScore(boardingPassBook.getUseScore() + "");
        baseCreateCouponOrder.setCurrency(HandlerConstants.CURRENCY_CODE);
        baseCreateCouponOrder.setPreference(supportFlightInfo.getSeatNo());
        baseCreateCouponOrder.setDepAirportCode(supportFlightInfo.getDepAirportCode());
        baseCreateCouponOrder.setArrAirportCode(supportFlightInfo.getArrAirportCode());
        baseCreateCouponOrder.setFlightNo(supportFlightInfo.getFlightNo());
        baseCreateCouponOrder.setFlightDate(supportFlightInfo.getFlightDate());
        baseCreateCouponOrder.setArrDate(supportFlightInfo.getSchArrFlightDate());
        baseCreateCouponOrder.setDepTime(supportFlightInfo.getDepDateTime().replace(":", ""));
        baseCreateCouponOrder.setArrTime(supportFlightInfo.getArrDateTime().replace(":", ""));
        baseCreateCouponOrder.setPlaneType(supportFlightInfo.getAircraftCode());
        baseCreateCouponOrder.setCabin(supportFlightInfo.getCabin());
        //产品信息
        BookProductInfo bookProductInfo = new BookProductInfo();
        bookProductInfo.setProductId(productInfo.getProductId());
        bookProductInfo.setProductType(productInfo.getProductType());
        bookProductInfo.setProductName(productInfo.getProductName());
        int size = productInfo.getResourceList().size();
        BookResourceInfo[] bookResourceInfos = new BookResourceInfo[size];
        for (int i = 0; i < size; i++) {
            ResourceInfo resourceInfo = productInfo.getResourceList().get(i);
            BookResourceInfo bookResourceInfo = new BookResourceInfo();
            bookResourceInfo.setResourceId(resourceInfo.getResourceId());
            bookResourceInfo.setVendorMessageId(resourceInfo.getVendorMessageId());
            bookResourceInfo.setResourceType(resourceInfo.getResourceType());
            bookResourceInfo.setUseStartDate(formatFlightDate(supportFlightInfo.getFlightDate(), supportFlightInfo.getDepDateTime()));
            bookResourceInfo.setFlightType(HandlerConstants.ROUTE_TYPE_OW);
            bookResourceInfo.setDepDateTime(formatFlightDate(supportFlightInfo.getFlightDate(), supportFlightInfo.getDepDateTime()));
            bookResourceInfo.setArrDateTime(formatFlightDate(supportFlightInfo.getSchArrFlightDate(), supportFlightInfo.getArrDateTime()));
            bookResourceInfo.setDepAirportCode(supportFlightInfo.getDepAirportCode());
            bookResourceInfo.setArrAirportCode(supportFlightInfo.getArrAirportCode());
            bookResourceInfo.setFlightNo(supportFlightInfo.getFlightNo());
            bookResourceInfo.setCabin(supportFlightInfo.getCabin());
            //价格明细
            ResourcePriceDetail resourcePriceDetail = new ResourcePriceDetail();
            resourcePriceDetail.setPriceType("Unit");
            resourcePriceDetail.setPriceUnit("Person");
            resourcePriceDetail.setSalePrice(BigDecimal.valueOf(100).multiply(BigDecimal.valueOf(productInfo.getMinPrice())).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());//销售价
            resourcePriceDetail.setStandardPrice(BigDecimal.valueOf(100).multiply(BigDecimal.valueOf(resourceInfo.getStandardPrice())).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());//原价
            resourcePriceDetail.setBookingCount(1);
            ResourcePriceDetail[] resourcePriceDetails = new ResourcePriceDetail[1];
            resourcePriceDetails[0] = resourcePriceDetail;
            bookResourceInfo.setResourcePriceDetails(resourcePriceDetails);
            bookResourceInfos[i] = bookResourceInfo;
        }
        bookProductInfo.setResources(bookResourceInfos);
        //总价 = 销售价
        BigDecimal total = BigDecimal.valueOf(productInfo.getMinPrice());
        //实际支付金额
        BigDecimal totalAmount = total.subtract(BigDecimal.valueOf(boardingPassBook.getUseScore()));
        //注意：此处单位是精确到分
        baseCreateCouponOrder.setTotalAmount(BigDecimal.valueOf(100).multiply(totalAmount).setScale(0, BigDecimal.ROUND_HALF_UP) + "");
        baseCreateCouponOrder.setBookProductInfo(bookProductInfo);
        ptRequest.setRequest(baseCreateCouponOrder);
        return ptRequest;
    }

    /**
     * 将航班日期拼接为yyyy-MM-dd HH:mm:ss
     *
     * @param flightDate
     * @param time
     * @return
     */
    private String formatFlightDate(String flightDate, String time) {
        return flightDate + " " + time + ":00";
    }

    /**
     * 订单信息转换
     *
     * @param couponOrderInfo
     * @return
     */
    private SaleCouponGet doCouponOrderInfoToSaleCouponGet(CouponOrderInfo couponOrderInfo,String channelCode,String ip) {
        Map<String,AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(channelCode,ip,DateUtils.toDate(couponOrderInfo.getFlightDate(),DateUtils.YYYY_MM_DD_PATTERN));
        SaleCouponGet saleCouponGet = new SaleCouponGet();
        //订单信息
        BaseCouponOrderIdentity couponOrderIdentity = couponOrderInfo.getOrderInfo();
        //支付信息
        BasePay payInfo = couponOrderInfo.getPayInfo();
        //乘客信息
        BasePassenger passenger = couponOrderInfo.getPassengerInfo();
        saleCouponGet.setOrderNo(couponOrderIdentity.getOrderNo());
        saleCouponGet.setChannelOrderNo(couponOrderIdentity.getOrderChannelOrderNo());
        saleCouponGet.setCouponSource(couponOrderIdentity.getOrderType());
        BoardingPass boardingPass = new BoardingPass();
        BaseAirport depInfo = couponOrderInfo.getDepInfo();
        BaseAirport arrInfo = couponOrderInfo.getArrInfo();
        AirPortInfoDto depAirPortInfo = airPortInfoMap.get(depInfo.getAirportCode());
        AirPortInfoDto arrAirPortInfo = airPortInfoMap.get(arrInfo.getAirportCode());
        if (depAirPortInfo != null && arrAirPortInfo != null) {
            depInfo.setAirportName(depAirPortInfo.getAirPortName());
            depInfo.setCityName(depAirPortInfo.getCityName());
            depInfo.setTime(transfer(depInfo.getTime()));
            arrInfo.setAirportName(arrAirPortInfo.getAirPortName());
            arrInfo.setCityName(arrAirPortInfo.getCityName());
            arrInfo.setTime(transfer(arrInfo.getTime()));
        }
        BeanUtils.copyProperties(couponOrderInfo, boardingPass);
        boardingPass.setPassenger(passenger);
        //处理航司，机型等数据
        if (couponOrderInfo.getFlightType() != null) {
            boardingPass.setFlightType("机型" + couponOrderInfo.getFlightType());
            Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
            AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, couponOrderInfo.getFlightType());
            if (aircraftModel != null) {
                boardingPass.setFlightType(aircraftModel.getRemark());
            }
        }
        saleCouponGet.setOrderName("预留登机牌");
        saleCouponGet.setBoardingPass(boardingPass);
        saleCouponGet.setFfpUseTotalScore(payInfo.getUseScore());
        saleCouponGet.setOrderTotalAmount(NumberUtil.divide(payInfo.getPayAmount(), 100, 2).doubleValue());
        saleCouponGet.setOrderAmoutPayable(saleCouponGet.getOrderTotalAmount()+saleCouponGet.getFfpUseTotalScore());
        saleCouponGet.setRefundRules("");
        OrderPayStateEnum orderPayStateEnum = convertOrderPayState(couponOrderIdentity.getStatus(), payInfo);
        saleCouponGet.setOrderState(orderPayStateEnum.getStateCode());
        saleCouponGet.setOrderStateName(orderPayStateEnum.getStateDesc());
        saleCouponGet.setOrderDatetime(DateUtils.timeStampToDateStr(couponOrderIdentity.getCreateAt(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
        saleCouponGet.setOrderFormatDatetime(DateUtils.timeStampToDateStr(couponOrderIdentity.getCreateAt(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
        //已支付的有支付时间和支付方式
        if ("Pay".equals(payInfo.getPayed())) {
            saleCouponGet.setPaidDatetime(DateUtils.timeStampToDateStr(payInfo.getPayAt(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
            saleCouponGet.setPayMethodName("");
        }
        saleCouponGet.setLinker(passenger.getContactName());
        saleCouponGet.setLinkerHandphone(passenger.getContactPhone());
        saleCouponGet.setTimeLimit(payInfo.getPayLimit() * 1000);
        return saleCouponGet;
    }

    //订单状态转换
    private OrderPayStateEnum convertOrderPayState(String orderState, BasePay basePay) {
        String payState = basePay.getPayed();
        if (VoucherStateEnum.REFUNDED.getCode().equals(orderState)) {
            return OrderPayStateEnum.Refund;
        } else if (VoucherStateEnum.APPLYREFUND.getCode().equals(orderState)) {
            return OrderPayStateEnum.Refunding;
        } else if (VoucherStateEnum.CANCEL.getCode().equals(orderState)) {
            return OrderPayStateEnum.Cancel;
        } else {
            if (PayEnum.Pay.getStateCode().equals(payState)) {
                return OrderPayStateEnum.Pay;
            } else if (PayEnum.UnPay.getStateCode().equals(payState)) {
                if (basePay.getPayLimit() < 1) {
                    return OrderPayStateEnum.Cancel;
                }
                return OrderPayStateEnum.UnPay;
            } else {
                return OrderPayStateEnum.PayFail;
            }
        }
    }
}
