package com.juneyaoair.mobile.handler.controller.companyverify;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.companyverify.request.CompanyVerifyApplyRequest;
import com.juneyaoair.baseclass.companyverify.response.CompanyVerifyApplyResponse;
import com.juneyaoair.baseclass.member.request.MemberCompanyRightsResponse;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.service.ICompanyMemberVerifyService;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName CompanyVerifyController
 * @Description 企业会员认证
 * <AUTHOR>
 * @Date 2023/7/18 10:09
 * @Version 1.0
 */
@RestController
@RequestMapping("/companyMemberVerify")
@Api(value = "CompanyVerifyController", consumes = "企业会员认证")
public class CompanyVerifyController extends BassController {

    @Autowired
    private ICompanyMemberVerifyService companyMemberVerifyService;

    @Autowired
    private HandConfig handConfig;

    @InterfaceLog
    @ApiOperation(value = "申请接口")
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/toApply", method = RequestMethod.POST)
    public BaseResp toApply(@RequestBody @Validated BaseReq<CompanyVerifyApplyRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        saveReqInfo("企业会员认证申请", reqId, ip, JsonMapper.buildNormalMapper().toJson(req));
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId(), req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            throw new CommonException(WSEnum.ERROR_925.getResultCode(), WSEnum.ERROR_925.getResultInfo());
        }
        try {
            boolean applyResult = companyMemberVerifyService.toApply(req.getRequest(), request, ip, req.getChannelCode());
            saveRespInfo("企业会员认证申请", reqId, ip, System.currentTimeMillis(), JsonMapper.buildNormalMapper().toJson(req), JsonMapper.buildNormalMapper().toJson(resp));
            if (applyResult) {
                return resp;
            }
        } catch (CommonException ce) {
            log.error("卡号:[{}]企业会员认证出错，错误码:{},错误信息:{}", req.getRequest().getFfpCardNo(), ce.getResultCode(), ce.getErrorMsg());
            resp.setResultCode(ce.getResultCode());
            resp.setResultInfo(ce.getErrorMsg());
            return resp;
        } catch (Exception e) {
            log.error("卡号:[{}]企业会员认证出错，错误信息:{}", req.getRequest().getFfpCardNo(), e.getMessage());
        }
        resp.setResultCode(WSEnum.ERROR_925.getResultCode());
        resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "查询认证结果")
    @RequestMapping(value = "/toCatchApplyResult", method = RequestMethod.POST)
    public BaseResp<CompanyVerifyApplyResponse> toCatchApplyResult(@RequestBody @Validated BaseReq<CompanyVerifyApplyRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<CompanyVerifyApplyResponse> resp = new BaseResp<>();
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
            return resp;
        }
        //未登录直接返回配置的券信息
        if (null == req || null == req.getRequest() || StringUtils.isEmpty(req.getRequest().getFfpId())) {
            MemberCompanyRightsResponse memberCompanyRightsResponse = new MemberCompanyRightsResponse();
            memberCompanyRightsResponse.setMemberCompanyRightsDesc("企业会员专享权益");
            CompanyVerifyApplyResponse companyVerifyApplyResponse = new CompanyVerifyApplyResponse();
            companyVerifyApplyResponse.setMemberCompanyRightsResponse(memberCompanyRightsResponse);
            resp.setObjData(companyVerifyApplyResponse);
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId(), req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            throw new CommonException(WSEnum.ERROR_925.getResultCode(), WSEnum.ERROR_925.getResultInfo());
        }
        try {
            resp.setObjData(companyMemberVerifyService.toCatchApplyResult(req.getRequest(), request, this.getClientIP(request), request.getHeader(HEAD_CHANNEL_CODE),true,true));
            return resp;
        } catch (CommonException ce) {
            log.error("卡号:[{}]企业会员认证结果查询出错，错误码:{},错误信息:{}", req.getRequest().getFfpCardNo(), ce.getResultCode(), ce.getErrorMsg());
            resp.setResultCode(ce.getResultCode());
            resp.setResultInfo(ce.getErrorMsg());
            return resp;
        } catch (Exception e) {
            log.error("卡号:[{}]企业会员认证结果查询出错，错误信息:{}", req.getRequest().getFfpCardNo(), e.getMessage());
        }
        resp.setResultCode(WSEnum.ERROR_925.getResultCode());
        resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "企业会员解除绑定")
    @RequestMapping(value = "/toUnbind", method = RequestMethod.POST)
    public BaseResp<CompanyVerifyApplyResponse> toUnbind(@RequestBody @Validated BaseReq<CompanyVerifyApplyRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<CompanyVerifyApplyResponse> resp = new BaseResp<>();
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        saveReqInfo("企业会员解除绑定", reqId, ip, JsonMapper.buildNormalMapper().toJson(req));
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId(), req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            throw new CommonException(WSEnum.ERROR_925.getResultCode(), WSEnum.ERROR_925.getResultInfo());
        }
        try {
            saveRespInfo("企业会员解除绑定", reqId, ip, System.currentTimeMillis(), JsonMapper.buildNormalMapper().toJson(req), JsonMapper.buildNormalMapper().toJson(resp));
            if (companyMemberVerifyService.toUnbind(req.getRequest(), request, ip, req.getChannelCode())) {
                return resp;
            }
        } catch (CommonException ce) {
            log.error("卡号:[{}]企业会员认证结果查询出错，错误码:{},错误信息:{}", req.getRequest().getFfpCardNo(), ce.getResultCode(), ce.getErrorMsg());
            resp.setResultCode(ce.getResultCode());
            resp.setResultInfo(ce.getErrorMsg());
            return resp;
        } catch (Exception e) {
            log.error("卡号:[{}]企业会员认证结果查询出错，错误信息:{}", req.getRequest().getFfpCardNo(), e.getMessage());
        }
        resp.setResultCode(WSEnum.ERROR_925.getResultCode());
        resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
        return resp;
    }

}
