package com.juneyaoair.mobile.handler.manage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.OrderCouponStateEnum;
import com.juneyaoair.appenum.order.OrderPayStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.ProductCouponTypesEnum;
import com.juneyaoair.appenum.rightcoupon.UnlimitedBindStatusEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.common.request.RequestData;
import com.juneyaoair.baseclass.common.response.ResponseCode;
import com.juneyaoair.baseclass.common.response.ResponseData;
import com.juneyaoair.baseclass.lounge.res.CommLoungeResp;
import com.juneyaoair.baseclass.newcoupon.req.*;
import com.juneyaoair.baseclass.newcoupon.resp.NewBasicOrderDetailResponse;
import com.juneyaoair.baseclass.prepayment.common.CommonDataResponseDto;
import com.juneyaoair.baseclass.prepayment.common.PrepaymentBaggageStatusEnum;
import com.juneyaoair.baseclass.prepayment.request.QueryCouponInfoRequest;
import com.juneyaoair.baseclass.prepayment.response.BasicCouponInfoResResponse;
import com.juneyaoair.baseclass.prepayment.response.CancelBaggageResp;
import com.juneyaoair.baseclass.request.booking.InsuranceFlightInfo;
import com.juneyaoair.baseclass.request.coupons.QueryMyProductCouponReq;
import com.juneyaoair.baseclass.response.order.query.SubOrderResp;
import com.juneyaoair.baseclass.taskcenter.request.SendOutCouponProductRequest;
import com.juneyaoair.baseclass.taskcenter.response.SendOutCouponProductResponse;
import com.juneyaoair.baseclass.unlimitedUp.response.UpgradeUnLimitQueryBindInfoResponseDto;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.*;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.service.IUnlimitedFlyService;
import com.juneyaoair.mobile.handler.util.ChannelUtils;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyV2BindRecord;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.request.PtorRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.comm.response.PtorResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberCertificateSoaModelV2;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.passengers.req.GeneralContactRequest;
import com.juneyaoair.thirdentity.passengers.resp.PtV2GeneralContactResponse;
import com.juneyaoair.thirdentity.request.order.query.PtSubOrderReq;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.thirdentity.salecoupon.request.PtQueryUpgradeCardRequest;
import com.juneyaoair.thirdentity.salecoupon.request.QueryMyProductCouponCountRequest;
import com.juneyaoair.thirdentity.salecoupon.response.PtBindingStatus;
import com.juneyaoair.thirdentity.salecoupon.response.PtQueryUpgradeCardResponse;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponOrderGetResponse;
import com.juneyaoair.thirdentity.salecoupon.response.QueryMyProductCouponCountResponse;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ProductInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.request.ProductAvailableReq;
import com.juneyaoair.thirdentity.salecoupon.v2.request.ProductQueryRequestDto;
import com.juneyaoair.thirdentity.salecoupon.v2.request.PtCreateOrderRequest;
import com.juneyaoair.thirdentity.salecoupon.v2.response.ProductAvailableRespDto;
import com.juneyaoair.thirdentity.salecoupon.v2.response.PtBaseCouponOrderIdentity;
import com.juneyaoair.thirdentity.salecoupon.v2.response.PtFlyCardInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.response.QueryPurchasableResponseDto;
import com.juneyaoair.thirdentity.theme.request.QueryProductsRequestDto;
import com.juneyaoair.thirdentity.ticket.request.PtTicketDigestRequestDto;
import com.juneyaoair.thirdentity.ticket.response.PtTicketListInfoResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 主要集中处理访问订单的相关接口
 * @date 2020/5/6  8:50.
 */
@Component
@Slf4j
public class OrderManage {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IMemberService memberService;

    @Autowired
    private IUnlimitedFlyService unlimitedFlyService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService redisService;

    /**
     * 查询休息室产品
     *
     * @param httpCouponReq
     * @param headMap
     * @return
     */
    public CommLoungeResp queryLoungeProduct(HttpCouponReq httpCouponReq, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_QUERY_PRODUCT_V2;
        HttpResult result = HttpUtil.doPostClient(httpCouponReq, url, headMap);
        CommLoungeResp commLoungeResp;
        if (result.isResult()) {
            commLoungeResp = (CommLoungeResp) JsonUtil.jsonToBean(result.getResponse(), CommLoungeResp.class);
        } else {
            commLoungeResp = new CommLoungeResp();
            commLoungeResp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
            commLoungeResp.setErrorInfo(WSEnum.NETWORK_ERROR.getResultInfo());
        }
        return commLoungeResp;
    }

    /**
     * 查询用户是否存在未绑定得升舱卡
     *
     * @param ptRequest{{hoOrderUrl}}/Order/UpgradeUnlimited/Binding
     * @param headMap
     * @return
     */
    public PtResponse<PtBindingStatus> queryUnBindingRecord(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNBINDING_RECORD;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            Type type = new TypeToken<PtResponse<PtBindingStatus>>() {
            }.getType();
            PtResponse ptResponse = (PtResponse) JsonUtil.jsonToBean(result.getResponse(), type);
            return ptResponse;
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 是否可以使用无限升舱卡
     *
     * @return
     */
    public boolean unlimitedUpClassAvailable(String ffpId, String ffpCardNo, String channelCode, String channelUserNo,
                                             String channelPwd, String certType, String certNo, String ip, boolean HKMCTWRegion, String passengerName) {
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, channelUserNo);
        ptRequest.setFfpCardNo(ffpCardNo);
        ptRequest.setFfpId(ffpId);
        PtResponse<PtBindingStatus> ptResponse = queryUnBindingRecord(ptRequest, headMap);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            throw new OperationFailedException("查询无限升舱卡绑定信息失败");
        }
        //1. 查询账户是否绑定升舱卡
        if (null == ptResponse.getResult() || !UnlimitedBindStatusEnum.STATUS1.getCode().equals(ptResponse.getResult().getUnbindingStatus())) {
            return false;
        }
        PtQueryUpgradeCardRequest ptQueryUpgradeCardRequest = new PtQueryUpgradeCardRequest("10", channelCode, channelUserNo, ffpId, ffpCardNo, "UpgradeUnlimited");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNLIMITED_UPCLASS_ORDER;
        HttpResult result = HttpUtil.doPostClient(ptQueryUpgradeCardRequest, url, headMap);
        if (!result.isResult()) {
            throw new NetworkException("网络异常，查询待出行升舱订单失败，请稍后重试");
        }
        PtQueryUpgradeCardResponse ptQueryUpgradeCardResponse = JsonUtil.fromJson(result.getResponse(), PtQueryUpgradeCardResponse.class);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptQueryUpgradeCardResponse.getResultCode())) {
            throw new OperationFailedException("查询待出行升舱订单失败");
        }
        int count = ptQueryUpgradeCardResponse.getCount();
        // 2校验账户中升舱卡升舱的订单是否多于3张
        if (3 <= count) {
            throw new ServiceException("抱歉！您的账户中已存在3段使用无限升舱卡升舱航段，请在使用一段航程后再办理其他升舱业务。");
        }
        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
        Header header = new Header();
        header.setClientIP(ip);
        header.setClientVersion("");
        header.setMemberId(Long.valueOf(ffpId));
        header.setToken("");
        header.setTimestamp(System.currentTimeMillis());
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(channelPwd);
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffpCardNo);
        String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        if (0 != detailPtCRMResponse.getCode()) {
            throw new OperationFailedException("证件信息错误或不完整，请确认");
        }
        // 3获取实名认证证件信息
        if (HKMCTWRegion) {
            // 港澳台升舱客票姓名必须与CRM系统中的英文姓名匹配
            return (detailPtCRMResponse.getData().getBasicInfo().getELastName() + detailPtCRMResponse.getData().getBasicInfo().getEFirstName())
                    .equalsIgnoreCase(passengerName.replace("/", "").replace(" ", ""));
        } else {
            // 国内升舱必须使用身份证购票且身份证信息必须实名认证
            Optional<MemberCertificateSoaModelV2> optional = detailPtCRMResponse.getData().getCertificateInfo().stream()
                    .filter(memberCertificateSoaModelV2 -> CertificateTypeEnum.ID_CARD.getCode() == memberCertificateSoaModelV2.getCertificateType()).findFirst();
            if (optional.isPresent()) {
                MemberCertificateSoaModelV2 realNameCert = optional.get();
                CertificateTypeEnum certificateType = CertificateTypeEnum.checkType(realNameCert.getCertificateType());
                return null != certificateType && certificateType.getShowCode().equals(certType) && realNameCert.getCertificateNumber().equals(certNo);
            }
        }
        return false;
    }

    /**
     * 查询产品相关信息
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<List<ProductInfo>> queryProducts(PtRequest<ProductQueryRequestDto> ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_PRODUCT_V10;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<List<ProductInfo>>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }


    public ProductAvailableRespDto productQueryAvailable(String channelCode, String ffpId, String ffpNo, String originIp, List<String> activityNo) {
        ProductAvailableReq productAvailableReq = new ProductAvailableReq();
        productAvailableReq.setActivityNo(activityNo);
        RequestData requestData = RequestData.builder()
                .channelNo(channelCode)
                .ffpId(ffpId)
                .ffpNo(ffpNo)
                .originIp(originIp)
                .data(productAvailableReq).build();
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.PRODUCT_QUERY_AVAILABLE;
        HttpResult httpResult = HttpUtil.doPostClient(requestData, url);
        if (httpResult.isResult()) {
            ResponseData<ProductAvailableRespDto> responseData = JsonUtil.fromJson(httpResult.getResponse(), new TypeToken<ResponseData<ProductAvailableRespDto>>() {
            }.getType());
            if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
                throw new ServiceException(responseData.getMessage());
            }
            return responseData.getData();
        } else {
            throw new NetworkException("请求关注航班网络异常");
        }
    }


    /**
     * 查询产品库存和可购买数量
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<List<ProductInfo>> queryRemnantQuantity(PtRequest<ProductQueryRequestDto> ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_PURCHASABLE_QUANTITY;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<QueryPurchasableResponseDto>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 产品下单相关信息
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<PtBaseCouponOrderIdentity> createProductOrder(PtRequest<PtCreateOrderRequest> ptRequest, Map<String, String> headMap, String url) {
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<PtBaseCouponOrderIdentity>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 查询畅飞卡绑定记录
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<PtFlyCardInfo> queryFlyCardBindingRecord(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNLIMITED_FLY_BING_RECORD;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new NetworkException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<PtFlyCardInfo>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 查询畅飞卡绑定记录
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<PtFlyCardInfo> queryFlyCardBindingRecordV2(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNLIMITED_FLY_BING_RECORD_V2;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new NetworkException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<PtFlyCardInfo>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 根据主题卡券码查询查询兑换机票订单状态
     *
     * @param
     * @param
     * @return
     */
    public PtorResponse QueryOrderPayStateByCouponCode(PtorRequest ptRequest) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_ORDER_PAYSTATEBY_COUPONCODE;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new NetworkException("返回数据空");
            } else {
                Type type = new TypeToken<PtorResponse>() {
                }.getType();
                PtorResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }


    /**
     * 查询是否存在未绑定的儿童卡
     *
     * @param ptRequest
     * @param headMap
     * @return * 0:不存在儿童畅飞卡
     * * 1:该用户已绑定儿童畅飞卡(最多绑定2个)
     * * 2.该用户未绑定儿童畅飞卡且只存在购买的
     * * 3.该用户未绑定儿童畅飞卡且只存在别人赠送的
     * * 4.该用户未绑定儿童畅飞卡 既包含购买的也包含别人赠送的
     */
    public PtResponse<Integer> queryUnBindInfo(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNBINDINFO;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<Integer>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 查询吉祥畅飞卡的绑定状态
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<Integer> queryAdtFlyUnBindInfo(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNBINDINFO;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<Integer>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 无限升舱卡绑定
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse bindingUpgradeUnlimited(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.UNLIMITED_UP_BIND;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), PtResponse.class);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 查看无线升舱卡绑定信息
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<UpgradeUnLimitQueryBindInfoResponseDto> queryBindingInfo(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.UNLIMITED_UP_QUERYBIND;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new NetworkException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<UpgradeUnLimitQueryBindInfoResponseDto>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 绑定儿童畅飞卡
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse bindingUnlimitedFly(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.UNLIMITED_FLY_BINDING;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), PtResponse.class);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 添加常用乘机人
     *
     * @param generalContactRequest
     * @param headMap
     * @return
     */
    public PtV2GeneralContactResponse addGeneralContact(GeneralContactRequest generalContactRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.ADD_COMMON_PERSON_V20;
        HttpResult result = HttpUtil.doPostClient(generalContactRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                PtV2GeneralContactResponse ptResponse = JsonUtil.fromJson(result.getResponse(), PtV2GeneralContactResponse.class);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 绑定成人畅飞卡
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse bindingAutUnlimitedFly(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.ADTFLY_BIND;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), PtResponse.class);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    public PtSaleCouponOrderGetResponse queryCouponList(CouponProductOrderGetRequestDto couponProductOrderGetRequestDto, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_ORDER_PRODUCT;
        HttpResult result = HttpUtil.doPostClient(couponProductOrderGetRequestDto, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
            } else {
                PtSaleCouponOrderGetResponse ptResponse = JsonUtil.fromJson(result.getResponse(), PtSaleCouponOrderGetResponse.class);
                return ptResponse;
            }
        } else {
            log.info("地址：{},状态：{}", url, result.getResponse());
            throw new NetworkException(WSEnum.NETWORK_ERROR.getResultInfo());
        }
    }

    /**
     * 畅飞卡2.0版本首页提示信息
     *
     * @param ptRequest
     * @param headMap
     * @return
     */
    public PtResponse<Integer> queryHomePageBindInfo(PtRequest ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNBINDINFO_V2;
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<Integer>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 是否可用升舱2.0卡
     *
     * @param ffpId
     * @param ffpCardNo
     * @param channelCode
     * @param channelUserNo
     * @param channelPwd
     * @param certType      @see CertificateTypeEnum#getCode()
     * @param certNo
     * @param ip
     * @param passengerName
     * @return
     */
    public boolean unlimitedUpClassV2Available(String ffpId, String ffpCardNo, String channelCode, String channelUserNo,
                                               String channelPwd, String certType, String certNo, String ip, String passengerName) {
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = unlimitedFlyService.listUpClassCardV2BindRecord(channelCode, ffpId, ip, ffpCardNo);
        Optional<UnlimitedFlyV2BindRecord> record = unlimitedFlyV2BindRecords.stream().filter(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus())).findFirst();
        if (!record.isPresent()) {
            return false;
        }
        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
        Header header = new Header();
        header.setClientIP(ip);
        header.setClientVersion("");
        header.setMemberId(Long.valueOf(ffpId));
        header.setToken("");
        header.setTimestamp(System.currentTimeMillis());
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(channelPwd);
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffpCardNo);
        String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        PtQueryUpgradeCardRequest ptQueryUpgradeCardRequest = new PtQueryUpgradeCardRequest("10", channelCode, channelUserNo, ffpId, ffpCardNo, "UnlimitUpgradeYear");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNLIMITED_UPCLASS_ORDER;
        HttpResult result = HttpUtil.doPostClient(ptQueryUpgradeCardRequest, url, headMap);
        if (!result.isResult()) {
            throw new NetworkException("网络异常，查询待出行升舱订单失败，请稍后重试");
        }
        PtQueryUpgradeCardResponse ptQueryUpgradeCardResponse = JsonUtil.fromJson(result.getResponse(), PtQueryUpgradeCardResponse.class);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptQueryUpgradeCardResponse.getResultCode())) {
            throw new OperationFailedException("查询待出行升舱订单失败");
        }
        int count = ptQueryUpgradeCardResponse.getCount();
        // 2校验账户中升舱卡升舱的订单是否多于3张
        if (3 <= count) {
            throw new ServiceException("抱歉！您的账户中已存在3段使用无限升舱卡升舱航段，请在使用一段航程后再办理其他升舱业务。");
        }
        PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        if (0 != detailPtCRMResponse.getCode()) {
            throw new OperationFailedException("证件信息错误或不完整，请确认");
        }
        // 3获取实名认证证件信息
        // 升舱2.0使用已经实名认证的证件，且姓名必须匹配
        Optional<MemberCertificateSoaModelV2> optional = detailPtCRMResponse.getData().getCertificateInfo().stream()
                .filter(MemberCertificateSoaModelV2::isVerify).findFirst();
        if (optional.isPresent()) {
            MemberCertificateSoaModelV2 certInfo = optional.get();
            if (CertificateTypeEnum.ID_CARD.getCode() == certInfo.getCertificateType()) {
                return (detailPtCRMResponse.getData().getBasicInfo().getCLastName() + detailPtCRMResponse.getData().getBasicInfo().getCFirstName()).equals(passengerName)
                        && CertificateTypeEnum.ID_CARD.getShowCode().equals(certType) && certInfo.getCertificateNumber().equals(certNo);
            } else {
                // 其他证件匹配英文姓名
                CertificateTypeEnum certificateType = CertificateTypeEnum.checkShowCode(certType);
                if (null == certificateType) {
                    return false;
                }
                return (detailPtCRMResponse.getData().getBasicInfo().getELastName() + detailPtCRMResponse.getData().getBasicInfo().getEFirstName())
                        .replaceAll("/", "").replace(" ", "")
                        .equals(passengerName.replaceAll("/", "").replace(" ", ""))
                        && certInfo.getCertificateType() == certificateType.getCode();
            }
        }
        return false;
    }

    /**
     * 任务中心积分、优惠券、权益券领取 2021-03-15
     *
     * @param sendOutCouponProductRequest
     * @param headMap
     * @return
     */
    public SendOutCouponProductResponse sendOutCouponProduct(SendOutCouponProductRequest sendOutCouponProductRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_SEND_OUT;
        HttpResult result = HttpUtil.doPostClient(sendOutCouponProductRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                SendOutCouponProductResponse ptResponse = JsonUtil.fromJson(result.getResponse(), SendOutCouponProductResponse.class);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 调用新的订单详情接口（/Order/BasicGetCouponOrder）查询订单项
     *
     * @param couponOrderDetailReq
     * @return
     */
    public NewBasicOrderDetailResponse queryCouponOrderDetail(NewCouponOrderDetailReq couponOrderDetailReq) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.BASIC_GET_COUPON_ORDER;
        HttpResult result = HttpUtil.doPostClient(couponOrderDetailReq, url);
        log.info("订单详情接口请求ID：{} 请求地址：{}，返回结果（选座专属）为：{}", MdcUtils.getRequestId(), url, result.getResponse());
        // 没有返回
        if (!result.isResult()) {
            log.info("订单详情请求ID：{} 接口地址：{},状态：{}", MdcUtils.getRequestId(), url, result.getResponse());
            throw new CommonException(WSEnum.NETWORK_ERROR.getResultCode(), "订单详情查询失败，请稍后再试！");
        }
        // 没有数据
        if (StringUtils.isBlank(result.getResponse())) {
            throw new CommonException(WSEnum.SERVICE_BUSY_ERROR.getResultCode(), "订单详情查询失败，请稍后再试！");
        }
        Type type = new TypeToken<NewOrderBaseResponse<NewBasicOrderDetailResponse>>() {
        }.getType();
        NewOrderBaseResponse<NewBasicOrderDetailResponse> ptResponse = JsonUtil.fromJson(result.getResponse(), type);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            return ptResponse.getData();
        }

        log.error("订单详情请求ID：{} 接口地址：{} 请求参数：{} 返回结果：{}", MdcUtils.getRequestId(), url, JsonUtil.objectToJson(couponOrderDetailReq), JsonUtil.objectToJson(ptResponse));
        throw new CommonException(WSEnum.SERVICE_BUSY_ERROR.getResultCode(), "订单详情查询失败，请稍后再试！");
    }

    /**
     * 调用新的查询订单退单详情（/Order/BasicGetCouponRefund）查询退单详情
     *
     * @param couponOrderDetailReq
     * @return
     */
    public <T> T queryCouponRefundOrderDetail(NewCouponRefundOrderDetailReq couponOrderDetailReq, TypeReference<NewOrderBaseResponse<T>> typeReference) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.BASIC_GET_COUPON_REFUND;
        HttpResult result = HttpUtil.doPostClient(couponOrderDetailReq, url);
        // 没有返回
        if (!result.isResult()) {
            log.info("订单退单详情地址：{},状态：{}", url, result.getResponse());
            throw new NetworkException(WSEnum.NETWORK_ERROR.getResultInfo());
        }
        // 没有数据
        if (StringUtils.isBlank(result.getResponse())) {
            throw new ServiceException(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
        }
        NewOrderBaseResponse<T> ptResponse = JSON.parseObject(result.getResponse(), typeReference);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            return ptResponse.getData();
        }

        log.error("订单退单详情地址：{} 请求参数：{} 返回结果：{}", url, JsonUtil.objectToJson(couponOrderDetailReq), JsonUtil.objectToJson(ptResponse));
        throw new ServiceException(ptResponse.getErrorInfo());
    }

    /**
     * 预付费行李,选座,是否购买
     * 仅支持单个
     *
     * @param requestDTO
     * @return
     */
    public String isPayCoupon(QueryCouponInfoRequest requestDTO) {
        CommonDataResponseDto<List<BasicCouponInfoResResponse>> listBaseResultDTO = basicGetCouponInfo(requestDTO);
        List<BasicCouponInfoResResponse> result = listBaseResultDTO.getData();
        if (StringUtil.isNullOrEmpty(result)) {
            return null;
        }

        //判断支付状态 必须是已支付的
        result = result.stream()
                .filter(couponInfo -> (PrepaymentBaggageStatusEnum.Not.getStatusCode().equals(couponInfo.getCouponState()) || PrepaymentBaggageStatusEnum.Apply.getStatusCode().equals(couponInfo.getCouponState()))
                        && OrderPayStateEnum.Pay.getStateCode().equals(couponInfo.getPayState()))
                .collect(Collectors.toList());

        StringBuilder sb = new StringBuilder();
        sb.append("此订单包含");
        boolean flag = false;
        if (result.stream()
                .anyMatch(response -> VoucherTypesEnum.EXTRABAGGAGE.getCode().equals(response.getCouponSource()))) {
            sb.append("付费行李");
            flag = true;
        }
        if (result.stream()
                .anyMatch(response -> VoucherTypesEnum.PAY_SEAT.getCode().equals(response.getCouponSource()))) {
            if (flag) {
                sb.append("、");
            }
            sb.append("付费选座订单");
            flag = true;
        }
        if (flag) {
            return sb.toString();
        } else {
            return null;
        }
    }

    /**
     * 根据客票号查询购买过的产品状态信息
     *
     * @param requestDTO
     * @return
     */
    public CommonDataResponseDto<List<BasicCouponInfoResResponse>> basicGetCouponInfo(QueryCouponInfoRequest requestDTO) {
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.ORDER_GET_COUPONOINFO;

        List<String> handlerList = new ArrayList<>();

        //统一处理,将-取消掉
        for (String s : requestDTO.getTicketNoList()) {
            handlerList.add(s.replace("-", ""));
        }
        requestDTO.setTicketNoList(handlerList);

        HttpResult httpResult = HttpUtil.doPostClient(requestDTO, url);
        // 没有数据
        if (httpResult.isResult()) {
            CancelBaggageResp cancelBaggageResp = (CancelBaggageResp) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<CancelBaggageResp>() {
            }.getType());
            if (!cancelBaggageResp.getResultCode().equals("1001")) {
                throw new ServiceException(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
            }
            CommonDataResponseDto<List<BasicCouponInfoResResponse>> baseResultDTO = (CommonDataResponseDto<List<BasicCouponInfoResResponse>>) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<CommonDataResponseDto<List<BasicCouponInfoResResponse>>>() {
            }.getType());
            return baseResultDTO;
        } else {
            log.info("订单退单详情地址：{},信息：{}", url, httpResult.getResponse());
            throw new ServiceException(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
        }
    }

    public PtResponse<List<ProductInfo>> queryThemeProducts(PtRequest<QueryProductsRequestDto> ptRequest, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.PRODUCT_QUERY_V10;
        url = url.replace("${couponSource}", ptRequest.getRequest().getSearchTypes());
        HttpResult result = HttpUtil.doPostClient(ptRequest, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                Type type = new TypeToken<PtResponse<List<ProductInfo>>>() {
                }.getType();
                PtResponse ptResponse = JsonUtil.fromJson(result.getResponse(), type);
                return ptResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }


    /**
     * 封装查询订单概要/子订单
     *
     * @return
     */
    public SubOrderResp getSubOrder(PtSubOrderReq ptSubOrderReq, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_SUB_ORDER;
        HttpResult result = HttpUtil.doPostClient(ptSubOrderReq, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("返回数据空");
            } else {
                return (SubOrderResp) JsonUtil.jsonToBean(result.getResponse(), SubOrderResp.class);
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 根据证件查询客票信息
     *
     * @param ptTicketDigestRequestDto
     * @param headMap
     * @return
     */
    public PtTicketListInfoResponse getTicketDigestInfo(PtTicketDigestRequestDto ptTicketDigestRequestDto, Map<String, String> headMap) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_TICKET_DIGEST_INFO;
        HttpResult result = HttpUtil.doPostClient(ptTicketDigestRequestDto, url, headMap);
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("订单中台：返回数据空");
            } else {
                PtTicketListInfoResponse ptTicketListInfoResponse = (PtTicketListInfoResponse) JsonUtil.jsonToBean(result.getResponse(), PtTicketListInfoResponse.class);
                if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptTicketListInfoResponse.getResultCode())) {
                    if (UnifiedOrderResultEnum.ERROR_CERT_TYPE.getResultCode().equals(ptTicketListInfoResponse.getResultCode())) {
                        return ptTicketListInfoResponse;
                    }
                    throw new ServiceException("订单中台：" + ptTicketListInfoResponse.getErrorInfo());
                }
                return ptTicketListInfoResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * 客票提取 结果会存入redis
     *
     * @param ticketInfoRequest
     * @param headMap
     * @return
     */
    public TicketListInfoResponse getTicketInfo(TicketInfoRequest ticketInfoRequest, Map<String, String> headMap) {
        return getTicketInfo(ticketInfoRequest, headMap, true);
    }

    /**
     * 客票提取
     *
     * @param ticketInfoRequest
     * @param headMap
     * @param useCache          是否放入redis缓存
     * @return
     */
    public TicketListInfoResponse getTicketInfo(TicketInfoRequest ticketInfoRequest, Map<String, String> headMap, boolean useCache) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_TICKET_INFO;
        HttpResult result = HttpUtil.doPostClient(ticketInfoRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                throw new ServiceException("订单中台：返回数据空");
            } else {
                TicketListInfoResponse ticketListInfoResponse = (TicketListInfoResponse) JsonUtil.jsonToBean(result.getResponse(), TicketListInfoResponse.class);
                if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                    if ("20004".equals(ticketListInfoResponse.getResultCode())) {
                        throw new CommonException(WSEnum.ERROR.getResultCode(),"查询客票失败。请您核对所输入的信息是否准确。如有疑问请致电咨询95520");
                    }
                    if ("2119".equals(ticketListInfoResponse.getResultCode())) {
                        throw new CommonException(WSEnum.ERROR.getResultCode(),"暂不支持此操作");
                    }
                    throw new RetryServiceException(WSEnum.ERROR.getResultCode(), "订单中台：" + ticketListInfoResponse.getErrorInfo());
                }
                if ("TN".equals(ticketInfoRequest.getCertType()) && useCache) {
                    String key = RedisKeyConfig.createTicketCacheKey(ticketInfoRequest.getQueryType(), ticketInfoRequest.getTicketNo());
                    redisService.putData(key, JsonUtil.objectToJson(ticketListInfoResponse), 10 * 60L);
                }
                return ticketListInfoResponse;
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * @description 根据票号，姓名提取保险购买情况
     * <AUTHOR>
     * @date 2024/12/6 19:10
     * @param channelCode
     * @param ticketNo
     * @param passName
     * @return InsuranceFlightInfo
     **/
    public InsuranceFlightInfo getInsuranceFlightInfo(String channelCode,String ticketNo,String passName,Map<String, String> headMap){
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION,channelCode,ChannelUtils.getChannelInfo(channelCode, "10"));
        ticketInfoRequest.setTicketNo(ticketNo);
        ticketInfoRequest.setCertType("TN");
        ticketInfoRequest.setPassengerName(passName);
        //调用统一订单查询保险信息
        HttpResult result = HttpUtil.doPostClient(ticketInfoRequest, HandlerConstants.URL_FARE_API + HandlerConstants.GET_TICKETINFO_INSURANCE,headMap);
        if (!result.isResult()) {
            throw new NetworkException("网络请求繁忙");
        }
        //转换数据model
        InsuranceFlightInfo insuranceFlightInfo = (InsuranceFlightInfo) JsonUtil.jsonToBean(result.getResponse(), InsuranceFlightInfo.class);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(insuranceFlightInfo.getResultCode())) {
           throw new CommonException(WSEnum.ERROR.getResultCode(), "未查询到客票信息");
        }
        return insuranceFlightInfo;
    }

    /**
     * /Order/QueryMyProductCouponCount
     * @param queryMyProductCouponReq
     * @param ip
     * @return
     */
    public QueryMyProductCouponCountResponse getQueryMyProductCouponCount(QueryMyProductCouponReq queryMyProductCouponReq, String ip) {
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.QUERY_MY_PRODUCT_COUPON_COUNT;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        QueryMyProductCouponCountRequest queryMyProductCouponCountRequest =  createQueryMyProductCouponCountRequest(queryMyProductCouponReq, ip);
        HttpResult result = HttpUtil.doPostClient(queryMyProductCouponCountRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (!result.isResult() || StringUtil.isNullOrEmpty(result.getResponse())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "数据返回为空");
        }
        QueryMyProductCouponCountResponse ptCouponProductGetResponseDto = (QueryMyProductCouponCountResponse) JsonUtil.jsonToBean(result.getResponse(), QueryMyProductCouponCountResponse.class);
        if ("1001".equals(ptCouponProductGetResponseDto.getResultCode()) && null != ptCouponProductGetResponseDto.getRecordCount()) {
            final String redisKey = RedisKeyConfig.MEMBER_CENTER_MEMBER_RIGHT_COUPON_TOTAL + queryMyProductCouponReq.getAvailableStatus() + ":" + queryMyProductCouponReq.getRuleModel() + ":" + queryMyProductCouponReq.getFfpCardNo();
            redisService.putData(redisKey, String.valueOf(ptCouponProductGetResponseDto.getRecordCount()), DateUtils.getSecondDayDifference(new Date()));
        }
        return ptCouponProductGetResponseDto;
    }

    /**
     * 查询权益劵/卡列表请求参数
     *
     * @return
     */
    public QueryMyProductCouponCountRequest createQueryMyProductCouponCountRequest(QueryMyProductCouponReq queryMyProductCouponReq, String ip) {
        QueryMyProductCouponCountRequest queryMyProductCouponCountRequest = new QueryMyProductCouponCountRequest();
        queryMyProductCouponCountRequest.setVersion("10");
        queryMyProductCouponCountRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        queryMyProductCouponCountRequest.setRemoteAddr(ip);
        queryMyProductCouponCountRequest.setFfpId(queryMyProductCouponReq.getFfpId());
        queryMyProductCouponCountRequest.setFfpCardNo(queryMyProductCouponReq.getFfpCardNo());
        queryMyProductCouponCountRequest.setRuleModel(queryMyProductCouponReq.getRuleModel());
        queryMyProductCouponCountRequest.setAvailableStatus(queryMyProductCouponReq.getAvailableStatus());
        queryMyProductCouponCountRequest.setUserNo("10001");
        queryMyProductCouponCountRequest.setPageNo(1);
        queryMyProductCouponCountRequest.setPageSize(100);
        List<String> stateList =new ArrayList<String>();

        List<String> voucherTypes =null;
        if ("Card".equals(queryMyProductCouponReq.getRuleModel())){
            if ("Active".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Not.getStateCode());
                stateList.add(OrderCouponStateEnum.Used.getStateCode());
                stateList.add(OrderCouponStateEnum.Available.getStateCode());
                stateList.add(OrderCouponStateEnum.Appointment.getStateCode());
                stateList.add(OrderCouponStateEnum.Giving.getStateCode());
            }
            if ("Expired".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Overdue.getStateCode());
                stateList.add(OrderCouponStateEnum.WittenOff.getStateCode());
                stateList.add(OrderCouponStateEnum.GiveAway.getStateCode());
                stateList.add(OrderCouponStateEnum.Refund.getStateCode());
            }
            List<String> productCouponType=new ArrayList<>();
            productCouponType.addAll(handConfig.getThemeCouponList());
            voucherTypes=productCouponType;
        }else {
            if("Not".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Not.getStateCode());
                stateList.add(OrderCouponStateEnum.Appointment.getStateCode());
            }
            if ("Used".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Used.getStateCode());
                stateList.add(OrderCouponStateEnum.Giving.getStateCode());
                stateList.add(OrderCouponStateEnum.WittenOff.getStateCode());
            }
            if ("Expired".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Overdue.getStateCode());
                stateList.add(OrderCouponStateEnum.GiveAway.getStateCode());
                stateList.add(OrderCouponStateEnum.Refund.getStateCode());
            }
            voucherTypes = ProductCouponTypesEnum.getAllProductCouponTypeCodes();
        }

        queryMyProductCouponCountRequest.setCouponState(stateList);
        queryMyProductCouponCountRequest.setVoucherTypes(voucherTypes);
        return queryMyProductCouponCountRequest;
    }
}
