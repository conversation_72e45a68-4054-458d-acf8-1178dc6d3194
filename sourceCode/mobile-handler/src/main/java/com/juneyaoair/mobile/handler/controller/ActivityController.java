package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.request.coupons.CouponsConfigInfo;
import com.juneyaoair.baseclass.request.coupons.ReceiveActivityCouponsRequest;
import com.juneyaoair.baseclass.request.coupons.ReceiveCouponReq;
import com.juneyaoair.baseclass.request.crm.VerifyConsumePwdReq;
import com.juneyaoair.baseclass.response.BaseResponse;
import com.juneyaoair.baseclass.response.coupons.ReceiveCouponResponse;
import com.juneyaoair.baseclass.response.crm.VerifyConsumePwdResp;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.gift.ExchangeActivityRequest;
import com.juneyaoair.mobile.handler.bean.gift.GiftActivityRequest;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.*;
import com.juneyaoair.thirdentity.request.activity.*;
import com.juneyaoair.thirdentity.response.activity.*;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.json.ResultJson;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.FileUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;

/**
 * Created by zc on 2016/10/28.
 */
@RequestMapping("/luckyDrawService")
@RestController
public class ActivityController extends BassController {
    private static final String INTERFACE_REQURL = HandlerConstants.REQURL_INTERFACE;
    private static final String PRICECACHE_REQURL = HandlerConstants.PRICECACHE_REQURL;
    private static final String INTERFACE_LUCKYDRAW = HandlerConstants.INTERFACE_LUCKYDRAW;
    public  static final String FFPID = "FFPID";
    public static final String MOBILE = "MOBILE";
    private static final String NO_ACCESS = "无权限访问";
    private Random random;
    @Autowired
    CrmWSClient crmClient;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService redisService;
    @Autowired
    private CrmController crmService;
    //查航班信息
    @RequestMapping(value = "/queryActivityAirlineEE", method = RequestMethod.POST)
    public QueryActivityAirlineProcessResp queryActivityAirlineEE() {
        QueryActivityAirlineProcessResp processResponse = new QueryActivityAirlineProcessResp();
        QueryActivityAirlineResponse interfaceResponse;
        String startDateStr = "2016-11-11 00:00:00";
        String endDateStr = "2016-11-11 23:59:59";
        String format = "yyyy-MM-dd HH:mm:ss";
        Date startDate = DateUtils.toDate(startDateStr, format);
        Date endDate = DateUtils.toDate(endDateStr, format);
        Date nowDate = new Date();
        String currentState = null;
        long secondsCountDown = 0;
        if (nowDate.before(startDate)) {
            currentState = "-1";
            secondsCountDown = startDate.getTime() - nowDate.getTime();
        } else if (nowDate.after(endDate)) {
            currentState = "1";
        } else {
            currentState = "0";
            secondsCountDown = endDate.getTime() - nowDate.getTime();
        }
        processResponse.setCurrentActState(currentState);
        processResponse.setSecondsCountDown(secondsCountDown / 1000);
        processResponse.setResultCode("1001");

        try {
            String url = INTERFACE_REQURL + "/QueryActivityAirline2";
            String result = this.doHttp("", url, "POST").getResponse();
            log.info("查询航班信息结果：{}", result);
            interfaceResponse = JsonUtil.getGson().fromJson(result, QueryActivityAirlineResponse.class);
            if (interfaceResponse.getResultCode().equals("1001")) {
                processResponse.setResultCode("1001");
                List<ActivityAirline> activityAirline88 = new ArrayList<>();
                List<ActivityAirline> activityAirline188 = new ArrayList<>();
                List<ActivityAirline> activityAirlineIn88 = new ArrayList<>();
                List<ActivityAirline> activityAirlineIn188 = new ArrayList<>();
                List<ActivityAirline> activityAirlines = interfaceResponse.getActivityAirlineList();

                for (int i = 0; i < activityAirlines.size(); i++) {
                    ActivityAirline activityAirline = activityAirlines.get(i);
                    //wait未开始  start 已开始 end 已售罄 over 结束
                    String checkStat = DateUtils.checkTime(activityAirline.getStartDatetime(), activityAirline.getEndDatetime());
                    if (("over").equals(checkStat) || ("wait").equals(checkStat)) {
                        activityAirline.setCheckStat(checkStat);
                    } else {
                        if (activityAirline.getCurrentPrice() != activityAirline.getPrice()) {
                            activityAirline.setCheckStat("end");
                        } else {
                            activityAirline.setCheckStat(checkStat);
                        }
                    }
                    if (activityAirline.getInterTag().equals("I")) {
                        if (activityAirline.getPrice() == 88) {
                            activityAirlineIn88.add(activityAirline);
                        } else if (activityAirline.getPrice() == 188) {
                            activityAirlineIn188.add(activityAirline);
                        }
                    } else {
                        if (activityAirline.getPrice() == 88) {
                            activityAirline88.add(activityAirline);
                        } else if (activityAirline.getPrice() == 188) {
                            activityAirline188.add(activityAirline);
                        }
                    }
                }
                processResponse.setActivityAirlineAList(activityAirline88);
                processResponse.setActivityAirlineBList(activityAirline188);
                processResponse.setActivityAirlineCList(activityAirlineIn88);
                processResponse.setActivityAirlineDList(activityAirlineIn188);
            }
        } catch (Exception e) {
            processResponse.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            processResponse.setErrorInfo("操作失败");
        }
        return processResponse;
    }

    //价格日历
    @RequestMapping(value = "/getEE", method = RequestMethod.POST)
    public String getEE(@RequestBody Map citysMap) {
        String logStr = JsonUtil.objectToJson(citysMap);
        log.info("查询价格日历请求：{}", logStr);
        String citys = (String) citysMap.get("citys");
        if (StringUtils.isBlank(citys)) {
            return null;
        }
        String[] cityArr = citys.split("-");
        if (cityArr.length != 2) {
            return null;
        }
        String sendCode = cityArr[0];
        String arrCode = cityArr[1];
        try {
            String url = PRICECACHE_REQURL + "/PriceCache1?flightType=OW&sendCode=" + sendCode + "&arrCode=" + arrCode;
            String result = this.doHttp("", url, "GET").getResponse();
            log.info("查询价格日历结果：{}", result);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    //按月获取价格日历
    @RequestMapping(value = "/getEEMonthly", method = RequestMethod.POST)
    public String getEEMonthly(@RequestBody Map citysMap) {
        String logStr = JsonUtil.objectToJson(citysMap);
        log.info("查询(按月)价格日历请求：{}", logStr);
        String citys = (String) citysMap.get("citys");
        String depDate = (String) citysMap.get("depDate");
        if (StringUtils.isBlank(citys) || StringUtils.isBlank(depDate)) {
            return null;
        }
        String[] cityArr = citys.split("-");
        if (cityArr.length != 2) {
            return null;
        }
        String sendCode = cityArr[0];
        String arrCode = cityArr[1];
        try {
            String url = HandlerConstants.GETEE_MONTHLY_URL + "/PriceCache?flightType=OW&departureDate=" + depDate + "&sendCode=" + sendCode + "&arrCode=" + arrCode + "&periodType=Monthly";
            String result = this.doHttp("", url, "GET").getResponse();
            log.info("查询（按月）价格日历结果：{}", result);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    //抽奖
    @RequestMapping(value = "/luckyDraw", method = RequestMethod.POST)
    public DrawResponse luckyDraw(@RequestBody DrawRequest req, HttpServletRequest request) {
        String logStr = JsonUtil.objectToJson(req);
        log.info("抽奖请求参数为：{}", logStr);
        DrawResponse response = new DrawResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<DrawRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }

        boolean flag = checkKeyInfo(String.valueOf(req.getFfpId()), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("用户信息认证失败");
            return response;
        }
        MemberInfoQueryResponseForClient mem =
                crmClient.memberInfoquery(Long.parseLong(req.getFfpId()), req.getChannelCode(), getChannelInfo(req.getChannelCode(), "40"));
        if (mem.getMessageHeader().getErrorCode().equals("S000")) {
            req.setVersion(HandlerConstants.VERSION);
            req.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
            req.setIpAddress(getClientIP(request));
            req.setLoginKeyInfo(null);
            CustomerInfoType customerInfoType = mem.getMemberQueryInfo().getCustomerInfo();
            req.setContactName(customerInfoType.getCLastName() + customerInfoType.getCFirstName());
            List<CustomerContactInfoType> contactInfoTypeList = mem.getMemberQueryInfo().getCustomerContactInfo();
            String mobile = "";
            for (int i = 0; i < contactInfoTypeList.size(); i++) {
                CustomerContactInfoType contactInfoType = contactInfoTypeList.get(i);
                if (contactInfoType.getContactType().equals(ContactType.MOBILE)) {
                    mobile = contactInfoType.getContactValue();
                    break;
                }
            }
            req.setMobile(mobile);
            Map<String, Object> map = new HashMap<>();
            map.put("DrawRequest", req);
            try {
                String url = INTERFACE_LUCKYDRAW + "/DrawActivity/AddDrawCardmemberDay";
                response = JsonUtil.getGson().fromJson(this.doHttp(req, url, "POST").getResponse(), DrawResponse.class);
                response.setDrawIndex(getPageDrawIndexForLottery(response.getPrizeId()));
                String logStr2 = JsonUtil.objectToJson(response);
                log.info("抽奖结果为{}", logStr2);
            } catch (Exception e) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setErrorInfo("操作失败");
            }
        } else {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo("用户信息查询失败");
        }
        return response;
    }

    //2017抽奖
    private int getPageDrawIndexForLottery(String prizeId) {
        int index = -1;
        if (prizeId == null) {
            return index;
        }
        //贵宾休息室
        if (prizeId.equals("241")) {
            index = 0;
        }
        //机模1:300
        else if (prizeId.equals("242")) {
            index = 8;
        }
        //钥匙扣
        else if (prizeId.equals("243")) {
            index = 3;
        }
        //扑克牌
        else if (prizeId.equals("244")) {
            index = 5;
        }
        //20元抵用券
        else if (prizeId.equals("245")) {
            index = this.randomNum();
        }
        //100元抵用券
        else if (prizeId.equals("246")) {
            index = 9;
        }
        //500元抵用券
        else if (prizeId.equals("247")) {
            index = 7;
        }
        //汪正儿童摄影
        else if (prizeId.equals("248")) {
            index = 2;
        }
        // 立秀宝儿童社会体验英语小镇
        else if (prizeId.equals("249")) {
            index = 6;
        }
        return index;
    }

    /**
     * 随机产生位置1和位置4
     *
     * @return
     */
    private int randomNum() {
        int[] arr = {1, 4};
        int len = arr.length;
        if (null == this.random) {
            random = new Random();
        }
        int arrIndex = random.nextInt(len);
        return arr[arrIndex];
    }

    //邮寄
    @RequestMapping(value = "/editPostInfo", method = RequestMethod.POST)
    public EditPostInfoResponse editPostInfo(@RequestBody EditPostInfoRequest req) {
        String logStr = JsonUtil.objectToJson(req);
        log.info("邮寄信息为{}", logStr);
        EditPostInfoResponse response = new EditPostInfoResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<EditPostInfoRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        req.setVersion(HandlerConstants.VERSION);
        req.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
        req.setUserNo(HandlerConstants.M_USER_NO);
        try {
            String url = INTERFACE_LUCKYDRAW + "/DrawActivity/EditPostInfo";
            response = JsonUtil.getGson().fromJson(this.doHttp(req, url, "POST").getResponse(), EditPostInfoResponse.class);
            String logStr2 = JsonUtil.objectToJson(response);
            log.info("邮寄信息返回为{}", logStr2);
        } catch (Exception e) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo("操作失败");
        }
        return response;
    }


    @RequestMapping(value = "/getAllWinningInfo", method = RequestMethod.POST)
    public QueryAllWinRecordsResponse getAllWinningInfo(@RequestBody QueryAllWinRecordsRequest req) {
        String logStr = JsonUtil.objectToJson(req);
        log.info("查询所有中奖纪录请求参数：{}", logStr);
        QueryAllWinRecordsResponse response = new QueryAllWinRecordsResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryAllWinRecordsRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        req.setVersion(HandlerConstants.VERSION);
        req.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
        req.setUserNo(HandlerConstants.M_USER_NO);
        try {
            String url = INTERFACE_LUCKYDRAW + "/DrawActivity/QueryAllWinRecords";
            response = JsonUtil.getGson().fromJson(this.doHttp(req, url, "POST").getResponse(), QueryAllWinRecordsResponse.class);
            String logStr2 = JsonUtil.objectToJson(response);
            log.info("查询所有中奖纪录返回参数：{}", logStr2);
        } catch (Exception e) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo("操作失败");
        }
        return response;
    }

    @RequestMapping(value = "/getMyWinningInfo", method = RequestMethod.POST)
    public QueryMyWinRecordsResponse getMyWinningInfo(@RequestBody QueryMyWinRecordsRequest req) {
        String logStr = JsonUtil.objectToJson(req);
        log.info("查询我的中奖纪录请求参数：{}", logStr);
        QueryMyWinRecordsResponse response = new QueryMyWinRecordsResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryMyWinRecordsRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        req.setVersion(HandlerConstants.VERSION);
        req.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
        req.setUserNo(HandlerConstants.M_USER_NO);
        if (req.getCount() > 50) {
            req.setCount(50);
        }
        try {
            String url = INTERFACE_LUCKYDRAW + "/DrawActivity/QueryMyWinRecords";
            response = JsonUtil.getGson().fromJson(this.doHttp(req, url, "POST").getResponse(), QueryMyWinRecordsResponse.class);
            String logStr2 = JsonUtil.objectToJson(response);
            log.info("查询我的中奖纪录请求参数：{}", logStr2);
        } catch (Exception e) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo("操作失败");
        }
        return response;
    }

    @RequestMapping(value = "/getLounge", method = RequestMethod.POST)
    public LoungeResponse getLounge(@RequestBody LoungeRequest req) {
        String logStr = JsonUtil.objectToJson(req);
        log.info("抽中休息室请求参数：{}", logStr);
        LoungeResponse resp = new LoungeResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<LoungeRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        MemberInfoQueryResponseForClient mem =
                crmClient.memberInfoquery(Long.parseLong(req.getFfpId()), req.getChannelCode(), getChannelInfo(req.getChannelCode(), "40"));
        if (mem.getMessageHeader().getErrorCode().equals("S000")) {
            req.setVersion(HandlerConstants.VERSION);
            req.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
            req.setUserNo(HandlerConstants.M_USER_NO);
            List<CustomerContactInfoType> contactInfoTypeList = mem.getMemberQueryInfo().getCustomerContactInfo();
            String mobile = "";
            for (int i = 0; i < contactInfoTypeList.size(); i++) {
                CustomerContactInfoType contactInfoType = contactInfoTypeList.get(i);
                if (contactInfoType.getContactType().equals(ContactType.MOBILE)) {
                    mobile = contactInfoType.getContactValue();
                    break;
                }
            }
            req.setMobile(mobile);
        }
        try {
            String url = INTERFACE_LUCKYDRAW + "/B2CAcitivityHandler/Lounge/DrawLoungebooking";
            resp = JsonUtil.getGson().fromJson(this.doHttp(req, url, "POST").getResponse(), LoungeResponse.class);
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info("抽中休息室返回参数：{}", logStr2);
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("操作失败");
        }
        return resp;
    }

    //领取优惠券(是会员的直接领取，不是会员的注册后领取)
    @RequestMapping(value = "/receiveActivityCoupons", method = RequestMethod.POST)
    @ResponseBody
    public ResultJson receiveActivityCoupons(@RequestBody ReceiveActivityCouponsRequest req, HttpServletRequest request) {
        String logStr = JsonUtil.objectToJson(req);
        log.info("领取优惠券请求参数：{}", logStr);
        ResultJson resp = new ResultJson();
        //白名单校验
        String  ip = getClientIP(request);
        String ipPassStr = HandlerConstants.RECEIVE_COUPONS_PASS_IP;
        List<String> ipPassList = Arrays.asList(ipPassStr.split(","));
        if(!ipPassList.contains(ip)){
            log.debug("周年优惠券领取，ip:{}无权限访问", ip);
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
            resp.setResultMsg(NO_ACCESS);
            return resp;
        }
        //参数校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ReceiveActivityCouponsRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultMsg(violations.iterator().next().getMessage());
            return resp;
        }
        String channelCode = req.getCheckChannelCode();
        if(channelCode.equalsIgnoreCase("MWEB")){
            channelCode = HandlerConstants.M_CHANNEL_CODE;
        }
        String ffpId = req.getFfpId();
        String ffpCardNo = req.getFfpCardNo();
        String loginKeyInfo = req.getLoginKeyInfo();
        if (FFPID.equals(req.getReceiveWay())) {
            if(StringUtil.isNullOrEmpty(ffpCardNo) || StringUtil.isNullOrEmpty(ffpId) || StringUtil.isNullOrEmpty(loginKeyInfo)){
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
                return resp;
            }
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
        } else if (MOBILE.equals(req.getReceiveWay())) {
            if(StringUtil.isNullOrEmpty(req.getMobile())){
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultMsg(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
                return resp;
            }
            //根据手机号查询会员信息
            log.debug("根据手机号查询会员库开始:手机号为{}", req.getMobile());
            GetMemberInfoResponseForClient memberInfoResponseForClient = crmClient.getMemberInfo(req.getMobile(), QueryMemberDataType.MOBILE.value(), HandlerConstants.M_CHANNEL_CODE, HandlerConstants.M_CLIENT_PWD);
            String logStr2 = JsonUtil.objectToJson(memberInfoResponseForClient);
            log.debug("手机号查询会员库结果：{}", logStr2);
            if (null != memberInfoResponseForClient) {
                if (memberInfoResponseForClient.getMessageHeader().getErrorCode().equals("S000")) {
                    MemberQueryInfoType memberQueryInfoType = memberInfoResponseForClient.getMemberQueryInfo();
                    if (null != memberQueryInfoType && !String.valueOf(memberQueryInfoType.getID()).isEmpty() && !memberQueryInfoType.getMemberID().isEmpty()) {
                        ffpId = String.valueOf(memberQueryInfoType.getID());
                        ffpCardNo = memberQueryInfoType.getMemberID();
                        String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                        loginKeyInfo = EncoderHandler.encode("MD5", String.valueOf(ffpId) + key).toUpperCase();
                    }
                } else {
                    //会员信息不存在，根据手机号快速注册
                    MemberB2CRegistResponseForClient memberB2CRegistResponseForClient = crmClient.fastRegist(req.getMobile(),req.getMobile(),HandlerConstants.M_CHANNEL_CODE, HandlerConstants.M_CLIENT_PWD);
                    String logStr3 = JsonUtil.objectToJson(memberB2CRegistResponseForClient);
                    log.debug("快速会员注册response：{}", logStr3);
                    if(null != memberB2CRegistResponseForClient){
                        if(memberB2CRegistResponseForClient.getMessageHeader().getErrorCode().equals("S000")){
                            ffpId = String.valueOf(memberB2CRegistResponseForClient.getID());
                            ffpCardNo = memberB2CRegistResponseForClient.getMemberID();
                            String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                            loginKeyInfo = EncoderHandler.encode("MD5", String.valueOf(ffpId) + key).toUpperCase();
                        }
                    }else{
                        String logStr4 = JsonUtil.objectToJson(memberB2CRegistResponseForClient);
                        log.debug("手机号注册会员失败，手机号：{},{}", req.getMobile(), logStr4);
                        resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                        resp.setResultMsg(memberB2CRegistResponseForClient.getMessageHeader().getDescription());
                        return resp;
                    }
                }
            } else {
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR.getResultInfo());
                return resp;
            }
        } else {
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }

        try {
            //配置文件读取活动优惠券
            String couponsKey = "ACTIVITYCOUPONSINFO";
            String activityCouponsInfo = redisService.getData(couponsKey);
            List<CouponsConfigInfo> couponsConfigInfoList;
            if(StringUtil.isNullOrEmpty(activityCouponsInfo)){
                String json = FileUtils.readJson("/activityCouponsInfo.json");
                redisService.replaceData(couponsKey,json,24 * 3600L);
                couponsConfigInfoList = ( List<CouponsConfigInfo>)JsonUtil.jsonToList(json, new TypeToken<List<CouponsConfigInfo>>() {}.getType());
            }else{
                couponsConfigInfoList = ( List<CouponsConfigInfo>)JsonUtil.jsonToList(activityCouponsInfo, new TypeToken<List<CouponsConfigInfo>>() {}.getType());
            }
            //匹配优惠券
            List<CouponsConfigInfo.ActivityInfo> resultActivityInfo = new ArrayList<>();
            Date now = new Date();
            for (CouponsConfigInfo couponsConfigInfo:couponsConfigInfoList ) {
                if(req.getChannelCode().equalsIgnoreCase(couponsConfigInfo.getChannelCode())){
                    for(CouponsConfigInfo.ActivityInfo activityInfo:couponsConfigInfo.getActivityInfoList()){
                        //校验使用日期
                        if(now.after(DateUtils.toDate(activityInfo.getActivityDateS())) && now.before(DateUtils.toDate(activityInfo.getActivityDateE()))){
                            resultActivityInfo.add(activityInfo);
                        }
                    }
                }
            }
            if(StringUtil.isNullOrEmpty(resultActivityInfo) ){
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                resp.setResultMsg("该渠道没有优惠券活动，或活动已过期");
                return resp;
            }
            //批量领取
            int cntSuccess = 0;
            int cntReceived = 0;
            int cntReceiveEnd = 0;
            String reasonFail = "";
            ReceiveCouponResponse receiveCouponResponse;
            CouponController couponController = new CouponController();
            ReceiveCouponReq receiveCouponReq = new ReceiveCouponReq();
            receiveCouponReq.setChannelCode("CRM");
            receiveCouponReq.setLoginKeyInfo(loginKeyInfo);
            receiveCouponReq.setFfpId(ffpId);
            receiveCouponReq.setFfpCardNo(ffpCardNo);
            for (CouponsConfigInfo.ActivityInfo couponInfo : resultActivityInfo) {
                receiveCouponReq.setActivityNo(couponInfo.getActivityNo());
                receiveCouponReq.setStartDate(couponInfo.getActivityDateS());
                receiveCouponReq.setEndDate(couponInfo.getActivityDateE());
                String logStr1 = JsonUtil.objectToJson(receiveCouponReq);
                log.info("开始领取优惠券request:{}", logStr1);
                receiveCouponResponse = couponController.receiveCoupon(receiveCouponReq,request);
                String logStr2 = JsonUtil.objectToJson(receiveCouponResponse);
                log.info("领取优惠券response:{}", logStr2);
                if (receiveCouponResponse != null) {
                    if (receiveCouponResponse.getResultCode().equals(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode())) {
                        //领取成功
                        ++cntSuccess;
                    } else if(receiveCouponResponse.getResultCode().equals("20001")){
                        //优惠券已领完
                        ++cntReceiveEnd;
                    }else if(receiveCouponResponse.getResultCode().equals("20002")){
                        //已领过优惠券
                        ++cntReceived;
                    }else {
                        String errorInfo = receiveCouponResponse.getErrorInfo().substring(receiveCouponResponse.getErrorInfo().indexOf("查询出错:") + 6);
                        if (reasonFail.indexOf(errorInfo) < 0) {
                            reasonFail = errorInfo;
                        }
                    }
                }
            }
            if (cntSuccess > 0) {
                resp.setResultMsg("领取成功!请登录微信或吉祥航空官网,在“我的账户—优惠券“中查看优惠券信息");
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode());
            } else if(cntReceived > 0 && cntSuccess == 0){
                resp.setResultMsg("您已领过优惠券,请登录微信或吉祥航空官网,在“我的账户—优惠券“中查看优惠券信息");
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode());
            }else if(cntReceiveEnd > 0 && cntSuccess ==0 && cntReceived ==0){
                resp.setResultMsg("很抱歉,优惠券已领取完！");
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode());
            }else {
                resp.setResultMsg(reasonFail);
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
            }
        }catch (Exception e){
            log.error("领取优惠券异常,会员id:"+ffpId+","+e.getMessage(),e);
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
            resp.setResultMsg("领取优惠券异常");
            return resp;
        }
        return resp;
    }

    //领取优惠券(是会员的直接领取，不是会员的注册后领取)（领取方式MOBILE）
    @RequestMapping(value = "/receiveActivityCoupons2", method = RequestMethod.POST)
    @ResponseBody
    public ResultJson receiveActivityCoupons2(@RequestBody ReceiveActivityCouponsRequest req, HttpServletRequest request) {
        String logStr = JsonUtil.objectToJson(req);
        log.info("领取优惠券请求参数：{}", logStr);
        ResultJson resp = new ResultJson();
        //白名单校验
        String  ip = getClientIP(request);
        String ipPassStr = HandlerConstants.RECEIVE_COUPONS_PASS_IP;
        List<String> ipPassList = Arrays.asList(ipPassStr.split(","));
        if(!ipPassList.contains(ip)){
            log.debug("周年优惠券领取，ip:{} 无权限访问", ip);
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
            resp.setResultMsg(NO_ACCESS);
            return resp;
        }
        //参数校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ReceiveActivityCouponsRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultMsg(violations.iterator().next().getMessage());
            return resp;
        }
        String channelCode = req.getCheckChannelCode();
        if(channelCode.equalsIgnoreCase("MWEB")){
            channelCode = HandlerConstants.M_CHANNEL_CODE;
        }
        String ffpId = req.getFfpId();
        String ffpCardNo = req.getFfpCardNo();
        String loginKeyInfo = req.getLoginKeyInfo();
        if (FFPID.equals(req.getReceiveWay())) {
            if(StringUtil.isNullOrEmpty(ffpCardNo) || StringUtil.isNullOrEmpty(ffpId) || StringUtil.isNullOrEmpty(loginKeyInfo)){
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
                return resp;
            }
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
        } else if (MOBILE.equals(req.getReceiveWay())) {
            if(StringUtil.isNullOrEmpty(req.getMobile())){
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
                return resp;
            }
            //根据手机号查询会员信息
            log.debug("根据手机号查询会员库开始:手机号为{} ", req.getMobile());
            GetMemberInfoResponseForClient memberInfoResponseForClient = crmClient.getMemberInfo(req.getMobile(), QueryMemberDataType.MOBILE.value(), HandlerConstants.M_CHANNEL_CODE, HandlerConstants.M_CLIENT_PWD);
            String logStr2 = JsonUtil.objectToJson(memberInfoResponseForClient);
            log.debug("手机号查询会员库结果：{}", logStr2);
            if (null != memberInfoResponseForClient) {
                if (memberInfoResponseForClient.getMessageHeader().getErrorCode().equals("S000")) {
                    MemberQueryInfoType memberQueryInfoType = memberInfoResponseForClient.getMemberQueryInfo();
                    if (null != memberQueryInfoType && !String.valueOf(memberQueryInfoType.getID()).isEmpty() && !memberQueryInfoType.getMemberID().isEmpty()) {
                        ffpId = String.valueOf(memberQueryInfoType.getID());
                        ffpCardNo = memberQueryInfoType.getMemberID();
                        String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                        loginKeyInfo = EncoderHandler.encode("MD5", String.valueOf(ffpId) + key).toUpperCase();
                        //将信息返回给页面缓存起来
                        ReceiveActivityCouponsRequest memberInfo = new ReceiveActivityCouponsRequest();
                        memberInfo.setFfpCardNo(ffpCardNo);
                        memberInfo.setFfpId(ffpId);
                        memberInfo.setLoginKeyInfo(loginKeyInfo);
                        resp.setResultObject(memberInfo);
                    }
                } else {
                    //会员信息不存在，根据手机号快速注册
                    MemberB2CRegistResponseForClient memberB2CRegistResponseForClient = crmClient.fastRegist(req.getMobile(),req.getMobile(),HandlerConstants.M_CHANNEL_CODE, HandlerConstants.M_CLIENT_PWD);
                    String logStr3 = JsonUtil.objectToJson(memberB2CRegistResponseForClient);
                    log.debug("快速会员注册response：{}", logStr3);
                    if(null != memberB2CRegistResponseForClient){
                        if(memberB2CRegistResponseForClient.getMessageHeader().getErrorCode().equals("S000")){
                            ffpId = String.valueOf(memberB2CRegistResponseForClient.getID());
                            ffpCardNo = memberB2CRegistResponseForClient.getMemberID();
                            String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                            loginKeyInfo = EncoderHandler.encode("MD5", String.valueOf(ffpId) + key).toUpperCase();
                            //将新注册信息返回给页面缓存起来
                            ReceiveActivityCouponsRequest memberInfo = new ReceiveActivityCouponsRequest();
                            memberInfo.setFfpCardNo(ffpCardNo);
                            memberInfo.setFfpId(ffpId);
                            memberInfo.setLoginKeyInfo(loginKeyInfo);
                            resp.setResultObject(memberInfo);
                        }
                    }else{
                        String logStr4 = JsonUtil.objectToJson(memberB2CRegistResponseForClient);
                        log.debug("手机号注册会员失败，手机号：{}, {}", req.getMobile(), logStr4);
                        resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                        resp.setResultMsg(memberB2CRegistResponseForClient.getMessageHeader().getDescription());
                        return resp;
                    }
                }
            } else {
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR.getResultInfo());
                return resp;
            }
        } else {
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultMsg(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }

        try {
            //配置文件读取活动优惠券
            String couponsKey = "ACTIVITYCOUPONSINFO";
            String activityCouponsInfo = redisService.getData(couponsKey);
            List<CouponsConfigInfo> couponsConfigInfoList;
            if(StringUtil.isNullOrEmpty(activityCouponsInfo)){
                String json = FileUtils.readJson("/activityCouponsInfo.json");
                redisService.replaceData(couponsKey,json,24 * 3600L);
                couponsConfigInfoList = ( List<CouponsConfigInfo>)JsonUtil.jsonToList(json, new TypeToken<List<CouponsConfigInfo>>() {}.getType());
            }else{
                couponsConfigInfoList = ( List<CouponsConfigInfo>)JsonUtil.jsonToList(activityCouponsInfo, new TypeToken<List<CouponsConfigInfo>>() {}.getType());
            }
            //匹配优惠券
            List<CouponsConfigInfo.ActivityInfo> resultActivityInfo = new ArrayList<>();
            Date now = new Date();
            for (CouponsConfigInfo couponsConfigInfo:couponsConfigInfoList ) {
                if(req.getChannelCode().equalsIgnoreCase(couponsConfigInfo.getChannelCode())){
                    for(CouponsConfigInfo.ActivityInfo activityInfo:couponsConfigInfo.getActivityInfoList()){
                        //校验使用日期
                        if(now.after(DateUtils.toDate(activityInfo.getActivityDateS())) && now.before(DateUtils.toDate(activityInfo.getActivityDateE()))){
                            resultActivityInfo.add(activityInfo);
                        }
                    }
                }
            }
            if(StringUtil.isNullOrEmpty(resultActivityInfo) ){
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                resp.setResultMsg("该渠道没有优惠券活动，或活动已过期");
                return resp;
            }
            //批量领取
            int cntSuccess = 0;
            int cntReceived = 0;
            int cntReceiveEnd = 0;
            String reasonFail = "";
            ReceiveCouponResponse receiveCouponResponse;
            CouponController couponController = new CouponController();
            ReceiveCouponReq receiveCouponReq = new ReceiveCouponReq();
            receiveCouponReq.setChannelCode(MOBILE);
            receiveCouponReq.setLoginKeyInfo(loginKeyInfo);
            receiveCouponReq.setFfpId(ffpId);
            receiveCouponReq.setFfpCardNo(ffpCardNo);
            for (CouponsConfigInfo.ActivityInfo couponInfo : resultActivityInfo) {
                receiveCouponReq.setActivityNo(couponInfo.getActivityNo());
                receiveCouponReq.setStartDate(couponInfo.getActivityDateS());
                receiveCouponReq.setEndDate(couponInfo.getActivityDateE());
                String logStr1 = JsonUtil.objectToJson(receiveCouponReq);
                log.info("开始领取优惠券request:{}", logStr1);
                receiveCouponResponse = couponController.receiveCoupon(receiveCouponReq,request);
                String logStr2 = JsonUtil.objectToJson(receiveCouponResponse);
                log.info("领取优惠券response:{}", logStr2);
                if (receiveCouponResponse != null) {
                    if (receiveCouponResponse.getResultCode().equals(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode())) {
                        //领取成功
                        ++cntSuccess;
                    } else if(receiveCouponResponse.getResultCode().equals("20001")){
                        //优惠券已领完
                        ++cntReceiveEnd;
                    }else if(receiveCouponResponse.getResultCode().equals("20002")){
                        //已领过优惠券
                        ++cntReceived;
                    }else {
                        String errorInfo = receiveCouponResponse.getErrorInfo().substring(receiveCouponResponse.getErrorInfo().indexOf("查询出错:") + 6);
                        if (reasonFail.indexOf(errorInfo) < 0) {
                            reasonFail = errorInfo;
                        }
                    }
                }
            }
            if (cntSuccess > 0) {
                resp.setResultMsg("领取成功!");
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode());
            } else if(cntReceived > 0 && cntSuccess == 0){
                resp.setResultMsg("您已领过优惠券!");
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode());
            }else if(cntReceiveEnd > 0 && cntSuccess ==0 && cntReceived ==0){
                resp.setResultMsg("很抱歉,优惠券已领取完！");
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode());
            }else {
                resp.setResultMsg(reasonFail);
                resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
            }
        }catch (Exception e){
            log.error("领取优惠券异常,会员id:"+ffpId+","+e.getMessage(),e);
            resp.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
            resp.setResultMsg("领取优惠券异常");
            return resp;
        }
        return resp;
    }

    @RequestMapping(value = "queryGiftByScore", method = RequestMethod.POST)
    @ResponseBody
    public Object queryGiftByScore() {
        JSONObject jsonObject = new JSONObject();
        try {
            String url = "http://172.20.94.10:96/B2CAcitivityHandler/ScoreExchange/HoScoreProduct";
            String result = invokePost("", url);
            jsonObject = JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("获取积分商品错误：" + JsonUtil.objectToJson(e), e);
        }
        return jsonObject;
    }

    @RequestMapping(value = "queryGiftByScore", method = RequestMethod.GET)
    @ResponseBody
    public Object getGiftByScore() {
        JSONObject jsonObject = new JSONObject();
        try {
            String url = "http://172.20.94.10:96/B2CAcitivityHandler/ScoreExchange/HoScoreProduct";
            String result = invokePost("", url);
            jsonObject = JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("获取积分商品错误：" + JsonUtil.objectToJson(e), e);
        }
        return jsonObject;
    }

    @RequestMapping(value = "exchangeProduct", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse exchangeProduct(@RequestBody GiftActivityRequest giftActivityRequest, HttpServletRequest request) {
        BaseResponse response = new BaseResponse();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<GiftActivityRequest>> violations = validator.validate(giftActivityRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        VerifyConsumePwdReq verifyConsumePwdReq = new VerifyConsumePwdReq();
        try {
            if ("MWEB".equals(giftActivityRequest.getChannelCode())) {//渠道为MWEB的需要更新渠道
                giftActivityRequest.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
            }
            String key = "SMS:" + giftActivityRequest.getMobile() + "Activity";
            String verifyCode = redisService.getData(key);
            redisService.removeData(key);
            if (StringUtil.isNullOrEmpty(verifyCode)) {
                response.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                response.setErrorInfo("验证码失效");
                return response;
            } else {
                if (!verifyCode.equals(giftActivityRequest.getMobileCode())) {
                    response.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                    response.setErrorInfo("验证码输入错误");
                    return response;
                }
            }
            //验证消费密码
            verifyConsumePwdReq.setChannelCode(giftActivityRequest.getChannelCode());
            verifyConsumePwdReq.setId(Long.valueOf(giftActivityRequest.getFfpId()));
            verifyConsumePwdReq.setPwd(giftActivityRequest.getPassword());
            verifyConsumePwdReq.setLoginKeyInfo(giftActivityRequest.getLoginKeyInfo());
            VerifyConsumePwdResp verifyConsumePwdResp = crmService.verifyConsumePwd(verifyConsumePwdReq, request);
            ExchangeActivityRequest req = new ExchangeActivityRequest();
            BeanUtils.copyNotNullProperties(giftActivityRequest, req);
            if ("10001".equals(verifyConsumePwdResp.getResultCode())) {
                String url = "http://172.20.94.10:96/B2CAcitivityHandler/ScoreExchange/HoScoreExchange";
                String result = invokePost(req, url);
                JSONObject jsonObject = JSONObject.parseObject(result);
                String logStr = jsonObject.toJSONString();
                log.info("积分兑换返回结果{}", logStr);
                if (!"1001".equals(jsonObject.getString("Code"))) {
                    response.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                    response.setErrorInfo(jsonObject.getJSONObject("ErrorMessage").getString("MessageContent"));
                    return response;
                } else {
                    response.setResultCode(com.juneyaoair.appenum.WSEnum.SUCCESS.getResultCode());
                }
            } else {
                response.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
                response.setErrorInfo(verifyConsumePwdResp.getErrorInfo());
            }

        } catch (Exception e) {
            log.error("积分兑换错误：" + e.getMessage(), e);
            response.setResultCode(com.juneyaoair.appenum.WSEnum.ERROR.getResultCode());
            response.setErrorInfo("积分兑换错误");
            return response;
        }
        return response;
    }
}

