package com.juneyaoair.mobile.handler.controller.v2.util;

import com.beust.jcommander.internal.Lists;
import com.google.common.base.Strings;
import com.juneyaoair.appenum.activity.ActivityVIPExperienceCardEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherStateEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.change.ChangeFlightInfo;
import com.juneyaoair.baseclass.change.ChangePassengerInfo;
import com.juneyaoair.baseclass.newcoupon.req.ChangeCouponReq;
import com.juneyaoair.baseclass.newcoupon.req.ReceiveChangeCouponRequest;
import com.juneyaoair.baseclass.request.booking.CabinFare;
import com.juneyaoair.baseclass.request.booking.FlightInfo;
import com.juneyaoair.baseclass.request.booking.PassengerInfo;
import com.juneyaoair.baseclass.response.coupons.AvailCoupon;
import com.juneyaoair.baseclass.response.coupons.UsePassengerSegment;
import com.juneyaoair.baseclass.unlimit.UpgradeCardV2Config;
import com.juneyaoair.baseclass.visa.resp.CouponProductVisaDictionariesResponseDto;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.invoice.CouponSourceEnum;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.response.MemberBasicInfoSoaModel;
import com.juneyaoair.thirdentity.member.response.MemberCertificateSoaModelV2;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.salecoupon.common.*;
import com.juneyaoair.thirdentity.salecoupon.request.PtCouponProductAcceptVoucherRequestDto;
import com.juneyaoair.thirdentity.salecoupon.request.PtCouponProductGiveVoucherRequestDto;
import com.juneyaoair.thirdentity.salecoupon.v2.common.AirlineLimit;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ProductRuleLimit;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 权益券转换类
 * @date 2019/3/1  9:21.
 */
public class RightCouponConvert {
    /**
     * 将VoucherInfo对象转换为AvailCoupon
     *
     * @param state           R-可用的  U-已使用  E-失效的
     * @param voucherInfoList
     * @return
     */

    public static List<AvailCoupon> formatAvailCouponList(String state, List<MainVoucherInfo> voucherInfoList, Map<String, CityInfoDto> cityInfoMap,
                                                          Map<String, AirPortInfoDto> airPortInfoMap, HandConfig handConfig) {
        List<AvailCoupon> availCouponList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(voucherInfoList)) {
            return availCouponList;
        }
        //主券信息
        voucherInfoList.stream().forEach(mainVoucherInfo -> {
            if (mainVoucherInfo.getVoucherInfos().size() > 1) {
                List<VoucherInfo> voucherInfos = mainVoucherInfo.getVoucherInfos();
                VoucherInfo wifiVoucherInfo = null;
                VoucherInfo baggageVoucherInfo = null;
                for (int i = 0; i < voucherInfos.size(); i++) {
                    if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(voucherInfos.get(i).getVoucherType())) {
                        wifiVoucherInfo = voucherInfos.get(i);
                    } else if (VoucherTypesEnum.BAGGAGE.getCode().equals(voucherInfos.get(i).getVoucherType())) {
                        baggageVoucherInfo = voucherInfos.get(i);
                    }
                }
                if (baggageVoucherInfo != null && wifiVoucherInfo != null) {
                    AvailCoupon availCoupon = new AvailCoupon();
                    ResourceInfo resourceInfo = baggageVoucherInfo.getVoucherDetail();
                    BaggageExtInfo baggageExt = baggageVoucherInfo.getVoucherDetail().getBaggageExt();
                    OnboardWifiExtInfo onboardWifiExt = wifiVoucherInfo.getVoucherDetail().getOnboardWifiExt();
                    availCoupon.setPackageFlag(true);
                    availCoupon.setHasApply(VoucherStateEnum.APPOINTMENT.getCode().endsWith(wifiVoucherInfo.getVoucherState()));
                    if (baggageExt != null) {
                        if (baggageExt.getBaggageValue() == 0 || StringUtil.isNullOrEmpty(baggageExt.getBaggageUnit())) {
                            availCoupon.setCouponName(baggageExt.getProductName());
                        } else {
                            availCoupon.setCouponName(baggageExt.getBaggageValue() + baggageExt.getBaggageUnit() + "行李券"
                                    +(onboardWifiExt==null?"":"(赠" + (int) onboardWifiExt.getNetFlow() + "M机上WiFi)"));
                        }
                    }
                    availCoupon.setCouponType("");
                    availCoupon.setCouponSource(resourceInfo.getResourceType());
                    availCoupon.setCouponState(baggageVoucherInfo.getVoucherState());
                    VoucherStateEnum voucherStateEnum = VoucherStateEnum.queryVoucherState(baggageVoucherInfo.getVoucherState());
                    availCoupon.setCouponStateName(voucherStateEnum == null ? "" : voucherStateEnum.getName());
                    availCoupon.setCouponNo(baggageVoucherInfo.getVoucherNo());
                    //判断是否可以允许赠送
                    if ((VoucherStateEnum.NOT.getCode().equals(baggageVoucherInfo.getVoucherState()) && VoucherStateEnum.NOT.getCode().equals(wifiVoucherInfo.getVoucherState())) || (VoucherStateEnum.Giving.getCode().equals(baggageVoucherInfo.getVoucherState()))) {
                        availCoupon.setIsGive(baggageVoucherInfo.getIsGiving() == 1 ? "Y" : "N");
                    } else {
                        availCoupon.setIsGive("N");
                    }
                    //赠送中为true
                    if (VoucherStateEnum.Giving.getCode().equals(baggageVoucherInfo.getVoucherState())) {
                        availCoupon.setGivingFlag(true);
                    } else {
                        availCoupon.setGivingFlag(false);
                    }
                    if (VoucherStateEnum.NOT.getCode().equals(baggageVoucherInfo.getVoucherState())) {
                        availCoupon.setCodeFlag(true);
                    } else {
                        availCoupon.setCodeFlag(false);
                    }
                    if (StringUtil.isNullOrEmpty(resourceInfo.getResourceType())) {
                        throw new RuntimeException("查询远程接口错误");
                    }
                    availCoupon.setSubOrderType(baggageVoucherInfo.getSubOrderType());
                    availCoupon.setUseMode(resourceInfo.getUseMode());
                    availCoupon.setStartDate(baggageVoucherInfo.getActivateTime());
                    availCoupon.setEndData(baggageVoucherInfo.getExpireTime());
                    availCoupon.setUsedStEndDt(baggageVoucherInfo.getActivateTime() + "至" + baggageVoucherInfo.getExpireTime());
                    availCoupon.setApplyScope(baggageVoucherInfo.getRuleRemark() == null ? "" : baggageVoucherInfo.getRuleRemark());
                    if (VoucherStateEnum.APPOINTMENT.getCode().equals(wifiVoucherInfo.getVoucherState()) && wifiVoucherInfo.getEquityUnitsUsage() != null) {
                        availCoupon.setApplyFlightDate(wifiVoucherInfo.getEquityUnitsUsage().getDepTime());
                        availCoupon.setApplyFlightNo(wifiVoucherInfo.getEquityUnitsUsage().getFlightNo());
                    }
                    List<VoucherInfo> vouchers = new ArrayList<>();
                    vouchers.add(wifiVoucherInfo);
                    availCoupon.setVouchers(vouchers);
                    //订单号  2020-06-19
                    availCoupon.setOrderNo(baggageVoucherInfo.getMainOrderNo());
                    //贵宾休息室剩余可取消预约次数  2020-06-19
                    availCoupon.setBookCancelCount(baggageVoucherInfo.getBookCancelCount());
                    //是否显示查看订单按钮  2020-06-19
                    if (StringUtils.isNotBlank(baggageVoucherInfo.getDisplay()) && "yes".equalsIgnoreCase(baggageVoucherInfo.getDisplay())) {
                        availCoupon.setIsDisplay(true);
                    } else {
                        availCoupon.setIsDisplay(false);
                    }
                    //是否可以取消预约  true 可以取消预约  2020-06-19
                    if (availCoupon.isHasApply()) {
                        availCoupon.setIsCancel(true);
                    } else {
                        availCoupon.setIsCancel(false);
                    }
                    if (baggageExt != null) {
                        if ("E".equals(state)) {
                            //处理失效券展示了有效券的问题
                            boolean baggageFlag = availCoupon.getCouponState().equals(VoucherStateEnum.WITTENOFF.getCode()) || availCoupon.getCouponState().equals(VoucherStateEnum.USED.getCode()) ||
                                    availCoupon.getCouponState().equals(VoucherStateEnum.GiveAway.getCode()) || availCoupon.getCouponState().equals(VoucherStateEnum.OVERDUE.getCode());
                            boolean wifiFlag = wifiVoucherInfo.getVoucherState().equals(VoucherStateEnum.WITTENOFF.getCode()) || wifiVoucherInfo.getVoucherState().equals(VoucherStateEnum.USED.getCode()) ||
                                    wifiVoucherInfo.getVoucherState().equals(VoucherStateEnum.GiveAway.getCode()) || wifiVoucherInfo.getVoucherState().equals(VoucherStateEnum.OVERDUE.getCode());
                            if (baggageFlag && wifiFlag) {
                                availCouponList.add(availCoupon);
                            }
                        } else {
                            availCouponList.add(availCoupon);
                        }

                    }
                }
            } else {
                //子券信息
                mainVoucherInfo.getVoucherInfos().forEach(voucherInfo -> {
                    AvailCoupon availCoupon = new AvailCoupon();
                    ResourceInfo resourceInfo = voucherInfo.getVoucherDetail();
                    availCoupon.setCouponName(resourceInfo.getProductName());
                    availCoupon.setCouponType("");
                    availCoupon.setPackageFlag(false);
                    if (resourceInfo.getResourceType().matches(PatternCommon.TOTALTYPE)){
                        availCoupon.setCouponSource(resourceInfo.getResourceType()+","+resourceInfo.getProNum());
                    }else {
                        availCoupon.setCouponSource(resourceInfo.getResourceType());
                    }
                    availCoupon.setCouponState(voucherInfo.getVoucherState());
                    availCoupon.setIsAvailable(voucherInfo.isAvailableStatus());
                    availCoupon.setUnAvailableMsg(voucherInfo.getUnAvailableMsg());
                    availCoupon.setProNum(resourceInfo.getProNum());
                    if (StringUtil.isNullOrEmpty(resourceInfo.getResourceType())) {
                        throw new RuntimeException("查询远程接口错误");
                    }
                    availCoupon.setSubOrderType(voucherInfo.getSubOrderType());
                    availCoupon.setAdvanceHour(voucherInfo.getAdvanceHour());
                    availCoupon.setRescheduleType(voucherInfo.getRescheduleType());
                    if (voucherInfo.getBookingLimit() != null) {
                        com.juneyaoair.baseclass.response.coupons.BookingLimit bookingLimit = new com.juneyaoair.baseclass.response.coupons.BookingLimit();
                        BeanUtils.copyNotNullProperties(voucherInfo.getBookingLimit(), bookingLimit);
                        availCoupon.setBookingLimit(bookingLimit);
                    }
                    VoucherStateEnum voucherStateEnum = VoucherStateEnum.queryVoucherState(voucherInfo.getVoucherState());
                    availCoupon.setCouponStateName(voucherStateEnum == null ? "" : voucherStateEnum.getName());
                    availCoupon.setCouponNo(voucherInfo.getVoucherNo());
                    //判断是否可赠送
                    if (VoucherStateEnum.NOT.getCode().equals(voucherInfo.getVoucherState()) || VoucherStateEnum.Giving.getCode().equals(voucherInfo.getVoucherState())) {
                        availCoupon.setIsGive(voucherInfo.getIsGiving() == 1 ? "Y" : "N");
                    } else {
                        availCoupon.setIsGive("N");
                    }
                    //赠送中为true
                    if (VoucherStateEnum.Giving.getCode().equals(voucherInfo.getVoucherState())) {
                        availCoupon.setGivingFlag(true);
                    } else {
                        availCoupon.setGivingFlag(false);
                    }
                    //是否展示二维码
                    if (VoucherTypesEnum.BAGGAGE.getCode().equals(voucherInfo.getVoucherType()) ||
                            VoucherTypesEnum.BAGGAGECOUPON.getCode().equals(voucherInfo.getVoucherType()) ||
                            VoucherTypesEnum.LOUNGECOUPON.getCode().equals(voucherInfo.getVoucherType()) ||
                            VoucherTypesEnum.LOUNGE.getCode().equals(voucherInfo.getVoucherType())
                                    && VoucherStateEnum.NOT.getCode().equals(voucherInfo.getVoucherState())) {
                        availCoupon.setCodeFlag(true);
                    } else {
                        availCoupon.setCodeFlag(false);
                    }
                    availCoupon.setUseMode(resourceInfo.getUseMode());
                    Date activateTime = DateUtils.toDate(voucherInfo.getActivateTime(), DateUtils.YYYY_MM_DD_PATTERN);
                    Date expireTime = DateUtils.toDate(voucherInfo.getExpireTime(), DateUtils.YYYY_MM_DD_PATTERN);
                    voucherInfo.setActivateTime(activateTime == null ? "" : DateUtils.dateToString(activateTime, DateUtils.YYYY_MM_DD_PATTERN));
                    voucherInfo.setExpireTime(expireTime == null ? "" : DateUtils.dateToString(expireTime, DateUtils.YYYY_MM_DD_PATTERN));
                    availCoupon.setStartDate(voucherInfo.getActivateTime() == null ? "" : voucherInfo.getActivateTime());
                    availCoupon.setEndData(voucherInfo.getExpireTime() == null ? "" : voucherInfo.getExpireTime());
                    availCoupon.setUsedStEndDt(voucherInfo.getActivateTime() + "至" + voucherInfo.getExpireTime());
                    availCoupon.setApplyScope(voucherInfo.getRuleRemark() == null ? "" : voucherInfo.getRuleRemark());
                    //升舱券
                    if (VoucherTypesEnum.UPGRADE.getCode().equals(voucherInfo.getVoucherType())) {
                        List<List<String>> routeList = RightCouponConvert.applyOtherAirLine(voucherInfo.getBookingLimit(), cityInfoMap);
                        availCoupon.setOtherApplyRoutes(routeList);
                        EquityUnitsUsage equityUnitsUsage = voucherInfo.getEquityUnitsUsage();
                        if (equityUnitsUsage != null && equityUnitsUsage.getDepCity() != null && equityUnitsUsage.getArrCity() != null) {
                            CityInfoDto depCityInfo = cityInfoMap.get(equityUnitsUsage.getDepCity());
                            CityInfoDto arrCityInfo = cityInfoMap.get(equityUnitsUsage.getArrCity());
                            availCoupon.setCouponName("升舱券—" + depCityInfo.getCityName() + "-" + arrCityInfo.getCityName());
                        }
                        //无限卡处理
                        if ("yes".equals(voucherInfo.getUnLimitedUpgrade())) {
                            availCoupon.setUnlimitedUpClass(true);
                            availCoupon.setBound("yes".equalsIgnoreCase(voucherInfo.getLimitBindingStatus()));
                            availCoupon.setCouponSubType(CouponSourceEnum.UPGRADEUNLIMITED.getCode());
                            UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                            Date saleTimeBegin = DateUtils.toDate(upgradeCardV2Config.getCardSaleTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                            Date activateDate = DateUtils.toDate(voucherInfo.getActivateTime(), DateUtils.YYYY_MM_DD_PATTERN);
                            //设置15周年无限升舱卡使用新的券面
                            if (null != saleTimeBegin && activateDate.getTime() >= saleTimeBegin.getTime()) {
                                availCoupon.setUnlimitedUpV2Flag(true);
                            }
                            if ("yes".equals(voucherInfo.getLimitBindingStatus())) {
                                availCoupon.setCouponBindState("B");
                                availCoupon.setUsedStEndDt("有效期至" + voucherInfo.getExpireTime());
                                if (null != saleTimeBegin && activateDate.getTime() >= saleTimeBegin.getTime()) {
                                    //设置无限升舱卡15周年版订单详情页 不可用日期
                                    availCoupon.setUsedLimit("(" + upgradeCardV2Config.getUnusableTimeBegin().substring(0, 10) + "至" + upgradeCardV2Config.getUnusableTimeEnd().substring(0, 10) + "不可用)");
                                    availCoupon.setBindInfoDesc("已绑定本人");
                                }
                            } else {
                                availCoupon.setCouponBindState("U");
                                Date limitBindingDate = DateUtils.toDate(voucherInfo.getLimitBindingDate(), DateUtils.YYYY_MM_DD_PATTERN);
                                String dateStr = limitBindingDate == null ? "" : DateUtils.dateToString(limitBindingDate, DateUtils.YYYY_MM_DD_PATTERN);
                                availCoupon.setUsedStEndDt("请于" + dateStr + "前绑定");
                            }
                            if ("E".equals(state)) {
                                availCoupon.setUsedStEndDt("有效期至" + voucherInfo.getExpireTime());
                            }
                        }
                    } else if (VoucherTypesEnum.LOUNGE.getCode().equals(voucherInfo.getVoucherType())
                            ||VoucherTypesEnum.LOUNGECOUPON.getCode().equals(voucherInfo.getVoucherType())) {
                        if (resourceInfo.getLoungeExt() != null) {
                            availCoupon.setAirPortCode(resourceInfo.getLoungeExt().getAirportCode());
                            availCoupon.setHasApply(VoucherStateEnum.APPOINTMENT.getCode().endsWith(voucherInfo.getVoucherState()));
                            LoungeExtInfo loungeExtInfo = resourceInfo.getLoungeExt();
                            availCoupon.setIsBookHoloShow(loungeExtInfo.getAppointmentStatus()==1);
                            if (StringUtils.isBlank(resourceInfo.getResourceName())) {
                                if (airPortInfoMap != null && loungeExtInfo != null) {
                                    AirPortInfoDto airPortInfo = airPortInfoMap.get(resourceInfo.getLoungeExt().getAirportCode());
                                    availCoupon.setCouponName("贵宾休息室券—" + airPortInfo.getCityName());
                                }
                            } else {
                                availCoupon.setCouponName(resourceInfo.getResourceName());
                            }
                        }
                        EquityUnitsUsage equityUnitsUsage = voucherInfo.getEquityUnitsUsage();
                        if (equityUnitsUsage != null) {
                            availCoupon.setApplyFlightDate(equityUnitsUsage.getDepTime());
                            availCoupon.setApplyFlightNo(equityUnitsUsage.getFlightNo());
                        }
                    } else if (VoucherTypesEnum.BAGGAGE.getCode().equals(voucherInfo.getVoucherType())) {//行李
                        availCoupon.setCouponName("逾重行李券");
                        if (resourceInfo.getBaggageExt() != null) {
                            if ("DOMESTIC".equals(resourceInfo.getBaggageExt().getIsIntl())) {
                                availCoupon.setCouponName("逾重行李券—" + "国内(" + resourceInfo.getBaggageExt().getBaggageValue() + resourceInfo.getBaggageExt().getBaggageUnit() + ")");
                            } else if ("INTL".equals(resourceInfo.getBaggageExt().getIsIntl())) {
                                availCoupon.setCouponName("逾重行李券—" + "国际及港澳台(" + resourceInfo.getBaggageExt().getProductName() + ")");
                            }
                        }
                    } else if (VoucherTypesEnum.RESCHEDULECOUPON.getCode().equals(voucherInfo.getVoucherType())) {
                        List<List<String>> routeList = RightCouponConvert.applyOtherAirLine(voucherInfo.getBookingLimit(), cityInfoMap);
                        availCoupon.setOtherApplyRoutes(routeList);
                        EquityUnitsUsage equityUnitsUsage = voucherInfo.getEquityUnitsUsage();
                        if (equityUnitsUsage != null && equityUnitsUsage.getDepCity() != null && equityUnitsUsage.getArrCity() != null) {
                            CityInfoDto depCityInfo = cityInfoMap.get(equityUnitsUsage.getDepCity());
                            CityInfoDto arrCityInfo = cityInfoMap.get(equityUnitsUsage.getArrCity());
                            availCoupon.setCouponName("改期券—" + depCityInfo.getCityName() + "-" + arrCityInfo.getCityName());
                        }
                    } else if (VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode().equals(voucherInfo.getVoucherType())) {
                        dealUnlimitFly(availCoupon, voucherInfo, state);
                    } else if (VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode().equals(voucherInfo.getVoucherType())) {
                        dealUnlimitFly(availCoupon, voucherInfo, state);
                    } else if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(voucherInfo.getVoucherType())
                            || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(voucherInfo.getVoucherType())) {
                        dealUnlimitFlyV2(availCoupon, voucherInfo, state);
                    } else if (handConfig.getThemeCouponList().contains(voucherInfo.getVoucherType())) {
                        dealUnlimitTheme(availCoupon, voucherInfo, state, voucherInfo.getVoucherType());
                        availCoupon.setUnlimitedUpClass(true);
                    } else if (ActivityVIPExperienceCardEnum.getVipExperienceCardTypeList().contains(voucherInfo.getVoucherType())) {
                        // VIP体验卡
                        dealVIPExperience(availCoupon, voucherInfo, state, voucherInfo.getVoucherType());
                    } else if (VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(voucherInfo.getVoucherType())) {
                        dealUnlimitFlyV2(availCoupon, voucherInfo, state);
                        availCoupon.setUnlimitedUpClass(true);
                    } else if (VoucherTypesEnum.UPGRADECOUPON.getCode().equals(voucherInfo.getVoucherType())
                            || VoucherTypesEnum.RESCHEDULE.getCode().equals(voucherInfo.getVoucherType())
                            || VoucherTypesEnum.PAY_SEAT_COUPON.getCode().equals(voucherInfo.getVoucherType())
                            || VoucherTypesEnum.EXTRA_BAGGAGE_VOUCHER.getCode().equals(voucherInfo.getVoucherType())) {
                        List<List<String>> routeList = RightCouponConvert.applyOtherAirLine(voucherInfo.getBookingLimit(), cityInfoMap);
                        availCoupon.setOtherApplyRoutes(routeList);
                        availCoupon.setCouponName(resourceInfo.getProductName());
                    } else if (VoucherTypesEnum.LOUNGECOUPON.getCode().equals(voucherInfo.getVoucherType())
                            || VoucherTypesEnum.BAGGAGECOUPON.getCode().equals(voucherInfo.getVoucherType())) {
                        availCoupon.setCouponName(resourceInfo.getProductName());
                        if (resourceInfo.getLoungeExt() != null) {
                            availCoupon.setAirPortCode(resourceInfo.getLoungeExt().getAirportCode());
                            availCoupon.setHasApply(VoucherStateEnum.APPOINTMENT.getCode().endsWith(voucherInfo.getVoucherState()));
                        }
                        EquityUnitsUsage equityUnitsUsage = voucherInfo.getEquityUnitsUsage();
                        if (equityUnitsUsage != null) {
                            availCoupon.setApplyFlightDate(equityUnitsUsage.getDepTime());
                            availCoupon.setApplyFlightNo(equityUnitsUsage.getFlightNo());
                        }
                    }
                    //订单号  2020-06-19
                    availCoupon.setOrderNo(voucherInfo.getMainOrderNo());
                    //贵宾休息室剩余可取消预约次数  2020-06-19
                    availCoupon.setBookCancelCount(voucherInfo.getBookCancelCount());
                    //渠道订单号 2020-11-22
                    availCoupon.setChannelOrderNo(voucherInfo.getChannelOrderNo());
                    //是否显示查看订单按钮  2020-06-19
                    if (StringUtils.isNotBlank(voucherInfo.getDisplay()) && "yes".equalsIgnoreCase(voucherInfo.getDisplay())) {
                        availCoupon.setIsDisplay(true);
                    } else {
                        availCoupon.setIsDisplay(false);
                    }
                    //是否可以取消预约  true 可以取消预约  2020-06-19
                    if (availCoupon.isHasApply()) {
                        availCoupon.setIsCancel(true);
                    } else {
                        availCoupon.setIsCancel(false);
                    }
                    //权益券是否限制本人使用  1:是  0:否  2021-04-07
                    availCoupon.setSelfUse(voucherInfo.getSelfUse());
                    availCouponList.add(availCoupon);
                });
            }

        });
        if ("R".equalsIgnoreCase(state)) {
            //其他券正常，过滤畅飞卡2.0 已出票状态的畅飞卡
            return availCouponList.stream().filter(availCoupon -> StringUtils.isBlank(availCoupon.getUsedStatus())
                    || (StringUtils.isNotBlank(availCoupon.getUsedStatus()) && !availCoupon.getUsedStatus().equalsIgnoreCase("Used"))).collect(Collectors.toList());
        } else if ("U".equalsIgnoreCase(state)) {
            ////其他券正常，过滤畅飞卡2.0 未出票状态的畅飞卡
            return availCouponList.stream().filter(availCoupon -> StringUtils.isBlank(availCoupon.getUsedStatus()) ||
                    (StringUtils.isNotBlank(availCoupon.getUsedStatus()) &&
                            (availCoupon.getUsedStatus().equalsIgnoreCase("Used") || availCoupon.getUsedStatus().equalsIgnoreCase("WittenOff"))
                    )
            ).collect(Collectors.toList());
        } else {
            return availCouponList;
        }
    }

    private static void dealVIPExperience(AvailCoupon availCoupon, VoucherInfo voucherInfo, String state, String type) {
        availCoupon.setUsedStatus(voucherInfo.getUsedStatus());
        //是否他人赠送标识 true 是，false 否 2020-11-24
        availCoupon.setReceiveStatus(voucherInfo.getReceiveStatus() == null ? false : (voucherInfo.getReceiveStatus() == 0 ? false : true));
        availCoupon.setBound("yes".equalsIgnoreCase(voucherInfo.getLimitBindingStatus()));
        availCoupon.setCouponSubType(voucherInfo.getVoucherType());

        if (VoucherStateEnum.USED.getCode().equals(voucherInfo.getVoucherState())
                || VoucherStateEnum.WITTENOFF.getCode().equals((voucherInfo.getVoucherState()))
                || VoucherStateEnum.GiveAway.getCode().equals((voucherInfo.getVoucherState()))) {
            availCoupon.setCouponBindState("B");
            // ”已于2023年5月1日激活“
            if (!Strings.isNullOrEmpty(voucherInfo.getActivateTime())) {
                availCoupon.setUsedStEndDt("已于" + voucherInfo.getActivateTime() + "激活");
            } else {
                Date limitBindingDate = DateUtils.toDate(voucherInfo.getLimitBindingDate(), DateUtils.YYYY_MM_DD_PATTERN);
                String dateStr = limitBindingDate == null ? "" : DateUtils.dateToString(limitBindingDate, DateUtils.YYYY_MM_DD_PATTERN);
                availCoupon.setUsedStEndDt("请于" + dateStr + "前激活");
            }
            //VIP体验卡已经绑定，在权益券列表增加绑定信息展示
            availCoupon.setBindInfoDesc("");
        } else {
            availCoupon.setCouponBindState("U");
            Date limitBindingDate = DateUtils.toDate(voucherInfo.getLimitBindingDate(), DateUtils.YYYY_MM_DD_PATTERN);
            String dateStr = limitBindingDate == null ? "" : DateUtils.dateToString(limitBindingDate, DateUtils.YYYY_MM_DD_PATTERN);
            availCoupon.setUsedStEndDt("请于" + dateStr + "前激活");
            availCoupon.setAvailDate(voucherInfo.getActivateTime() + "至" + dateStr);
            availCoupon.setBindInfoDesc("");
        }
        if ("E".equals(state)) {
            // e.g. 已于2023年9月1日过期
            if ("Overdue".equals(voucherInfo.getVoucherState())) {
                Date limitBindingDate = DateUtils.toDate(voucherInfo.getLimitBindingDate(), DateUtils.YYYY_MM_DD_PATTERN);
                String dateStr = limitBindingDate == null ? "" : DateUtils.dateToString(limitBindingDate, DateUtils.YYYY_MM_DD_PATTERN);
                availCoupon.setUsedStEndDt("已于" + dateStr + "过期");
            } else {
                availCoupon.setUsedStEndDt("");
            }
        }
    }
    //处理吉祥畅飞卡 儿童畅飞卡信息
    private static void dealUnlimitFly(AvailCoupon availCoupon, VoucherInfo voucherInfo, String state) {
        availCoupon.setBound("yes".equalsIgnoreCase(voucherInfo.getLimitBindingStatus()));
        availCoupon.setCouponSubType(voucherInfo.getVoucherType());
        if ("yes".equals(voucherInfo.getLimitBindingStatus())) {
            availCoupon.setCouponBindState("B");
            availCoupon.setUsedStEndDt("有效期至" + voucherInfo.getExpireTime());
        } else {
            availCoupon.setCouponBindState("U");
            Date limitBindingDate = DateUtils.toDate(voucherInfo.getLimitBindingDate(), DateUtils.YYYY_MM_DD_PATTERN);
            String dateStr = limitBindingDate == null ? "" : DateUtils.dateToString(limitBindingDate, DateUtils.YYYY_MM_DD_PATTERN);
            availCoupon.setUsedStEndDt("请于" + dateStr + "前绑定");
        }
        if ("E".equals(state)) {
            availCoupon.setUsedStEndDt("有效期至" + voucherInfo.getExpireTime());
        }
    }

    //处理吉祥畅飞卡2.0 信息
    private static void dealUnlimitFlyV2(AvailCoupon availCoupon, VoucherInfo voucherInfo, String state) {
        availCoupon.setUsedStatus(voucherInfo.getUsedStatus());
        //是否他人赠送标识 true 是，false 否 2020-11-24
        availCoupon.setReceiveStatus(voucherInfo.getReceiveStatus() == null ? false : (voucherInfo.getReceiveStatus() == 0 ? false : true));
        availCoupon.setBound("yes".equalsIgnoreCase(voucherInfo.getLimitBindingStatus()));
        availCoupon.setCouponSubType(voucherInfo.getVoucherType());
        if ("yes".equals(voucherInfo.getLimitBindingStatus())) {
            availCoupon.setCouponBindState("B");
            availCoupon.setUsedStEndDt("有效期 " + dateLimitFilter(voucherInfo.getBookingLimit().getFlightDates(), 1));
            if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(voucherInfo.getVoucherType())
                    || VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(voucherInfo.getVoucherType())) {
                String desc = dateLimitFilter(voucherInfo.getBookingLimit().getFlightDates(), 0);
                availCoupon.setUsedLimit(StringUtils.isNotBlank(desc) ? "(不含" + desc + ")" : "");
            }
            //畅飞卡已经绑定，在权益券列表增加绑定信息展示
            if (HandlerConstants.PASSENGER_TYPE_CHD.equalsIgnoreCase(voucherInfo.getBindingType())) {
                availCoupon.setBindInfoDesc("已绑定" + voucherInfo.getChildCnName());
            } else if (HandlerConstants.PASSENGER_TYPE_ADT.equalsIgnoreCase(voucherInfo.getBindingType())
                    || (VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(voucherInfo.getVoucherType()) && StringUtils.isBlank(voucherInfo.getBindingType()))) {
                if (StringUtils.isNotBlank(voucherInfo.getReceiveMemberId())) {
                    availCoupon.setBindInfoDesc("已绑定" + voucherInfo.getAdultName());
                } else {
                    availCoupon.setBindInfoDesc("已绑定本人");
                }
            } else {
                availCoupon.setBindInfoDesc("");
            }
        } else {
            availCoupon.setCouponBindState("U");
            Date limitBindingDate = DateUtils.toDate(voucherInfo.getExpireTime(), DateUtils.YYYY_MM_DD_PATTERN);
            String dateStr = limitBindingDate == null ? "" : DateUtils.dateToString(limitBindingDate, DateUtils.YYYY_MM_DD_PATTERN);
            availCoupon.setUsedStEndDt("请于" + dateStr + "前绑定");
            availCoupon.setBindInfoDesc("");
        }
        if ("E".equals(state)) {
            availCoupon.setUsedStEndDt("有效期 " + dateLimitFilter(voucherInfo.getBookingLimit().getFlightDates(), 1));
            if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(voucherInfo.getVoucherType())
                    || VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(voucherInfo.getVoucherType())) {
                String desc = dateLimitFilter(voucherInfo.getBookingLimit().getFlightDates(), 0);
                availCoupon.setUsedLimit(StringUtils.isNotBlank(desc) ? "(不含" + desc + ")" : "");
            }
        }
    }

    //主题卡 信息
    private static void dealUnlimitTheme(AvailCoupon availCoupon, VoucherInfo voucherInfo, String state, String type) {
        availCoupon.setUsedStatus(voucherInfo.getUsedStatus());
        //是否他人赠送标识 true 是，false 否 2020-11-24
        availCoupon.setReceiveStatus(voucherInfo.getReceiveStatus() == null ? false : (voucherInfo.getReceiveStatus() == 0 ? false : true));
        availCoupon.setBound("yes".equalsIgnoreCase(voucherInfo.getLimitBindingStatus()));
        availCoupon.setCouponSubType(voucherInfo.getVoucherType());
        if ("yes".equals(voucherInfo.getLimitBindingStatus())) {
            availCoupon.setCouponBindState("B");
            availCoupon.setUsedStEndDt("有效期 " + voucherInfo.getActivateTime()+"至"+voucherInfo.getExpireTime());
            if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(voucherInfo.getVoucherType())
                    || VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(voucherInfo.getVoucherType())) {
                String desc = dateLimitFilter(voucherInfo.getBookingLimit().getFlightDates(), 0);
                availCoupon.setUsedLimit(StringUtils.isNotBlank(desc) ? "(不含" + desc + ")" : "");
            }
            //畅飞卡已经绑定，在权益券列表增加绑定信息展示
            if (HandlerConstants.PASSENGER_TYPE_CHD.equalsIgnoreCase(voucherInfo.getBindingType())) {
                availCoupon.setBindInfoDesc("已绑定" + (StringUtils.isNotBlank(voucherInfo.getChildCnName())? voucherInfo.getChildCnName():voucherInfo.getChildElastName()+ "/" + voucherInfo.getChildEfirstName()));
            } else if (HandlerConstants.PASSENGER_TYPE_ADT.equalsIgnoreCase(voucherInfo.getBindingType())
                    || (type.equals(voucherInfo.getVoucherType()) && StringUtils.isBlank(voucherInfo.getBindingType()))) {
                if (StringUtils.isNotBlank(voucherInfo.getReceiveMemberId())) {
                    availCoupon.setBindInfoDesc("已绑定" + voucherInfo.getAdultName());
                } else {
                    availCoupon.setBindInfoDesc("已绑定本人");
                }
            } else {
                availCoupon.setBindInfoDesc("");
            }
        } else {
            availCoupon.setCouponBindState("U");
            Date limitBindingDate = DateUtils.toDate(voucherInfo.getExpireTime(), DateUtils.YYYY_MM_DD_PATTERN);
            String dateStr = limitBindingDate == null ? "" : DateUtils.dateToString(limitBindingDate, DateUtils.YYYY_MM_DD_PATTERN);
            availCoupon.setUsedStEndDt("请于" + dateStr + "前绑定");
            availCoupon.setBindInfoDesc("");
        }
        if ("E".equals(state)) {
            availCoupon.setUsedStEndDt("有效期 " + dateLimitFilter(voucherInfo.getBookingLimit().getFlightDates(), 1));
            if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(voucherInfo.getVoucherType())
                    || VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(voucherInfo.getVoucherType())) {
                String desc = dateLimitFilter(voucherInfo.getBookingLimit().getFlightDates(), 0);
                availCoupon.setUsedLimit(StringUtils.isNotBlank(desc) ? "(不含" + desc + ")" : "");
            }
        }
    }

    /**
     * 获取航班日期时间范围
     *
     * @param dateLimits
     * @return
     */
    public static String dateLimitFilter(List<DateLimit> dateLimits, int limitType) {
        if (CollectionUtils.isNotEmpty(dateLimits)) {
            for (DateLimit dateLimit : dateLimits) {
                //0：不适用条件 1：适用条件 多条日期范围可能会有交集，以不适用优先。例：2018-11-11至2019-06-30适用但 2019-02-01至2019-02-20不适用
                if (dateLimit.getLimitType() == limitType) {
                    return dateLimit.getStartDate() + "至" + dateLimit.getEndDate();
                }
            }
        }
        return "";
    }

    /**
     * 机票预订中使用行李券类型转换
     *
     * @param voucherInfoList
     * @return
     */
    public static List<AvailCoupon> formatBaggegeCouponList(List<MainVoucherInfo> voucherInfoList) {
        List<AvailCoupon> availCouponList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(voucherInfoList)) {
            return availCouponList;
        }
        voucherInfoList.stream().forEach(mainVoucherInfo -> {
            if (!StringUtil.isNullOrEmpty(mainVoucherInfo.getMainVoucherNo()) && !StringUtil.isNullOrEmpty(mainVoucherInfo.getVoucherInfos())) {
                mainVoucherInfo.getVoucherInfos().forEach(voucherInfo -> {
                    AvailCoupon availCoupon = new AvailCoupon();
                    ResourceInfo resourceInfo = voucherInfo.getVoucherDetail();
                    availCoupon.setCouponName(resourceInfo.getResourceName());
                    availCoupon.setCouponType("");
                    availCoupon.setCouponSource(resourceInfo.getResourceType());
                    availCoupon.setCouponSourceName("行李券");
                    availCoupon.setCouponState(voucherInfo.getVoucherState());
                    VoucherStateEnum voucherStateEnum = VoucherStateEnum.queryVoucherState(voucherInfo.getVoucherState());
                    availCoupon.setCouponStateName(voucherStateEnum == null ? "" : voucherStateEnum.getName());
                    availCoupon.setCouponNo(voucherInfo.getVoucherNo());
                    availCoupon.setIsGive("Y");//默认暂时都是允许赠送
                    availCoupon.setUseMode(resourceInfo.getUseMode());
                    availCoupon.setStartDate(voucherInfo.getActivateTime());
                    availCoupon.setEndData(voucherInfo.getExpireTime());
                    availCoupon.setUsedStEndDt(voucherInfo.getActivateTime() + "至" + voucherInfo.getExpireTime());
                    availCoupon.setUnit(resourceInfo.getBaggageExt().getBaggageUnit());
                    availCoupon.setOverweight(resourceInfo.getBaggageExt().getBaggageValue());
                    availCouponList.add(availCoupon);
                });
            }
        });
        return availCouponList;
    }

    /**
     * 其他适用航线转换方法
     *
     * @param bookingLimit
     * @param cityInfoMap
     * @return
     */
    public static List<List<String>> applyOtherAirLine(BookingLimit bookingLimit, Map<String, CityInfoDto> cityInfoMap) {
        //航线限制处理
        List<List<String>> routeList = new ArrayList<>();
        if (bookingLimit != null && !StringUtil.isNullOrEmpty(bookingLimit.getFlightRoutes())) {
            List<BlackList> whiteList = bookingLimit.getFlightRoutes().stream().filter(flightRoute -> "White".equals(flightRoute.getListType())).collect(Collectors.toList());
            //遍历白名单数据
            Map<String, List<String>> whiteMap = new HashMap<>();
            for (BlackList item : whiteList) {
                for (String str : item.getListValue()) {
                    String[] values = str.split("-");
                    List<String> arrList;
                    if (whiteMap.get(values[0]) == null) {
                        arrList = new ArrayList<>();
                        arrList.add(values[1]);
                    } else {
                        arrList = whiteMap.get(values[0]);
                        if (!arrList.contains(values[1])) {
                            arrList.add(values[1]);
                        }
                    }
                    whiteMap.put(values[0], arrList);
                }
            }
            for (Map.Entry<String, List<String>> entry : whiteMap.entrySet()) {
                String key = entry.getKey();
                List<String> arrList = new ArrayList<>();
                if (cityInfoMap.get(key) != null) {
                    String depCityName = cityInfoMap.get(key).getCityName();
                    entry.getValue().forEach(s -> {
                        if ("*".equals(s)) {
                            arrList.add(depCityName + "出港");
                        } else {
                            if (cityInfoMap.get(s) != null) {
                                arrList.add(depCityName + "-" + cityInfoMap.get(s).getCityName());
                            } else if ("DOMESTIC".equals(s)) {
                                arrList.add(depCityName + "-国内机场");
                            }
                        }
                    });
                } else if ("DOMESTIC".equals(key)) {
                    entry.getValue().forEach(s -> {
                        if ("*".equals(s)) {
                            arrList.add("国内出港");
                        } else {
                            if (cityInfoMap.get(s) != null) {
                                arrList.add("国内机场-" + cityInfoMap.get(s).getCityName());
                            } else if ("DOMESTIC".equals(s)) {
                                arrList.add("国内航线（不含港澳台地区）");
                            }
                        }
                    });
                } else if ("*".equals(key)) {
                    entry.getValue().forEach(s -> {
                        if ("DOMESTIC".equals(s)) {
                            arrList.add("国内到达（不含港澳台地区）");
                        } else if ("*".equals(s)) {
                            arrList.add("所有航线");
                        } else {
                            String arrCityName = cityInfoMap.get(s).getCityName();
                            if (arrCityName != null) {
                                arrList.add(arrCityName + "到达");
                            }
                        }

                    });
                }
                routeList.add(arrList);
            }
        } else {
            List<String> all = new ArrayList<>();
            all.add("所有航线");
            routeList.add(all);
        }
        return routeList;
    }

    /**
     * 其他适用航线转换方法（使用于新产品平台的改期、升舱）
     *
     * @param productRuleLimit
     * @return
     */
    public static List<List<String>> applyOtherAirLineNew(ProductRuleLimit productRuleLimit, Map<String, String> cityMap) {
        //航线限制处理
        List<List<String>> routeList = new ArrayList<>();
        if (productRuleLimit != null && !StringUtil.isNullOrEmpty(productRuleLimit.getAirLine())) {
            List<String> limitAirLines = new ArrayList<>();
            for (AirlineLimit limit : productRuleLimit.getAirLine()) {
                limitAirLines.addAll(limit.getValue());
            }
            //遍历白名单数据
            Map<String, List<String>> whiteMap = new HashMap<>();
            for (String str : limitAirLines) {
                String[] values = str.split("-");
                List<String> arrList;
                if (whiteMap.get(values[0]) == null) {
                    arrList = new ArrayList<>();
                    arrList.add(values[1]);
                } else {
                    arrList = whiteMap.get(values[0]);
                    if (!arrList.contains(values[1])) {
                        arrList.add(values[1]);
                    }
                }
                whiteMap.put(values[0], arrList);
            }
            for (Map.Entry<String, List<String>> entry : whiteMap.entrySet()) {
                String key = entry.getKey();
                List<String> arrList = new ArrayList<>();
                if (cityMap.get(key) != null) {
                    CityInfoDto cityInfoDto = JsonUtil.fromJson(cityMap.get(key), CityInfoDto.class);
                    String depCityName = cityInfoDto.getCityName();
                    entry.getValue().forEach(s -> {
                        if ("*".equals(s)) {
                            arrList.add(depCityName + "出港");
                        } else {
                            if (cityMap.get(s) != null) {
                                CityInfoDto arrCity = JsonUtil.fromJson(cityMap.get(s), CityInfoDto.class);
                                arrList.add(depCityName + "-" + arrCity.getCityName());
                            } else if ("DOMESTIC".equals(s)) {
                                arrList.add(depCityName + "-国内机场");
                            }
                        }
                    });
                } else if ("DOMESTIC".equals(key)) {
                    entry.getValue().forEach(s -> {
                        if ("*".equals(s)) {
                            arrList.add("国内出港");
                        } else {
                            if (cityMap.get(s) != null) {
                                CityInfoDto arrCity = JsonUtil.fromJson(cityMap.get(s), CityInfoDto.class);
                                arrList.add("国内机场-" + arrCity.getCityName());
                            } else if ("DOMESTIC".equals(s)) {
                                arrList.add("国内航线（不含港澳台地区）");
                            }
                        }
                    });
                } else if ("*".equals(key)) {
                    entry.getValue().forEach(s -> {
                        if ("DOMESTIC".equals(s)) {
                            arrList.add("国内到达（不含港澳台地区）");
                        } else if ("*".equals(s)) {
                            arrList.add("所有航线");
                        } else {
                            CityInfoDto arrCity = JsonUtil.fromJson(cityMap.get(s), CityInfoDto.class);
                            String arrCityName = arrCity.getCityName();
                            if (arrCityName != null) {
                                arrList.add(arrCityName + "到达");
                            }
                        }
                    });
                }
                routeList.add(arrList);
            }
        } else {
            List<String> all = new ArrayList<>();
            all.add("所有航线");
            routeList.add(all);
        }
        return routeList;
    }

    /**
     * 赠送优惠券名称处理
     *
     * @param couponSource
     * @return
     */
    public static String showCouponNameBySource(String couponSource) {
        String couponName;
        if (StringUtil.isNullOrEmpty(couponSource)) {
            couponSource = "";
        }

        couponSource = couponSource.toUpperCase();
        switch (couponSource) {
            case "BAGGAGE":
            case "BAGGAGECOUPON": {
                couponName = "行李额度兑换券";
                break;
            }
            case "LOUNGE":
            case "LOUNGECOUPON": {
                couponName = "贵宾休息室券";
                break;
            }
            case "UPGRADE":
            case "UPGRADECOUPON": {
                couponName = "升舱兑换券";
                break;
            }
            case "RESCHEDULE": {
                couponName = "改期兑换券";
                break;
            }
            case "UNLIMITUPGRADEYEAR": {
                couponName = "无限升舱卡";
                break;
            }
            case "CHILDUNLIMITEDFLY": {
                couponName = "儿童畅飞卡";
                break;
            }
            case "ADULTUNLIMITEDFLY":
            case "UNLIMITEDFLYSF":
            case "UNLIMITEDFLY": {
                couponName = "吉祥畅飞卡";
                break;
            }
            default: {
                couponName = "权益券";
                break;
            }
        }
        return couponName;
    }

    /**
     * 赠送权益券请求参数
     *
     * @param changeCouponReq
     * @param channelCode
     * @param userNo
     * @return
     */
    public static PtCouponProductGiveVoucherRequestDto toPtChangeCouponReq(ChangeCouponReq changeCouponReq, String channelCode, String userNo) {
        PtCouponProductGiveVoucherRequestDto ptCouponProductGiveVoucherRequestDto = new PtCouponProductGiveVoucherRequestDto(HandlerConstants.VERSION, channelCode, userNo);
        ptCouponProductGiveVoucherRequestDto.setFfpId(changeCouponReq.getFfpId());
        ptCouponProductGiveVoucherRequestDto.setFfpCardNo(changeCouponReq.getFfpCardNo());
        ptCouponProductGiveVoucherRequestDto.setVoucherNoList(changeCouponReq.getCouponNos());
        ptCouponProductGiveVoucherRequestDto.setCouponSource(changeCouponReq.getCouponSource());
        return ptCouponProductGiveVoucherRequestDto;
    }

    /**
     * 领取权益券请求参数
     *
     * @param receiveChangeCouponRequest
     * @param ffpId
     * @param ffpCardNo
     * @param channelCode
     * @param userNo
     * @return
     */
    public static PtCouponProductAcceptVoucherRequestDto toPtReceiveChangeCouponRequest(ReceiveChangeCouponRequest receiveChangeCouponRequest, String ffpId, String ffpCardNo, String channelCode, String userNo) {
        PtCouponProductAcceptVoucherRequestDto ptCouponProductAcceptVoucherRequestDto = new PtCouponProductAcceptVoucherRequestDto(HandlerConstants.VERSION, channelCode, userNo);
        ptCouponProductAcceptVoucherRequestDto.setFfpId(ffpId);
        ptCouponProductAcceptVoucherRequestDto.setFfpCardNo(ffpCardNo);
        ptCouponProductAcceptVoucherRequestDto.setVoucherNoList(receiveChangeCouponRequest.getCouponCodes());
        ptCouponProductAcceptVoucherRequestDto.setCouponSource(receiveChangeCouponRequest.getCouponSource());
        return ptCouponProductAcceptVoucherRequestDto;
    }

    /**
     * 转换字典值
     *
     * @param data
     * @return
     */
    public static Map<String, Map<String, String>> getStringMapMap(Map<String, List<CouponProductVisaDictionariesResponseDto.SimpleTreeNode>> data) {
        Map<String, Map<String, String>> mapMap = new HashMap<>();
        data.keySet().forEach(s -> {
            List<CouponProductVisaDictionariesResponseDto.SimpleTreeNode> list = data.get(s);
            list.removeIf(a -> "全部".equals(a.getLabel()));
            //将list转换为map
            Map<String, String> map = new HashMap<>();
            list.forEach(a -> {
                map.put(a.getValue(), a.getLabel());
            });
            mapMap.put(s, map);
        });
        return mapMap;
    }

    /**
     * 生成可用人航段信息
     *
     * @param availCoupon
     * @param passengerInfoList
     * @param flightInfoList
     * @return
     */
    public static List<UsePassengerSegment> genUsePassengerSegment(AvailCoupon availCoupon, List<ChangePassengerInfo> passengerInfoList,
                                                                   List<FlightInfo> flightInfoList, List<ChangeFlightInfo> changeFlightInfoList,
                                                                   Map<String, CityInfoDto> cityInfoMap, PtCRMResponse<PtMemberDetail> ptCRMResponse) {
        List<UsePassengerSegment> usePassengerSegments = Lists.newArrayList();

        com.juneyaoair.baseclass.response.coupons.BookingLimit bookingLimit = availCoupon.getBookingLimit();
        if (bookingLimit != null) {
            passengerInfoList.forEach(passenger -> {
                boolean ownPass = false;
                if (availCoupon.getSelfUse() == 1) {
                    //判断是否是本人
                    ownPass = isOwnPass(ptCRMResponse, passenger);
                } else {
                    ownPass = true;
                }
                if (ownPass) {
                    for (FlightInfo flightInfo : flightInfoList) {
                        CabinFare cabinFare = ChangeObjectConvert.findCabin(flightInfo, passenger.getPassengerType());
                        // 改期手续费为0的不可用改期券
                        if (null == cabinFare) {
                            continue;
                        }
                        Boolean isAvailable = true;
                        String unAvailableMsg = "";
                        Optional<ChangeFlightInfo> old = changeFlightInfoList.stream().filter(flight -> flight.getDepCityCode().equals(flightInfo.getDepCity())
                                && flight.getArrCityCode().equals(flightInfo.getArrCity())).findFirst();
                        if (old.isPresent()) {
                            ChangeFlightInfo oldFlight = old.get();
                            // 判断券是否适用
                            if (!listLimitFilter(Collections.singletonList(bookingLimit.getFlightNos()), oldFlight.getFlightNo())
                                    || !listLimitFilter(Collections.singletonList(bookingLimit.getCabins()), oldFlight.getCabin())
                                    || !listLimitFilter(bookingLimit.getFlightRoutes(), suitFlightLine(oldFlight.getDepCityCode(), oldFlight.getArrCityCode(), cityInfoMap))  //航线判断
                                    || !dateLimitFilter(bookingLimit.getFlightDates(), DateUtils.toDate(oldFlight.getDepFlightDate(), DateUtils.YYYY_MM_DD_PATTERN))) {
                                continue;
                            }

                            if (!availCoupon.getIsAvailable()) {

                                isAvailable = availCoupon.getIsAvailable();
                                unAvailableMsg = availCoupon.getUnAvailableMsg();

                            } else if (availCoupon.getRescheduleType() != null && availCoupon.getRescheduleType().equals("1") && availCoupon.getAdvanceHour() != null) {
                                if (DateUtils.differHours(DateUtils.getStringDate(), oldFlight.getDepFlightDate() + " " + oldFlight.getDepDateTime()) <= Integer.parseInt(availCoupon.getAdvanceHour())) {
                                    isAvailable = false;
                                    unAvailableMsg = "该改期券仅限航班起飞" + availCoupon.getAdvanceHour() + "小时前可用";
                                }
                            }

                            UsePassengerSegment usePassengerSegment = new UsePassengerSegment(
                                    passenger.getPassengerID() + oldFlight.getDepCityCode() + oldFlight.getArrCityCode(),
                                    passenger, flightInfo, BigDecimal.valueOf(cabinFare.getChangeServiceCharge()), availCoupon.getCouponNo(), isAvailable, unAvailableMsg);
                            usePassengerSegments.add(usePassengerSegment);


                        }
                    }
                }
            });
        }

        return usePassengerSegments;

    }


    public static boolean isOwnPass(PtCRMResponse<PtMemberDetail> ptCRMResponse, PassengerInfo passenger) {
        //证件信息
        List<MemberCertificateSoaModelV2> certificateInfoList = ptCRMResponse.getData().getCertificateInfo();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(passenger.getCertNo()) && CollectionUtils.isNotEmpty(certificateInfoList)) {
            MemberCertificateSoaModelV2 soaModelV2 = certificateInfoList.stream().filter(certificateInfo -> certificateInfo.getCertificateNumber().equalsIgnoreCase(passenger.getCertNo())).findFirst().orElse(null);
            if (soaModelV2 != null) {
                CertificateTypeEnum certificateType = CertificateTypeEnum.checkType(soaModelV2.getCertificateType());
                if (null != certificateType && certificateType.getShowCode().equals(passenger.getCertType())
                        && soaModelV2.getCertificateNumber().equalsIgnoreCase(passenger.getCertNo())) {
                    //基础信息
                    MemberBasicInfoSoaModel memberInfo = ptCRMResponse.getData().getBasicInfo();
                    // 匹配中文姓名
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(memberInfo.getCLastName())
                            && org.apache.commons.lang3.StringUtils.isNotBlank(memberInfo.getCFirstName())
                            && (memberInfo.getCLastName() + memberInfo.getCFirstName()).equals(passenger.getPassengerName())) {
                        return true;
                    } else if (org.apache.commons.lang3.StringUtils.isNotBlank(memberInfo.getELastName())
                            && org.apache.commons.lang3.StringUtils.isNotBlank(memberInfo.getEFirstName())
                            && (memberInfo.getELastName() + "/" + memberInfo.getEFirstName()).equals(passenger.getPassengerName())) {
                        // 匹配英文姓名
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private static boolean isOwnPass(PtCRMResponse<PtMemberDetail> ptCRMResponse, ChangePassengerInfo passenger) {
        //证件信息
        List<MemberCertificateSoaModelV2> certificateInfoList = ptCRMResponse.getData().getCertificateInfo();
        if (StringUtils.isNotBlank(passenger.getCertNo()) && CollectionUtils.isNotEmpty(certificateInfoList)) {
            MemberCertificateSoaModelV2 soaModelV2 = certificateInfoList.stream().filter(certificateInfo -> certificateInfo.getCertificateNumber().equalsIgnoreCase(passenger.getCertNo())).findFirst().orElse(null);
            if (soaModelV2 != null) {
                CertificateTypeEnum certificateType = CertificateTypeEnum.checkType(soaModelV2.getCertificateType());
                if (null != certificateType && certificateType.getShowCode().equals(passenger.getCertType())
                        && soaModelV2.getCertificateNumber().equalsIgnoreCase(passenger.getCertNo())) {
                    //基础信息
                    MemberBasicInfoSoaModel memberInfo = ptCRMResponse.getData().getBasicInfo();
                    // 匹配中文姓名
                    if (StringUtils.isNotBlank(memberInfo.getCLastName())
                            && StringUtils.isNotBlank(memberInfo.getCFirstName())
                            && (memberInfo.getCLastName() + memberInfo.getCFirstName()).equals(passenger.getPassengerName())) {
                        return true;
                    } else if (StringUtils.isNotBlank(memberInfo.getELastName())
                            && StringUtils.isNotBlank(memberInfo.getEFirstName())
                            && (memberInfo.getELastName() + "/" + memberInfo.getEFirstName()).equals(passenger.getPassengerName())) {
                        // 匹配英文姓名
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private static List<String> suitFlightLine(String depCityCode, String arrCityCode, Map<String, CityInfoDto> cityInfoMap) {
        List<String> suitAirline = new ArrayList<>();
        suitAirline.add("*-*");
        suitAirline.add(depCityCode + "-*");
        suitAirline.add("*-" + arrCityCode);
        suitAirline.add(depCityCode + "-" + arrCityCode);
        CityInfoDto depCity = cityInfoMap.get(depCityCode);
        CityInfoDto arrCity = cityInfoMap.get(arrCityCode);
        if (depCity != null && arrCity != null) {
            if (HandlerConstants.TRIP_TYPE_D.equals(depCity.getIsInternational()) && HandlerConstants.TRIP_TYPE_D.equals(arrCity.getIsInternational())) {
                suitAirline.add("*-DOMESTIC");
                suitAirline.add("DOMESTIC-*");
                suitAirline.add("DOMESTIC-DOMESTIC");
                suitAirline.add(depCityCode + "-DOMESTIC");
                suitAirline.add("DOMESTIC-" + arrCityCode);
            } else if (HandlerConstants.TRIP_TYPE_D.equals(depCity.getIsInternational()) && HandlerConstants.TRIP_TYPE_I.equals(arrCity.getIsInternational())) {
                suitAirline.add("*-INTL");
                suitAirline.add("DOMESTIC-*");
                suitAirline.add("DOMESTIC-INTL");
                suitAirline.add(depCityCode + "-INTL");
                suitAirline.add("DOMESTIC-" + arrCityCode);
            } else if (HandlerConstants.TRIP_TYPE_I.equals(depCity.getIsInternational()) && HandlerConstants.TRIP_TYPE_D.equals(arrCity.getIsInternational())) {
                suitAirline.add("*-DOMESTIC");
                suitAirline.add("INTL-*");
                suitAirline.add("INTL-DOMESTIC");
                suitAirline.add(depCityCode + "-DOMESTIC");
                suitAirline.add("INTL-" + arrCityCode);
            } else {
                suitAirline.add("*-INTL");
                suitAirline.add("INTL-*");
                suitAirline.add("INTL-INTL");
                suitAirline.add(depCityCode + "-INTL");
                suitAirline.add("INTL-" + arrCityCode);
            }
        }
        return suitAirline;
    }

    /**
     * 过滤时间参数
     *
     * @param dateLimits
     * @param date
     * @return
     */
    public static boolean dateLimitFilter(List<DateLimit> dateLimits, Date date) {
        boolean success = true;
        if (null == date) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(dateLimits)) {
            for (DateLimit dateLimit : dateLimits) {
                Date startDate = DateUtils.toDate(dateLimit.getStartDate(), DateUtils.YYYY_MM_DD_PATTERN);
                Date endDate = DateUtils.toDate(dateLimit.getEndDate(), DateUtils.YYYY_MM_DD_PATTERN);
                if (dateLimit.getLimitType() == 1) {
                    if (null != startDate && date.getTime() < startDate.getTime()) {
                        success = false;
                    }
                    if (null != endDate && date.getTime() > endDate.getTime()) {
                        success = false;
                    }
                } else {
                    if (null != startDate && date.getTime() >= startDate.getTime() && null != endDate && date.getTime() <= endDate.getTime()) {
                        success = false;
                    }
                }
                if (!success) {
                    break;
                }
            }
        }
        return success;
    }

    /**
     * 过滤列表条件
     *
     * @param limitLists
     * @param value
     * @return
     */
    private static boolean listLimitFilter(List<BlackList> limitLists, String value) {
        if (CollectionUtils.isEmpty(limitLists)) {
            return true;
        }
        boolean success = true;
        for (BlackList limitList : limitLists) {
            if (limitList != null && CollectionUtils.isNotEmpty(limitList.getListValue())) {
                success = limitList.getListValue().contains(value);
            }
            if (!success) {
                break;
            }
        }
        return success;
    }

    /**
     * list 航线比对
     *
     * @param limitLists
     * @param valueList
     * @return
     */
    private static boolean listLimitFilter(List<BlackList> limitLists, List<String> valueList) {
        if (CollectionUtils.isEmpty(limitLists)) {
            return true;
        }
        boolean success = false;
        for (BlackList limit : limitLists) {
            if (limit != null && "White".equals(limit.getListType())) {
                if (CollectionUtils.isNotEmpty(limit.getListValue())) {
                    for (String str : valueList) {
                        success = limit.getListValue().stream().anyMatch(s -> s.equals(str));
                        if (success) {
                            return true;
                        }
                    }
                }
            }
        }
        return success;
    }
}
