package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.*;
import com.juneyaoair.appenum.order.OrderCouponStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.comm.CertificateModel;
import com.juneyaoair.baseclass.member.request.MemberDetailReq;
import com.juneyaoair.baseclass.salecoupon.bean.CouponBindInfo;
import com.juneyaoair.baseclass.unlimitedUp.request.*;
import com.juneyaoair.baseclass.unlimitedUp.response.ApplyAppointmentResponseDto;
import com.juneyaoair.baseclass.unlimitedUp.response.QueryMemberInfoDTO;
import com.juneyaoair.baseclass.unlimitedUp.response.UpQueryBindInfoResponse;
import com.juneyaoair.baseclass.upclass.AvailableUpClassInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.IUnlimitCouponService;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.PtRealNameReq;
import com.juneyaoair.thirdentity.member.response.*;
import com.juneyaoair.thirdentity.passengers.common.GeneralContactCertInfo;
import com.juneyaoair.thirdentity.passengers.common.GeneralContactInfo;
import com.juneyaoair.thirdentity.passengers.req.GeneralContactRequest;
import com.juneyaoair.thirdentity.passengers.resp.PtV2GeneralContactResponse;
import com.juneyaoair.thirdentity.salecoupon.v2.common.UnlimitedFlyBindDetail;
import com.juneyaoair.thirdentity.salecoupon.v2.response.PtFlyCardInfo;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName UnlimitedUpgradeController
 * @Description 无限升舱卡
 * <AUTHOR>
 * @Date 2020/7/3 13:44
 **/
@RequestMapping("/unlimited/up")
@RestController
@Api(value = "UnlimitedUpgradeController", tags = {"无限卡服务"})
public class UnlimitedUpgradeController extends BassController {
    private static final String SERVICE_NAME = "无限卡服务";
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IUnlimitCouponService unlimitCouponService;

    @ApiOperation(value = "预约申请", notes = "预约申请")
    @RequestMapping(value = "/applyAppointment", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<AvailableUpClassInfo> applyAppointment(@RequestBody BaseReq<UpApplyRequest> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String logStr = JsonUtil.objectToJson(req);
        log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, logStr);
        UpApplyRequest applyRequest = req.getRequest();
        //参数有效性检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;
        }
        UpApplyRequest upApplyRequest = req.getRequest();
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId() + "", req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
            String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
            //同盾参数
//            if ("Y".equals(handConfig.getUseTongDun())) {
//                FraudApiResponse fraudApiResponse = FraudApiInvoker.unlimitedUpgradeReservationRiskControl(upApplyRequest.getBlackBox(), upApplyRequest.getFfpCardNo(), platform,
//                        headChannelCode, ip, upApplyRequest.getMobile());
//                if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
//                    resp.setResultCode(WSEnum.ERROR.getResultCode());
//                    resp.setResultInfo("活动太火爆了，请稍后重试");
//                    log.info("同盾拦截：用户卡号:{} 同盾响应:{}", upApplyRequest.getFfpCardNo(), JsonUtil.objectToJson(fraudApiResponse));
//                    return resp;
//                }
//            }
            //创建无限升舱预约申请对象
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            ApplyAppointmentRequestDto applyAppointmentRequestDto = new ApplyAppointmentRequestDto();
            applyAppointmentRequestDto.setVersion(SystemConstants.VERSION);
            applyAppointmentRequestDto.setUserNo(userNo);
            applyAppointmentRequestDto.setChannelCode(req.getChannelCode());
            if (StringUtils.isBlank(upApplyRequest.getProductType())) {
                applyAppointmentRequestDto.setServiceType("limitlessUpgradeCard");
            } else {
                applyAppointmentRequestDto.setServiceType(upApplyRequest.getProductType());
            }
            applyAppointmentRequestDto.setFfCardNo(applyRequest.getFfpCardNo());
            applyAppointmentRequestDto.setChannelCustomerNo(applyRequest.getFfpId());
            //调用会员信息查询手机号
            String channelCode = req.getChannelCode();//MOBILE
            //查询会员当前级别
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
            PtApiCRMRequest ptApiCRMRequest = buildCommReqNoToken(request, channelCode);
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(applyRequest.getFfpCardNo());
            ptMemberDetailRequest.setRequestItems(items);
            ptApiCRMRequest.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
            if (ptCRMResponse.getCode() == 0) {
                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                if (!applyRequest.getFfpId().equals(String.valueOf(basicInfo.getMemberId()))) {
                    // 检证失败返回
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("会员信息不匹配");
                    return resp;
                } else {
                    //获取手机号，如果手机号为空，不调用绑定接口，直接返回成功
                    List<MemberContactSoaModel> contactSoaModelList = ptCRMResponse.getData().getContactInfo();
                    if (!StringUtil.isNullOrEmpty(contactSoaModelList)) {
                        MemberContactSoaModel memberContactMobile = CRMReqUtil.getContactInfo(contactSoaModelList, ContactTypeEnum.MOBILE.getCode());
                        if (memberContactMobile != null) {
                            applyAppointmentRequestDto.setLinkerHandphone(memberContactMobile.getContactNumber());
                        }
                    }
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("会员信息不存在");
                return resp;
            }
            //如果手机号为空，不调用绑定接口，直接返回成功
            if (StringUtils.isBlank(applyAppointmentRequestDto.getLinkerHandphone())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            }

            //5.3版本使用同盾
//        if ("Y".equals(handConfig.getUseTongDun())) {
//            String code = request.getHeader(CHANNEL_CODE);
//            if (VersionNoUtil.toVerInt(req.getClientVersion()) >= 5030000 || ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(code) || ChannelCodeEnum.WEIXIN.getChannelCode().equalsIgnoreCase(code)) {
//                //验证同盾
//                FraudApiResponse fraudApiResponse = FraudApiInvoker.registerRiskControl(headChannelCode,req.getPlatformInfo(), applyRequest.getMobile(), applyRequest.getBlackBox(), RegisterTypeEnum.NORMAL.getCode(), ip, req.getClientVersion());
//                String logStr2 = JsonUtil.objectToJson(fraudApiResponse);
//                log.info("【无限升舱卡预约申请】请求号:{}，IP地址:{}，同盾返回结果:{}", reqId, ip, logStr2);
//
//                applyAppointmentRequestDto.setTongdunScore(fraudApiResponse.getFinal_score());
//            }
//        }
            Map<String, String> headMap = HttpUtil.getHeaderMap(getClientIP(request), "");
            HttpResult result = doPostClient(applyAppointmentRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.UNLIMITED_UP_APPLY, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            ApplyAppointmentResponseDto applyAppointmentResponseDto = null;
            if (result.isResult()) {
                applyAppointmentResponseDto = (ApplyAppointmentResponseDto) JsonUtil.jsonToBean(result.getResponse(), ApplyAppointmentResponseDto.class);
                if (applyAppointmentResponseDto != null) {
                    //根据状态返回1001（成功）和 2208（已预约），提示预约成功
                    if (applyAppointmentResponseDto.getResultCode().equals("1001")) {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(applyAppointmentResponseDto.getErrorInfo());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR.getResultInfo());
                }
            }
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;

        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "绑定", notes = "绑定")
    @RequestMapping(value = "/binding", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp binding(@RequestBody BaseReq<UpBindRequest> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String logStr = JsonUtil.objectToJson(req);
        log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, logStr);
        UpBindRequest applyRequest = req.getRequest();
        //参数有效性检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;
        }
        //2021-09-09 无限升舱1.0兼容其他版本app，不传默认使用6.5.1传的参数，没有实际用处，只是为了不报类型不可空
        if(StringUtils.isBlank(applyRequest.getCouponSource())){
            applyRequest.setCouponSource("Upgrade");
        }
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(realChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= 60000) {
            ObjCheckUtil.notBlank(applyRequest.getCouponSource(), "类型不可为空");
        }
        if (!ChannelCodeEnum.MOBILE.getChannelCode().equals(realChannelCode)) {
            ObjCheckUtil.notBlank(applyRequest.getCouponSource(), "类型不可为空");
        }
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId() + "", req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        Map<String, String> headMap = HttpUtil.getHeaderMap(getClientIP(request), "");
        try {
            String channelCode = req.getChannelCode();//MOBILE
            //查询会员当前级别
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName,
                    MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.ADDRESSINFOS.eName};
            PtApiCRMRequest ptApiCRMRequest = buildCommReqNoToken(request, channelCode);
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(applyRequest.getFfpCardNo());
            ptMemberDetailRequest.setRequestItems(items);
            ptApiCRMRequest.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
            if (ptCRMResponse.getCode() == 0) {
                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                if (!applyRequest.getFfpId().equals(String.valueOf(basicInfo.getMemberId()))) {
                    // 检证失败返回
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("会员信息不匹配");
                    return resp;
                } else {
                    log.info("会员信息：" + ptCRMResponse.getData());
                    //创建无限升舱绑定对象
                    PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
                    ptRequest.setFfpId(applyRequest.getFfpId());
                    ptRequest.setFfpCardNo(applyRequest.getFfpCardNo());
                    if (applyRequest.getCouponBindInfo() == null && applyRequest.getCouponNo().startsWith("SC")) {//未传值代表原有的无限升舱卡操作
                        resp = unlimitCouponService.bindUnLimitUpgrade(applyRequest, ptRequest, ptCRMResponse, headMap);
                    } else if (applyRequest.getCouponBindInfo() != null && VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode().equals(applyRequest.getCouponSource())) {//绑定儿童畅飞卡
                        //检验输入信息
                        CouponBindInfo couponBindInfo = applyRequest.getCouponBindInfo();
                        ObjCheckUtil.notNull(couponBindInfo, "儿童绑定信息不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getChildCnName(), "儿童姓名不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getChildIdNumber(), "儿童证件不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getChildEnLastName(), "儿童英文姓信息不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getChildEnFirstName(), "儿童英文名不可为空");
                        String clientPwd = getChannelInfo(channelCode, "40");
                        boolean matches = couponBindInfo.getChildIdNumber().matches(PatternCommon.ID_NUMBER);
                        if (!matches) {
                            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                            resp.setResultInfo("身份证格式不正确");
                            return resp;
                        }
                        resp = unlimitCouponService.bindChdFlyUnLimit(applyRequest, ptRequest,ptCRMResponse, channelCode, clientPwd, headMap);
                    }else if(VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode().equals(applyRequest.getCouponSource())) {
                        //吉祥畅飞卡绑定
                        resp = unlimitCouponService.bindAutFlyUnLimit(applyRequest, ptRequest, ptCRMResponse,
                                channelCode, getChannelInfo(channelCode, "40"), headMap);
                    } else if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(applyRequest.getCouponSource())
                            || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(applyRequest.getCouponSource())){
                        //检验输入信息
                        CouponBindInfo couponBindInfo = applyRequest.getCouponBindInfo();
                        ObjCheckUtil.notNull(couponBindInfo, "绑定信息不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getCertType(), "证件类型不可为空");
                        CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkShowCode(couponBindInfo.getCertType());
                        if(certificateTypeEnum == null){
                            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                            resp.setResultInfo("证件类型不正确");
                            return resp;
                        }
                        String clientPwd = getChannelInfo(channelCode, "40");
                        if(HandlerConstants.PASSENGER_TYPE_CHD.equalsIgnoreCase(couponBindInfo.getBindingType())){
                            ObjCheckUtil.notBlank(couponBindInfo.getChildCnName(), "儿童姓名不可为空");
                            ObjCheckUtil.notBlank(couponBindInfo.getChildIdNumber(), "儿童证件不可为空");
                            ObjCheckUtil.notBlank(couponBindInfo.getChildEnLastName(), "儿童英文姓信息不可为空");
                            ObjCheckUtil.notBlank(couponBindInfo.getChildEnFirstName(), "儿童英文名不可为空");
                            ObjCheckUtil.notBlank(couponBindInfo.getChildBirthDate(), "儿童出生日期不可为空");
                            if(CertificateTypeEnum.ID_CARD.getShowCode().equalsIgnoreCase(couponBindInfo.getCertType())){
                                boolean matches = couponBindInfo.getChildIdNumber().matches(PatternCommon.ID_NUMBER);
                                if (!matches) {
                                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                    resp.setResultInfo("身份证格式不正确");
                                    return resp;
                                }
                                String certNo = couponBindInfo.getChildIdNumber().toUpperCase();
                                String birthDate = CertUtil.certNoToDate(certNo);
                                couponBindInfo.setChildBirthDate(birthDate);
                            }
                            if(CertificateTypeEnum.PASSPORT.getShowCode().equalsIgnoreCase(couponBindInfo.getCertType())){
                                boolean matches = couponBindInfo.getChildIdNumber().matches(PatternCommon.PASSPORT_NO);
                                if (!matches) {
                                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                    resp.setResultInfo("护照格式不正确");
                                    return resp;
                                }
                            }
                            String curDate = DateUtils.getCurrentDateStr();
                            int age = DateUtils.getAgeByBirthIncludeBirthDay(couponBindInfo.getChildBirthDate(), curDate, DateUtils.YYYY_MM_DD_PATTERN);
                            if (age >= 12 || age < 2) {
                                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                resp.setResultInfo("儿童年龄不符合要求，儿童年龄需在2周岁（含）至12周岁（不含）");
                                return resp;
                            }
                        }else if(HandlerConstants.PASSENGER_TYPE_ADT.equalsIgnoreCase(couponBindInfo.getBindingType())){
                            ObjCheckUtil.notBlank(couponBindInfo.getAdultCnName(), "本人姓名不可为空");
                            ObjCheckUtil.notBlank(couponBindInfo.getAdultIdNumber(), "本人证件不可为空");
                            ObjCheckUtil.notBlank(couponBindInfo.getAdultEnLastName(), "本人英文姓信息不可为空");
                            ObjCheckUtil.notBlank(couponBindInfo.getAdultEnFirstName(), "本人英文名不可为空");
                            if(CertificateTypeEnum.ID_CARD.getShowCode().equalsIgnoreCase(couponBindInfo.getCertType())){
                                boolean matches = couponBindInfo.getAdultIdNumber().matches(PatternCommon.ID_NUMBER);
                                if (!matches) {
                                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                    resp.setResultInfo("身份证格式不正确");
                                    return resp;
                                }
                            }
                            if(CertificateTypeEnum.PASSPORT.getShowCode().equalsIgnoreCase(couponBindInfo.getCertType())){
                                boolean matches = couponBindInfo.getAdultIdNumber().matches(PatternCommon.PASSPORT_NO);
                                if (!matches) {
                                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                    resp.setResultInfo("护照格式不正确");
                                    return resp;
                                }
                            }
                        } else{
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("绑定类型不正确");
                            return resp;
                        }
                        resp = unlimitCouponService.bindFlyUnLimitV2(applyRequest, ptRequest,ptCRMResponse, channelCode, clientPwd, headMap);
                    }else if(VoucherTypesEnum.UnlimitUpgradeYear.getCode().equalsIgnoreCase(applyRequest.getCouponSource())){
                        //检验输入信息
                        CouponBindInfo couponBindInfo = applyRequest.getCouponBindInfo();
                        ObjCheckUtil.notNull(couponBindInfo, "绑定信息不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getCertType(), "证件类型不可为空");
                        CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkShowCode(couponBindInfo.getCertType());
                        if(certificateTypeEnum == null){
                            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                            resp.setResultInfo("证件类型不正确");
                            return resp;
                        }
                        String clientPwd = getChannelInfo(channelCode, "40");
                        ObjCheckUtil.notBlank(couponBindInfo.getAdultCnName(), "本人姓名不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getAdultIdNumber(), "本人证件不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getAdultEnLastName(), "本人英文姓信息不可为空");
                        ObjCheckUtil.notBlank(couponBindInfo.getAdultEnFirstName(), "本人英文名不可为空");
                        if(CertificateTypeEnum.ID_CARD.getShowCode().equalsIgnoreCase(couponBindInfo.getCertType())){
                            boolean matches = couponBindInfo.getAdultIdNumber().matches(PatternCommon.ID_NUMBER);
                            if (!matches) {
                                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                resp.setResultInfo("身份证格式不正确");
                                return resp;
                            }
                        }
                        if(CertificateTypeEnum.PASSPORT.getShowCode().equalsIgnoreCase(couponBindInfo.getCertType())){
                            boolean matches = couponBindInfo.getAdultIdNumber().matches(PatternCommon.PASSPORT_NO);
                            if (!matches) {
                                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                resp.setResultInfo("护照格式不正确");
                                return resp;
                            }
                        }
                        resp = unlimitCouponService.bindUnLimitUpgradeV2(applyRequest, ptRequest,ptCRMResponse, channelCode, clientPwd, headMap);
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("不支持的操作类型！");
                    }
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("会员信息不存在");
                return resp;
            }
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;
        } catch (Exception e) {
            log.error("绑定失败", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }


    @ApiOperation(value = "查看绑定信息", notes = "查看绑定信息")
    @RequestMapping(value = "/queryBinding", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<UpQueryBindInfoResponse> queryBindingInfo(@RequestBody BaseReq<UpQueryBindInfoRequest> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String reqJson = JsonUtil.objectToJson(req);
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        long st = System.currentTimeMillis();
        UpQueryBindInfoRequest applyRequest = req.getRequest();
        //参数有效性检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;
        }
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId() + "", req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String channelCode = req.getChannelCode();
            Map<String, String> headMap = HttpUtil.getHeaderMap(getClientIP(request), "");
            //儿童无限飞
            if (VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode().equals(applyRequest.getCouponSource())) {
                PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
                ptRequest.setFfpId(applyRequest.getFfpId());
                ptRequest.setFfpCardNo(applyRequest.getFfpCardNo());
                ptRequest.setRequest(applyRequest.getCouponNo());
                resp = unlimitCouponService.queryBindingInfoChdFly(applyRequest, ptRequest, channelCode, headMap, request);
            } else if (VoucherTypesEnum.UPGRADE.getCode().equals(applyRequest.getCouponSource()) && VoucherTypesEnum.UPGRADEUNLIMITED.getCode().equals(applyRequest.getCouponSubType())) {
                //创建无限升舱绑定对象
                UpgradeUnlimitedBindRequestDto bindRequestDto = new UpgradeUnlimitedBindRequestDto();
                bindRequestDto.setVoucherNo(applyRequest.getCouponNo());
                PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
                ptRequest.setFfpId(applyRequest.getFfpId());
                ptRequest.setFfpCardNo(applyRequest.getFfpCardNo());
                ptRequest.setRequest(bindRequestDto);
                resp = unlimitCouponService.queryBindingInfo(applyRequest, ptRequest, channelCode, headMap, request);
            } else if (VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode().equals(applyRequest.getCouponSource())) {
                PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
                ptRequest.setFfpId(applyRequest.getFfpId());
                ptRequest.setFfpCardNo(applyRequest.getFfpCardNo());
                ptRequest.setRequest(applyRequest.getCouponNo());
                ptRequest.setSearchType(VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode());
                resp = unlimitCouponService.queryBindingInfoAutFly(applyRequest, ptRequest, channelCode, headMap, request);
            } else if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(applyRequest.getCouponSource())
                    || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(applyRequest.getCouponSource())){
                PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
                ptRequest.setFfpId(applyRequest.getFfpId());
                ptRequest.setFfpCardNo(applyRequest.getFfpCardNo());
                ptRequest.setRequest(applyRequest.getCouponNo());
                resp = unlimitCouponService.queryBindingInfoFlyV2(applyRequest, ptRequest, channelCode, headMap, request);
            } else if(VoucherTypesEnum.UnlimitUpgradeYear.getCode().equalsIgnoreCase(applyRequest.getCouponSource())){
                PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
                ptRequest.setFfpId(applyRequest.getFfpId());
                ptRequest.setFfpCardNo(applyRequest.getFfpCardNo());
                ptRequest.setRequest(applyRequest.getCouponNo());
                resp = unlimitCouponService.queryBindingInfoUpgradeV2(applyRequest, ptRequest, channelCode, headMap, request);
            } else{
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("不支持的业务类型！");
                return resp;
            }
            String respJson = JsonUtil.objectToJson(resp);
            saveRespInfo(SERVICE_NAME, reqId, ip, st, reqJson, respJson);
            return resp;
        } catch (NetworkException e) {
            log.error("查询绑定信息网络异常", e);
            resp.setResultInfo(WSEnum.NETWORK_ERROR.getResultInfo());
            resp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    @RequestMapping(value = "makeupCommonPerson", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "补偿插入常用乘机人数据", notes = "补偿插入常用乘机人数据")
    @InterfaceLog
    public BaseResp makeupCommonPerson(@RequestBody MakeupCommonPersonReq req, HttpServletRequest request){
        BaseResp<String> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            String channelCode = "MOBILE";
            //查询会员当前级别
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName,
                    MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.ADDRESSINFOS.eName};
            PtApiCRMRequest ptApiCRMRequest = buildCommReqNoToken(request, channelCode);
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(req.getFfpCardNo());
            ptMemberDetailRequest.setRequestItems(items);
            ptApiCRMRequest.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
            if (ptCRMResponse.getCode() != 0) {
                throw new OperationFailedException("查询会员详情异常");
            }
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            boolean accmflag = false;
            //验证是否实名认证
            PtRealNameReq ptRealNameReq = new PtRealNameReq();
            ptRealNameReq.setID(req.getFfpId());
            ptRealNameReq.setClientCode(channelCode);
            ptRealNameReq.setSignature(EncoderHandler.encodeByMD5(channelCode + req.getFfpId() + getChannelInfo(channelCode, "40")).toUpperCase());
            PtRealNameResp realNameResp = memberService.realNameState(ptRealNameReq);
            VerifyChannelEnum verifyChannelEnum = null;
            if ("000".equals(realNameResp.getStatusCode())) {
                VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(realNameResp.getVerifyStatus());
                if (verifyStatusEnum != null && VerifyStatusEnum.PASS.code.equals(verifyStatusEnum.code)) {
                    verifyChannelEnum = VerifyChannelEnum.checkEnum(realNameResp.getVerifyChannel());
                    accmflag = true;
                }
            }
            if (!accmflag) {
                resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
                resp.setResultInfo(WSEnum.NO_REAL_NAME.getResultInfo());
                return resp;
            }
            PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
            ptRequest.setFfpId(req.getFfpId());
            ptRequest.setFfpCardNo(req.getFfpCardNo());
            boolean adtResult;
            boolean chdResult;
            if (VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode().equals(req.getResourceType())) {
                adtResult = processOwnCommonPerson(ptRequest, ptCRMResponse,
                        ChannelCodeEnum.MOBILE.getChannelCode(), headMap, verifyChannelEnum);
                chdResult = true;
            } else if (VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode().equals(req.getResourceType())) {
                adtResult = processOwnCommonPerson(ptRequest, ptCRMResponse,
                        ChannelCodeEnum.MOBILE.getChannelCode(), headMap, verifyChannelEnum);
                CouponBindInfo couponBindInfo = new CouponBindInfo(
                        req.getChildCnName(),
                        req.getChildEnLastName(),
                        req.getChildEnFirstName(),
                        req.getChildIdNumber()
                );
                chdResult = processChildCommonPerson(ptRequest, couponBindInfo, ptCRMResponse.getData().getContactInfo(), channelCode, headMap);
            } else if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(req.getResourceType())) {
                adtResult = processOwnCommonPerson(ptRequest, ptCRMResponse,
                        ChannelCodeEnum.MOBILE.getChannelCode(), headMap, verifyChannelEnum);
                chdResult = true;
            } else {
                throw new OperationFailedException("绑定类型不能为空");
            }
            if (adtResult && chdResult) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            }
        } catch (Exception e) {
            log.error("补偿常用乘机人出现异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    //绑定儿童的时候将儿童信息添加到常用乘机人列表
    private boolean processChildCommonPerson(PtRequest ptRequest, CouponBindInfo couponBindInfo, List<MemberContactSoaModel> memberContactSoaModelList,
                                          String channelCode, Map<String, String> headMap){
        GeneralContactRequest generalContactRequest = new GeneralContactRequest(HandlerConstants.VERSION, channelCode, ptRequest.getUserNo());
        generalContactRequest.setAddFlag(true);
        generalContactRequest.setInterFlag(false);
        List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
        GeneralContactInfo generalContactInfo = new GeneralContactInfo();
        generalContactInfo.setPassengerName(couponBindInfo.getChildCnName().toUpperCase());
        generalContactInfo.setPassEnNameS(couponBindInfo.getChildEnLastName().toUpperCase());
        generalContactInfo.setPassEnNameF(couponBindInfo.getChildEnFirstName().toUpperCase());
        generalContactInfo.setChannelCustomerType("CRM");
        generalContactInfo.setNationality("CN");
        generalContactInfo.setChannelCustomerNo(ptRequest.getFfpId());
        generalContactInfo.setSex(CertUtil.checkSex(couponBindInfo.getChildIdNumber()));
        generalContactInfo.setBirthdate(CertUtil.certNoToDate(couponBindInfo.getChildIdNumber()));
        generalContactInfo.setContactType("A");
        int age = DateUtils.getAgeByBirthIncludeBirthDay(generalContactInfo.getBirthdate(), DateUtils.getCurrentDateStr(), DateUtils.YYYY_MM_DD_PATTERN);
        String passType = CertNoUtil.toPassType(age);
        generalContactInfo.setPassengerType(passType);
        MemberContactSoaModel memberContactSoaModel = CRMReqUtil.getContactInfo(memberContactSoaModelList, ContactTypeEnum.MOBILE.getCode());
        if (memberContactSoaModel != null) {
            generalContactInfo.setCountryTelCode("86");
            generalContactInfo.setHandphoneNo(memberContactSoaModel.getContactNumber());
        }
        GeneralContactCertInfo generalContactCertInfo = new GeneralContactCertInfo();
        generalContactCertInfo.setCertNo(couponBindInfo.getChildIdNumber());
        generalContactCertInfo.setCertType(CertificateTypeEnum.ID_CARD.getShowCode());
        List<GeneralContactCertInfo> generalContactCertInfoList = new ArrayList<>();
        generalContactCertInfoList.add(generalContactCertInfo);
        generalContactInfo.setContactCertList(generalContactCertInfoList);
        generalContactInfoList.add(generalContactInfo);
        generalContactRequest.setGeneralContactList(generalContactInfoList);
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.ADD_COMMON_PERSON_V20;
        log.info("ffpId{}补偿常用乘机人请求{}", generalContactRequest.getUserNo(),JsonUtil.objectToJson(generalContactRequest));
        HttpResult httpResult = doPostClient(generalContactRequest, url, headMap);
        log.info("ffpId{}补偿常用乘机人响应{}", generalContactRequest.getUserNo(), JsonUtil.objectToJson(generalContactRequest));
        if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
            return false;
        }
        PtV2GeneralContactResponse ptResponse = (PtV2GeneralContactResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtV2GeneralContactResponse.class);
        if (UnifiedOrderResultEnum.REPEATED_PASS.getResultCode().equals(ptResponse.getResultCode())
            || UnifiedOrderResultEnum.CHECK_90010.getResultCode().equals(ptResponse.getResultCode())
            || UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            return true;
        }
        return false;
    }

    //绑定儿童/成人畅飞卡的时候，将会员信息本人添加到常用乘机人列表
    private boolean processOwnCommonPerson(PtRequest ptRequest, PtCRMResponse<PtMemberDetail> ptCRMResponse,
                                        String channelCode, Map<String, String> headMap, VerifyChannelEnum verifyChannelEnum){
        MemberBasicInfoSoaModel basicInfoSoaModel = ptCRMResponse.getData().getBasicInfo();
        GeneralContactRequest generalContactRequest = new GeneralContactRequest(HandlerConstants.VERSION, channelCode, ptRequest.getUserNo());
        generalContactRequest.setAddFlag(true);
        generalContactRequest.setInterFlag(false);
        List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
        GeneralContactInfo generalContactInfo = new GeneralContactInfo();
        generalContactInfo.setPassengerName(basicInfoSoaModel.getCLastName() + basicInfoSoaModel.getCFirstName());
        generalContactInfo.setPassEnNameF(basicInfoSoaModel.getEFirstName());
        generalContactInfo.setPassEnNameS(basicInfoSoaModel.getELastName());
//        generalContactInfo.setContactType("B");
        generalContactInfo.setContactType("C");
        generalContactInfo.setChannelCustomerType("CRM");
        List<MemberAddressSoaModel> addressInfos = ptCRMResponse.getData().getAddressInfos();
        if (CollectionUtils.isNotEmpty(addressInfos)) {
            generalContactInfo.setNationality(addressInfos.get(0).getCountryCode());
        }
        generalContactInfo.setChannelCustomerNo(ptRequest.getFfpId());
        generalContactInfo.setSex(basicInfoSoaModel.getSex() == 1 ? "M" : "F");
        String birthdate= DateUtils.dateToString(new Date(basicInfoSoaModel.getBirthDay()), DateUtils.YYYY_MM_DD_PATTERN);
        generalContactInfo.setBirthdate(birthdate);
        String curDate = DateUtils.getCurrentDateStr();
        int age = DateUtils.getAgeByBirthIncludeBirthDay(birthdate, curDate, DateUtils.YYYY_MM_DD_PATTERN);
        String passType = CertNoUtil.toPassType(age);
        generalContactInfo.setPassengerType(passType);
        MemberContactSoaModel memberContactSoaModel = CRMReqUtil.getContactInfo(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.MOBILE.getCode());
        if (memberContactSoaModel != null) {
            generalContactInfo.setCountryTelCode("86");
            generalContactInfo.setHandphoneNo(memberContactSoaModel.getContactNumber());
        }
        //获取会员实名认证的证件信息
        List<MemberCertificateSoaModelV2> certificateInfo = ptCRMResponse.getData().getCertificateInfo();
        List<GeneralContactCertInfo> generalContactCertInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(certificateInfo)) {
            for (MemberCertificateSoaModelV2 soaModelV2 : certificateInfo) {
                if (soaModelV2.isVerify()) {
                    GeneralContactCertInfo generalContactCertInfo = new GeneralContactCertInfo();
                    generalContactCertInfo.setCertNo(soaModelV2.getCertificateNumber());
                    generalContactCertInfo.setCertType(CertificateTypeEnum.checkType(soaModelV2.getCertificateType()).getShowCode());
                    generalContactCertInfoList.add(generalContactCertInfo);
                }
            }
        }
        if (CollectionUtils.isEmpty(generalContactCertInfoList)) {
            // 非证件照认证默认身份证
            if (!VerifyChannelEnum.Photo.equals(verifyChannelEnum)) {
                if (CollectionUtils.isNotEmpty(certificateInfo)) {
                    for (MemberCertificateSoaModelV2 soaModelV2 : certificateInfo) {
                        if (soaModelV2.getCertificateType() == CertificateTypeEnum.ID_CARD.getCode()) {
                            GeneralContactCertInfo generalContactCertInfo = new GeneralContactCertInfo();
                            generalContactCertInfo.setCertNo(soaModelV2.getCertificateNumber());
                            generalContactCertInfo.setCertType(CertificateTypeEnum.checkType(soaModelV2.getCertificateType()).getShowCode());
                            generalContactCertInfoList.add(generalContactCertInfo);
                        }
                    }
                }
            } else {
                //其他证件不判断
                if (CollectionUtils.isNotEmpty(certificateInfo)) {
                    for (MemberCertificateSoaModelV2 soaModelV2 : certificateInfo) {
                        GeneralContactCertInfo generalContactCertInfo = new GeneralContactCertInfo();
                        generalContactCertInfo.setCertNo(soaModelV2.getCertificateNumber());
                        generalContactCertInfo.setCertType(CertificateTypeEnum.checkType(soaModelV2.getCertificateType()).getShowCode());
                        generalContactCertInfoList.add(generalContactCertInfo);
                    }
                }
            }
        }
        generalContactInfo.setContactCertList(generalContactCertInfoList);
        generalContactInfoList.add(generalContactInfo);
        generalContactRequest.setGeneralContactList(generalContactInfoList);
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.ADD_COMMON_PERSON_V20;
        log.info("ffpId{}补偿常用乘机人请求{}", generalContactRequest.getUserNo(),JsonUtil.objectToJson(generalContactRequest));
        HttpResult httpResult = doPostClient(generalContactRequest, url, headMap);
        log.info("ffpId{}补偿常用乘机人响应{}", generalContactRequest.getUserNo(), JsonUtil.objectToJson(generalContactRequest));
        if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
            return false;
        }
        PtV2GeneralContactResponse ptResponse = (PtV2GeneralContactResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtV2GeneralContactResponse.class);
        if (UnifiedOrderResultEnum.REPEATED_PASS.getResultCode().equals(ptResponse.getResultCode())
                || UnifiedOrderResultEnum.CHECK_90010.getResultCode().equals(ptResponse.getResultCode())
                || UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            return true;
        }
        return false;
    }

    @ApiOperation(value = "绑定本人初始化会员信息", notes = "绑定本人初始化会员信息")
    @InterfaceLog
    @RequestMapping(value = "memberInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<QueryMemberInfoDTO> memberDetail(@RequestBody BaseReq<MemberDetailReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            MemberDetailReq memberDetailReq = req.getRequest();
            String channelCode = req.getChannelCode();
            boolean flag = this.checkKeyInfo(memberDetailReq.getFfpId(), memberDetailReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String memberCardNo = memberDetailReq.getFfpCardNo();
            String memberId = memberDetailReq.getFfpId();
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                    , MemberDetailRequestItemsEnum.STATEINFO.eName
                    , MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName
                    , MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(memberCardNo, memberId, request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            if (ptCRMResponse.getCode() == 0) {
                QueryMemberInfoDTO detail = new QueryMemberInfoDTO();
                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                String name = CRMReqUtil.getMemberName(basicInfo);
                detail.setCName(name);
                detail.setELastName(basicInfo.getELastName());
                detail.setEFirstName(basicInfo.getEFirstName());

                List<MemberCertificateSoaModelV2> soaModelV2s = null;
                List<MemberRealNameSummarySoaModel> realVerifyInfos = ptCRMResponse.getData().getRealVerifyInfos();
                //筛选审核通过的证件，实名认证时间降序排序，最近的实名认证时间是否在功能上线之后
                List<MemberRealNameSummarySoaModel> collect = realVerifyInfos.stream().filter(realVerifyInfo -> realVerifyInfo.getStatus().equals(VerifyStatusEnum.PASS.code))
                        .sorted(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate, Comparator.reverseOrder())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(collect)){
                    MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = collect.get(0);
                    //支付宝，人脸，银联使用身份证
                    if(VerifyChannelEnum.ZhiFuBao.code.equals(memberRealNameSummarySoaModel.getVerifyChannel())
                            || VerifyChannelEnum.Unionpay.code.equals(memberRealNameSummarySoaModel.getVerifyChannel())
                            || VerifyChannelEnum.Face.code.equals(memberRealNameSummarySoaModel.getVerifyChannel()) ){
                        //证件处理
                        if (!StringUtil.isNullOrEmpty(ptCRMResponse.getData().getCertificateInfo())) {
                            //获取已经实名的证件信息；证件类型降序排列，取第一位，方便多种证件进行实名认证，优先获取身份证、其次护照、其他
                            soaModelV2s = ptCRMResponse.getData().getCertificateInfo().stream()
                                    .sorted(Comparator.comparing(MemberCertificateSoaModelV2::getCertificateType))
                                    .limit(1).collect(Collectors.toList());
                        }

                    }else{
                        if (!StringUtil.isNullOrEmpty(ptCRMResponse.getData().getCertificateInfo())) {
                            //获取已经实名的证件信息；证件类型降序排列，取第一位，方便多种证件进行实名认证，优先获取身份证、其次护照、其他
                            soaModelV2s = ptCRMResponse.getData().getCertificateInfo().stream()
                                    .filter(memberCertificateSoaModelV2 -> memberCertificateSoaModelV2.isVerify())
                                    .sorted(Comparator.comparing(MemberCertificateSoaModelV2::getCertificateType))
                                    .limit(1).collect(Collectors.toList());
                        }
                    }
                }
                //证件处理
                if(CollectionUtils.isNotEmpty(soaModelV2s)){
                    CertificateModel cert = new CertificateModel();
                    BeanUtils.copyNotNullProperties(soaModelV2s.get(0), cert);
                    CertificateTypeEnum ct = CertificateTypeEnum.checkType(cert.getCertificateType());
                    detail.setCertificateTypeCName(ct.getDesc());
                    detail.setCertificateType(ct.getShowCode());
                    detail.setCertificateNumber(cert.getCertificateNumber());
                    detail.setReVerifyFlag(false);
                }else{
                    detail.setReVerifyFlag(true);
                }
                resp.setObjData(detail);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询会员详情出错！");
            return resp;
        }
    }
    @ApiOperation(value = "畅飞卡2.0退款之前获取该券的绑定信息", notes = "畅飞卡2.0退款之前获取该券的绑定信息")
    @InterfaceLog
    @RequestMapping(value = "getRefundFlyV2Tip", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<QueryMemberInfoDTO> getRefundFlyV2Tip(@RequestBody @Validated BaseReq<UpQueryBindInfoRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        JSONObject jsonObject = new JSONObject();
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            UpQueryBindInfoRequest bindInfoRequest = req.getRequest();
            String channelCode = req.getChannelCode();
            boolean flag = this.checkKeyInfo(bindInfoRequest.getFfpId(), bindInfoRequest.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            Map<String, String> headMap = HttpUtil.getHeaderMap(getClientIP(request), "");
            if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(bindInfoRequest.getCouponSource())
                    || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(bindInfoRequest.getCouponSource())
                    || VoucherTypesEnum.UnlimitUpgradeYear.getCode().equalsIgnoreCase(bindInfoRequest.getCouponSource())
                    || handConfig.getThemeCouponList().contains(bindInfoRequest.getCouponSource())){

                PtRequest ptRequest = this.buildCommPtReq(request, channelCode);
                ptRequest.setFfpId(bindInfoRequest.getFfpId());
                ptRequest.setFfpCardNo(bindInfoRequest.getFfpCardNo());
                ptRequest.setRequest(bindInfoRequest.getCouponNo());
                PtResponse<PtFlyCardInfo> ptResponse = orderManage.queryFlyCardBindingRecordV2(ptRequest, headMap);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                    PtFlyCardInfo ptFlyCardInfo = ptResponse.getResult();
                    //券码查询一般只有一条记录
                    UnlimitedFlyBindDetail flyBindDetail = ptFlyCardInfo.getBindDetail();
                    jsonObject.put("tip","");
                    if (flyBindDetail == null) {
                        jsonObject.put("tip","");
                    }else{
                        //券码查询一般只有一条记录
                        if (StringUtils.isBlank(flyBindDetail.getReceiveMemberId())) {
                            if("yes".equalsIgnoreCase(flyBindDetail.getBindStatus())){
                                if(VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(flyBindDetail.getResourceType())){
                                    jsonObject.put("tip","此产品卡已绑定本人使用，您申请退款后此产品卡立即失效，将无法使用");
                                }else{
                                    if(HandlerConstants.PASSENGER_TYPE_CHD.equalsIgnoreCase(flyBindDetail.getBindingType())){
                                        jsonObject.put("tip","此产品卡已绑定"+flyBindDetail.getChildCnName()+"使用，您申请退款后此产品卡立即失效，将无法使用");
                                    }else if(HandlerConstants.PASSENGER_TYPE_ADT.equalsIgnoreCase(flyBindDetail.getBindingType())){
                                        jsonObject.put("tip","此产品卡已绑定本人使用，您申请退款后此产品卡立即失效，将无法使用");
                                    }
                                }
                            }else{
                                jsonObject.put("tip","");
                            }
                        }else{
                            //表示购买人查看赠送信息
                            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
                            PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(flyBindDetail.getReceiveMemberCard(), flyBindDetail.getReceiveMemberId(), request, channelCode, items);
                            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
                            ptMemberDetailRequest.setCardNO(flyBindDetail.getReceiveMemberCard());
                            ptMemberDetailRequest.setRequestItems(items);
                            ptApiCRMRequest.setData(ptMemberDetailRequest);
                            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
                            if (ptCRMResponse.getCode() == 0) {
                                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                                if ("no".equals(flyBindDetail.getBindStatus()) && OrderCouponStateEnum.GiveAway.getStateCode().equals(bindInfoRequest.getCouponState())) {
                                    //显示手机号后四位
                                    List<MemberContactSoaModel> contactInfo = ptCRMResponse.getData().getContactInfo();
                                    if (CollectionUtils.isNotEmpty(contactInfo)) {
                                        MemberContactSoaModel memberContactMobile = CRMReqUtil.getContactInfo(contactInfo, ContactTypeEnum.MOBILE.getCode());
                                        if (memberContactMobile != null) {
                                            //获取受赠人的姓名
                                            String name =basicInfo.getCLastName()+basicInfo.getCFirstName();
                                            if (StringUtils.isNotEmpty(name)){
                                                jsonObject.put("tip","此产品卡已赠送给"+name+"，您申请退款后此产品卡立即失效，受赠人将无法使用");
                                            }else {
                                                String mobile = memberContactMobile.getContactNumber();
                                                jsonObject.put("tip","此产品卡已赠送给手机尾号"+mobile.substring(mobile.length() - 4)+"的用户，您申请退款后此产品卡立即失效，受赠人将无法使用");
                                            }

                                        }else{
                                            jsonObject.put("tip","此产品卡已赠送给用户，您申请退款后此产品卡立即失效，受赠人将无法使用");
                                        }
                                    }else{
                                        jsonObject.put("tip","此产品卡已赠送给用户，您申请退款后此产品卡立即失效，受赠人将无法使用");
                                    }
                                }
                                if ("yes".equals(flyBindDetail.getBindStatus())) {
                                    if (OrderCouponStateEnum.Not.getStateCode().equals(bindInfoRequest.getCouponState()) || OrderCouponStateEnum.GiveAway.getStateCode().equals(bindInfoRequest.getCouponState())) {
                                        //返回会员姓名或者本人
                                        String name = CRMReqUtil.getMemberName(basicInfo);
                                        jsonObject.put("tip","此产品卡已赠送给"+name+"，您申请退款后此产品卡立即失效，受赠人将无法使用");
                                    }
                                }

                            } else {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setErrorInfo("会员信息不存在");
                                return resp;
                            }

                        }
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setObjData(jsonObject);
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptResponse.getErrorInfo());
                }
                return resp;
            } else {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("很抱歉，您查询的权益券不符合要求！");
                return resp;
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询该券绑定信息出错！");
            return resp;
        }
    }
    @Autowired
    private OrderManage orderManage;

}
