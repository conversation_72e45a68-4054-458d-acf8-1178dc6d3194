package com.juneyaoair.mobile.handler.controller;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.base.ThemeCoupon;
import com.juneyaoair.baseclass.request.payment.GatewayReq;
import com.juneyaoair.baseclass.request.payment.PaymentCardReq;
import com.juneyaoair.baseclass.request.payment.PaymentReq;
import com.juneyaoair.baseclass.request.payment.WxPaymentReq;
import com.juneyaoair.baseclass.response.payment.*;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.service.bean.PayGatewayPageUrl;
import com.juneyaoair.thirdentity.request.payment.PtGatewayReq;
import com.juneyaoair.thirdentity.request.payment.PtPaymentReq;
import com.juneyaoair.thirdentity.request.payment.PtPaymentWXReq;
import com.juneyaoair.thirdentity.response.payment.PtGatewayInfo;
import com.juneyaoair.thirdentity.response.payment.PtGatewayResp;
import com.juneyaoair.thirdentity.response.payment.PtPaymentResp;
import com.juneyaoair.utils.DES3;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Type;
import java.util.*;

/**
 * Created by qinxiaoming on 2016-4-26.
 */
@RequestMapping("/paymentService")
@RestController
public class PaymentController extends BassController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final String LOG_RESP_ONE = "【易宝信用卡支付】请求号:{}，IP地址:{}，服务器响应结果：{}";
    private static final String LOG_RESP_TWO = "支付出错，请重新支付。";
    private static final String LOG_RESP_TWO_925 = "小吉正在为您处理中，等候片刻即可支付";
    private static final String LOG_RESP_THREE = "支付失败:{}";
    private static final String VAULE_ONE = "PaymentChannelNo";
    private static final String VALUE_TWO = "ChkValue";
    private static final String VALUE_THREE = "{'BillCreateIP':'";

    // 支付网关查询
    @InterfaceLog
    @ApiOperation(value = "支付网关查询")
    @RequestMapping(value = "/queryGateway", method = RequestMethod.POST)
    public GatewayResp queryGateway(@RequestBody GatewayReq req, HttpServletRequest request) {
        GatewayResp response = new GatewayResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        //
        PtGatewayReq gateWayReq = new PtGatewayReq(HandlerConstants.VERSION,
                req.getChannelCode(), req.getPaymentChannelCode());
        BeanUtils.copyProperties(req, gateWayReq);

        String chkValue = gateWayReq.getVersion() + gateWayReq.getChannelNo() + req.getPaymentChannelCode() + getChannelInfo(gateWayReq.getChannelNo(), "20");
        chkValue = sha1Encode(chkValue);
        gateWayReq.setChkValue(chkValue);
        String preUrl = HandlerConstants.URL_GATEWAY_QUERY;
        String postUrl = preUrl + "?Version=" + gateWayReq.getVersion() + "&ChannelNo=" + gateWayReq.getChannelNo() + "&PaymentChannelNo=" + gateWayReq.getPaymentChannelNo()
                + "&ChkValue=" + gateWayReq.getChkValue();
        HttpResult result = HttpUtil.doGetClient(postUrl,handConfig.getReadTimeout(),handConfig.getConnectTimeout());
        if (result.isResult()) {
            logger.info("postUrl:{},响应结果:{}", postUrl, result.getResponse());
            PtGatewayResp resp = (PtGatewayResp) JsonUtil.jsonToBean(result.getResponse(), PtGatewayResp.class);
            if (resp.getRespCode().equals("1001")) {
                BeanUtils.copyProperties(resp, response);
                //信用卡支付网关，排除微信支付
                PtGatewayInfo[] ptGatewayInfos = resp.getGatewayInfoList();
                List<PtGatewayInfo> ptGatewayInfoList = new ArrayList<>();
                for (int i = 0; i < ptGatewayInfos.length; i++) {
                    PtGatewayInfo baseInfo = ptGatewayInfos[i];
                    if (baseInfo.getGatewayType().equals("4")) {
                        ptGatewayInfoList.add(baseInfo);
                    }
                }
                GatewayInfo[] gatewayInfos = new GatewayInfo[ptGatewayInfoList.size()];
                int n = 0;
                for (int i = 0; i < ptGatewayInfos.length; i++) {
                    PtGatewayInfo baseInfo = resp.getGatewayInfoList()[i];
                    if (!baseInfo.getGatewayType().equals("4")) {
                        continue;
                    }
                    GatewayInfo gi = new GatewayInfo();
                    BeanUtils.copyProperties(baseInfo, gi);
                    gatewayInfos[n] = gi;
                    n++;
                }
                // 银联卡支付页面新增服务协议配置
                for (int i = 0; i < gatewayInfos.length; i++) {
                    GatewayInfo gatewayInfo = gatewayInfos[i];
                    Map<String, PayGatewayPageUrl> pageUrlMap = toPayGatewayPageMap(handConfig.getPayGatewayPageUrl());
                    PayGatewayPageUrl payGatewayPageUrl = pageUrlMap.get(gatewayInfo.getGatewayNo());
                    if (payGatewayPageUrl != null) {
                        gatewayInfo.setJumpUrl(payGatewayPageUrl.getJumpUrl());
                    }
                }
                response.setGatewayInfoList(gatewayInfos);
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return response;
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo(resp.getErrMsg());
                return response;
            }

        } else {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("查询网络出错");
            return response;
        }
    }

    //信用卡支付
    @InterfaceLog
    @RequestMapping(value = "/orderPay", method = RequestMethod.POST)
    public PaymentResp payment(@RequestBody @Validated PaymentCardReq req, BindingResult bindingResult, HttpServletRequest request) {
        PaymentResp resp = new PaymentResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        PtPaymentReq paymentReq = new PtPaymentReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, "",
                HandlerConstants.BACK_RETURN_URL_PAY, "", "订单", "机票订单", "", "Web", req.getChannelCode());
        String postUrl = HandlerConstants.URL_PAY;
        //暂时处理   处理请求参数中的.00
        if (req.getAmount().split("\\.").length > 2) {//异常金额
            String amount = req.getAmount();
            amount = amount.substring(0, amount.lastIndexOf('.'));
            if (amount.substring(amount.indexOf('.')).length() < 3) {
                amount = amount + "0";
            }
            req.setAmount(amount);
        }
        BeanUtils.copyProperties(req, paymentReq);
        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = "";
        if (HandlerConstants.M_CHANNEL_CODE.equals(req.getChannelCode())) {//手机
            payChannel = HandlerConstants.M_CHANNEL_CODE;
        } else if (HandlerConstants.W_CHANNEL_CODE.equals(req.getChannelCode())) {
            payChannel = HandlerConstants.W_CHANNEL_CODE;
        }
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //支付卡信息
        //支付卡加密
        String key = getChannelInfo(paymentReq.getChannelNo(), "20");
        String cardInfo = DES3.des3EncodeECB(key.substring(0, key.length() - 8), paymentReq.getCardInfo());
        paymentReq.setCardInfo(cardInfo);
        parametersMap.put("CardInfo", paymentReq.getCardInfo());
        //签名
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        HttpResult result = doPayPost(postUrl, parametersMap);
        if (result.isResult()) {
            PtPaymentResp ptResp = (PtPaymentResp) JsonUtil.jsonToBean(result.getResponse(), PtPaymentResp.class);
            BeanUtils.copyProperties(ptResp, resp);
            if (resp.getRespCode().equals("1001")) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(resp.getErrorMsg());
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }

    //微信支付
    @RequestMapping(value = "/orderPayByWX", method = RequestMethod.POST)
    public PaymentWXResp orderPayByWX(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        PaymentWXResp resp = new PaymentWXResp();

        //<editor-fold desc="验证">
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //</editor-fold>
        //<editor-fold desc="加密,生成加密字符串">
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, "",
                HandlerConstants.BACK_RETURN_URL_PAY, "", "订单", "机票订单", "", "Web");
        String postUrl = HandlerConstants.URL_PAY;
        //暂时处理   处理请求参数中的.00
        if (req.getAmount().split("\\.").length > 2) {//异常金额
            String amount = req.getAmount();
            amount = amount.substring(0, amount.lastIndexOf('.'));
            String s = String.valueOf(amount.substring(amount.indexOf('.')).length());
            logger.info(s);
            if (amount.substring(amount.indexOf('.')).length() < 3) {
                amount = amount + "0";
            }
            req.setAmount(amount);
        }
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        //微信支付网关暂时就一个，固定
        //根据渠道判断
        String billCreateIP = this.getClientIP(request);
        String dynamicParameters = "";

        String gatewayNo = "";

        gatewayNo = "31603";
        paymentReq.setGatewayNo(gatewayNo);
        dynamicParameters = VALUE_THREE + billCreateIP + "'}";

        paymentReq.setGatewayType("6");
        paymentReq.setDynamicParameters(dynamicParameters);

        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();

        //19支付平台渠道用户号
        String payChannel = HandlerConstants.M_CHANNEL_CODE;
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = getChannelInfo(paymentReq.getChannelNo(), "20");
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        //</editor-fold>
        HttpResult result = doPayPost(postUrl, parametersMap);
        if (result.isResult()) {
            resp = (PaymentWXResp) JsonUtil.jsonToBean(result.getResponse(), PaymentWXResp.class);
            if (StringUtil.isNullOrEmpty(resp.getAppId()) || StringUtil.isNullOrEmpty(resp.getNonceStr())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("支付异常:" + resp.getErrorMsg());
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            }
            String respJson = JsonUtil.objectToJson(resp);
            logger.info("orderPayByWX返回的结果:{}", respJson);
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO_925);
            return resp;
        }
    }

    //支付宝支付
    @RequestMapping(value = "/orderPayByAlipay", method = RequestMethod.POST)
    public PaymentAlipayResp orderPayByAlipay(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        PaymentAlipayResp resp = new PaymentAlipayResp();

        //<editor-fold desc="验证">
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS_925.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS_925.getResultInfo());
            return resp;
        }
        //</editor-fold>
        //<editor-fold desc="加密,生成加密字符串">
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, "",
                HandlerConstants.BACK_RETURN_URL_PAY, "", "订单", "机票订单", "", "Web");
        String postUrl = HandlerConstants.URL_PAY;
        //暂时处理   处理请求参数中的.00
        if (req.getAmount().split("\\.").length > 2) {//异常金额
            String amount = req.getAmount();
            amount = amount.substring(0, amount.lastIndexOf('.'));
            String s = String.valueOf(amount.substring(amount.indexOf('.')).length());
            logger.info(s);
            if (amount.substring(amount.indexOf('.')).length() < 3) {
                amount = amount + "0";
            }
            req.setAmount(amount);
        }
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());

        //微信支付网关暂时就一个，固定
        //根据渠道判断
        String billCreateIP = this.getClientIP(request);
        String dynamicParameters = "";
        String gatewayNo = "42600";
        paymentReq.setGatewayNo(gatewayNo);
        dynamicParameters = StringUtils.isBlank(req.getPromoParam())? VALUE_THREE + billCreateIP + "'}":
                VALUE_THREE + billCreateIP + "'," + "'promo_params_str':'"+req.getPromoParam()+"'}";


        paymentReq.setGatewayType("6");
        paymentReq.setDynamicParameters(dynamicParameters);

        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();

        //19支付平台渠道用户号
        String payChannel = "";
        if (HandlerConstants.M_CHANNEL_CODE.equals(req.getChannelCode())) {//手机
            payChannel = HandlerConstants.M_CHANNEL_CODE;
        } else if (HandlerConstants.W_CHANNEL_CODE.equals(req.getChannelCode())) {
            payChannel = HandlerConstants.W_CHANNEL_CODE;
        }
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = getChannelInfo(paymentReq.getChannelNo(), "20");
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        //</editor-fold>
        HttpResult result = doPayPost(postUrl, parametersMap);
        if (result.isResult()) {
            logger.info("支付返回结果:{}", result.getResponse());
            String paymentInfo = result.getResponse().trim();
            if ((!StringUtil.isNullOrEmpty(paymentInfo)) && paymentInfo.indexOf("RespCode") > -1) {
                resp = (PaymentAlipayResp) JsonUtil.jsonToBean(paymentInfo, PaymentAlipayResp.class);
            }
            if (StringUtil.isNullOrEmpty(resp.getRespCode())) {
                resp.setPaymentInfo(paymentInfo);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_THREE + resp.getErrorMsg());
            }
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
    }


    /**
     * 银联卡网关集合
     *
     * @param
     * @return
     */
    public static Map<String, PayGatewayPageUrl> toPayGatewayPageMap(String themeStr) {
        try {
            Type type = new TypeToken<Map<String, PayGatewayPageUrl>>() {
            }.getType();
            return (Map<String, PayGatewayPageUrl>) JsonUtil.jsonToMap(themeStr, type);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    private static String sha1Encode(String str) {
        String chkValue = null;
        chkValue = EncoderHandler.encodeBySHA1(str);
        return chkValue;
    }
    @Autowired
    private HandConfig handConfig;
}
