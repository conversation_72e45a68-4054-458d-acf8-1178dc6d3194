package com.juneyaoair.mobile.handler.controller;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.common.DocTypeEnum;
import com.juneyaoair.appenum.member.*;
import com.juneyaoair.baseclass.common.base.DocContent;
import com.juneyaoair.baseclass.common.base.UserInfoNoMust;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.request.crm.*;
import com.juneyaoair.baseclass.request.myScore.MyScoreReq;
import com.juneyaoair.baseclass.request.xinyongfei.CreditLimitReq;
import com.juneyaoair.baseclass.response.crm.*;
import com.juneyaoair.baseclass.response.score.ScoreUseRuleResp;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.device.AppCustomer;
import com.juneyaoair.mobile.core.bean.device.DeviceInfo;
import com.juneyaoair.mobile.core.service.device.mongo.IAppCustomerServiceMongo;
import com.juneyaoair.mobile.core.service.device.mongo.IDeviceServiceMongo;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.SourceType;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.service.IFileService;
import com.juneyaoair.mobile.handler.service.IOrderService;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.*;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtCrmMileageRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtCrmMileageResponse;
import com.juneyaoair.thirdentity.member.request.MileageAccountQueryRequest;
import com.juneyaoair.thirdentity.member.request.PtCertificateRealNameRequest;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.PtModifyCustomerInfoRequest;
import com.juneyaoair.thirdentity.member.response.MemberCertificateSoaModelV2;
import com.juneyaoair.thirdentity.member.response.MemberContactSoaModel;
import com.juneyaoair.thirdentity.member.response.MileageAccountQueryResponse;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.xinyongfei.PtCreditLimitReq;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.http.HttpsUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.CertUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by qinxiaoming on 2016-4-26.
 */

@RequestMapping("/crmService")
@RestController
@Api(value = "会员相关操作1.0", tags = "会员相关操作1.0")
public class CrmController extends BassController {
    @Autowired
    private IMemberService memberService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IOrderService orderService;
    @Autowired
    @Qualifier("minioFileServiceImpl")
    private IFileService fileService;
    private static final String LOGIN_ERR = "loginErr";
    private static final String LOGIN = "LOGIN";
    private static final String OPERATION_TOO_MUCH = "操作过于频繁，请联系服务人员";
    private static final String OPERATION_FAILED = "操作失败！";
    private static final String QUERY_USER_INFO_FAILED = "查询用户信息出错！";
    private static final String EMPTY_USER_INFO = "用户信息为空！";
    private static final String VALID_CODE_EXPIRED = "验证码错误或已经失效";
    private static final String BIRTHDAY = "birthDate";
    private static final String C_FIRST_NAME = "cFirstName";
    private static final String C_LAST_NAME = "cLastName";
    private static final String E_FIRST_NAME = "eFirstName";
    private static final String E_LAST_NAME = "eLastName";
    private static final String SERVICE_NAME = "会员服务1.0";

    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    private boolean filterTime() {
        Date newDate = new Date();
        long nowDate = newDate.getTime();
        Date depDate = DateUtils.toDate("2015-10-09");
        long endDate = depDate.getTime();
        return nowDate <= endDate;
    }

    /**
     * 旅客登录
     *
     * @param loginReq
     * @param request
     * @return
     */
    @RequestMapping(value = "/crmLogin", method = RequestMethod.POST)
    public MemberLoginResponse memberLogin(@RequestBody MemberLoginRequest loginReq, HttpServletRequest request) {
        MemberLoginResponse resp = new MemberLoginResponse();
        String reqId = StringUtil.newGUID() + "_crmLogin";
        String ip = StringUtil.isNullOrEmpty(loginReq.getIp()) ? this.getClientIP(request) : loginReq.getIp();
        String logStr = JsonUtil.objectToJson(loginReq);
        log.info("{}会员登录请求IP:{},内容{}", reqId, ip, logStr);
        if (StringUtil.isNullOrEmpty(loginReq.getLoginCode())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("请求参数不正确！");
            return resp;
        }
        if (StringUtil.isNullOrEmpty(loginReq.getPwd())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("请求参数不正确！");
            return resp;
        }
        if (!this.chkDayOptErr(ip, LOGIN_ERR, "")) {//IP控制
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(OPERATION_TOO_MUCH);
            return resp;
        }
        if (!this.chkDayOptErr(loginReq.getLoginCode(), LOGIN_ERR, "")) {//账户控制
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(OPERATION_TOO_MUCH);
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberLoginRequest>> violations = validator.validate(loginReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //密码处理方式 方便单点登录返回统一的结果
        String method = "";
        if (!StringUtil.isNullOrEmpty(loginReq.getLoginMethod()) && "MWEB".equals(loginReq.getLoginMethod())) {//不限制访问频率
            //验证下双方约定的访问token
            if (EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(loginReq.getLoginMethod()) + "juneyaoair").equals(loginReq.getLoginMethodToken())) {
                method = "SMS";
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("非法的访问请求");
                return resp;
            }
        } else {
            if (!this.chkDayVisit(ip, LOGIN, "")) {//每日访问控制
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_TOO_MUCH);
                return resp;
            }
        }
        String channelCode = loginReq.getChannelCode();
        //无版本信息默认是老的接口访问方式
        boolean isCheckCode;
        if ("MWEB".equals(loginReq.getVersionNo()) && "MWEB_JUNEYAOAIR".equals(loginReq.getVcode())) {
            isCheckCode = false;
        } else {
            isCheckCode = true;
        }
        if (isCheckCode) {
            String sRandom = "";
            String vcode = loginReq.getVcode();
            if (HandlerConstants.M_CHANNEL_CODE.equals(channelCode) || HandlerConstants.W_CHANNEL_CODE.equals(channelCode)) {
                sRandom = apiRedisService.getData(loginReq.getChannelId());
                log.info("{} 登录 验证码:传入{},缓存中为{}", channelCode, vcode, sRandom);
                if (StringUtil.isNullOrEmpty(vcode) && filterTime()) {//放开15天的更新时间 不做验证
                } else {
                    if (StringUtil.isNullOrEmpty(sRandom) || StringUtil.isNullOrEmpty(vcode) || (!(vcode.toUpperCase()).equals(sRandom))) {

                        resp.setResultCode(WSEnum.ERROR_LONGIN_CHK_ERROR.getResultCode());
                        resp.setErrorInfo("登录验证码错误!");
                        return resp;
                    }
                }
                if (!StringUtil.isNullOrEmpty(sRandom)) {
                    apiRedisService.removeData(loginReq.getChannelId());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR_LONGIN_CHK_ERROR.getResultCode());
                resp.setErrorInfo("登录验证码错误2!");
                return resp;
            }
        } else {
            log.info("当前访问接口的版本{}", loginReq.getVersionNo());
        }
        MemberLoginResponseForClient loginClient = crmClient.login(loginReq.getLoginCode(), loginReq.getPwd(),
                loginReq.getLoginCodeType(), ip, channelCode, getClientPwd(channelCode), method);

        String infoIdKey = "";
        if (loginClient.getMessageHeader().getErrorCode().equals("S000")) {
            String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
            infoIdKey = EncoderHandler.encode("MD5", String.valueOf(loginClient.getMemberInfo().getID()) + key).toUpperCase();
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setLoginKeyInfo(infoIdKey);
            BeanUtils.copyProperties(loginClient.getMemberInfo(), resp);
            resp.setName((StringUtil.isNullOrEmpty(resp.getName()) || "/".equals(resp.getName())) ? "" : resp.getName());
            resp.setId(loginClient.getMemberInfo().getID());//ffp_id
            resp.setGuid(loginClient.getMemberInfo().getGUID());
            //取人员证件信息
            getMemberInfo(resp, loginClient.getMemberInfo().getID(), channelCode);

            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(loginClient.getMemberInfo().getID(), channelCode);
            if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!memberInfo.getMessageHeader().getErrorCode().endsWith("S000")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                return resp;
            }
            // 客户信息返回
            CustomerInfoType infoType = memberInfo.getMemberQueryInfo().getCustomerInfo();
            if (null != infoType) {
                // 中文姓
                resp.setcLastName(infoType.getCLastName());
                // 中文名
                resp.setcFirstName(infoType.getCFirstName());
                // 英文姓
                resp.seteFirstName(infoType.getEFirstName());
                // 英文名
                resp.seteLastName(infoType.getELastName());
                // 出生日期
                resp.setBirthday(infoType.getBirthDate().toString());
            }
            // 地址信息返回·
            CustomerAddressInfoType addInfo = memberInfo.getMemberQueryInfo().getCustomerAddressInfo();
            if (null != addInfo) {
                // 国家
                resp.setCountryCode(StringUtil.isNullOrEmpty(addInfo.getCountryCode()) ? "中国" : addInfo.getCountryCode());
                // 省份
                resp.setProvinceCode(addInfo.getProvinceCode());
                // 城市
                resp.setAddressCityCode(addInfo.getAddressCityCode());
                // 地址
                resp.setAddressContent(addInfo.getAddressContent());
                // 邮编
                resp.setPostCode(addInfo.getPostCode());
            }

            // 实名认证状态
            resp.setRealVerifyStatus(memberInfo.getMemberQueryInfo().getRealVerifyStatus());
            // 实名认证方式
            resp.setRealVerifyChannel(memberInfo.getMemberQueryInfo().getRealVerifyChannel());

            //保存登陆人员设备信息 异步调用
            if (loginClient.getMemberInfo() != null && !StringUtil.isNullOrEmpty(loginReq.getDeviceId())) {
                try {
                    AppCustomer appCustomer = createAppCustomer(loginClient.getMemberInfo(), loginReq.getDeviceId());
                    appCustomer.setClientVersion(loginReq.getClientVersion());
                    DeviceInfo device = new DeviceInfo(loginReq.getDeviceId(), loginReq.getSystemInfo(), loginReq
                            .getPushNum(), LOGIN, ip, appCustomer.getFfp_id() + "", appCustomer.getFfp_card_no());
                    device.setClientVersion(loginReq.getClientVersion());
                    taskExecutor.execute(new DeviceThread(reqId, appCustomer, device));
                } catch (Exception e) {
                    log.error("设备信息保存异常!", e);
                }
            }
        } else if ("E312".equals(loginClient.getMessageHeader().getErrorCode())) {//用户密码不匹配错误
            this.chkDayVisit(ip, LOGIN_ERR, "");//IP限制
            this.chkDayVisit(loginReq.getLoginCode(), LOGIN_ERR, "");//账号登录限制
            resp.setResultCode(WSEnum.ERROR_LONGIN_ERROR.getResultCode());
            resp.setErrorInfo(loginClient.getMessageHeader().getDescription());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR_LONGIN_ERROR.getResultCode());
            resp.setErrorInfo(loginClient.getMessageHeader().getDescription());
            return resp;
        }
        String logStr2 = JsonUtil.objectToJson(resp);
        log.info("{} 会员登录结果:{}", reqId, logStr2);
        return resp;
    }

    //构建人员设备信息
    private AppCustomer createAppCustomer(MemberLoginInfoType memberInfo, String deviceId) {
        AppCustomer appCustomer = new AppCustomer(memberInfo.getMemberID(), memberInfo.getID(), memberInfo.getName());
        appCustomer.setLast_deviceId(deviceId);
        return appCustomer;
    }

    /**
     * 根据会员编号获取会员信息2
     *
     * @param memberInfoRequest
     * @param request
     * @return
     */
    @RequestMapping(value = "/memberQueryByID2.html", method = RequestMethod.POST)
    @ResponseBody
    public MemberLoginResponse memberQueryByID2(@RequestBody MemberInfoRequest memberInfoRequest,
                                                HttpServletRequest request) {
        MemberLoginResponse response = new MemberLoginResponse();
        MemberInfoQueryResponseForClient resp = new MemberInfoQueryResponseForClient();
        try {
            if (HandlerConstants.MWEB_CHANNEL_CODE.equals(memberInfoRequest.getChannelCode())) {//渠道为MWEB的需要更新渠道
                memberInfoRequest.setChannelCode(HandlerConstants.M_CHANNEL_CODE);
            }
            resp = memberQueryByID(memberInfoRequest, request);

            try {
                //重新组装返回参数
                MemberQueryInfoType memberQueryInfo = resp.getMemberQueryInfo();
                response.setId(memberQueryInfo.getID());
                response.setMemberID(memberQueryInfo.getMemberID());
                response.setMemberLevelCode(memberQueryInfo.getMemberLevelCode());

                String key = HandlerConstants.W_CHANNEL_CODE.equals(memberInfoRequest.getChannelCode()) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                String infoIdKey = EncoderHandler.encode("MD5", String.valueOf(memberQueryInfo.getID()) + key).toUpperCase();
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setLoginKeyInfo(infoIdKey);
                // 客户信息返回
                CustomerInfoType infoType = memberQueryInfo.getCustomerInfo();
                if (null != infoType) {
                    // 中文姓
                    response.setcLastName(infoType.getCLastName());
                    // 中文名
                    response.setcFirstName(infoType.getCFirstName());
                    // 英文姓
                    response.seteFirstName(infoType.getEFirstName());
                    // 英文名
                    response.seteLastName(infoType.getELastName());
                    // 出生日期
                    response.setBirthday(infoType.getBirthDate().toString());
                    response.setSex(infoType.getSex().toString());
                    response.setName(infoType.getCLastName() + infoType.getCFirstName());
                    if (infoType.getSex().equals(SexType.MALE)) {
                        response.setTitle("先生");
                    } else if (infoType.getSex().equals(SexType.FEMALE)) {
                        response.setTitle("女士");
                    }
                }
                // 地址信息返回·
                CustomerAddressInfoType addInfo = memberQueryInfo.getCustomerAddressInfo();
                if (null != addInfo) {
                    // 国家
                    response.setCountryCode(StringUtil.isNullOrEmpty(addInfo.getCountryCode()) ? "中国" : addInfo.getCountryCode());
                    // 省份
                    response.setProvinceCode(addInfo.getProvinceCode());
                    // 城市
                    response.setAddressCityCode(addInfo.getAddressCityCode());
                    // 地址
                    response.setAddressContent(addInfo.getAddressContent());
                    // 邮编
                    response.setPostCode(addInfo.getPostCode());
                }

                List<CustomerCertificateInfoType> certificateInfo = memberQueryInfo.getCustomerCertificateInfo();
                if (certificateInfo != null && certificateInfo.size() > 0) {
                    for (int i = 0; i < certificateInfo.size(); i++) {
                        if (certificateInfo.get(i).getCertType().equals(CertificateType.IDCard)) {
                            response.setCertType(CertificateType.IDCard.value());
                            response.setCertNumber(certificateInfo.get(i).getCertNumber());
                            break;
                        } else {
                            response.setCertType(certificateInfo.get(i).getCertType().value());
                            response.setCertNumber(certificateInfo.get(i).getCertNumber());
                        }
                    }
                }

                List<CustomerContactInfoType> contactInfoTypeList = memberQueryInfo.getCustomerContactInfo();
                if (contactInfoTypeList != null && contactInfoTypeList.size() > 0) {
                    for (int i = 0; i < contactInfoTypeList.size(); i++) {
                        if (contactInfoTypeList.get(i).getContactType().equals(ContactType.MOBILE)) {
                            response.setMemberTel(contactInfoTypeList.get(i).getContactValue());
                        } else if (contactInfoTypeList.get(i).getContactType().equals(ContactType.EMAIL)) {
                            response.setMemberEmail(contactInfoTypeList.get(i).getContactValue());
                        }
                    }
                }
                List<CustomerCertificateInfoType> customerCertificateInfoTypeList = memberQueryInfo.getCustomerCertificateInfo();
                if (null != customerCertificateInfoTypeList && customerCertificateInfoTypeList.size() > 0) {
                    CustomerCertificateInfo certificate = null;
                    List<CustomerCertificateInfo> infoList = new ArrayList<>();
                    for (int j = 0; j < customerCertificateInfoTypeList.size(); j++) {
                        certificate = new CustomerCertificateInfo();
                        certificate.setCertType(customerCertificateInfoTypeList.get(j).getCertType().value());
                        certificate.setCertNumber(customerCertificateInfoTypeList.get(j).getCertNumber());
                        infoList.add(certificate);
                    }
                    response.setCustomerCertificateInfos(infoList);
                }
                // 实名认证状态
                response.setRealVerifyStatus(memberQueryInfo.getRealVerifyStatus());
                // 实名认证方式
                response.setRealVerifyChannel(memberQueryInfo.getRealVerifyChannel());

            } catch (Exception e) {
                log.error("rebuildB2CPasswordSms数据转换异常:" + e.getMessage());
                ResponseMessageHeaderType headerType = new ResponseMessageHeaderType();
                headerType.setErrorCode(WSEnum.ERROR.getResultCode());
                headerType.setDescription("数据转换异常");
                resp.setMessageHeader(headerType);
            }
        } catch (Exception e) {
            log.error("调用数据接口异常：" + e.getMessage());
            ResponseMessageHeaderType headerType = new ResponseMessageHeaderType();
            headerType.setErrorCode(WSEnum.ERROR.getResultCode());
            headerType.setDescription("接口异常");
            resp.setMessageHeader(headerType);
        }
        return response;

    }

    /**
     * 验证短信验证码快捷登录
     *
     * @return
     */
    @ApiOperation(value = "msgLogin", notes = "短信快捷登录")
    @RequestMapping(value = "msgLogin", method = RequestMethod.POST)
    public Object msgLogin(@RequestBody MemberMsgLoginRequest req, HttpServletRequest request) {
        MemberLoginResponse resp = new MemberLoginResponse();
        String reqId = StringUtil.newGUID() + "_msgLogin";
        String channelCode = req.getChannelCode();
        String ip = StringUtil.isNullOrEmpty(req.getIp()) ? this.getClientIP(request) : req.getIp();
        ChannelCodeEnum channelCodeEnum = ChannelCodeEnum.checkEnum(req.getChannelCode());
        if (channelCodeEnum == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("您的请求抛锚了！");
            return resp;
        } else {
            if (channelCodeEnum.getChannelCode().equals("TAOLX")) {//更换为手机渠道
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberMsgLoginRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        if (!this.chkDayVisit(ip, LOGIN, "")) {//每日访问控制
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(OPERATION_TOO_MUCH);
            return resp;
        }
        if (!this.chkDayOptErr(ip, LOGIN_ERR, "")) {//IP出错控制
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(OPERATION_TOO_MUCH);
            return resp;
        }
        if (!this.chkDayOptErr(req.getLoginCode(), LOGIN_ERR, "")) {//账户出错控制
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(OPERATION_TOO_MUCH);
            return resp;
        }
        /**
         * 验证手机验证码是否正确
         */
        String regCacheKey = req.getLoginCode() + HandlerConstants.LOGINCODE_MOBILE_SOURCE;
        String veryCode = req.getVeriCode();
        String veryCodeCacheStr = apiRedisService.getData(regCacheKey);
        if (StringUtils.isBlank(veryCode) || StringUtils.isBlank(veryCodeCacheStr) || !veryCode.equals(veryCodeCacheStr)) {
            this.chkDayVisit(ip, LOGIN_ERR, "");//IP限制
            this.chkDayVisit(req.getLoginCode(), LOGIN_ERR, "");//账号登录限制
            String errorInfo = VALID_CODE_EXPIRED;
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(errorInfo);
            return resp;
        }
        this.clearDayVisit("", req.getLoginCode(), HandlerConstants.LOGINCODE_MOBILE_SOURCE, "");//验证成功，清除账号限制
        GetMemberIdByPhoneForClient getMemberIdByPhoneForClient = crmClient.getMemberIdByPhone(req.getLoginCode(), channelCode, getClientPwd(channelCode));
        String logStr = JsonUtil.objectToJson(getMemberIdByPhoneForClient);
        log.info("{} 查询会员卡号：{}", reqId, logStr);
        if (!"S000".equals(getMemberIdByPhoneForClient.getMessageHeader().getErrorCode())) {
            if ("E4902".equals(getMemberIdByPhoneForClient.getMessageHeader().getErrorCode())) {//未注册用户
                log.info("{} 未注册会员：{}", reqId, req.getLoginCode());
                MemberB2CRegistResponseForClient memberB2CRegistResponseForClient = crmClient.fastRegist(req.getLoginCode(), req.getLoginCode(), channelCode, getChannelInfo(channelCode, "40"));
                String logStr2 = JsonUtil.objectToJson(memberB2CRegistResponseForClient);
                log.info("{} 快速会员注册response：{}", reqId, logStr2);
                if (!"S000".equals(memberB2CRegistResponseForClient.getMessageHeader().getErrorCode())) {//手机快速注册成功
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("验证码登录失败！");
                    return resp;
                }
            } else {//其他未知错误
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(getMemberIdByPhoneForClient.getMessageHeader().getDescription());
                return resp;
            }
        }
        GetMemberInfoResponseForClient getMemberInfoResponse = crmClient.getMemberInfo(req.getLoginCode(), QueryMemberDataType.MOBILE.value(),
                channelCode, getClientPwd(channelCode));
        if ("S000".equals(getMemberInfoResponse.getMessageHeader().getErrorCode())) {
            long id = getMemberInfoResponse.getMemberQueryInfo().getID();
            //获取密码
            String b2cPassword = getMemberInfoResponse.getMemberQueryInfo().getMemberPasswordInfo().getB2CPassword();
            MemberLoginResponseForClient loginClient = crmClient.login(req.getLoginCode(), b2cPassword,
                    "", ip, channelCode, getClientPwd(channelCode), "SMS");
            if (loginClient.getMessageHeader().getErrorCode().equals("S000")) {
                this.clearDayVisit("", req.getLoginCode(), HandlerConstants.LOGINCODE_MOBILE_SOURCE, "");//清除账号的限制
                this.clearDayVisit("", ip, HandlerConstants.LOGINCODE_IP_SOURCE, "");
                String infoIdKey = "";
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                infoIdKey = EncoderHandler.encode("MD5", String.valueOf(id) + key).toUpperCase();
                resp.setLoginKeyInfo(infoIdKey);
                BeanUtils.copyProperties(loginClient.getMemberInfo(), resp);
                resp.setName((StringUtil.isNullOrEmpty(resp.getName()) || "/".equals(resp.getName())) ? "" : resp.getName());
                resp.setId(loginClient.getMemberInfo().getID());//ffp_id
                resp.setGuid(loginClient.getMemberInfo().getGUID());
                //取人员证件信息
                getMemberInfo(resp, loginClient.getMemberInfo().getID(), channelCode);

                // 根据id获取用户信息
                MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(loginClient.getMemberInfo().getID(), channelCode);
                if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(OPERATION_FAILED);
                    return resp;
                } else if (!memberInfo.getMessageHeader().getErrorCode().endsWith("S000")) {
                    // 认证失败返回
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                    return resp;
                }
                // 客户信息返回
                CustomerInfoType infoType = memberInfo.getMemberQueryInfo().getCustomerInfo();
                if (null != infoType) {
                    // 中文姓
                    resp.setcLastName(infoType.getCLastName());
                    // 中文名
                    resp.setcFirstName(infoType.getCFirstName());
                    // 英文姓
                    resp.seteFirstName(infoType.getEFirstName());
                    // 英文名
                    resp.seteLastName(infoType.getELastName());
                    // 出生日期
                    resp.setBirthday(infoType.getBirthDate().toString());
                }
                // 地址信息返回
                CustomerAddressInfoType addInfo = memberInfo.getMemberQueryInfo().getCustomerAddressInfo();
                if (null != addInfo) {
                    // 国家
                    resp.setCountryCode(StringUtil.isNullOrEmpty(addInfo.getCountryCode()) ? "中国" : addInfo.getCountryCode());
                    // 省份
                    resp.setProvinceCode(addInfo.getProvinceCode());
                    // 城市
                    resp.setAddressCityCode(addInfo.getAddressCityCode());
                    // 地址
                    resp.setAddressContent(addInfo.getAddressContent());
                    // 邮编
                    resp.setPostCode(addInfo.getPostCode());
                }
                // 实名认证状态
                resp.setRealVerifyStatus(memberInfo.getMemberQueryInfo().getRealVerifyStatus());
                // 实名认证方式
                resp.setRealVerifyChannel(memberInfo.getMemberQueryInfo().getRealVerifyChannel());
                //保存登陆人员设备信息
                if (loginClient.getMemberInfo() != null && !StringUtil.isNullOrEmpty(req.getDeviceId())) {
                    try {
                        AppCustomer appCustomer = createAppCustomer(loginClient.getMemberInfo(), req.getDeviceId());
                        appCustomer.setClientVersion(req.getClientVersion());
                        DeviceInfo device = new DeviceInfo(req.getDeviceId(), req.getSystemInfo(), req.getPushNum(),
                                "MSG_LOGIN", ip, appCustomer.getFfp_id() + "", appCustomer.getFfp_card_no());
                        device.setClientVersion(req.getClientVersion());
                        //  2017/12/22利用线程池
                        taskExecutor.execute(new DeviceThread(reqId, appCustomer, device));
                    } catch (Exception e) {
                        log.error("设备信息保存异常!", e);
                    }
                }
                apiRedisService.removeData(regCacheKey);//清除验证码
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(getMemberInfoResponse.getMessageHeader().getDescription());
        }
        String logStr2 = JsonUtil.objectToJson(resp);
        log.info("{} 会员登录结果:{}", reqId, logStr2);
        if ("TAOLX".equals(channelCodeEnum.getChannelCode())) {//封装给外部系统的请求返回
            TaolxMemberResponse taolxResp = new TaolxMemberResponse();
            BeanUtils.copyProperties(resp, taolxResp);
            return taolxResp;
        }
        return resp;
    }

    /**
     * 联合快速注册
     *
     * @param murit
     * @param request
     * @return
     */
    @RequestMapping(value = "/unionFastRegist", method = RequestMethod.POST)
    public MemberUnitRegistResponse unionFastRegist(@RequestBody MemberUnitRegistInfoTypeRequest murit, HttpServletRequest request) {
        MemberUnitRegistResponse resp = new MemberUnitRegistResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberUnitRegistInfoTypeRequest>> violations = validator.validate(murit);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        CertificateType certificateType = null;
        String cNo = murit.getCertificateTypeNo();
        String ip = this.getClientIP(request);
        String logStr = JsonUtil.objectToJson(murit);
        log.info("会员联合快速注册请求IP:{},内容{}", ip, logStr);
        if (!this.chkDayVisit(ip, REG_SOURCE, "")) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
        if (cNo != null) {
            if (cNo.equals("1")) {
                certificateType = CertificateType.IDCard;
            } else if (cNo.equals("2")) {
                certificateType = CertificateType.Passport;
            } else if (cNo.equals("3")) {
                certificateType = CertificateType.Other;
            }
        }
        String channelCode = murit.getChannelCode();
        MemberUnitRegistResponseForClient clientResponse = crmClient.unionFastRegist(murit.getLastName(), murit.getFirstName(), certificateType, murit.getCertificateNo(),
                murit.getEmail(), murit.getMobile(), murit.getOtherContact(), murit.getPassword(), channelCode, getClientPwd(channelCode));

        if (clientResponse != null && clientResponse.getMessageHeader().getErrorCode().equals("S000")) {
            resp.setId(clientResponse.getID());
            resp.setMemberID(clientResponse.getMemberID());
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    /**
     * 会员注册
     *
     * @param mrit
     * @param request
     * @return
     */
    @RequestMapping(value = "/memberRegist", method = RequestMethod.POST)
    public MemberRegistResp memberRegist(@RequestBody MemberRegistInfoTypeReq mrit, HttpServletRequest request) {
        MemberRegistResp resp = new MemberRegistResp();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberRegistInfoTypeReq>> violations = validator.validate(mrit);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        CertificateType certificateType = null;
        String cNo = mrit.getCertificateTypeNo();
        String ip = StringUtil.isNullOrEmpty(mrit.getIp()) ? this.getClientIP(request) : mrit.getIp();
        /**
         * 验证是否可以注册
         */
        if (!this.chkDayVisit(ip, REG_SOURCE, "")) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }

        /**
         * 验证手机验证码是否正确
         */
        String regCacheKey = mrit.getMobile() + VERYCODE_SOURCE;
        String veryCode = mrit.getVeryCode();
        String veryCodeCacheStr = apiRedisService.getData(regCacheKey);
        if (StringUtils.isBlank(veryCode) || StringUtils.isBlank(veryCodeCacheStr) || !veryCode.equals(veryCodeCacheStr)) {
            String errorInfo = VALID_CODE_EXPIRED;
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(errorInfo);
            return resp;
        }

        log.info("注册memberRegist================{}", ip);
        if (cNo != null) {
            if (cNo.equals("1")) {
                certificateType = CertificateType.IDCard;
            } else if (cNo.equals("2")) {
                certificateType = CertificateType.Passport;
            } else if (cNo.equals("3")) {
                certificateType = CertificateType.Other;
            }
        }

        String channelCode = mrit.getChannelCode();
        MemberRegistResponseForClient clientResponse = crmClient.memberRegist(mrit.getLastName(), mrit.getFirstName(), certificateType, mrit.getCertificateNo(),
                mrit.getEmail(), mrit.getMobile(), mrit.getBirthday(), mrit.getPassword(), channelCode, getClientPwd(channelCode), ip);
        String infoIdKey = "";
        log.info("返回的数据结果:MessageID={},ErrorCode={},Description={}",
                clientResponse.getMessageHeader().getMessageID(), clientResponse.getMessageHeader().getErrorCode(),
                clientResponse.getMessageHeader().getDescription());
        if (clientResponse.getMessageHeader().getErrorCode().equals("S000")) {
            log.info("注册信息成功：ip={},mobile={},veryCode={}", ip, mrit.getMobile(), mrit.getVeryCode());
            String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
            infoIdKey = EncoderHandler.encode("MD5", clientResponse.getID() + key).toUpperCase();
            resp.setId(clientResponse.getID());
            resp.setMemberID(clientResponse.getMemberID());
            resp.setLoginKeyInfo(infoIdKey);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            //注册成功清除短信验证码缓存
            if (apiRedisService.getData(regCacheKey) != null) {
                apiRedisService.removeData(regCacheKey);
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("注册失败：" + clientResponse.getMessageHeader().getDescription());
            log.info("注册信息失败：ip={},mobile={},veryCode={}", ip, mrit.getMobile(), mrit.getVeryCode());
        }
        return resp;
    }

    /**
     * 根据会员编号获取会员信息
     *
     * @param memberInfoRequest
     * @param request
     * @return
     */
    @RequestMapping(value = "/memberQueryByID", method = RequestMethod.POST)
    public MemberInfoQueryResponseForClient memberQueryByID(@RequestBody MemberInfoRequest memberInfoRequest, HttpServletRequest request) {
        MemberInfoQueryResponseForClient resp = new MemberInfoQueryResponseForClient();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberInfoRequest>> violations = validator.validate(memberInfoRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            ResponseMessageHeaderType headerType = new ResponseMessageHeaderType();
            headerType.setErrorCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            headerType.setDescription(violations.iterator().next().getMessage());
            resp.setMessageHeader(headerType);
            return resp;
        }

        String ip = this.getClientIP(request);
        log.info("获取旅客信息memberQueryByID================{}", ip);
        String channelCode = memberInfoRequest.getChannelCode();
        boolean flag = checkKeyInfo(String.valueOf(memberInfoRequest.getId()), memberInfoRequest.getLoginKeyInfo(), channelCode);
        if (flag) {
            resp = crmClient.memberInfoquery(memberInfoRequest.getId(), channelCode, getClientPwd(channelCode));
        } else {
            ResponseMessageHeaderType headerType = new ResponseMessageHeaderType();
            headerType.setErrorCode("S400");
            headerType.setDescription("用户认证失败");
            resp.setMessageHeader(headerType);
        }
        return resp;
    }

    /**
     * 修改登录密码
     *
     * @param memberChangeB2CPasswordReq
     * @param request
     * @return
     */
    @RequestMapping(value = "/changeB2CPwd", method = RequestMethod.POST)
    public MemberChangeB2CPasswordResp changeB2CPwd(@RequestBody MemberChangeB2CPasswordReq memberChangeB2CPasswordReq, HttpServletRequest request) {
        MemberChangeB2CPasswordResp resp = new MemberChangeB2CPasswordResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberChangeB2CPasswordReq>> violations = validator.validate(memberChangeB2CPasswordReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        log.info("修改B2C密码================{}", ip);
        String channelCode = memberChangeB2CPasswordReq.getChannelCode();
        boolean flag = checkKeyInfo(String.valueOf(memberChangeB2CPasswordReq.getID()), memberChangeB2CPasswordReq.getLoginKeyInfo(), channelCode);
        if (flag) {
            MemberChangeB2CPasswordResponseForClient clientResponse = crmClient.ChangeB2CPwd(memberChangeB2CPasswordReq.getID(), memberChangeB2CPasswordReq.getOldB2CPassword(), memberChangeB2CPasswordReq.getNewB2CPassword(), channelCode, getClientPwd(channelCode));
            if (clientResponse.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResponse.getMessageHeader().getDescription());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
        }
        return resp;
    }

    /**
     * 获取积分
     *
     * @param memberAccmRequest
     * @param request
     * @return
     */
    @RequestMapping(value = "/memberAccmQuery", method = RequestMethod.POST)
    public MemberUnionQueryResponseForClient memberAccmQuery(@RequestBody MemberAccmRequest memberAccmRequest, HttpServletRequest request) {
        MemberUnionQueryResponseForClient resp = new MemberUnionQueryResponseForClient();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberAccmRequest>> violations = validator.validate(memberAccmRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            ResponseMessageHeaderType headerType = new ResponseMessageHeaderType();
            headerType.setErrorCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            headerType.setDescription(violations.iterator().next().getMessage());
            resp.setMessageHeader(headerType);
            return resp;
        }
        String ip = StringUtil.isNullOrEmpty(memberAccmRequest.getIp()) ? this.getClientIP(request) : memberAccmRequest.getIp();
        log.info("获取积分memberAccm================{}", ip);
        String channelCode = memberAccmRequest.getChannelCode();
        boolean flag = checkKeyInfo(memberAccmRequest.getId(), memberAccmRequest.getLoginKeyInfo(), channelCode);
        if (flag) {
            resp = crmClient.queryMemAccm(memberAccmRequest.getId(), memberAccmRequest.getFrDate(), memberAccmRequest.getEndDate(), channelCode, getClientPwd(channelCode));
            //处理微信登录账号信息不存在
            if (resp != null && resp.getMessageHeader() != null && "E1002".equals(resp.getMessageHeader().getErrorCode())) {
                ResponseMessageHeaderType headerType = new ResponseMessageHeaderType();
                headerType.setErrorCode("S000");
                headerType.setDescription("账号信息不存在");
                resp.setMessageHeader(headerType);
            }
        } else {
            ResponseMessageHeaderType headerType = new ResponseMessageHeaderType();
            headerType.setErrorCode("S400");
            headerType.setDescription("用户认证失败");
            resp.setMessageHeader(headerType);
        }
        return resp;
    }

    /**
     * 积分补登
     *
     * @param mileageRetroInfoTypeReq
     * @param request
     * @return
     * @deprecated 新接口 {@link NewCrmController#memberPointPatchApply(com.juneyaoair.baseclass.common.request.BaseReq, javax.servlet.http.HttpServletRequest)}
     */
    @ApiOperation(value = "积分补登", notes = "此接口废弃使用")
    @RequestMapping(value = "/memberPointPatchApply", method = RequestMethod.POST)
    @Deprecated
    public MileageRetroResp memberPointPatchApply(@RequestBody MileageRetroInfoTypeReq mileageRetroInfoTypeReq, HttpServletRequest request) {
        MileageRetroResp resp = new MileageRetroResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MileageRetroInfoTypeReq>> violations = validator.validate(mileageRetroInfoTypeReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        log.info("积分补登memberPointPatchApply================{}", ip);
        String channelCode = mileageRetroInfoTypeReq.getChannelCode();
        boolean flag = checkKeyInfo(String.valueOf(mileageRetroInfoTypeReq.getId()), mileageRetroInfoTypeReq.getLoginKeyInfo(), channelCode);
        if (flag) {
            MileageRetroResponseForClient clientResponse = crmClient.memberPointPatchApply(mileageRetroInfoTypeReq.getMemberID(), mileageRetroInfoTypeReq.getId(), mileageRetroInfoTypeReq.getFlightDate(), mileageRetroInfoTypeReq.getAirlineCode(),
                    mileageRetroInfoTypeReq.getPassangerName(), mileageRetroInfoTypeReq.getFlightNum(), mileageRetroInfoTypeReq.getOriginationCode(), mileageRetroInfoTypeReq.getDestinationCode(), mileageRetroInfoTypeReq.getSeatNo(), channelCode, getClientPwd(channelCode));
            if (clientResponse.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResponse.getMessageHeader().getDescription());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
        }
        return resp;
    }

    /**
     * 会员账户查询
     *
     * @param memberAccountQueryRequest
     * @param request
     * @return
     * @deprecated
     */
    @RequestMapping(value = "/memberAccountQuery", method = RequestMethod.POST)
    @Deprecated
    public MemberAccountQueryResp memberAccountQuery(@RequestBody MemberAccountQueryRequest memberAccountQueryRequest, HttpServletRequest request) {
        MemberAccountQueryResp resp = new MemberAccountQueryResp();
        String reqId = StringUtil.newGUID() + "_memberAccountQuery";
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberAccountQueryRequest>> violations = validator.validate(memberAccountQueryRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        String logStr = JsonUtil.objectToJson(memberAccountQueryRequest);
        log.info("请求号:{},请求参数:{},IP:{}", reqId, logStr, ip);
        String channelCode = memberAccountQueryRequest.getChannelCode();
        boolean flag = checkKeyInfo(String.valueOf(memberAccountQueryRequest.getId()), memberAccountQueryRequest.getLoginKeyInfo(), channelCode);
        if (flag) {
            MemberAccountQueryResponseForClient clientResponse = crmClient.memberAccountQuery(memberAccountQueryRequest.getId(), memberAccountQueryRequest.getStartDate(), memberAccountQueryRequest.getEndDate(), channelCode, getClientPwd(channelCode));
            String logStr2 = JsonUtil.objectToJson(clientResponse);
            log.info("请求号:{},crm积分状态信息返回:{}", reqId, logStr2);
            if (clientResponse.getMessageHeader().getErrorCode().equals("S000")) {
                MemberAccountType memberAccountType = clientResponse.getMemberAccount();
                AccountType sumAccount = memberAccountType.getSumAccount();
                AccountType rangeAccount = memberAccountType.getRangeAccount();

                resp.setClubMiles(sumAccount.getClubMiles());
                resp.setSupplierMiles(sumAccount.getSupplierMiles());
                resp.setPromotionMiles(sumAccount.getPromotionMiles());
                resp.setExtraMiles(sumAccount.getExtraMiles());
                resp.setRedeemMiles(sumAccount.getRedeemMiles());
                resp.setExpiredMiles(sumAccount.getExpiredMiles());

                resp.setRangeClubMiles(rangeAccount.getClubMiles());
                resp.setRangeSupplierMiles(rangeAccount.getSupplierMiles());
                resp.setRangePromotionMiles(rangeAccount.getPromotionMiles());
                resp.setRangeExtraMiles(rangeAccount.getExtraMiles());
                resp.setRangeRedeemMiles(rangeAccount.getRedeemMiles());
                resp.setRangeExpiredMiles(rangeAccount.getExpiredMiles());

                resp.setLockedMiles(memberAccountType.getLockedMiles());
                resp.setNearingExpiredMiles(memberAccountType.getNearingExpiredMiles());//即将失效里程1个月
                resp.setNearingExpiredDate(DateUtils.lastDayofMonth());//即将到期的日期
                resp.setNearingExpiredMiles2(memberAccountType.getNearingExpiredMiles2());
                resp.setNearingExpiredMiles3(memberAccountType.getNearingExpiredMiles3());
                resp.setBalanceOfMileage(sumAccount.getClubMiles() + sumAccount.getSupplierMiles() + sumAccount.getExtraMiles() + sumAccount.getPromotionMiles()
                        - sumAccount.getRedeemMiles() - sumAccount.getExpiredMiles());
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResponse.getMessageHeader().getDescription());
                if (clientResponse.getMessageHeader() != null && "E1002".equals(clientResponse.getMessageHeader().getErrorCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                }
            }
        } else {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
        }
        return resp;
    }

    /**
     * 生成验证码
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "/createVerificationCode", method = RequestMethod.POST)
    public CreateVerificationCodeResp createVerificationCodeResponseForClient(@RequestBody CreateVerificationCodeReq req, HttpServletRequest request) {
        CreateVerificationCodeResp resp = new CreateVerificationCodeResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CreateVerificationCodeReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String verifyMode = req.getVerifyMode();
        if (ContactTypeEnum.EMAIL.geteName().equalsIgnoreCase(verifyMode)) {
            req.setEndPoint("http://homobile.juneyaoair.com:90/check/checkEmail");
            String endPoint = req.getEndPoint();
            if (StringUtils.isBlank(endPoint)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("邮件url不能为空");
                return resp;
            }
        }
        String ip = this.getClientIP(request);
        log.info("会员验证码memberVerification================{}", ip);
        String channelCode = req.getChannelCode();
        CreateVerificationCodeResponseForClient verificationCodeResp = crmClient.verifyContacts(req.getMemberID(), VerifyModeType.valueOf(verifyMode), req.getVerifyType(), req.getEndPoint(), channelCode, getClientPwd(channelCode));
        if (verificationCodeResp.getMessageHeader().getErrorCode().equals("S000")) {
            resp.setVerifyMode(verificationCodeResp.getVerifyMode().name());
            resp.setContactValue(verificationCodeResp.getContactValue());
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(verificationCodeResp.getMessageHeader().getDescription());
        }
        return resp;
    }

    /**
     * 验证验证码  旧的版本
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "/verifyVerificationCode", method = RequestMethod.POST)
    public VerifyVerificationCodeResp verifyVerificationCodeResponseForClient(@RequestBody VerifyVerificationCodeReq req, HttpServletRequest request) {
        VerifyVerificationCodeResp resp = new VerifyVerificationCodeResp();
        String logStr = JsonUtil.objectToJson(req);
        log.info("verifyVerificationCode参数:{}", logStr);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<VerifyVerificationCodeReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String verifyMode = req.getVerifyMode();
        if (ContactTypeEnum.EMAIL.geteName().equalsIgnoreCase(verifyMode)) {
            String endPoint = req.getEndPoint();
            if (StringUtils.isBlank(endPoint)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("邮件url不能为空");
                return resp;
            }
        }
        String ip = this.getClientIP(request);
        String channelCode = req.getChannelCode();
        VerifyVerificationCodeResponseForClient verificationCodeResp = crmClient.verifyContactsVerify(req.getVerifyType(), req.getVerifyCode(), VerifyModeType.valueOf(verifyMode), req.getId(), channelCode, getClientPwd(channelCode));
        String logStr2 = JsonUtil.objectToJson(verificationCodeResp);
        log.info("{}请求CRM请求结果:{}", ip, logStr2);
        if (verificationCodeResp.getMessageHeader().getErrorCode().equals("S000")) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(verificationCodeResp.getMessageHeader().getDescription());
        }
        return resp;
    }

    /**
     * 邮箱连接验证
     *
     * @param
     * @param request
     * @return
     */
    @RequestMapping(value = "/verifyVerificationCodeEmail", method = RequestMethod.POST)
    public VerifyVerificationCodeResp verifyVerificationCodeEmailResponseForClient(@RequestBody VerifyEmailReq req, HttpServletRequest request) {
        VerifyVerificationCodeResp resp = new VerifyVerificationCodeResp();
        if (StringUtil.isNullOrEmpty(req.getVft()) || StringUtil.isNullOrEmpty(req.getVfc()) || StringUtil.isNullOrEmpty(String.valueOf(req.getSid()))) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("邮箱验证参数异常");
        }
        String ip = this.getClientIP(request);
        log.info("verifyVerificationCode================{}", ip);

        String channelCode = HandlerConstants.M_CHANNEL_CODE;
        VerifyVerificationCodeResponseForClient verificationCodeResp = crmClient.verifyContactsVerify(req.getVft(), req.getVfc(), VerifyModeType.EMAIL, Long.valueOf(req.getSid()), channelCode, getClientPwd(channelCode));
        if (verificationCodeResp.getMessageHeader().getErrorCode().equals("S000")) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(verificationCodeResp.getMessageHeader().getDescription());
        }
        return resp;
    }

    /**
     * 验证短信验证码并重置登录密码(新接口)
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "/newMemberRebuildB2CPassword", method = RequestMethod.POST)
    public MemberRebuildB2CPwdResp newRebuildB2CPasswordSms(@RequestBody NewMemberRebuildB2CPasswordReq req, HttpServletRequest request) {
        MemberRebuildB2CPwdResp resp = new MemberRebuildB2CPwdResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<NewMemberRebuildB2CPasswordReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        log.info("密码重置NewMemberRebuildB2C================{}", ip);
        String channelCode = req.getChannelCode();
        GetMemberInfoResponseForClient getMemberInfoResponse = crmClient.getMemberInfo(req.getPhone(), QueryMemberDataType.MOBILE.value(), channelCode, getClientPwd(channelCode));
        if (getMemberInfoResponse.getMessageHeader().getErrorCode().equals("S000")) {
            long id = getMemberInfoResponse.getMemberQueryInfo().getID();
            MemberRebuildB2CPasswordResponseForClient memberRebuildB2CPasswordResponseForClient = crmClient.rebuildB2CPassword(id, req.getNewB2CPassword(), channelCode, getClientPwd(channelCode));
            if (memberRebuildB2CPasswordResponseForClient.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(memberRebuildB2CPasswordResponseForClient.getMessageHeader().getDescription());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(getMemberInfoResponse.getMessageHeader().getDescription());
        }
        return resp;
    }

    /**
     * 验证短信验证码并重置登录密码
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "/memberRebuildB2CPassword", method = RequestMethod.POST)
    public MemberRebuildB2CPwdResp rebuildB2CPasswordSms(@RequestBody MemberRebuildB2CPasswordReq req, HttpServletRequest request) {
        MemberRebuildB2CPwdResp resp = new MemberRebuildB2CPwdResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberRebuildB2CPasswordReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        log.info("密码重置memberRebuildB2C================{}", ip);
        String channelCode = req.getChannelCode();
        GetMemberInfoResponseForClient getMemberInfoResponse = crmClient.getMemberInfo(req.getPhone(), QueryMemberDataType.MOBILE.value(), channelCode, getClientPwd(channelCode));
        if (getMemberInfoResponse.getMessageHeader().getErrorCode().equals("S000")) {
            long id = getMemberInfoResponse.getMemberQueryInfo().getID();
            //MMCZYZ
            VerifyVerificationCodeResponseForClient verificationCodeResp = crmClient.verifyContactsVerify("", req.getVerifyCode(), VerifyModeType.SMS, id, channelCode, getClientPwd(channelCode));
            if (verificationCodeResp.getMessageHeader().getErrorCode().equals("S000")) {
                MemberRebuildB2CPasswordResponseForClient memberRebuildB2CPasswordResponseForClient = crmClient.rebuildB2CPassword(id, req.getNewB2CPassword(), channelCode, getClientPwd(channelCode));
                if (memberRebuildB2CPasswordResponseForClient.getMessageHeader().getErrorCode().equals("S000")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(memberRebuildB2CPasswordResponseForClient.getMessageHeader().getDescription());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(verificationCodeResp.getMessageHeader().getDescription());
            }

        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(getMemberInfoResponse.getMessageHeader().getDescription());
        }

        return resp;
    }

    //激活，重置消费密码
    @RequestMapping(value = "/rebuildConsumePwd", method = RequestMethod.POST)
    public MemberRebuildPwdResp rebuildConsumePwd(@RequestBody MemberRebuildPwdReq req, HttpServletRequest request) {
        MemberRebuildPwdResp resp = new MemberRebuildPwdResp();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberRebuildPwdReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        log.info("rebuildConsume密码================{}", ip);
        String channelCode = req.getChannelCode();
        boolean flag = checkKeyInfo(String.valueOf(req.getId()), req.getLoginKeyInfo(), channelCode);
        if (flag) {
            MemberRebuildPasswordResponseForClient clientResp = crmClient.rebuildConsumePwd(req.getId(), req.getPwd(), req.getRebuildType(), channelCode, getClientPwd(channelCode));
            if (clientResp.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResp.getMessageHeader().getDescription());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
        }
        return resp;
    }

    //修改消费密码
    @RequestMapping(value = "/updateConsumePwd", method = RequestMethod.POST)
    public MemberChangePwdResp updateConsumePwd(@RequestBody MemberChangePwdReq req, HttpServletRequest request) {
        MemberChangePwdResp resp = new MemberChangePwdResp();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MemberChangePwdReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        log.info("updateConsume密码================{}", ip);
        String channelCode = req.getChannelCode();
        boolean flag = checkKeyInfo(String.valueOf(req.getId()), req.getLoginKeyInfo(), channelCode);
        if (flag) {
            MemberChangePasswordResponseForClient clientResp = crmClient.updateConsumePwd(req.getId(), req.getOldPwd(), req.getNewPwd(), channelCode, getClientPwd(channelCode));
            if (clientResp.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResp.getMessageHeader().getDescription());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
        }
        return resp;
    }

    //校验消费密码
    @RequestMapping(value = "/verifyConsumePwd", method = RequestMethod.POST)
    public VerifyConsumePwdResp verifyConsumePwd(@RequestBody VerifyConsumePwdReq req, HttpServletRequest request) {
        VerifyConsumePwdResp resp = new VerifyConsumePwdResp();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<VerifyConsumePwdReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        log.info("verifyConsume密码================{}", ip);
        String channelCode = req.getChannelCode();
        boolean flag = checkKeyInfo(String.valueOf(req.getId()), req.getLoginKeyInfo(), channelCode);
        if (flag) {
            PayPasswordReqDto reqDto = new PayPasswordReqDto();
            PtCrmMileageRequest ptCrmMileageRequest = buildCommCrmReq(request, req.getChannelCode());
            reqDto.setId((int) req.getId());
            reqDto.setPassword(req.getPwd());
            ptCrmMileageRequest.setData(reqDto);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult httpResult = this.doPostClient(ptCrmMileageRequest, HandlerConstants.CRM_OPENAPI_URL + HandlerConstants.PAY_PASSWORD, headMap);
            if (httpResult.isResult()) {
                PtCrmMileageResponse ptCrmMileageResponse = (PtCrmMileageResponse) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<PtCrmMileageResponse>() {
                }.getType());
                if (ptCrmMileageResponse.getCode() == 0) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCrmMileageResponse.getDesc());
                    resp.setErrorInfo(ptCrmMileageResponse.getDesc());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR.getResultInfo());
                resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
            }
        }else {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
        }
        return resp;
    }
        /**
         * 微信登录
         *
         * @param loginReq
         * @param request
         * @return
         */
        @RequestMapping(value = "/weixinLoginInfo", method = RequestMethod.POST)
        public MemberLoginResponse weixinLogin (@RequestBody WeixinLoginRequest loginReq, HttpServletRequest request){
            MemberLoginResponse resp = new MemberLoginResponse();
            WeixinLoginResponse res;
            String ip = this.getClientIP(request);
            String logStr = JsonUtil.objectToJson(loginReq);
            log.info("会员登录请求IP:{},内容{}", ip, logStr);
            if (!this.chkDayVisit(ip, LOGIN, "")) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            }
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<WeixinLoginRequest>> violations = validator.validate(loginReq);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String channelCode = loginReq.getChannelCode();
            Map<String, String> param = new HashMap<>();
            param.put("appid", HandlerConstants.WEIXIN_APP_ID);
            param.put("secret", HandlerConstants.WEIXIN_SECRET);
            param.put("code", loginReq.getCode());
            param.put("grant_type", HandlerConstants.WEIXIN_GRANT_TYPE);

            String paramStr = assemblyGETParam(param);
            log.info("请求微信：{}，参数{}", HandlerConstants.WEIXIN_LOGIN, paramStr);
            String result = new String(HttpsUtil.doGet(HandlerConstants.WEIXIN_LOGIN + "?" + paramStr));
            log.info("请求微信结果 {}", result);
            if (!StringUtil.isNullOrEmpty(result)) {
                res = (WeixinLoginResponse) JsonUtil.jsonToBean(result, WeixinLoginResponse.class);
                if (res.getErrcode() != null) {
                    resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
                    resp.setResultCode(res.getErrmsg());
                    log.error("请求微信-->Errcode:{},Errmsg:{}", res.getErrcode(), res.getErrmsg());
                    return resp;
                } else if (StringUtil.isNullOrEmpty(res.getAccess_token()) || StringUtil.isNullOrEmpty(res.getOpenid())) {
                    resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    log.error("请求微信-->{} ? {} Access_token:{},Openid:{}", HandlerConstants.WEIXIN_LOGIN, paramStr, res.getAccess_token(), res.getOpenid());
                    return resp;
                }
            } else {
                resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                log.error("请求微信网络错误:{} ? {} ", HandlerConstants.WEIXIN_LOGIN, paramStr);
                return resp;
            }
            // 微信openID
            String openID = res.getOpenid();
            TempMemberLoginResponseForClient memberinfo = crmClient.ZFBOrWeiXinLogin(null, openID, null, null,
                    channelCode, getClientPwd(channelCode));
            String logStr2 = JsonUtil.objectToJson(memberinfo);
            log.info("crm请求结果{}", logStr2);
            String infoIdKey;
            if (memberinfo.getMessageHeader().getErrorCode().equals("S000")) {
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                infoIdKey = EncoderHandler.encode("MD5", memberinfo.getID() + key).toUpperCase();
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setLoginKeyInfo(infoIdKey);
                resp.setId(memberinfo.getID());//ffp_id
                resp.setMemberID(memberinfo.getMemberID());
                //获取证件信息
                resp.setMemberType("OTHER");//微信登陆暂不获取账户安全相关信息
                if (!("OTHER").equals(resp.getMemberType())) {
                    getMemberInfo(resp, memberinfo.getID(), channelCode);
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR_LONGIN_ERROR.getResultCode());
                resp.setErrorInfo(memberinfo.getMessageHeader().getDescription());
                return resp;
            }

            return resp;
        }

        public void getMemberInfo (MemberLoginResponse resp, Long id, String channelCode){
            //取人员证件信息
            MemberInfoQueryResponseForClient memberInfo1 = crmClient.memberInfoquery(id, channelCode, getClientPwd(channelCode));
            if (memberInfo1 != null && memberInfo1.getMemberQueryInfo() != null) {
                if (!StringUtil.isNullOrEmpty(memberInfo1.getMemberQueryInfo().getCustomerCertificateInfo())) {
                    resp.setCertType(memberInfo1.getMemberQueryInfo().getCustomerCertificateInfo().get(0).getCertType().value());
                    resp.setCertNumber(memberInfo1.getMemberQueryInfo().getCustomerCertificateInfo().get(0).getCertNumber());
                    //新增，证件遍历
                    List<CustomerCertificateInfo> list = new ArrayList<>();
                    for (CustomerCertificateInfoType customerCertificateInfoType : memberInfo1.getMemberQueryInfo()
                            .getCustomerCertificateInfo()) {
                        CustomerCertificateInfo cinfo = new CustomerCertificateInfo();
                        String certType = customerCertificateInfoType.getCertType().value();
                        String certNum = customerCertificateInfoType.getCertNumber();
                        if (!StringUtil.isNullOrEmpty(certNum)) {
                            cinfo.setCertType(convertCertType(certType));
                            cinfo.setCertNumber(certNum);
                            list.add(cinfo);
                        }
                    }
                    resp.setCustomerCertificateInfos(list);
                }
                if (!StringUtil.isNullOrEmpty(memberInfo1.getMemberQueryInfo().getCustomerContactInfo())) {
                    for (CustomerContactInfoType cust : memberInfo1.getMemberQueryInfo().getCustomerContactInfo()) {
                        String custType = cust.getContactType().value().toUpperCase();
                        if (ContactTypeEnum.MOBILE.geteName().equalsIgnoreCase(custType)) {
                            resp.setMemberTel(cust.getContactValue());
                        }
                        if (ContactTypeEnum.EMAIL.geteName().equalsIgnoreCase(custType)) {
                            resp.setMemberEmail(cust.getContactValue());
                        }
                    }
                }
                if (null != memberInfo1.getMemberQueryInfo().getCustomerInfo()) {
                    resp.setSex(memberInfo1.getMemberQueryInfo().getCustomerInfo().getSex().value());
                }
                resp.setMemberLevelCode(memberInfo1.getMemberQueryInfo().getMemberLevelCode());
                resp.setMemberStatusCode(memberInfo1.getMemberQueryInfo().getMemberStatusCode());
            }

        }

        //证件类型转换
        private String convertCertType (String certType){
            String type = "";
            switch (certType) {
                case "IDCard":
                    type = "NI";
                    break;
                case "Passport":
                    type = "PP";
                    break;
                case "Other":
                    type = "CC";
                    break;
                default:
                    type = "CC";
                    break;
            }
            return type;
        }

        /**
         * 获取我的积分
         *
         * @return
         */
        @RequestMapping(value = "/myScore", method = RequestMethod.POST)
        public ScoreUseRuleResp getMyScore (@RequestBody MyScoreReq myScoreReq, HttpServletRequest request){
            ScoreUseRuleResp resp = new ScoreUseRuleResp();
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<MyScoreReq>> violations = validator.validate(myScoreReq);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //验证用户查询是否正常
            try {
                boolean flag = this.checkKeyInfo(myScoreReq.getFfpId(), myScoreReq.getLoginKeyInfo(), myScoreReq.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
                //返回客户可用积分
                String channelCode = myScoreReq.getChannelCode();
                VerifyConsumeMilesResponseForClient accmClient = crmClient.getAccmInfo(Long.parseLong(myScoreReq.getFfpId()), channelCode, getChannelInfo(channelCode, "40"));
                if (accmClient.getMessageHeader().getErrorCode().equals("S000")) {
                    resp.setUserScore(Integer.parseInt(accmClient.getMemberRedeemInfo().getAvailableMiles()));
                } else {
                    String logStr = JsonUtil.objectToJson(myScoreReq);
                    log.info("crm查询积分出错：{}", logStr);
                    resp.setUserScore(0);
                }
            } catch (Exception e) {
                log.error("查询积分出错", e);
                resp.setUserScore(0);
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        }


        /**
         * 将请求参数拼成key=value&key=value&key=value&的形式
         *
         * @param map
         * @return
         */
        public String assemblyGETParam (Map < String, String > map){
            StringBuilder par = new StringBuilder();
            map.forEach((key, value) -> par.append(key + "=" + value + "&"));
            return par.substring(0, par.length() - 1);
        }

        /**
         * 会员银行卡实名认证
         *
         * @param cardAuthRequest 银行卡认证请求
         * @param request
         * @return 验证结果返回
         */
        @RequestMapping(value = "/cardAuth", method = RequestMethod.POST)
        public AuthResultResponse memberAuthByCard (@RequestBody CardAuthRequest cardAuthRequest, HttpServletRequest
        request){
            AuthResultResponse resp = new AuthResultResponse();
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("该功能暂不可用,请至官网完成操作");
            return resp;
        }

        /**
         * 根据证件类型获取证件号
         *
         * @param customerCertificateInfoTypeList 证件列表一览
         * @param certType                        证件类型
         * @return 证件号
         */
        private String getCertNumber
        (List < CustomerCertificateInfoType > customerCertificateInfoTypeList, CertificateType certType){
            String certNumber = null;
            for (CustomerCertificateInfoType certInfo : customerCertificateInfoTypeList) {
                if (certInfo.getCertType().equals(certType)) {
                    certNumber = certInfo.getCertNumber();
                    break;
                }
            }
            return certNumber;
        }

        /**
         * 会员证件照实名认证
         *
         * @param photoAuthRequest 证件照认证请求
         * @param request
         * @return 验证结果返回
         */
        @RequestMapping(value = "/photoAuth", method = RequestMethod.POST)
        @InterfaceLog
        public AuthResultResponse memberAuthByPhoto (@RequestBody @Validated PhotoAuthRequest
        photoAuthRequest, BindingResult bindingResult, HttpServletRequest request){
            AuthResultResponse resp = new AuthResultResponse();
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("抱歉的通知您，因技术原因，拍照认证功能暂时无法提供服务，请您使用其他方式或联系吉祥航空官方客服电话95520");
            return resp;
            // 传入请求属性检证
        /*ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<PhotoAuthRequest>> violations = validator.validate(photoAuthRequest);
        if (CollectionUtils.isNotEmpty(violations)) {
            // 检证失败返回
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }

        log.info("memberAuthByPhoto================{}", ip);
        // 认证信息认证
        boolean flag = this.checkKeyInfo(String.valueOf(photoAuthRequest.getId()), photoAuthRequest.getLoginKeyInfo(), photoAuthRequest.getChannelCode());
        if (!flag) {
            // 认证失败返回
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }

        if (!photoAuthRequest.getConsumePwd().equals(photoAuthRequest.getConfirmePwd())) {
            // 密码确认错误
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("确认消费密码和消费密码不相同！");
            return resp;
        }
        // 渠道用户号获取
        String channelCode = photoAuthRequest.getChannelCode();
        //证件基本信息检验
        // 根据id获取用户信息
        MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(photoAuthRequest.getId(), channelCode);
        if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(OPERATION_FAILED);
            return resp;
        } else if (!memberInfo.getMessageHeader().getErrorCode().equals("S000")) {
            resp.setResultCode(memberInfo.getMessageHeader().getErrorCode());
            resp.setErrorInfo(memberInfo.getMessageHeader().getDescription());
            return resp;
        } else {
            String elastName = memberInfo.getMemberQueryInfo().getCustomerInfo().getELastName();//英文姓
            String efirstName = memberInfo.getMemberQueryInfo().getCustomerInfo().getEFirstName();//英文名
            if (StringUtil.isNullOrEmpty(memberInfo.getMemberQueryInfo().getCustomerCertificateInfo()) || StringUtil.isNullOrEmpty(elastName + efirstName)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("请先去账户管理中补全信息！");
                return resp;
            }
        }
        FileInfo addressFace;
        FileInfo addressHand;
        try {
            addressFace = FavFTPUtil.uploadFile(photoAuthRequest.getPhotoFace(), "sftp", PicTypeEnum.ID_PHOTO_IMAGE);
            addressHand = FavFTPUtil.uploadFile(photoAuthRequest.getPhotoInHand(), "sftp", PicTypeEnum.ID_PHOTO_IMAGE);
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
        String facePhotoUrl = addressFace.getImgServiceUrl();
        String handPhotoUrl = addressHand.getImgServiceUrl();
        // 消费密码调用（证件照实名认证）接口调用
        MemberCreatePasswordResponseForClient createPwdClient = crmClient.createPwd(photoAuthRequest.getId(), channelCode, getClientPwd(channelCode), photoAuthRequest.getConsumePwd(), facePhotoUrl, handPhotoUrl);
        String logStr2 = JsonUtil.objectToJson(createPwdClient);
        log.info("证件照认证CRM请求结果:{}", logStr2);
        String infoIdKey = "";
        if (!this.respMessageHeaderTypeCheck(createPwdClient.getMessageHeader())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(OPERATION_FAILED);
        } else if (!createPwdClient.getMessageHeader().getErrorCode().equals("S000")) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(createPwdClient.getMessageHeader().getDescription());
        } else {
            String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
            infoIdKey = EncoderHandler.encode("MD5", String.valueOf(photoAuthRequest.getId()) + key).toUpperCase();
            resp.setLoginKeyInfo(infoIdKey);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setMemberID(memberInfo.getMemberQueryInfo().getMemberID());
            resp.setAuthType("Photo");//照片认证
            PhotoAuthResponse photoAuthResponse = new PhotoAuthResponse();
            photoAuthResponse.setAuthState("待审核");
            photoAuthResponse.setPhotoFace(facePhotoUrl);//正面照
            photoAuthResponse.setPhotoInHand(handPhotoUrl);//手持证件照
            resp.setPhotoAuthResponse(photoAuthResponse);
        }
        String logStr3 = JsonUtil.objectToJson(resp);
        log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr3);
        return resp;*/
        }

        /**
         * 会员证件照实名认证，不需要传入消费密码
         *
         * @param photoAuthNoPwdRequest 证件照认证请求
         * @param request
         * @return 验证结果返回
         */
        @RequestMapping(value = "/photoAuthNoPwd", method = RequestMethod.POST)
        @InterfaceLog
        public AuthResultResponse memberAuthByPhotoNoPwd (@RequestBody @Validated PhotoAuthNoPwdRequest
        photoAuthNoPwdRequest, BindingResult bindingResult, HttpServletRequest request){
            AuthResultResponse resp = new AuthResultResponse();
        /*resp.setResultCode(WSEnum.ERROR.getResultCode());
        resp.setErrorInfo("抱歉的通知您，因技术原因，拍照认证功能暂时无法提供服务，请您使用其他方式或联系吉祥航空官方客服电话95520");
        return resp;*/
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(photoAuthNoPwdRequest.getId()), photoAuthNoPwdRequest.getLoginKeyInfo(), photoAuthNoPwdRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            // 渠道用户号获取
            String channelCode = photoAuthNoPwdRequest.getChannelCode();
            // 根据id获取用户信息
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(photoAuthNoPwdRequest.getMemberID(), String.valueOf(photoAuthNoPwdRequest.getId()), request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptCRMDetailResponse = memberService.memberDetail(ptApiRequest);
            if (ptCRMDetailResponse.getCode() != 0) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), "获取到用户信息失败");
            }
            PtMemberDetail ptMemberDetail = ptCRMDetailResponse.getData();
            String elastName = ptMemberDetail.getBasicInfo().getELastName();//英文姓
            String efirstName = ptMemberDetail.getBasicInfo().getEFirstName();//英文名
            if (StringUtil.isNullOrEmpty(ptMemberDetail.getCertificateInfo()) || StringUtil.isNullOrEmpty(elastName + efirstName)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("请先去账户管理中补全信息！");
                return resp;
            }
            String facePhotoUrl;//证件正面
            String handPhotoUrl;//手持证件
            //boolean useMinio = FileUtils.useMinio(handConfig.getUseMinioType(), PicTypeEnum.ID_PHOTO_IMAGE.getCode());
            String host = handConfig.getEndpoint() + "/" + handConfig.getBucket();
            if (ChannelCodeEnum.MOBILE.getChannelCode().equals(request.getHeader(HEAD_CHANNEL_CODE))) {
                facePhotoUrl = host + photoAuthNoPwdRequest.getPhotoFace();
                handPhotoUrl = host + photoAuthNoPwdRequest.getPhotoInHand();
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("抱歉的通知您，因技术原因，拍照认证功能暂时无法提供服务，请您使用其他方式或联系吉祥航空官方客服电话95520");
                return resp;
            } /*else if (ChannelCodeEnum.MWEB.getChannelCode().equals(request.getHeader(HEAD_CHANNEL_CODE))) {
            facePhotoUrl = photoAuthNoPwdRequest.getPhotoFace();
            handPhotoUrl = photoAuthNoPwdRequest.getPhotoInHand();
        } else {
            FileInfo addressFace;
            FileInfo addressHand;
            try {
                addressFace = FavFTPUtil.uploadFile(photoAuthNoPwdRequest.getPhotoFace(), "sftp", PicTypeEnum.ID_PHOTO_IMAGE);
                addressHand = FavFTPUtil.uploadFile(photoAuthNoPwdRequest.getPhotoInHand(), "sftp", PicTypeEnum.ID_PHOTO_IMAGE);
            } catch (Exception e) {
                log.error("图片上传失败", e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
                return resp;
            }
            facePhotoUrl = addressFace.getImgServiceUrl();
            handPhotoUrl = addressHand.getImgServiceUrl();
        }*/
            PtApiCRMRequest<PtCertificateRealNameRequest> ptApiCRMRequest = buildCommReq(request, channelCode, String.valueOf(photoAuthNoPwdRequest.getId()), "");
            PtCertificateRealNameRequest ptCertificateRealNameRequest = new PtCertificateRealNameRequest();
            ptCertificateRealNameRequest.setInHandPhotoUrl(handPhotoUrl);
            ptCertificateRealNameRequest.setPositivePhotoUrl(facePhotoUrl);
            ptCertificateRealNameRequest.setConsumePassword("");
            ptApiCRMRequest.setData(ptCertificateRealNameRequest);
            PtCRMResponse ptCRMResponse = memberService.photoRealSub(ptApiCRMRequest);
            if (ptCRMResponse.getCode() != 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
                return resp;
            } else {
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                String infoIdKey = EncoderHandler.encode("MD5", String.valueOf(photoAuthNoPwdRequest.getId()) + key).toUpperCase();
                resp.setLoginKeyInfo(infoIdKey);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setMemberID(ptMemberDetail.getBasicInfo().getCardNO());
                resp.setAuthType("Photo");//照片认证
                PhotoAuthResponse photoAuthResponse = new PhotoAuthResponse();
                photoAuthResponse.setAuthState("待审核");
                photoAuthResponse.setPhotoFace("");//正面照
                photoAuthResponse.setPhotoInHand("");//手持证件照
                resp.setPhotoAuthResponse(photoAuthResponse);
            }
            return resp;
        }

        /**
         * 会员认证信息查询
         *
         * @param authResultRequest 会员认证信息查询请求
         * @param request
         * @return 认证结果返回
         * @deprecated 新的方法参考MemberController.memberRealName
         */
        @RequestMapping(value = "/authResult", method = RequestMethod.POST)
        @Deprecated
        public AuthResultResponse getAuthResult (@RequestBody AuthResultRequest authResultRequest, HttpServletRequest
        request){
            String reqId = StringUtil.newGUID() + "_authResult";
            String ip = this.getClientIP(request);
            String logStr = JsonUtil.objectToJson(authResultRequest);
            log.info(COMMON_LOG_WITH_REQ_INFO, reqId, ip, logStr);
            AuthResultResponse resp = new AuthResultResponse();
            // 传入请求属性检证
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<AuthResultRequest>> violations = validator.validate(authResultRequest);
            if (CollectionUtils.isNotEmpty(violations)) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(authResultRequest.getId()), authResultRequest.getLoginKeyInfo(), authResultRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = authResultRequest.getChannelCode();
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(authResultRequest.getId(), channelCode);
            String infoIdKey = "";
            if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (memberInfo.getMessageHeader().getErrorCode().equals("E555")) {//微信登录等返回空信息
                resp.setResultCode(WSEnum.ERROR_USER_INFO.getResultCode());
                resp.setErrorInfo("微信登录用户暂不支持修改信息！");
            } else if (!memberInfo.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(memberInfo.getMessageHeader().getErrorCode());
                resp.setErrorInfo(memberInfo.getMessageHeader().getDescription());
            } else {
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                infoIdKey = EncoderHandler.encode("MD5", String.valueOf(authResultRequest.getId()) + key).toUpperCase();
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setLoginKeyInfo(infoIdKey);
                resp.setMemberID(memberInfo.getMemberQueryInfo().getMemberID());
                resp.setAuthType(memberInfo.getMemberQueryInfo().getRealVerifyChannel());
                // 银行卡实名认证信息返回
                CardAuthResponse cardAuthResponse = new CardAuthResponse();
                cardAuthResponse.setAuthState(memberInfo.getMemberQueryInfo().getRealVerifyStatus());
                cardAuthResponse.setName("*" + memberInfo.getMemberQueryInfo().getCustomerInfo().getCFirstName());
                cardAuthResponse.setCertificateNo(getCertNumber(memberInfo.getMemberQueryInfo().getCustomerCertificateInfo(), CertificateType.IDCard));
                resp.setCardAuthResponse(cardAuthResponse);
                // 证件照实名认证信息返回
                PhotoAuthResponse photoAuthResponse = new PhotoAuthResponse();
                photoAuthResponse.setAuthState(memberInfo.getMemberQueryInfo().getRealVerifyStatus());
                photoAuthResponse.setPhotoFace(memberInfo.getMemberQueryInfo().getPhotoPath());//正面照
                photoAuthResponse.setPhotoInHand(memberInfo.getMemberQueryInfo().getPhotoPath2());//手持照片
                resp.setPhotoAuthResponse(photoAuthResponse);
                //账户信息返回 证件列表
                if (!StringUtil.isNullOrEmpty(memberInfo.getMemberQueryInfo().getCustomerCertificateInfo())) {
                    //证件遍历
                    List<CustomerCertificateInfo> list = new ArrayList<>();
                    for (CustomerCertificateInfoType customerCertificateInfoType : memberInfo.getMemberQueryInfo()
                            .getCustomerCertificateInfo()) {
                        CustomerCertificateInfo cinfo = new CustomerCertificateInfo();
                        String certType = customerCertificateInfoType.getCertType().value();
                        String certNum = customerCertificateInfoType.getCertNumber();
                        if (!StringUtil.isNullOrEmpty(certNum)) {
                            cinfo.setCertType(convertCertType(certType));
                            cinfo.setCertNumber(certNum);
                            list.add(cinfo);
                        }
                    }
                    resp.setCustomerCertificateInfos(list);
                }
                if (!StringUtil.isNullOrEmpty(memberInfo.getMemberQueryInfo().getCustomerContactInfo())) {
                    for (CustomerContactInfoType cust : memberInfo.getMemberQueryInfo().getCustomerContactInfo()) {
                        String custType = cust.getContactType().value().toUpperCase();
                        if (ContactTypeEnum.MOBILE.geteName().equalsIgnoreCase(custType)) {
                            resp.setMemberTel(cust.getContactValue());
                        }
                        if (ContactTypeEnum.EMAIL.geteName().equalsIgnoreCase(custType)) {
                            resp.setMemberEmail(cust.getContactValue());
                        }
                    }
                }
                // 客户信息返回
                CustomerInfoType infoType = memberInfo.getMemberQueryInfo().getCustomerInfo();
                if (null != infoType) {
                    // 中文姓
                    resp.setcLastName(infoType.getCLastName());
                    // 中文名
                    resp.setcFirstName(infoType.getCFirstName());
                    if (!StringUtil.isNullOrEmpty(infoType.getCLastName()) && !StringUtil.isNullOrEmpty(infoType.getCFirstName())) {
                        resp.setName(infoType.getCLastName() + infoType.getCFirstName());
                    } else {
                        resp.setName("");
                    }
                    // 英文姓
                    resp.seteFirstName(infoType.getEFirstName());
                    // 英文名
                    resp.seteLastName(infoType.getELastName());
                    // 出生日期
                    resp.setBirthday(infoType.getBirthDate().toString());
                    //性别
                    resp.setSex(infoType.getSex().value());
                }
                // 地址信息返回·
                CustomerAddressInfoType addInfo = memberInfo.getMemberQueryInfo().getCustomerAddressInfo();
                if (null != addInfo) {
                    // 国家
                    resp.setCountryCode(StringUtil.isNullOrEmpty(addInfo.getCountryCode()) ? "中国" : addInfo.getCountryCode());
                    // 省份
                    resp.setProvinceCode(addInfo.getProvinceCode());
                    // 城市
                    resp.setAddressCityCode(addInfo.getAddressCityCode());
                    // 地址
                    resp.setAddressContent(addInfo.getAddressContent());
                    // 邮编
                    resp.setPostCode(addInfo.getPostCode());
                }

            }
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;
        }

        /**
         * 修改消费密码  新的
         *
         * @param consumePwdRequest 修改消费密码请求
         * @param request
         * @return 操作结果返回
         */
        @RequestMapping(value = "/updConsumePwd", method = RequestMethod.POST)
        public ConsumePwdResponse updConsumePassword (@RequestBody ConsumePwdRequest
        consumePwdRequest, HttpServletRequest request){
            ConsumePwdResponse resp = new ConsumePwdResponse();
            // 传入请求属性检证
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<ConsumePwdRequest>> violations = validator.validate(consumePwdRequest);
            if (CollectionUtils.isNotEmpty(violations)) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(consumePwdRequest.getId()), consumePwdRequest.getLoginKeyInfo(), consumePwdRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = consumePwdRequest.getChannelCode();
            if (!consumePwdRequest.getNewPassword().equals(consumePwdRequest.getConfirmPwd())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("确认密码和消费密码不同！");
                return resp;
            }
            // 验证消费密码接口调用
            VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(consumePwdRequest.getId(), consumePwdRequest.getOldPassword(), channelCode, getClientPwd(channelCode));
            if (!this.respMessageHeaderTypeCheck(clientResp.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!clientResp.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("原消费密码错误!");
                return resp;
            }
            // 修改消费密码接口调用
            MemberChangePasswordResponseForClient memberChangePwd = crmClient.updateConsumePwd(consumePwdRequest.getId(), consumePwdRequest.getOldPassword(), consumePwdRequest.getNewPassword(), channelCode, getClientPwd(channelCode));
            if (!this.respMessageHeaderTypeCheck(memberChangePwd.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!memberChangePwd.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(memberChangePwd.getMessageHeader().getErrorCode());
                resp.setErrorInfo(memberChangePwd.getMessageHeader().getDescription());
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            }
            return resp;
        }

        /**
         * 会员基本信息补全
         *
         * @param memberBaseInfoRequest 会员基本补全信息请求
         * @param request
         * @return 会员基本信息返回
         */
        @RequestMapping(value = "/updMemberBaseInfo", method = RequestMethod.POST)
        public MemberBaseInfoResponse updMemberBaseInfo (@RequestBody MemberBaseInfoRequest
        memberBaseInfoRequest, HttpServletRequest request){

            MemberBaseInfoResponse resp = new MemberBaseInfoResponse();
            //
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<MemberBaseInfoRequest>> violations = validator.validate(memberBaseInfoRequest);
            if (CollectionUtils.isNotEmpty(violations)) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String ip = this.getClientIP(request);
            log.info("updMemberBaseInfo================{}", ip);
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(memberBaseInfoRequest.getId()), memberBaseInfoRequest.getLoginKeyInfo(), memberBaseInfoRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            // 用户渠道号
            String channelCode = memberBaseInfoRequest.getChannelCode();

            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(memberBaseInfoRequest.getId(), channelCode);
            // 信息未追加标记
            boolean noInfoAdd = true;
            if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (memberInfo.getMessageHeader().getErrorCode().equals("E555")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("微信登录用户暂不支持修改信息！");
                return resp;
            } else if (!(memberInfo.getMessageHeader().getErrorCode().endsWith("S000"))) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询用户信息错误！");
                return resp;
            }

            //<editor-fold desc="验证用户信息,是否可以修改相关信息">
            CustomerInfoType customerInfoType = memberInfo.getMemberQueryInfo().getCustomerInfo();
            Map<String, Object> addParam = new HashedMap();
            // 出生日期检证选择
            if (StringUtil.isNullOrEmpty(customerInfoType.getBirthDate().toString()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getBirthDate())) {
                addParam.put(BIRTHDAY, memberBaseInfoRequest.getBirthDate());
                noInfoAdd = false;
            } else if (!StringUtil.isNullOrEmpty(customerInfoType.getBirthDate().toString()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getBirthDate())) {
                if (!customerInfoType.getBirthDate().toString().equals(memberBaseInfoRequest.getBirthDate())) {
                    // 无法修改已有信息
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("出生日期无法修改！");
                    return resp;
                } else {
                    addParam.put(BIRTHDAY, customerInfoType.getBirthDate().toString());
                }
            } else {
                addParam.put(BIRTHDAY, customerInfoType.getBirthDate().toString());
            }
            // 中文姓名检证选择
            if (StringUtil.isNullOrEmpty(customerInfoType.getCFirstName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getcFirstName())) {
                addParam.put(C_FIRST_NAME, memberBaseInfoRequest.getcFirstName());
                noInfoAdd = false;
            } else if (!StringUtil.isNullOrEmpty(customerInfoType.getCFirstName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getcFirstName())) {
                if (!customerInfoType.getCFirstName().equals(memberBaseInfoRequest.getcFirstName())) {
                    // 无法修改已有信息
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("中文姓名无法修改！");
                    return resp;
                } else {
                    addParam.put(C_FIRST_NAME, customerInfoType.getCFirstName());
                }
            } else {
                addParam.put(C_FIRST_NAME, customerInfoType.getCFirstName());
            }
            if (StringUtil.isNullOrEmpty(customerInfoType.getCLastName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getcLastName())) {
                addParam.put(C_LAST_NAME, memberBaseInfoRequest.getcLastName());
                noInfoAdd = false;
            } else if (!StringUtil.isNullOrEmpty(customerInfoType.getCLastName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getcLastName())) {
                if (!customerInfoType.getCLastName().equals(memberBaseInfoRequest.getcLastName())) {
                    // 无法修改已有信息
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("中文姓名无法修改！");
                    return resp;
                } else {
                    addParam.put(C_LAST_NAME, customerInfoType.getCLastName());
                }
            } else {
                addParam.put(C_LAST_NAME, customerInfoType.getCLastName());
            }
            // 英文姓名检证选择
            if (StringUtil.isNullOrEmpty(customerInfoType.getEFirstName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.geteFirstName())) {
                addParam.put(E_FIRST_NAME, memberBaseInfoRequest.geteFirstName());
                noInfoAdd = false;
            } else if (!StringUtil.isNullOrEmpty(customerInfoType.getEFirstName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.geteFirstName())) {
                if (!customerInfoType.getEFirstName().equals(memberBaseInfoRequest.geteFirstName())) {
                    // 无法修改已有信息
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("英文名无法修改！");
                    return resp;
                } else {
                    addParam.put(E_FIRST_NAME, customerInfoType.getEFirstName());
                }
            } else {
                addParam.put(E_FIRST_NAME, customerInfoType.getEFirstName());
            }
            if (StringUtil.isNullOrEmpty(customerInfoType.getELastName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.geteLastName())) {
                addParam.put(E_LAST_NAME, memberBaseInfoRequest.geteLastName());
                noInfoAdd = false;
            } else if (!StringUtil.isNullOrEmpty(customerInfoType.getELastName()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.geteLastName())) {
                if (!customerInfoType.getELastName().equals(memberBaseInfoRequest.geteLastName())) {
                    // 无法修改已有信息
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("英文姓无法修改！");
                    return resp;
                } else {
                    addParam.put(E_LAST_NAME, customerInfoType.getELastName());
                }
            } else {
                addParam.put(E_LAST_NAME, customerInfoType.getELastName());
            }
            // 性别检证选择
            if (StringUtil.isNullOrEmpty(customerInfoType.getSex().toString()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getSex())) {
                addParam.put("sex", memberBaseInfoRequest.getSex());
                noInfoAdd = false;
            } else if (!StringUtil.isNullOrEmpty(customerInfoType.getSex().toString()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getSex())) {
                if (!customerInfoType.getSex().value().equals(memberBaseInfoRequest.getSex())) {
                    // 无法修改已有信息
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("性别无法修改！");
                    return resp;
                } else {
                    addParam.put("sex", customerInfoType.getSex());
                }
            } else {
                addParam.put("sex", customerInfoType.getSex());
            }
            // 联系方式检证选择
            List<CustomerContactInfoType> dbContactInfoList = memberInfo.getMemberQueryInfo().getCustomerContactInfo();
            boolean hasMobile = false;
            boolean hasEMail = false;
            String paramMobile = "mobile";
            String paramEmail = "email";
            if (CollectionUtils.isNotEmpty(dbContactInfoList)) {
                for (CustomerContactInfoType customerContactInfoType : dbContactInfoList) {
                    if (customerContactInfoType.getContactType().equals(ContactType.MOBILE)) {
                        hasMobile = true;
                        if (StringUtil.isNullOrEmpty(customerContactInfoType.getContactValue()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getMobileNumber())) {
                            addParam.put(paramMobile, memberBaseInfoRequest.getMobileNumber());
                            noInfoAdd = false;
                        } else if (!StringUtil.isNullOrEmpty(customerContactInfoType.getContactValue()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getMobileNumber())) {
                            if (!customerContactInfoType.getContactValue().equals(memberBaseInfoRequest.getMobileNumber())) {
                                // 无法修改已有信息
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setErrorInfo("手机号码无法修改！");
                                return resp;
                            } else {
                                addParam.put(paramMobile, customerContactInfoType.getContactValue());
                            }
                        } else {
                            addParam.put(paramMobile, customerContactInfoType.getContactValue());
                        }
                        continue;
                    }
                    if (customerContactInfoType.getContactType().equals(ContactType.EMAIL)) {
                        hasEMail = true;
                        if (StringUtil.isNullOrEmpty(customerContactInfoType.getContactValue()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getEmailAddress())) {
                            addParam.put(paramEmail, memberBaseInfoRequest.getEmailAddress());
                            noInfoAdd = false;
                        } else if (!StringUtil.isNullOrEmpty(customerContactInfoType.getContactValue()) && !StringUtil.isNullOrEmpty(memberBaseInfoRequest.getEmailAddress())) {
                            if (!customerContactInfoType.getContactValue().equals(memberBaseInfoRequest.getEmailAddress())) {
                                // 无法修改已有信息
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setErrorInfo("邮件地址无法修改！");
                                return resp;
                            } else {
                                addParam.put(paramEmail, customerContactInfoType.getContactValue());
                            }
                        } else {
                            addParam.put(paramEmail, customerContactInfoType.getContactValue());
                        }
                    }
                }
            }
            if (!hasMobile) {
                addParam.put(paramMobile, memberBaseInfoRequest.getMobileNumber());
                noInfoAdd = false;
            }
            if (!hasEMail) {
                addParam.put(paramEmail, memberBaseInfoRequest.getEmailAddress());
                noInfoAdd = false;
            }
            //</editor-fold>

            if (noInfoAdd) {
                // 补全联系方式出错返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("基本信息未修改！");
                return resp;
            } else {
                MemberBaseInfoUpdateResponseForClient updBaseInfoResp = this.updMemberBaseInfo(memberBaseInfoRequest.getId(), channelCode, addParam);
                if (!this.respMessageHeaderTypeCheck(updBaseInfoResp.getMessageHeader())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(OPERATION_FAILED);
                } else if (!updBaseInfoResp.getMessageHeader().getErrorCode().equals("S000")) {
                    // 补全基本信息出错返回
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(updBaseInfoResp.getMessageHeader().getDescription());
                    return resp;
                }
            }
            resp = this.getMemberBaseInfo(memberBaseInfoRequest.getId(), channelCode);
            return resp;
        }

        /**
         * 获取用户信息用于前台返回
         *
         * @param id          用户id
         * @param channelCode 渠道用户号
         * @return 返回对象
         */
        private MemberBaseInfoResponse getMemberBaseInfo ( long id, String channelCode){

            MemberBaseInfoResponse resp = new MemberBaseInfoResponse();
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient mInfo = this.getMemberInfoById(id, channelCode);
            CustomerInfoType cInfo = mInfo.getMemberQueryInfo().getCustomerInfo();
            String infoIdKey = "";
            String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
            infoIdKey = EncoderHandler.encode("MD5", String.valueOf(mInfo.getMemberQueryInfo().getID()) + key).toUpperCase();

            resp.setLoginKeyInfo(infoIdKey);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setId(mInfo.getMemberQueryInfo().getID());

            resp.setChannelCode(channelCode);
            resp.setSex(cInfo.getSex().value());
            resp.setcLastName(cInfo.getCLastName());
            resp.setcFirstName(cInfo.getCFirstName());
            resp.seteLastName(cInfo.getELastName());
            resp.seteFirstName(cInfo.getEFirstName());
            if (!StringUtil.isNullOrEmpty(cInfo.getCFirstName()) && !StringUtil.isNullOrEmpty(cInfo.getCLastName())) {
                resp.setName(cInfo.getCLastName() + cInfo.getCFirstName());
            } else {
                resp.setName("");
            }
            resp.setMemberID(mInfo.getMemberQueryInfo().getMemberID());
            resp.setBirthDate(cInfo.getBirthDate().toString());

            for (CustomerContactInfoType contactInfo : mInfo.getMemberQueryInfo().getCustomerContactInfo()) {
                if (contactInfo.getContactType().equals(ContactType.MOBILE) && null != contactInfo.getContactValue()) {
                    resp.setMobileNumber(contactInfo.getContactValue());
                }
                if (contactInfo.getContactType().equals(ContactType.EMAIL) && null != contactInfo.getContactValue()) {
                    resp.setEmailAddress(contactInfo.getContactValue());
                }
            }
            List<CustomerCertificateInfo> credInfoList = new ArrayList<>();
            for (CustomerCertificateInfoType cCertInfo : mInfo.getMemberQueryInfo().getCustomerCertificateInfo()) {
                if (cCertInfo.getCertType().equals(CertificateType.IDCard)) {
                    resp.setCertificateNo(cCertInfo.getCertNumber());
                }
                CustomerCertificateInfo cerdInfo = new CustomerCertificateInfo();
                cerdInfo.setCertType(this.convertCertType(cCertInfo.getCertType().value()));
                cerdInfo.setCertNumber(cCertInfo.getCertNumber());
                credInfoList.add(cerdInfo);
            }
            if (CollectionUtils.isNotEmpty(credInfoList)) {
                resp.setCertificateInfos(credInfoList);
            }
            return resp;
        }

        /**
         * 会员地址信息修改
         *
         * @param memberAddrInfoRequest 会员地址信息修改请求
         * @param request
         * @return 会员地址信息返回
         */
        @RequestMapping(value = "/updMemberAddressInfo", method = RequestMethod.POST)
        public MemberAddrInfoResponse updMemberAddressInfo (@RequestBody MemberAddrInfoRequest
        memberAddrInfoRequest, HttpServletRequest request){
            MemberAddrInfoResponse resp = new MemberAddrInfoResponse();
            // 传入请求属性检证
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<MemberAddrInfoRequest>> violations = validator.validate(memberAddrInfoRequest);
            if (CollectionUtils.isNotEmpty(violations)) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String ip = this.getClientIP(request);
            log.info("updMemberAddressInfo================{}", ip);
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(memberAddrInfoRequest.getId()), memberAddrInfoRequest.getLoginKeyInfo(), memberAddrInfoRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = memberAddrInfoRequest.getChannelCode();
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(memberAddrInfoRequest.getId(), channelCode);
            // 信息未追加标记
            boolean noInfoChange = true;
            if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!memberInfo.getMessageHeader().getErrorCode().endsWith("S000")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                return resp;
            }
            if (null != memberInfo.getMemberQueryInfo().getCustomerAddressInfo()) {
                CustomerAddressInfoType addInfo = memberInfo.getMemberQueryInfo().getCustomerAddressInfo();
                if (!addInfo.getCountryCode().equals(memberAddrInfoRequest.getCountryCode())) {
                    noInfoChange = false;
                }
                if (!addInfo.getProvinceCode().equals(memberAddrInfoRequest.getProvinceCode())) {
                    noInfoChange = false;
                }
                if (!addInfo.getAddressCityCode().equals(memberAddrInfoRequest.getAddressCityCode())) {
                    noInfoChange = false;
                }
                if (!addInfo.getAddressContent().equals(memberAddrInfoRequest.getAddressContent())) {
                    noInfoChange = false;
                }
                if (!addInfo.getPostCode().equals(memberAddrInfoRequest.getPostCode())) {
                    noInfoChange = false;
                }
            } else {
                noInfoChange = false;
            }
            if (noInfoChange) {
                // 修改地址失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("地址信息未作修改！");
                return resp;
            }
            String company = memberInfo.getMemberQueryInfo().getCustomerInfo().getCompanyName();
            // 会员地址信息修改
            MemberAddressInfoUpdateResponseForClient updAddressResp = this.updAddressResp(memberAddrInfoRequest, company, channelCode);
            if (!this.respMessageHeaderTypeCheck(updAddressResp.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!updAddressResp.getMessageHeader().getErrorCode().equals("S000")) {
                // 修改地址失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("地址信息修改失败！");
                return resp;
            }

            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfoResp = getMemberInfoById(memberAddrInfoRequest.getId(), channelCode);
            if (!this.respMessageHeaderTypeCheck(memberInfoResp.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!memberInfoResp.getMessageHeader().getErrorCode().equals("S000")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                return resp;
            } else {
                String infoIdKey = "";
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                infoIdKey = EncoderHandler.encode("MD5", String.valueOf(memberInfoResp.getMemberQueryInfo().getID()) + key).toUpperCase();
                resp.setLoginKeyInfo(infoIdKey);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());

                CustomerAddressInfoType addInfoResp = memberInfoResp.getMemberQueryInfo().getCustomerAddressInfo();
                BeanUtils.copyProperties(addInfoResp, resp);
                resp.setId(memberInfoResp.getMemberQueryInfo().getID());
                resp.setMemberID(memberInfoResp.getMemberQueryInfo().getMemberID());
                resp.setChannelCode(channelCode);
            }
            return resp;
        }

        /**
         * 会员证件信息补全
         *
         * @param memberCardInfoRequest 会员证件信息补全请求
         * @param request
         * @return 会员基本信息一览返回
         */
        @RequestMapping(value = "/updCredInfo", method = RequestMethod.POST)
        public MemberBaseInfoResponse updCredInfo (@RequestBody MemberCardInfoRequest
        memberCardInfoRequest, HttpServletRequest request){
            MemberBaseInfoResponse resp = new MemberBaseInfoResponse();
            String ip = this.getClientIP(request);
            String reqId = StringUtil.newGUID() + "_updCredInfo";
            String logStr = JsonUtil.objectToJson(memberCardInfoRequest);
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, logStr);
            // 传入请求属性检证
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<MemberCardInfoRequest>> violations = validator.validate(memberCardInfoRequest);
            if (CollectionUtils.isNotEmpty(violations)) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(memberCardInfoRequest.getId()), memberCardInfoRequest.getLoginKeyInfo(), memberCardInfoRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = memberCardInfoRequest.getChannelCode();
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(memberCardInfoRequest.getId(), channelCode);
            //中文姓
            String cLastName = memberInfo.getMemberQueryInfo().getCustomerInfo().getCLastName();
            //中文名
            String cFirstName = memberInfo.getMemberQueryInfo().getCustomerInfo().getCFirstName();
            //英文姓
            String eLastName = memberInfo.getMemberQueryInfo().getCustomerInfo().getELastName();
            //英文名
            String eFirstName = memberInfo.getMemberQueryInfo().getCustomerInfo().getEFirstName();
            CertificateType certType = null;
            String certTypeStr = memberCardInfoRequest.getCertType();
            //身份证
            if (CertificateType.ID_CARD.value().equals(certTypeStr) || "NI".equals(certTypeStr)) {
                certType = CertificateType.IDCard;
                if (hasIDCard(memberInfo.getMemberQueryInfo().getCustomerCertificateInfo())) {
                    // 修改地址失败返回
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("会员已存在身份证号码，不允许修改！");
                    return resp;
                }
                if (StringUtil.isNullOrEmpty(cLastName) || StringUtil.isNullOrEmpty(cFirstName)) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("请先补全中文姓名！");
                    return resp;
                }
            }
            //护照
            else if (CertificateType.PASSPORT.value().equals(certTypeStr) || "PP".equals(certTypeStr)) {
                certType = CertificateType.Passport;
                if (!checkEName(eLastName, eFirstName)) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("请先补全英文姓名！");
                    return resp;
                }
            }
            //其他
            else {
                certType = CertificateType.Other;
                if (!checkEName(eLastName, eFirstName)) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("请先补全英文姓名！");
                    return resp;
                }
            }
            // 证件信息修改
            CertificateUpdateResponseForClient certificateUpd = crmClient.certificateUpd(memberCardInfoRequest.getId(), channelCode, getClientPwd(channelCode), certType, memberCardInfoRequest.getCertNumber());
            String logStr2 = JsonUtil.objectToJson(certificateUpd);
            log.info("【请求号:{}，IP地址:{}，CRM响应结果：{}】", reqId, ip, logStr2);
            if (!this.respMessageHeaderTypeCheck(certificateUpd.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!certificateUpd.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(certificateUpd.getMessageHeader().getDescription());
            } else {
                if (CertificateType.IDCard.equals(certType)) {
                    //2018-8-21 20:15:18  利用线程异步修改性别，尊称，出生日期
                    taskExecutor.execute(new ModifyThread(request, channelCode, String.valueOf(memberCardInfoRequest.getId()), memberCardInfoRequest.getCertNumber()));
                }
                // 返回信息获取
                resp = this.getMemberBaseInfo(memberCardInfoRequest.getId(), channelCode);
            }
            String logStr3 = JsonUtil.objectToJson(resp);
            log.info("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, logStr3);
            return resp;
        }

        /**
         * 检验英文姓名是否完整
         *
         * @param eLastName
         * @param eFirstName
         * @return
         */
        private boolean checkEName (String eLastName, String eFirstName){
            return StringUtils.isNotBlank(eLastName) && StringUtils.isNotBlank(eFirstName);
        }

        /**
         * 手机号码修改
         *
         * @param updateMobileNumberRequest 手机号码修改请求
         * @param request
         * @return
         */
        @RequestMapping(value = "/updMobileNumber", method = RequestMethod.POST)
        public UpdateMobileNumberResponse updMobileNumber (@RequestBody UpdateMobileNumberRequest
        updateMobileNumberRequest, HttpServletRequest request){
            UpdateMobileNumberResponse resp = new UpdateMobileNumberResponse();
            // 传入请求属性检证
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<UpdateMobileNumberRequest>> violations = validator.validate(updateMobileNumberRequest);
            if (CollectionUtils.isNotEmpty(violations)) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String ip = this.getClientIP(request);
            log.info("updCredInfo================{}", ip);
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(updateMobileNumberRequest.getId()), updateMobileNumberRequest.getLoginKeyInfo(), updateMobileNumberRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = updateMobileNumberRequest.getChannelCode();
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(updateMobileNumberRequest.getId(), channelCode);
            if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!memberInfo.getMessageHeader().getErrorCode().endsWith("S000")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                return resp;
            }

            // 实名认证状态验证
            String realVerifyStatus = memberInfo.getMemberQueryInfo().getRealVerifyStatus();
            if (!"认证成功".equals(realVerifyStatus)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("未经过实名认证，不允许修改手机号！");
                return resp;
            }
            // 消费密码验证
            String memberPassword = memberInfo.getMemberQueryInfo().getMemberPasswordInfo().getMemberPassword();
            if (!memberPassword.equals(getEncryptionPwd(updateMobileNumberRequest.getPassword()))) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("消费密码不正确！");
                return resp;
            }
            List<CustomerContactInfoType> updContactInfoTypeList = new ArrayList<>();
            CustomerContactInfoType updContactInfoType = new CustomerContactInfoType();
            updContactInfoType.setContactType(ContactType.MOBILE);
            List<CustomerContactInfoType> contactInfoTypeList = memberInfo.getMemberQueryInfo().getCustomerContactInfo();
            if (null == contactInfoTypeList) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(EMPTY_USER_INFO);
                return resp;
            }
            for (CustomerContactInfoType contactInfoType : contactInfoTypeList) {
                if (contactInfoType.getContactType() == ContactType.MOBILE) {
                    updContactInfoTypeList.clear();
                    updContactInfoType.setContactValue(updateMobileNumberRequest.getNewMobileNumber());
                    updContactInfoTypeList.add(updContactInfoType);
                    break;
                }
            }
            if (CollectionUtils.isNotEmpty(updContactInfoTypeList)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("手机号未填写，请进行信息补全！");
                return resp;
            }
            MemberContactInfoUpdateResponseForClient contactResp = this.updContactResp(updateMobileNumberRequest.getId(), updContactInfoTypeList, updateMobileNumberRequest.getChannelCode());
            if (!this.respMessageHeaderTypeCheck(contactResp.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
                return resp;
            } else if (!contactResp.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(contactResp.getMessageHeader().getDescription());
                return resp;
            }
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfoResp = getMemberInfoById(updateMobileNumberRequest.getId(), channelCode);
            if (!this.respMessageHeaderTypeCheck(memberInfoResp.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
                return resp;
            } else if (!memberInfoResp.getMessageHeader().getErrorCode().equals("S000")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                return resp;
            } else {
                String infoIdKey = "";
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                infoIdKey = EncoderHandler.encode("MD5", String.valueOf(memberInfoResp.getMemberQueryInfo().getID()) + key).toUpperCase();
                resp.setLoginKeyInfo(infoIdKey);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setId(memberInfoResp.getMemberQueryInfo().getID());
                resp.setMemberID(memberInfoResp.getMemberQueryInfo().getMemberID());
                resp.setChannelCode(channelCode);

                List<CustomerContactInfoType> contactInfoTypeListResp = memberInfoResp.getMemberQueryInfo().getCustomerContactInfo();
                if (null == contactInfoTypeListResp) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(EMPTY_USER_INFO);
                    return resp;
                }
                for (CustomerContactInfoType contactInfoType : contactInfoTypeListResp) {
                    if (contactInfoType.getContactType() == ContactType.MOBILE) {
                        resp.setNewMobileNumber(contactInfoType.getContactValue());
                        break;
                    }
                }
            }
            return resp;
        }

        /**
         * 邮箱修改
         *
         * @param updateEmailRequest 邮箱修改请求
         * @param request
         * @return
         */
        @RequestMapping(value = "/updEmail", method = RequestMethod.POST)
        public UpdateEmailResponse updEmail (@RequestBody UpdateEmailRequest updateEmailRequest, HttpServletRequest
        request){
            UpdateEmailResponse resp = new UpdateEmailResponse();
            // 传入请求属性检证
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<UpdateEmailRequest>> violations = validator.validate(updateEmailRequest);
            if (CollectionUtils.isNotEmpty(violations)) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String ip = this.getClientIP(request);
            log.info("updCredInfo================{}", ip);
            // 认证信息认证
            boolean flag = this.checkKeyInfo(String.valueOf(updateEmailRequest.getId()), updateEmailRequest.getLoginKeyInfo(), updateEmailRequest.getChannelCode());
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = updateEmailRequest.getChannelCode();
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfo = this.getMemberInfoById(updateEmailRequest.getId(), channelCode);
            if (!this.respMessageHeaderTypeCheck(memberInfo.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
            } else if (!memberInfo.getMessageHeader().getErrorCode().endsWith("S000")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                return resp;
            }
            // 实名认证状态验证
            String realVerifyStatus = memberInfo.getMemberQueryInfo().getRealVerifyStatus();
            if (!"认证成功".equals(realVerifyStatus)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("未经过实名认证，不允许修改邮箱！");
                return resp;
            }
            // 消费密码验证
            String memberPassword = memberInfo.getMemberQueryInfo().getMemberPasswordInfo().getMemberPassword();
            if (!memberPassword.equals(getEncryptionPwd(updateEmailRequest.getPassword()))) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("消费密码不正确！");
                return resp;
            }
            // 新旧邮箱是否相同验证
            if (updateEmailRequest.getNewEmail().equals(updateEmailRequest.getOldEmail())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("新旧邮箱不能相同！");
                return resp;
            }
            if (!this.checkEmail(updateEmailRequest.getNewEmail())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("修改邮箱格式错误！");
                return resp;
            }
            List<CustomerContactInfoType> updContactInfoTypeList = new ArrayList<>();
            CustomerContactInfoType updContactInfoType = new CustomerContactInfoType();
            updContactInfoType.setContactType(ContactType.EMAIL);
            List<CustomerContactInfoType> contactInfoTypeList = memberInfo.getMemberQueryInfo().getCustomerContactInfo();
            if (null == contactInfoTypeList) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(EMPTY_USER_INFO);
                return resp;
            }
            String emailNumber = "";
            for (CustomerContactInfoType contactInfoType : contactInfoTypeList) {
                if (contactInfoType.getContactType() == ContactType.EMAIL) {
                    updContactInfoTypeList.clear();
                    emailNumber = contactInfoType.getContactValue();
                    updContactInfoType.setContactValue(updateEmailRequest.getNewEmail());
                    updContactInfoTypeList.add(updContactInfoType);
                    break;
                }
            }
            if (StringUtil.isNullOrEmpty(emailNumber) || CollectionUtils.isEmpty(updContactInfoTypeList)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("邮箱未填写，请进行信息补全！");
                return resp;
            }
            if (!emailNumber.equals(updateEmailRequest.getOldEmail())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("旧邮箱错误！");
                return resp;
            }
            MemberContactInfoUpdateResponseForClient contactResp = this.updContactResp(updateEmailRequest.getId(), updContactInfoTypeList, updateEmailRequest.getChannelCode());
            if (!this.respMessageHeaderTypeCheck(contactResp.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
                return resp;
            } else if (!contactResp.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(contactResp.getMessageHeader().getErrorCode());
                resp.setErrorInfo(contactResp.getMessageHeader().getDescription());
                return resp;
            }
            // 根据id获取用户信息
            MemberInfoQueryResponseForClient memberInfoResp = getMemberInfoById(updateEmailRequest.getId(), channelCode);
            if (!this.respMessageHeaderTypeCheck(memberInfoResp.getMessageHeader())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(OPERATION_FAILED);
                return resp;
            } else if (!memberInfoResp.getMessageHeader().getErrorCode().equals("S000")) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(QUERY_USER_INFO_FAILED);
                return resp;
            } else {
                String infoIdKey = "";
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                infoIdKey = EncoderHandler.encode("MD5", String.valueOf(memberInfoResp.getMemberQueryInfo().getID()) + key).toUpperCase();
                resp.setLoginKeyInfo(infoIdKey);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setId(memberInfoResp.getMemberQueryInfo().getID());
                resp.setMemberID(memberInfoResp.getMemberQueryInfo().getMemberID());
                resp.setChannelCode(channelCode);

                List<CustomerContactInfoType> contactInfoTypeListResp = memberInfoResp.getMemberQueryInfo().getCustomerContactInfo();
                if (null == contactInfoTypeListResp) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(EMPTY_USER_INFO);
                    return resp;
                }
                for (CustomerContactInfoType contactInfoType : contactInfoTypeListResp) {
                    if (contactInfoType.getContactType() == ContactType.EMAIL) {
                        resp.setNewEmail(contactInfoType.getContactValue());
                        break;
                    }
                }
            }
            return resp;
        }

        /**
         * 短信验证码验证  新的验证
         *
         * @param verifyCodeReq
         * @param request
         * @return
         */
        @RequestMapping(value = "/verifyCode", method = RequestMethod.POST)
        public VerifyVerificationCodeResp verifyVerificationCode (@RequestBody VerifyCodeReq
        verifyCodeReq, HttpServletRequest request){
            VerifyVerificationCodeResp resp = new VerifyVerificationCodeResp();
            if (!SourceType.checkType(verifyCodeReq.getType())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("非法的参数类型");
                return resp;
            }
            String veryCode = verifyCodeReq.getVerifyCode();
            if (!checkVeriCode("SMS", verifyCodeReq.getMobiles(), verifyCodeReq.getType(), veryCode)) {
                String errorInfo = VALID_CODE_EXPIRED;
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(errorInfo);
                return resp;
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            }
            return resp;
        }

        //首付游信誉额度
        @InterfaceLog
        @RequestMapping(value = "myLimit", method = RequestMethod.POST)
        public BaseResp queryMyShouFuYouLimit
        (@RequestBody @Validated CreditLimitReq < UserInfoNoMust > creditLimitReq, BindingResult
        bindingResult, HttpServletRequest request){
            String reqId = MdcUtils.getRequestId();
            String channelCode = creditLimitReq.getChannelCode();
            BaseResp baseResp = new BaseResp();
            // 传入请求属性检证
            if (bindingResult.hasErrors()) {
                // 检证失败返回
                baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                baseResp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return baseResp;
            }
            if ("Y".equals(handConfig.getXinfeiChange())) {
                DocContent docContent = new DocContent();
                docContent.setDocType(DocTypeEnum.URL.getType());
                docContent.setDocResult(handConfig.getXinfeiUrl());
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                baseResp.setObjData(docContent);
                return baseResp;
            } else {
                //登录状态下的执行逻辑
                String extraParam = "";//自定义参数
                UserInfoNoMust userInfo = creditLimitReq.getRequest();
                String loginKeyInfo = "";
                String ffpId = "";
                String ffpCardNo = "";
                if (userInfo != null) {
                    ffpCardNo = userInfo.getFfpCardNo();
                    ffpId = userInfo.getFfpId();
                    loginKeyInfo = userInfo.getLoginKeyInfo();
                } else {
                    ffpCardNo = creditLimitReq.getFfpCardNo();
                    ffpId = String.valueOf(creditLimitReq.getFfpId());
                    loginKeyInfo = creditLimitReq.getLoginKeyInfo();
                }
                PtCreditLimitReq ptReq;
                if ((!StringUtil.isNullOrEmpty(loginKeyInfo)) && (!StringUtil.isNullOrEmpty(ffpId)) && (!StringUtil.isNullOrEmpty(ffpCardNo))) {
                    // 认证信息认证
                    boolean flag = this.checkKeyInfo(ffpId, loginKeyInfo, creditLimitReq.getChannelCode());
                    if (!flag) {
                        // 认证失败返回
                        baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                        baseResp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                        baseResp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                        return baseResp;
                    }
                    //查询相关会员信息
                    String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName,
                            MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                            MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
                    PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(ffpCardNo, ffpId, request, channelCode, items);
                    PtCRMResponse<PtMemberDetail> ptCRMResponse = this.memberService.memberDetail(ptApiCRMRequest);
                    if (ptCRMResponse.getCode() != 0) {
                        baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                        baseResp.setErrorInfo(ptCRMResponse.getMsg());
                        baseResp.setResultInfo(ptCRMResponse.getMsg());
                        return baseResp;
                    }
                    PtMemberDetail ptMemberDetail = ptCRMResponse.getData();
                    MemberContactSoaModel mobile = CRMReqUtil.getContactInfo(ptMemberDetail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
                    String name = CRMReqUtil.getChinaName(ptMemberDetail.getBasicInfo());
                    MemberCertificateSoaModelV2 idCard = CRMReqUtil.getCertificateInfo(ptMemberDetail.getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
                    if (StringUtil.isNullOrEmpty(name)) {
                        baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                        baseResp.setErrorInfo("姓名信息不能为空!请先补全信息!");
                        baseResp.setResultInfo("姓名信息不能为空!请先补全信息!");
                        return baseResp;
                    }
                    if (mobile == null || StringUtil.isNullOrEmpty(mobile.getContactNumber())) {
                        baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                        baseResp.setErrorInfo("手机信息不能为空!请先补全信息!");
                        baseResp.setResultInfo("手机信息不能为空!请先补全信息!");
                        return baseResp;
                    }
                    if (idCard == null || StringUtil.isNullOrEmpty(idCard.getCertificateNumber())) {
                        baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                        baseResp.setErrorInfo("身份信息不能为空!请先补全信息!");
                        baseResp.setResultInfo("身份信息不能为空!请先补全信息!");
                        return baseResp;
                    }
                    ptReq = new PtCreditLimitReq(creditLimitReq.getChannelCode(), mobile.getContactNumber(), name, idCard.getCertificateNumber(), extraParam);
                } else {
                    ptReq = new PtCreditLimitReq(creditLimitReq.getChannelCode(), extraParam);
                }
                String utmSource = request.getParameter("utm_source");
                ptReq.setUtmSource(utmSource == null ? "" : utmSource);
                //ptReq.setGatewayNo("40501");//测试
                ptReq.setGatewayNo("40508");
                Map<String, String> parametersMap = ptReq.getParaMap();
                HttpResult result = doPayPost(HandlerConstants.LIMIT_PAY_BY_SHOUFUYOU, parametersMap);
                if (null != result && result.isResult()) {
                    try {
                        log.info("{}返回结果:{}", reqId, result.getResponse());
                        String resultStr = result.getResponse().trim();
                        if (!StringUtil.isNullOrEmpty(resultStr)) {
                            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                            baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            DocContent docContent = new DocContent();
                            docContent.setDocType(DocTypeEnum.FORM.getType());
                            docContent.setDocResult(resultStr);
                            baseResp.setObjData(docContent);
                        } else {
                            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                            baseResp.setErrorInfo("返回结果空");
                            baseResp.setResultInfo("返回结果空");
                        }
                        return baseResp;
                    } catch (Exception e) {
                        log.error("{}查询额度异常:{}", reqId, e.getMessage());
                        baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                        baseResp.setErrorInfo("查询额度异常返回结果空");
                        baseResp.setResultInfo("查询额度异常返回结果空");
                        return baseResp;
                    }
                } else {
                    baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                    baseResp.setErrorInfo(WSEnum.ERROR.getResultInfo());
                    baseResp.setResultInfo(WSEnum.ERROR.getResultInfo());
                    return baseResp;
                }
            }
        }

        /**
         * 新版crm查询积分余额接口，无需实名认证
         *
         * @param memberRemainScoreRequest
         * @param request
         * @return
         */
        @ApiOperation(value = "查询积分余额", notes = "新版crm查询积分余额接口，无需实名认证")
        @RequestMapping(value = "/queryMemberRemainScore", method = RequestMethod.POST)
        public MemberRemainScoreResp queryMemberRemainScore (@RequestBody @Validated MemberRemainScoreRequest
        memberRemainScoreRequest, BindingResult bindingResult, HttpServletRequest request){
            MemberRemainScoreResp resp = new MemberRemainScoreResp();
            String ip = this.getClientIP(request);
            // 传入请求属性检证
            if (bindingResult.hasErrors()) {
                // 检证失败返回
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            // 认证信息认证
            String channelCode = memberRemainScoreRequest.getChannelCode();
            boolean flag = this.checkKeyInfo(String.valueOf(memberRemainScoreRequest.getFfpId()), memberRemainScoreRequest.getLoginKeyInfo(), channelCode);
            if (!flag) {
                // 认证失败返回
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            ClientInfo clientInfo = initClientInfo(request, channelCode, String.valueOf(memberRemainScoreRequest.getFfpId()), memberRemainScoreRequest.getMemberCardNo());
            Integer availableMiles = 0;
            Integer freezeMiles = 0;
            try {
                //查询积分余额
                String clientPwd = getClientPwd(channelCode);
                PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildCommReq(channelCode, clientPwd, String.valueOf(memberRemainScoreRequest.getFfpId()), "", ip);
                MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
                String[] items = new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName};
                mileageAccountQueryRequest.setMemberCardNo(memberRemainScoreRequest.getMemberCardNo());
                mileageAccountQueryRequest.setRequestItems(items);
                ptApiCRMRequest.setData(mileageAccountQueryRequest);
                PtCRMResponse<MileageAccountQueryResponse> ptCRMResponse = memberService.mileageAccountQuery(ptApiCRMRequest);
                if (ptCRMResponse.getCode() == 0) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    //可用积分余额
                    availableMiles = ptCRMResponse.getData().getTotalBill().getAvailableMiles() == null ? 0 : ptCRMResponse.getData().getTotalBill().getAvailableMiles();
                    freezeMiles = ptCRMResponse.getData().getTotalBill().getFreezeMiles() == null ? 0 : ptCRMResponse.getData().getTotalBill().getFreezeMiles();
                    resp.setPoint(String.valueOf(availableMiles + freezeMiles));
                    resp.setFreezePoint(String.valueOf(freezeMiles));
                /*if (orderService.checkScoreUseRule(clientInfo,channelCode,null)) {
                    resp.setVerifyCode(WSEnum.NO_REAL_NAME.getResultCode());
                }*/
                    PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildCommReq(channelCode, clientPwd, String.valueOf(memberRemainScoreRequest.getFfpId()), "", ip);
                    items = new String[]{MemberDetailRequestItemsEnum.STATEINFO.eName};
                    PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
                    ptMemberDetailRequest.setCardNO(memberRemainScoreRequest.getMemberCardNo());
                    ptMemberDetailRequest.setRequestItems(items);
                    ptApiRequest.setData(ptMemberDetailRequest);
                    PtCRMResponse<PtMemberDetail> ptCRMMemberResponse = memberService.memberDetail(ptApiRequest);
                    if (ptCRMMemberResponse.getCode() == 0) {
                        //小额免密开通状态
                        resp.setSmallExemptPwdStatus("Y".equals(ptCRMMemberResponse.getData().getStateInfo().getIsSmallExemptPwd()));
                        resp.setFreeScoreLimit(handConfig.getScoreFreeLimit());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(resp.getMessage());
                }
            } catch (CommonException commonException) {
                if (WSEnum.DO_REAL_NAME.getResultCode().equals(commonException.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setVerifyCode(WSEnum.NO_REAL_NAME.getResultCode());
                    resp.setErrorInfo("非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，请前往实名认证");
                    resp.setPoint(String.valueOf(availableMiles + freezeMiles));
                    resp.setFreezePoint(String.valueOf(freezeMiles));
                } else {
                    resp.setResultCode(commonException.getResultCode());
                    resp.setErrorInfo(commonException.getErrorMsg());
                }
                return resp;
            } catch (Exception e) {
                log.error("请求:{},错误信息:", MdcUtils.getRequestId(), e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
                return resp;
            }
            return resp;
        }

        /**
         * 邮箱格式验证
         *
         * @param email
         * @return
         */
        private boolean checkEmail (String email){
            String check = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
            Pattern regex = Pattern.compile(check);
            Matcher matcher = regex.matcher(email);
            return matcher.matches();
        }

        /**
         * 会员是否存在身份证号码
         *
         * @param certList
         * @return
         */
        private boolean hasIDCard (List < CustomerCertificateInfoType > certList) {
            if (null == certList) {
                return false;
            }
            for (CustomerCertificateInfoType cert : certList) {
                if (cert.getCertType().equals(CertificateType.IDCard) && !StringUtil.isNullOrEmpty(cert.getCertNumber())) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 返回结果头部检证
         *
         * @param messageHeader 返回结果头部
         * @return 检证结果 true为通过
         */
        private boolean respMessageHeaderTypeCheck (ResponseMessageHeaderType messageHeader){
            return null != messageHeader.getErrorCode();
        }


        /**
         * 会员联系方式补全
         *
         * @param id               用户id
         * @param contactInfoTypes 补全联系方式请求
         * @param channelCode      渠道用户号
         * @return 会员联系方式补全结果
         */
        private MemberContactInfoUpdateResponseForClient updContactResp (Long
        id, List < CustomerContactInfoType > contactInfoTypes, String channelCode){
            return crmClient.updateContacts(id, contactInfoTypes, channelCode, getClientPwd(channelCode));
        }

        /**
         * 会员基本信息修改
         *
         * @param id          用户id
         * @param channelCode 渠道用户号
         * @param addParam    修改参数
         * @return 会员基本信息修改返回
         */
        private MemberBaseInfoUpdateResponseForClient updMemberBaseInfo ( long id, String
        channelCode, Map < String, Object > addParam){
            return crmClient.updMemberBaseInfo(id, channelCode, getClientPwd(channelCode), addParam);
        }

        /**
         * 会员地址信息修改
         *
         * @param memberAddrInfoRequest 地址信息修改请求
         * @param channelCode           渠道用户号
         * @return 地址信息修改结果返回
         */
        private MemberAddressInfoUpdateResponseForClient updAddressResp (MemberAddrInfoRequest
        memberAddrInfoRequest, String company, String channelCode){
            return crmClient.updMemberAddressInfo(memberAddrInfoRequest.getId(), channelCode, getClientPwd(channelCode), AddressType.HOME, memberAddrInfoRequest.getCountryCode(), memberAddrInfoRequest.getProvinceCode(), memberAddrInfoRequest.getAddressCityCode(), memberAddrInfoRequest.getAddressContent(), memberAddrInfoRequest.getPostCode(), company);
        }

        /**
         * 根据id获取用户信息
         *
         * @param id          用户id
         * @param channelCode 渠道用户号
         * @return 用户信息一览
         */
        private MemberInfoQueryResponseForClient getMemberInfoById ( long id, String channelCode){
            return crmClient.memberInfoquery(id, channelCode, getClientPwd(channelCode));
        }

        /**
         * 获得加密密码，MD5加密大写、加密大写、加juneyaoair加密大写
         * 2015年9月16日修改
         *
         * @param pwd
         * @return
         */
        private String getEncryptionPwd (String pwd){
            pwd = EncoderHandler.encodeByMD5(pwd).toUpperCase();
            pwd = EncoderHandler.encodeByMD5(pwd).toUpperCase();
            pwd = EncoderHandler.encodeByMD5(pwd + "juneyaoair").toUpperCase();
            return pwd;
        }

        @Autowired
        private CrmWSClient crmClient;

        @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
        private IRedisService apiRedisService;

        @Autowired
        private IAppCustomerServiceMongo appCustomerServiceMongo;

        @Autowired
        private IDeviceServiceMongo deviceServiceMongo;

        @Autowired
        private ThreadPoolTaskExecutor taskExecutor;

        /**
         * 设备信息关联
         */
        class DeviceThread implements Runnable {
            private String reqId;
            private AppCustomer appCustomer;
            private DeviceInfo deviceInfo;

            public DeviceThread(AppCustomer appCustomer, DeviceInfo deviceInfo) {
                this.appCustomer = appCustomer;
                this.deviceInfo = deviceInfo;
            }

            public DeviceThread(String reqId, AppCustomer appCustomer, DeviceInfo deviceInfo) {
                this.reqId = reqId;
                this.appCustomer = appCustomer;
                this.deviceInfo = deviceInfo;
            }

            public AppCustomer getAppCustomer() {
                return appCustomer;
            }

            public void setAppCustomer(AppCustomer appCustomer) {
                this.appCustomer = appCustomer;
            }

            public DeviceInfo getDeviceInfo() {
                return deviceInfo;
            }

            public void setDeviceInfo(DeviceInfo deviceInfo) {
                this.deviceInfo = deviceInfo;
            }

            public String getReqId() {
                return reqId;
            }

            public void setReqId(String reqId) {
                this.reqId = reqId;
            }

            @Override
            public void run() {
                String logStr = JsonUtil.objectToJson(appCustomer);
                log.info("{}利用线程{}mongo保存设备用户关联{}", reqId, Thread.currentThread().getName(), logStr);
                //用户设备信息
                appCustomerServiceMongo.addOrUpdateAppCustomer(appCustomer);
                //设备信息
                deviceServiceMongo.addDevice(deviceInfo);
                log.info("{}利用线程{}mongo保存操作完毕", reqId, Thread.currentThread().getName());

            }
        }

        /**
         * 根据身份证修改性别，尊称，出生日期
         */
        class ModifyThread implements Runnable {
            private HttpServletRequest request;
            private String channelCode;
            private String ffpId;
            private String certNo;

            public ModifyThread(HttpServletRequest request, String channelCode, String ffpId, String certNo) {
                this.request = request;
                this.channelCode = channelCode;
                this.ffpId = ffpId;
                this.certNo = certNo;
            }

            @Override
            public void run() {
                String birthDate = CertUtil.certNoToDate(certNo);
                String sex = CertUtil.checkSex(certNo);
                PtModifyCustomerInfoRequest ptModifyCustomerInfoRequest = new PtModifyCustomerInfoRequest();
                Date birDate = DateUtils.toDate(birthDate, "yyyy-MM-dd");
                ptModifyCustomerInfoRequest.setBirthday(birDate.getTime());
                ptModifyCustomerInfoRequest.setSex(sex);
                ptModifyCustomerInfoRequest.setSalutationCode("M".equals(sex) ? SalutationEnum.MR.geteName() : SalutationEnum.MS.geteName());
                PtApiCRMRequest<PtModifyCustomerInfoRequest> ptApiRequest = buildCommReq(request, channelCode, ffpId, "");
                ptApiRequest.setData(ptModifyCustomerInfoRequest);
                PtCRMResponse modifyResponse = memberService.modifyCustomerInfo(ptApiRequest);
                String logStr = JsonUtil.objectToJson(modifyResponse);
                log.info("[CRM利用线程{}异步更新性别，出生日期，尊称；结果：{}]", Thread.currentThread().getName(), logStr);
            }
        }
    }
