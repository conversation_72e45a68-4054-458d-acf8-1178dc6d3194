package com.juneyaoair.mobile.handler.controller.v2;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.EnvEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.*;
import com.juneyaoair.appenum.common.AirCompanyEnum;
import com.juneyaoair.appenum.common.PlatFormEnum;
import com.juneyaoair.appenum.flight.FareSourceEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.BrandRightStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.av.common.FlightInfoComb;
import com.juneyaoair.baseclass.av.common.InterBrandRightInfo;
import com.juneyaoair.baseclass.av.common.LabelInfo;
import com.juneyaoair.baseclass.av.common.SpecialFareQuery;
import com.juneyaoair.baseclass.basicsys.request.Antifraud;
import com.juneyaoair.baseclass.basicsys.response.*;
import com.juneyaoair.baseclass.common.base.Advertisement;
import com.juneyaoair.baseclass.common.base.ThemeCoupon;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.base.UserInfoNoMust;
import com.juneyaoair.baseclass.common.request.BaseQueryFTRideMPConRequest;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseQueryFTRideMPConResponse;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.flight.common.FlightReminder;
import com.juneyaoair.baseclass.flight.common.FlightReminderV2;
import com.juneyaoair.baseclass.flight.request.QueryFirstTripMemberParam;
import com.juneyaoair.baseclass.flight.response.NearFlightRecommend;
import com.juneyaoair.baseclass.request.av.MultipleFlightFareRequestSegment;
import com.juneyaoair.baseclass.request.av.QueryFTRideMPCertDto;
import com.juneyaoair.baseclass.request.av.QueryFlightFareReq;
import com.juneyaoair.baseclass.request.av.QueryMultipleFlightFareRequest;
import com.juneyaoair.baseclass.request.console.PictureRequest;
import com.juneyaoair.baseclass.response.av.*;
import com.juneyaoair.baseclass.unlimit.UnlimitedCard2Config;
import com.juneyaoair.bo.FlightQueryBO;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.config.bean.TempleteConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.FraudApiInvoker;
import com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV2;
import com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV3;
import com.juneyaoair.mobile.handler.controller.v2.util.AvObjectConvertCommon;
import com.juneyaoair.mobile.handler.controller.v2.util.MultipleFlightFareConvert;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.adtunlimitedfly.PtQueryUnlimitedFlyRequest;
import com.juneyaoair.thirdentity.av.comm.FareTaxInfo;
import com.juneyaoair.thirdentity.av.comm.V2FlightInfo;
import com.juneyaoair.thirdentity.av.request.PtFlightFareMultiRequestDto;
import com.juneyaoair.thirdentity.av.request.PtMultipleFlightQuerySegment;
import com.juneyaoair.thirdentity.av.request.PtQueryFlightFareRequest;
import com.juneyaoair.thirdentity.av.request.QueryTransferCityRequest;
import com.juneyaoair.thirdentity.av.response.FlightSimpleResultDto;
import com.juneyaoair.thirdentity.av.response.PtMultipleFlightFareResponse;
import com.juneyaoair.thirdentity.av.response.PtQueryFlightFareResponse;
import com.juneyaoair.thirdentity.av.response.QueryTransferCityResponse;
import com.juneyaoair.thirdentity.basic.HotelProductRespDTO;
import com.juneyaoair.thirdentity.brandright.BrandRightDetailVo;
import com.juneyaoair.thirdentity.brandright.request.BrandRightQueryRequest;
import com.juneyaoair.thirdentity.brandright.response.BrandRightQueryResponse;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyBindRecord;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyV2BindRecord;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtPdmRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtPdmResponse;
import com.juneyaoair.thirdentity.member.request.MarketActivityRuleQueryRequest;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.*;
import com.juneyaoair.thirdentity.request.av.Segment;
import com.juneyaoair.thirdentity.salecoupon.response.PtQueryUpgradeCardResponse;
import com.juneyaoair.thirdentity.tongdun.FinalDecisionEnum;
import com.juneyaoair.thirdentity.tongdun.response.FraudApiResponse;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.common.LogLevelEnum;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.mortbay.util.SingletonList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import static com.juneyaoair.mobile.handler.controller.v2.V2MyRightCouponController.toThemeModelMap;

/**
 * <AUTHOR>
 * @description
 * @date 2019/1/7  15:30.
 */
@Api(value = "航班查询管理", tags = "航班查询管理")
@RequestMapping("/v2/flight")
@RestController
public class V2FlightController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private TempleteConfig templeteConfig;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private IUnlimitedFlyService unlimitedFlyService;
    @Autowired
    private IMemberCacheService memberCacheService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private RouteLabelService routeLabelService;
    @Autowired
    private FlightAggrService flightAggrService;

    private static final String CITY_CODE = "cityCode";
    private static final String MIN_PRICE = "minPrice";


    //航班查询
    @ApiIgnore
    @ApiOperation(value = "航班查询", notes = "航班查询", tags = "航班查询管理")
    @RequestMapping(value = "/AvFare", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public QueryFlightFareResp queryFlightFare(@RequestBody @Validated QueryFlightFareReq fareReq, BindingResult bindingResult, HttpServletRequest request) {
        QueryFlightFareResp resp = new QueryFlightFareResp();
        String ip = this.getClientIP(request);
        String headClientVer = request.getHeader(HandlerConstants.CLIENT_VERSION);
        String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String channelCode = fareReq.getChannelCode();
        String headChannelCode = StringUtils.isNotEmpty(request.getHeader(HandlerConstants.TOKEN_CHANNELCODE))
                ? request.getHeader(HandlerConstants.TOKEN_CHANNELCODE) : channelCode;
        String userNo = getChannelInfo(channelCode, "10");
        //基本参数检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        AtomicBoolean useSignFlag = new AtomicBoolean(true);
        //不为空时需要验证key值
        if (StringUtils.isNotBlank(fareReq.getFfpId())) {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(fareReq.getFfpId(), fareReq.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
                return resp;
            }
        }
        //前端参数存在“null”,需要特殊处理
        if ("null".equals(fareReq.getSendAirportCode()) || "undefined".equals(fareReq.getSendAirportCode())) {
            fareReq.setSendAirportCode(null);
        }
        if ("null".equals(fareReq.getArrAirportCode()) || "undefined".equals(fareReq.getArrAirportCode())) {
            fareReq.setArrAirportCode(null);
        }
        if (StringUtils.isNotBlank(fareReq.getQueryType())) {
            FlightQueryTypeEnum queryTypeEnum = FlightQueryTypeEnum.checkEnum(fareReq.getQueryType());
            if (queryTypeEnum == null) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("超出指定范围的数值");
                return resp;
            }
            //检查请求参数对应的查询条件
            if (!checkFlightQueryType(fareReq, handConfig, useSignFlag)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo("无符合条件的航班");
                return resp;
            }
        }
        boolean useSign = useSignFlag.get();
        //验证请求参数是否篡改 APP5.8之后
        if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= 58000 && useSign) {
            if (StringUtil.isNullOrEmpty(fareReq.getSign())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS_925_3.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS_925_3.getResultInfo());
                return resp;
            }
            //签名验证
            /*boolean checkFlag = checkSign(fareReq);
            if (!checkFlag) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS_925_3.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS_925_3.getResultInfo());
                return resp;
            }*/
        }
        //风控策略控制
        boolean isAccess = windControl(headChannelCode, headClientVer, versionCode, platform, ip, fareReq, request, resp);
        if (!isAccess) {
            log.error("{},参数:{},航班查询IP:{},返回信息:{}", MdcUtils.getRequestId(), JsonUtil.objectToJson(fareReq), ip, JsonUtil.objectToJson(resp));
            return resp;
        }
        //航班查询条件
        PtQueryFlightFareRequest queryFlightFareRequest = createQueryFareRequest(channelCode, userNo, fareReq);
        //MWEB渠道的航班查询渠道转化
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)) {
            queryFlightFareRequest.setChannelCode(handConfig.getMwebOrderChannel());
            queryFlightFareRequest.setUserNo(getChannelInfo(handConfig.getMwebOrderChannel(), "10"));
        }
        //5.4.1之后的国际运价支持addOn查询方式
        int ver = VersionNoUtil.toMVerInt(versionCode);
        // 是否使用searchone接口
        AtomicBoolean useFareV30 = new AtomicBoolean(false);
        //2021-07-19 是否使用简化版运价查询
        AtomicBoolean useFareSimple = new AtomicBoolean(false);
        //封装app应用信息
        ClientInfo clientInfo = initClientInfo(request, channelCode, fareReq.getFfpId(), fareReq.getFfpCardNo());
        try {
            //根据不同的前端版本选择不同的请求URL
            String url = genQueryUrl(fareReq, queryFlightFareRequest, headChannelCode, useFareV30);
            resp = dealFlightResponse(queryFlightFareRequest, fareReq, url, useFareV30, useFareSimple, channelCode, ip, ver, clientInfo);
            // 国内非免票查询航班售罄情况需要查询联程航班信息
            if (HandlerConstants.FLIGHT_INTER_D.equals(fareReq.getTripType())
                    && (StringUtils.isBlank(fareReq.getQueryType()) ||
                    FlightQueryTypeEnum.NORMAL.getType().equals(fareReq.getQueryType()) ||
                    FlightQueryTypeEnum.PACKAGE_CABIN.getType().equals(fareReq.getQueryType()))
                    && isSoldOut(resp)) {
                boolean queryTransferFlight = true;
                //默认中转次数为1
                queryFlightFareRequest.setTransferTimes(1);
                //MOBILE 5.9.0支持国内组合运价方式
                if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
                    queryFlightFareRequest.setTransferTimes(2);
                }
                //
                if (queryTransferFlight) {
                    //中转信息查询
                    queryFlightFareRequest.setFareSource(FareSourceEnum.HO.getFareSource());
                    url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONE;
                    useFareV30.set(true);
                    // 上线控制
                    // useTransferCityFlag
                    if ("Y".equals(handConfig.getUseTransferCityFlag())) {
                        // 由中转系统查询中转城市 至queryFlightFareRequest
                        queryFlightTranInfoByTransferSys(fareReq, queryFlightFareRequest, ip);
                    } else {
                        queryFlightTranInfo(fareReq, queryFlightFareRequest, ip);
                    }
                    //有中转信息的 使用吉祥组合运价
                    if (CollectionUtils.isNotEmpty(queryFlightFareRequest.getTransferCitys())) {
                        QueryFlightFareResp resp2 = dealFlightResponse(queryFlightFareRequest, fareReq, url, useFareV30, useFareSimple, channelCode, ip, ver, clientInfo);
                        if (!isSoldOut(resp2)) {
                            resp = resp2;
                        }
                    }
                }
            }
            try {
                // 直达航线展示酒店广告
                if (HandlerConstants.ROUTE_TYPE_OW.equals(fareReq.getFlightType())
                        && (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode)
                        && ((PlatFormEnum.ANDROID.getSystemCode().equalsIgnoreCase(platform) && VersionNoUtil.toMVerInt(versionCode) >= 63000)/* Android Version check */
                        || (PlatFormEnum.IOS.getSystemCode().equalsIgnoreCase(platform) && VersionNoUtil.toMVerInt(versionCode) >= 63000)) /* IOS Version check */
                        || ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)/* Mweb Version check */)) {
                    List<HotelProductRespDTO> hotelProductRespDTOS = basicService
                            .queryHotelProductList(headChannelCode, ip, "1", fareReq.getDepartureDate(), Collections.singletonList(fareReq.getArrCode()));
                    List<LabelInfo> flightAdvertisementList = new ArrayList<>();
                    for (HotelProductRespDTO hotelProductRespDTO : hotelProductRespDTOS) {
                        LabelInfo labelInfo = new LabelInfo();
                        labelInfo.setPictureUrl(hotelProductRespDTO.getBannerUrl());
                        labelInfo.setLabelName(hotelProductRespDTO.getTitle());
                        labelInfo.setLabelDetail(hotelProductRespDTO.getSecondTitle());
                        labelInfo.setLabelUrl(hotelProductRespDTO.getUrl());
                        flightAdvertisementList.add(labelInfo);
                    }
                    resp.setFlightAdvertisement(flightAdvertisementList);
                }
            } catch (Exception e) {
                this.log.error("查询航班广告出现异常", e);
            }
            // 设置航线类型
            String segmentType = localCacheService.getSegment(fareReq.getSendCode(), fareReq.getArrCode()).getSegmentType();
            resp.setSegmentType(segmentType);
            // 国际票是否实名开关开关
            resp.setPersonInternalRealNameFlag((HandlerConstants.TRIP_TYPE_I.equals(resp.getSegmentType()) || HandlerConstants.TRIP_TYPE_R.equals(resp.getSegmentType())) && "Y".equals(handConfig.getPersonInternalRealNameFlag()));
            // 国内票是否实名开关开关
            resp.setPersonDomesticRealNameFlag(HandlerConstants.TRIP_TYPE_D.equals(resp.getSegmentType()) && "Y".equals(handConfig.getPersonDomesticRealNameFlag()));
            // 2022-06-30 前端BUG兼容处理无任何实际价值
            if (CollectionUtils.isNotEmpty(resp.getTransferFlightInfoList())) {
                Map<String, String> districtTipsTextMap = handConfig.getDistrictTipsTextMap();
                for (FlightInfoComb flightInfoComb : resp.getTransferFlightInfoList()) {
                    if (CollectionUtils.isNotEmpty(flightInfoComb.getAdtCabinFareList())) {
                        for (CabinFare cabinFare : flightInfoComb.getAdtCabinFareList()) {
                            if (null != cabinFare) {
                                cabinFare.setSegmentType(segmentType);
                                String returnFeeDesc = handConfig.getReturnFeeDescMap().get(cabinFare.getCabinComb());
                                cabinFare.setReturnFeeDesc(returnFeeDesc);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(flightInfoComb.getCombFlightInfoList())) {
                        boolean busFlight = flightInfoComb.getAdtCabinFareList().stream()
                                .filter(cabinFare -> StringUtils.isNotBlank(cabinFare.getFareBasis()))
                                .anyMatch(cabinFare -> cabinFare.getFareBasis().endsWith(FareBasisEnum.BUS_FARE.getFareBasisCode()));
                        if (busFlight) {
                            String busFlightTips = districtTipsTextMap.get(HandlerConstants.BUS_FARE);
                            if (StringUtils.isNotBlank(busFlightTips)) {
                                flightInfoComb.setDistrictTipsText(busFlightTips);
                            }
                        } else {
                            for (FlightInfo flightInfo : flightInfoComb.getCombFlightInfoList()) {
                                String arrTips = districtTipsTextMap.get(flightInfo.getArrAirport());
                                String depTips = districtTipsTextMap.get(flightInfo.getDepAirport());
                                if (StringUtils.isNotBlank(depTips)) {
                                    flightInfoComb.setDistrictTipsText(depTips);
                                }
                                if (StringUtils.isNotBlank(arrTips)) {
                                    flightInfoComb.setDistrictTipsText(arrTips);
                                }

                            }
                        }

                    }

                }
            }
            //版本控制
            mobileVersion(headChannelCode, versionCode, resp, fareReq.getQueryType());
        } catch (Exception e) {
            this.log.error("{},参数:{},错误信息：", MdcUtils.getRequestId(), JsonUtil.objectToJson(fareReq), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(e.getMessage());
        }
        if (handConfig.isSearchTheme()) {
            basicService.searchThemeFlightInfoList(fareReq, resp);
        }
        // 国内航线舱位展示排序顺序
        this.sortFlightInfoAndCabinList(resp);
        return resp;
    }

    /**
     * 从中转系统中查询中转城市信息
     *
     * @param fareReq
     * @param queryFlightFareRequest
     * @param ip
     */
    private void queryFlightTranInfoByTransferSys(QueryFlightFareReq fareReq, PtQueryFlightFareRequest queryFlightFareRequest, String ip) {

        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_QUERY_TRANSFER_CITYS;
        QueryTransferCityRequest transferCityRequest = new QueryTransferCityRequest();
        transferCityRequest.setVersion("10");
        transferCityRequest.setChannelCode("MOBILE");
        transferCityRequest.setDepCity(fareReq.getSendCode());
        transferCityRequest.setArrCity(fareReq.getArrCode());
        transferCityRequest.setFlightDate(fareReq.getDepartureDate());
        transferCityRequest.setRandCode(IdUtil.randomUUID());

        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = this.doPostClient(
                transferCityRequest,
                url,
                headMap,
                handConfig.getReadTimeout(),
                handConfig.getConnectTimeout(),
                "Y".equals(handConfig.getShowAvlog()) ? LogLevelEnum.DEBUG : null);

        List<QueryTransferCityResponse.TransferInfo> resTransList = new ArrayList<>();
        if (result.isResult() && StringUtils.isNotBlank(result.getResponse())) {
            try {
                QueryTransferCityResponse res = (QueryTransferCityResponse) JsonUtil.jsonToBean(result.getResponse(), QueryTransferCityResponse.class);
                resTransList = res.getTransferInfoList();
            } catch (Exception e) {
                throw new OperationFailedException("中转城市查询出错!", e);
            }
            if (CollectionUtils.isNotEmpty(resTransList)) {
                Set<String> citySet = new HashSet<>();
                for (QueryTransferCityResponse.TransferInfo transferInfo : resTransList) {
                    citySet.add(transferInfo.getTransitCity());
                }
                if (!citySet.isEmpty()) {
                    queryFlightFareRequest.setTransferCitys(new ArrayList<>(citySet));
                }
            }
        }
    }


    /**
     * flightInfo 排序
     * <p>
     * flightInfo.CabinFareList排序
     * <p>
     * 1、按舱位分组 低价舱位在前 高价舱位在下
     * <p>
     * 1.1、同舱组内位按指定产品顺序排序（ Apollo Key : cabinSortJsonStr）
     * <p>
     * 1.2、同舱组内该舱等的基础价
     * <p>
     * 1.3、同舱组内其他产品由低到高排序
     *
     * @param resp
     */
    private void sortFlightInfoAndCabinList(QueryFlightFareResp resp) {

        //
        String segmentType = resp.getSegmentType();
        String cabinSortJsonStr = handConfig.getCabinSortJsonStr();

        // 针对FlightInfoList中航司按规则进行排序
        if (CollectionUtils.isNotEmpty(resp.getFlightInfoList())) {

            // flightInfo
            resp.getFlightInfoList().parallelStream().forEach(flightInfo -> {
                flightInfo.setAirlineSortNum(getAirlineSortNum(flightInfo.getFlightNo(), flightInfo.getCarrierNo()));
                flightInfo.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), flightInfo.getCarrierNo()));

            });

            // flightInfo.CabinFareList
            resp.getFlightInfoList().parallelStream().forEach(flightInfo -> {
                // 国内航线舱位展示排序顺序
                if (HandlerConstants.TRIP_TYPE_D.equals(segmentType) && CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {

                    // finalProductSortMap 自定义排序configMap
                    Map<String, Double> productSortMap = new HashMap<>();
                    productSortMap = new Gson().fromJson(cabinSortJsonStr, productSortMap.getClass());
                    Map<String, Double> finalProductSortMap = productSortMap;

                    // 按舱位分组 compareByCabinCode 低价舱位在前
                    Map<String, List<CabinFare>> cabinFareListMap = flightInfo.getCabinFareList().stream().collect(Collectors.groupingBy(CabinFare::getCabinCode));
                    List<Map.Entry<String, List<CabinFare>>> entryList = new ArrayList<>(cabinFareListMap.entrySet());
                    entryList.sort(Map.Entry.comparingByValue(this::compareByCabinCodeGroup));

                    // 同舱位按同舱产品排序
                    LinkedHashMap<String, List<CabinFare>> linkHashMap = new LinkedHashMap<>();
                    for (Map.Entry<String, List<CabinFare>> e : entryList) {
                        List<CabinFare> eValue = e.getValue();
                        // 按finalProductSortMap 自定义顺序排序
                        eValue.sort(Comparator.comparing(CabinFare::getCabinType, (v1, v2) -> this.compareByCabinTypeProduct(v1, v2, finalProductSortMap)).reversed());
                        linkHashMap.put(e.getKey(), eValue);
                    }
                    flightInfo.setCabinFareList(linkHashMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                }
            });
        }
    }


    private int compareByCabinCodeGroup(List<CabinFare> v1, List<CabinFare> v2) {
        CabinFare v1CabinLowPrice = v1.stream().filter(v -> "CABIN_NORMAL".equals(v.getCabinType())).sorted().findFirst().orElse(null);
        CabinFare v2CabinLowPrice = v2.stream().filter(v -> "CABIN_NORMAL".equals(v.getCabinType())).sorted().findFirst().orElse(null);
        if (v1CabinLowPrice != null && v2CabinLowPrice != null) {
            return Double.compare(v1CabinLowPrice.getPriceValue(), v2CabinLowPrice.getPriceValue());
        }
        return 0;
    }


    //版本控制
    private static void mobileVersion(String headChannelCode, String versionCode, QueryFlightFareResp resp, String queryType) {
        //机票候补 版本控制
        if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) < 73400) {
            if (CollectionUtils.isNotEmpty(resp.getFlightInfoList())) {
                resp.getFlightInfoList().stream().forEach(flightInfo -> {
                    List<CabinFare> cabinFareList = flightInfo.getCabinFareList().stream().filter(cabinFare -> !SystemConstants.WAIT.equals(cabinFare.getFlightFareType())).collect(Collectors.toList());
                    flightInfo.setCabinFareList(cabinFareList);
                });
            }
        }

        if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) < 75000) {
            if (CollectionUtils.isNotEmpty(resp.getFlightInfoList())) {
                resp.getFlightInfoList().stream().forEach(flightInfo -> {
                    List<CabinFare> cabinFareList = flightInfo.getCabinFareList().stream().filter(cabinFare -> !FlightQueryTypeEnum.FIRST_RIDE_MEMBER_FARE.getType().equals(cabinFare.getCabinType())).collect(Collectors.toList());
                    flightInfo.setCabinFareList(cabinFareList);
                });
            }
        }
        boolean aqVersionCode = ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) < 710000;
        //九元航班过滤
        if (aqVersionCode || !(ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(headChannelCode)
                || ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode))) {
            List<FlightInfo> flightInfoList = resp.getFlightInfoList().stream().filter(flightInfo -> !AirCompanyEnum.AQ.getAirCompanyCode().equals(flightInfo.getCarrierNo().substring(0, 2))).collect(Collectors.toList());
            resp.setFlightInfoList(flightInfoList);
        }
        if (FlightQueryTypeEnum.YJ_YS.getType().equals(queryType)) {
            List<FlightInfo> flightInfoList = resp.getFlightInfoList().stream().filter(flightInfo -> AirCompanyEnum.HO.getAirCompanyCode().equals(flightInfo.getCarrierNo().substring(0, 2))).collect(Collectors.toList());
            resp.setFlightInfoList(flightInfoList);
        }

        boolean busVersionCode = ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) < 79000;
        //深港通
        if (busVersionCode || !(ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(headChannelCode)
                || ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode))) {
            List<FlightInfo> flightInfoList = resp.getFlightInfoList().stream().filter(flightInfo -> !FareBasisEnum.BUS_FARE.getFareBasisCode().equals(flightInfo.getThemeFlight())).collect(Collectors.toList());
            resp.setFlightInfoList(flightInfoList);
        }

    }

    /**
     * 根据 apollo 配置参数自定义排序
     *
     * @param c1      cabin1
     * @param c2      cabin2
     * @param sortMap sequence
     * @return
     */
    int compareByCabinTypeProduct(String c1, String c2, Map<String, Double> sortMap) {
        if (sortMap == null) {
            return 0;
        }
        return Double.compare(
                sortMap.getOrDefault(c1, 0D),
                sortMap.getOrDefault(c2, 0D));
    }


    /**
     * 航班查询页面展示排序
     *
     * @param flightNo
     * @param carrierNo
     */
    private int getAirlineSortNum(String flightNo, String carrierNo) {
        // 中转航班优先级最低
        if (StringUtils.isNotBlank(flightNo) && flightNo.contains("-")) {
            return 3;
        }
        // 航班号是否吉祥航班
        boolean flightIsHo = StringUtils.isNotBlank(flightNo) && flightNo.startsWith("HO");
        // 承运航班号是否吉祥航班
        boolean carrierNoIsHo = StringUtils.isNotBlank(carrierNo) && carrierNo.startsWith("HO");
        // 航班号吉祥 承运航班号吉祥航班
        if (flightIsHo && carrierNoIsHo) {
            return 1;
        }
        // 航班号吉祥 承运航班号非吉祥航班
        if (flightIsHo) {
            return 2;
        }
        // 航班号非吉祥 承运航班号非吉祥航班
        return 3;
    }

    /**
     * @param fareReq
     * @param handConfig
     * @param useSignFlag
     */
    private boolean checkFlightQueryType(QueryFlightFareReq fareReq, HandConfig handConfig, AtomicBoolean useSignFlag) {
        //军残警残的查询不走数据签名,参数不匹配的直接返回
        if (FlightQueryTypeEnum.POLICE_REMNANTS.getType().equals(fareReq.getQueryType())) {
            if (HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType())
                    || (CollectionUtils.isNotEmpty(handConfig.getGmjcLimitLine()) && handConfig.getGmjcLimitLine().contains(fareReq.getSendCode() + fareReq.getArrCode()))) {
                return false;
            }
            useSignFlag.set(false);
        }
        //免票
        if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(fareReq.getQueryType())) {
            //限制免票
            if (handConfig.getMaxDays() != 0) {
                Date date = DateUtils.toDate(DateUtils.getCurrentDateStr(), "yyyy-MM-dd");
                Date maxDate = DateUtils.addOrLessDay(date, handConfig.getMaxDays());
                Date departureDate = DateUtils.toDate(fareReq.getDepartureDate());
                if (departureDate.compareTo(maxDate) < 0) {
                    return false;
                }
            }
            useSignFlag.set(false);
            UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
            if (!unlimitedCard2Config.isSfTimeShowDoctorFreeTicket()) {
                Date sfStart = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                Date sfEnd = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                Date depTime = DateUtils.toDate(fareReq.getDepartureDate(), DateUtils.YYYY_MM_DD_PATTERN);
                Date arrTime = DateUtils.toDate(fareReq.getReturnDate(), DateUtils.YYYY_MM_DD_PATTERN);
                if (null != sfStart && null != sfEnd && null != depTime && null != arrTime
                        && ((depTime.getTime() <= sfEnd.getTime() && depTime.getTime() >= sfStart.getTime())
                        || (arrTime.getTime() >= sfStart.getTime() && arrTime.getTime() <= sfEnd.getTime()))) {
                    return false;
                }
            }
        }
        // 奖励飞免票 限制条件
        if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(fareReq.getQueryType())
                || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(fareReq.getQueryType())) {
            useSignFlag.set(false);
            //畅飞卡2.0限制
            UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
            if (!unlimitedCard2Config.isSfTimeShowDoctorFreeTicket()) {
                Date sfStart = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                Date sfEnd = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                Date depTime = DateUtils.toDate(fareReq.getDepartureDate(), DateUtils.YYYY_MM_DD_PATTERN);
                Date arrTime = DateUtils.toDate(fareReq.getReturnDate(), DateUtils.YYYY_MM_DD_PATTERN);
                if (null != sfStart && null != sfEnd && null != depTime && null != arrTime
                        && ((depTime.getTime() <= sfEnd.getTime() && depTime.getTime() >= sfStart.getTime())
                        || (arrTime.getTime() >= sfStart.getTime() && arrTime.getTime() <= sfEnd.getTime()))) {
                    return false;
                }
            }
        }
        //留学生运价
        if (FlightQueryTypeEnum.STUDENT.getType().equals(fareReq.getQueryType())) {
            useSignFlag.set(false);
            if (HandlerConstants.TRIP_TYPE_D.equals(fareReq.getTripType())) {
                return false;
            }
        }
        //主题卡参数条件限制
        if (FlightQueryTypeEnum.THEME_CARD.getType().equals(fareReq.getQueryType())) {
            useSignFlag.set(false);
            return checkThemeCardLimit(fareReq);
        }
        //拥军优属
        if (FlightQueryTypeEnum.YJ_YS.getType().equals(fareReq.getQueryType())) {
            useSignFlag.set(false);
            if (HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType())) {
                return false;
            }
        }
        //电商OPEN票
        if (FlightQueryTypeEnum.OPEN_FREE_TICKET.getType().equals(fareReq.getQueryType())) {
            useSignFlag.set(false);
            if (HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType())) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param fareReq
     */
    private boolean checkThemeCardLimit(QueryFlightFareReq fareReq) {
        //国内单程航班适用于主题卡航班查询
        if (HandlerConstants.ROUTE_TYPE_OW.equals(fareReq.getFlightType())) {
            //检验航班查询日期需在当前日期之后
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate depDate = LocalDate.parse(fareReq.getDepartureDate(), dateTimeFormatter);
            LocalDate curDate = LocalDate.now();
            int durationDay = Math.toIntExact(depDate.toEpochDay() - curDate.toEpochDay());
            if (durationDay >= handConfig.getThemeBuyDayLimit()) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    //多程航班查询
    @ApiOperation(value = "多程航班查询", notes = "多程航班查询")
    @RequestMapping(value = "queryMultipleFlightFare", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public MultipleQueryFlightFareResponse queryMultipleFlightFare(@RequestBody BaseReq<QueryMultipleFlightFareRequest> req, HttpServletRequest request) {
        MultipleQueryFlightFareResponse resp = new MultipleQueryFlightFareResponse();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            //不为空时需要验证key值
            QueryMultipleFlightFareRequest fareReq = req.getRequest();
            String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
            String headClientVer = request.getHeader(HandlerConstants.CLIENT_VERSION);
            String platForm = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
            String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
            //风控策略控制
            boolean isAccess = riskControl(req.getChannelCode(), headChannelCode, headClientVer, versionCode, platForm, ip, fareReq, request, resp);
            if (!isAccess) {
                log.error("航班查询IP:{},返回信息:{}", ip, JsonUtil.objectToJson(resp));
                return resp;
            }
            PtFlightFareMultiRequestDto ptQueryFlightFareRequest = createMultipleFlightQueryRequest(fareReq, req.getChannelCode(), ip);
            resp = queryMultipleFlight(ptQueryFlightFareRequest, ip, headChannelCode, req.getClientVersion());
            resp.setMultipleFlightTransferConfig(handConfig.getMultipleFlightTransferConfig());
        } catch (Exception e) {
            String requestJson = JsonUtil.objectToJson(req);
            log.error("多程运价查询出现异常,请求参数：{}，请求号：{}, IP：{},", requestJson, reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("多程运价查询出现异常");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "查询首乘会员价条件", notes = "查询首乘会员价条件")
    @RequestMapping(value = "queryFTRideMPCon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public QueryFTRideMPConResponse queryFTRideMPCon(@RequestBody BaseReq<UserInfoNoMust> req, HttpServletRequest request) {
        QueryFTRideMPConResponse queryFTRideMPConResponse = new QueryFTRideMPConResponse();
        BaseQueryFTRideMPConRequest baseQueryFTRideMPConRequest = new BaseQueryFTRideMPConRequest();
        List<QueryFTRideMPCertDto> queryFTRideMPCertDtos = new ArrayList<>();
        String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptMemberDetailRequestPtApiCRMRequest = CRMReqUtil.buildMemberDetailReq(req.getRequest().getFfpCardNo(),
                req.getRequest().getFfpId(), request, req.getChannelCode(), items);
        PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptMemberDetailRequestPtApiCRMRequest);
        if (ptMemberDetailPtCRMResponse.getCode() == 0) {
            MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = CrmUtil.getNewRealNamePassSummary(ptMemberDetailPtCRMResponse.getData().getRealVerifyInfos());
            if (memberRealNameSummarySoaModel == null) {
                queryFTRideMPConResponse.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
                queryFTRideMPConResponse.setErrorInfo("该产品仅限实名用户购买，完成实名认证后可参与");
                return queryFTRideMPConResponse;
            }
            PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
            if (CollectionUtils.isEmpty(ptMemberDetail.getCertificateInfo())) {
                throw new OperationFailedException("请先填写证件信息");
            }
            for (MemberCertificateSoaModelV2 memberCertificateSoaModel : ptMemberDetail.getCertificateInfo()) {
                QueryFTRideMPCertDto queryFTRideMPCertDto = new QueryFTRideMPCertDto();
                queryFTRideMPCertDto.setCertType(CertificateTypeEnum.checkType(memberCertificateSoaModel.getCertificateType()).getShowCode());
                queryFTRideMPCertDto.setCertNo(memberCertificateSoaModel.getCertificateNumber());
                queryFTRideMPCertDtos.add(queryFTRideMPCertDto);
            }
        } else {
            queryFTRideMPConResponse.setResultCode(WSEnum.ERROR.getResultCode());
            queryFTRideMPConResponse.setErrorInfo(ptMemberDetailPtCRMResponse.getMsg());
            return queryFTRideMPConResponse;
        }
        String userNo = this.getChannelInfo(ChannelCodeEnum.MOBILE.getChannelCode(), "10");
        baseQueryFTRideMPConRequest.setUserNo(userNo);
        baseQueryFTRideMPConRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        baseQueryFTRideMPConRequest.setVersion("10");
        baseQueryFTRideMPConRequest.setQueryFTRideMPCertDtos(queryFTRideMPCertDtos);
        baseQueryFTRideMPConRequest.setFFPId(req.getRequest().getFfpId());
        baseQueryFTRideMPConRequest.setSfCardNo(req.getRequest().getFfpCardNo());
        HttpResult result = this.doPostClient(baseQueryFTRideMPConRequest, HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_FTRIDE_MPCON);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
        }
        BaseQueryFTRideMPConResponse ptResponse;
        try {
            ptResponse = JsonUtil.fromJson(result.getResponse(), BaseQueryFTRideMPConResponse.class);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), ptResponse.getErrorInfo());
            }
            if ("Y".equals(ptResponse.getIsExistLastYearTravelRecord())) {
                queryFTRideMPConResponse.setResultCode(WSEnum.ERROR.getResultCode());
                queryFTRideMPConResponse.setErrorInfo("您近1年乘坐过吉祥航空承运的航班，不符合参与条件");
            } else if ("Y".equals(ptResponse.getIsExistNextYearTravelPlan())) {
                queryFTRideMPConResponse.setResultCode(WSEnum.ERROR.getResultCode());
                queryFTRideMPConResponse.setErrorInfo("您当前有待出行行程，不符合参与条件");
            } else if ("Y".equals(ptResponse.getIsExistWaitingPaymentOrder())) {
                queryFTRideMPConResponse.setResultCode(WSEnum.ERROR.getResultCode());
                queryFTRideMPConResponse.setErrorInfo("账户中存在乘机人为本人的未支付的订单，请取消后下单");
            }  else if ("Y".equals(ptResponse.getIsExistPayBookUnRebateOrder())) {
                queryFTRideMPConResponse.setResultCode(WSEnum.ERROR.getResultCode());
                queryFTRideMPConResponse.setErrorInfo("账户中存在乘机人为本人的已支付待出票订单，不符合参与条件");
            }else {
                queryFTRideMPConResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
                queryFTRideMPConResponse.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            }
            queryFTRideMPConResponse.setFFPId(req.getRequest().getFfpId());
            queryFTRideMPConResponse.setFfpCardNo(req.getRequest().getFfpCardNo());
        } catch (Exception e) {
            throw new OperationFailedException("查询首乘会员价条件出错!", e);
        }

        return queryFTRideMPConResponse;
    }

    @InterfaceLog
    @ApiOperation(value = "查询首乘会员价资格", notes = "查询首乘会员价资格")
    @RequestMapping(value = "query", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryFirstTripMember(@RequestBody @Validated BaseReq<QueryFirstTripMemberParam> req, BindingResult bindingResult, HttpServletRequest request) {
        QueryFirstTripMemberParam queryFirstTripMemberParam = req.getRequest();
        ObjCheckUtil.notNull(queryFirstTripMemberParam, "业务参数不可为空");
        ObjCheckUtil.notNull(req.getSign(), "签名不可为空");
        if (bindingResult.hasErrors()) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "请完善参数");
        }
        //签名信息验证
        String queryFirstTripMemberParamStr = JsonMapper.buildNonNullMapper().toJson(queryFirstTripMemberParam);
        log.info("{},待签名字段:{}", MdcUtils.getRequestId(), queryFirstTripMemberParamStr);
        String sign = EncoderHandler.encodeByMD5(queryFirstTripMemberParamStr + handConfig.getApiKey());
        if (!req.getSign().equals(sign)) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "参数检验不通过");
        }
        List<QueryFTRideMPCertDto> queryFTRideMPCertDtos = new ArrayList<>();
        QueryFTRideMPCertDto queryFTRideMPCertDto = new QueryFTRideMPCertDto();
        queryFTRideMPCertDto.setCertType(queryFirstTripMemberParam.getCertType());
        queryFTRideMPCertDto.setCertNo(queryFirstTripMemberParam.getQueryParam());
        queryFTRideMPCertDtos.add(queryFTRideMPCertDto);
        String userNo = this.getChannelInfo(ChannelCodeEnum.MOBILE.getChannelCode(), "10");
        BaseQueryFTRideMPConRequest baseQueryFTRideMPConRequest = new BaseQueryFTRideMPConRequest();
        baseQueryFTRideMPConRequest.setUserNo(userNo);
        baseQueryFTRideMPConRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        baseQueryFTRideMPConRequest.setVersion("10");
        baseQueryFTRideMPConRequest.setQueryFTRideMPCertDtos(queryFTRideMPCertDtos);
        baseQueryFTRideMPConRequest.setFFPId("1111");
        baseQueryFTRideMPConRequest.setSfCardNo("1111");
        HttpResult result = this.doPostClient(baseQueryFTRideMPConRequest, HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_FTRIDE_MPCON);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
        }
        BaseQueryFTRideMPConResponse ptResponse = JsonUtil.fromJson(result.getResponse(), BaseQueryFTRideMPConResponse.class);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), ptResponse.getErrorInfo());
        }
        if ("Y".equals(ptResponse.getIsExistLastYearTravelRecord())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "您近1年乘坐过吉祥航空承运的航班，不符合参与条件");
        } else if ("Y".equals(ptResponse.getIsExistNextYearTravelPlan())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "您近1年乘坐过吉祥航空承运的航班，您当前有待出行行程，不符合参与条件");
        } else if ("Y".equals(ptResponse.getIsExistWaitingPaymentOrder())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "账户中存在乘机人为本人的未支付的订单，请取消后下单");
        } else if ("Y".equals(ptResponse.getIsExistPayBookUnRebateOrder())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "账户中存在乘机人为本人的已支付待出票订单，不符合参与条件");
        }
        return BaseResp.success();
    }


    /**
     * 查询多程运价
     *
     * @return
     */
    private MultipleQueryFlightFareResponse queryMultipleFlight(PtFlightFareMultiRequestDto ptQueryFlightFareRequest, String ip,
                                                                String headChannelCode, String clientVersion) {
        MultipleQueryFlightFareResponse resp = new MultipleQueryFlightFareResponse();
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = this.doPostClient(ptQueryFlightFareRequest, HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_MULTPLE_FLIGHT_FARE,
                headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout(), "Y".equals(handConfig.getShowAvlog()) ? LogLevelEnum.DEBUG : null);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
        }
        PtMultipleFlightFareResponse ptResponse;
        try {
            ptResponse = JsonUtil.fromJson(result.getResponse(), PtMultipleFlightFareResponse.class);
        } catch (Exception e) {
            throw new OperationFailedException("航班查询出错!", e);
        }
        if (!"1001".equals(ptResponse.getResultCode())) {
            throw new OperationFailedException("运价查询出现异常");
        }
        StringBuilder noFlightRouteMessage = new StringBuilder();
        for (PtMultipleFlightQuerySegment segment : ptQueryFlightFareRequest.getSegCondList()) {
            FlightSimpleResultDto flightSimpleResultDto = null;
            if (null != ptResponse.getMultiFlightInfoCombMap()) {
                flightSimpleResultDto = ptResponse.getMultiFlightInfoCombMap().get(segment.getSegNO());
            }
            if (null == flightSimpleResultDto || CollectionUtils.isEmpty(flightSimpleResultDto.getFlightInfoCombApiDtos())) {
                if (StringUtils.isNotBlank(noFlightRouteMessage)) {
                    noFlightRouteMessage.append("、");
                }
                CityInfoDto depCity = localCacheService.getLocalCity(segment.getDepCity());
                CityInfoDto arrCity = localCacheService.getLocalCity(segment.getArrCity());
                if (depCity != null && arrCity != null) {
                    noFlightRouteMessage.append(depCity.getCityName()).append("-").append(arrCity.getCityName());
                }
            }
        }
        if (StringUtils.isNotBlank(noFlightRouteMessage)) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setErrorInfo("您选择的航班" + noFlightRouteMessage.toString() + "暂无航班计划，请您更换行程重新查询或致电95520");
            return resp;
        }
        // 多程视为直达
        ptResponse.setRouteType(StringUtil.isNullOrEmpty(ptResponse.getRouteType()) ? HandlerConstants.ROUTE_TYPE_OW : ptResponse.getRouteType());
        ptResponse.setCurrencyCode(StringUtil.isNullOrEmpty(ptResponse.getCurrencyCode()) ? HandlerConstants.CURRENCY_CODE : ptResponse.getCurrencyCode());
        // 查询广告信息，出错时不影响运价查询
        List<FlightAdvertisementDto> flightAdvertisementDtos = queryFareAdvertisements(headChannelCode, clientVersion);
        resp = MultipleFlightFareConvert.toFlightFareAddOnResponse(ptResponse, localCacheService, handConfig, flightAdvertisementDtos);

        return resp;
    }

    /**
     * 构建多程运价查询请求参数
     *
     * @param fareReq
     * @param channelCode
     * @return
     */
    private PtFlightFareMultiRequestDto createMultipleFlightQueryRequest(QueryMultipleFlightFareRequest fareReq, String channelCode, String ip) {
        PtFlightFareMultiRequestDto flightFareReq = new PtFlightFareMultiRequestDto();
        flightFareReq.setVersion(HandlerConstants.VERSION);
        flightFareReq.setChannelCode(channelCode);
        flightFareReq.setUserNo(getChannelInfo(channelCode, "10"));
        flightFareReq.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
        flightFareReq.setCurrencyCode(HandlerConstants.CURRENCY_CODE);
        flightFareReq.setLangCode(HandlerConstants.LANG_CODE);
        flightFareReq.setReadRedis(handConfig.getAvReadRedis());
        List<PtMultipleFlightQuerySegment> segCondList = new ArrayList<>();
        for (int i = 0; i < fareReq.getSegments().size(); i++) {
            MultipleFlightFareRequestSegment segment = fareReq.getSegments().get(i);
            PtMultipleFlightQuerySegment querySegment = new PtMultipleFlightQuerySegment();
            querySegment.setSegNO(i);
            querySegment.setDepAirport(segment.getSendAirportCode());
            querySegment.setArrAirport(segment.getArrAirportCode());
            querySegment.setFlightDate(segment.getDepartureDate());
            //北京大兴国内航班的特殊处理
            String sendCode = segment.getSendCode();
            String arrCode = segment.getArrCode();
            if (HandlerConstants.TRIP_TYPE_D.equals(segment.getTripType())) {
                /*if ("PKX".equals(segment.getSendCode())) {
                    sendCode = "PEK";
                }
                if ("PKX".equals(segment.getArrCode())) {
                    arrCode = "PEK";
                }*/
                querySegment.setSegFareSource(FareSourceEnum.HO.getFareSource());
            } else if (HandlerConstants.TRIP_TYPE_I.equals(segment.getTripType())) {
                querySegment.setSegFareSource(FareSourceEnum.SEARCH_ONE.getFareSource());
            }
            querySegment.setDepCity(sendCode);
            querySegment.setArrCity(arrCode);
            querySegment.setFlightDirection(FlightDirection.GO.getCode());

            // 查询航线，不存在直达航线且存在中转航线时才查询中转运价
            List<AirLineLabelDTO> airLineLabelDTOList = basicService.queryCacheFlightLine(segment.getSendCode(), segment.getArrCode(), channelCode, ip);
            if (!StringUtil.isNullOrEmpty(airLineLabelDTOList)) {
                List<String> tranCityList = new ArrayList<>();
                boolean containDirectAirline = false;
                for (AirLineLabelDTO airLineLabelDTO : airLineLabelDTOList) {
                    if (!StringUtil.isNullOrEmpty(airLineLabelDTO.getTransitCity())) {
                        String[] tranCitys = airLineLabelDTO.getTransitCity().split(",");
                        for (String tranCity : tranCitys) {
                            if (!tranCityList.contains(tranCity)) {
                                tranCityList.add(tranCity);
                            }
                        }
                    } else {
                        containDirectAirline = true;
                    }
                }
                if (!containDirectAirline && !StringUtil.isNullOrEmpty(tranCityList)) {
                    querySegment.setTransferCities(tranCityList);
                }
            }
            segCondList.add(querySegment);
        }
        flightFareReq.setSegCondList(segCondList);
        return flightFareReq;
    }


    /**
     * 根据查询类型不同分发不同的url
     * 获取查询运价url
     */
    private String genQueryUrl(QueryFlightFareReq fareReq, PtQueryFlightFareRequest queryFlightFareRequest, String headChannelCode, AtomicBoolean useFareV30) {
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_FLIGHT_FARE_V20;
        // 免票运价查询
        if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(fareReq.getQueryType())
                || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(fareReq.getQueryType())
                || FlightQueryTypeEnum.OPEN_FREE_TICKET.getType().equals(fareReq.getQueryType())) {
            url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONEX;
            useFareV30.set(true);
        } else if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(fareReq.getQueryType())) {
            //奖励飞 请求url地址
            url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONEX;
            useFareV30.set(true);
            queryFlightFareRequest.setIsRewardFly(true);
        } else if ((FlightQueryTypeEnum.THEME_CARD.getType().equals(fareReq.getQueryType())
                &&HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType()))) {
            //多次卡国际 请求url地址
            url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONEX;
            useFareV30.set(true);
        }else if (HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType())) {
            queryFlightFareRequest.setFareSource(FareSourceEnum.SEARCH_ONE.getFareSource());
            String interBrandFareType = handConfig.getInterBrandFareType();
            if (StringUtils.isNotBlank(interBrandFareType)) {
                List<String> interBrandFareCountryCodes = handConfig.getInterBrandFareCountryCodes();
                boolean isUsePinPai = choosePinPai(fareReq.getSendCode(), fareReq.getArrCode(), interBrandFareCountryCodes);
                queryFlightFareRequest.setVirtualType(isUsePinPai);
            }
            url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONE;
            useFareV30.set(true);
        } else if (HandlerConstants.TRIP_TYPE_D.equals(fareReq.getTripType())
                && FlightQueryTypeEnum.YJ_YS.getType().equals(fareReq.getQueryType())) {
            //优待证  军人优属运价查询 SearchOne 接口
            url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONE;
            useFareV30.set(true);

        } else if (HandlerConstants.TRIP_TYPE_D.equals(fareReq.getTripType())
                && ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode)) {
            url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONE;
            useFareV30.set(true);
        }
        return url;
    }

    /**
     * 品牌运价查询判断逻辑
     * 始发或到达其中一方为欧洲国家城市
     * 中国境内始发或到达
     *
     * @param sendCode
     * @param arrCode
     * @param interBrandFareCountryCodes
     * @return
     */
    private boolean choosePinPai(String sendCode, String arrCode, List<String> interBrandFareCountryCodes) {
        if (CollectionUtils.isEmpty(interBrandFareCountryCodes)) {
            return false;
        }
        CityInfoDto sendCityInfoDto = localCacheService.getLocalCity(sendCode);
        CityInfoDto arrCityInfoDto = localCacheService.getLocalCity(arrCode);
        if (HandlerConstants.TRIP_TYPE_D.equals(sendCityInfoDto.getIsInternational()) && interBrandFareCountryCodes.contains(arrCityInfoDto.getCountryCode())) {
            return true;
        } else if (HandlerConstants.TRIP_TYPE_D.equals(arrCityInfoDto.getIsInternational()) && interBrandFareCountryCodes.contains(sendCityInfoDto.getCountryCode())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否售罄
     *
     * @param resp
     * @return
     */
    private boolean isSoldOut(QueryFlightFareResp resp) {
        boolean soldOut = false;// 是否无航线或售罄
        // 判断V20运价是否售罄
        if (CollectionUtils.isEmpty(resp.getFlightInfoList())) {
            soldOut = true;
        } else {
            for (FlightInfo flightInfo : resp.getFlightInfoList()) {
                if (CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {
                    soldOut = false;
                    break;
                } else {
                    soldOut = true;
                }
            }
        }
        if (soldOut) {
            if (CollectionUtils.isNotEmpty(resp.getTransferFlightInfoList())) {
                for (FlightInfoComb flightInfoComb : resp.getTransferFlightInfoList()) {
                    if (CollectionUtils.isNotEmpty(flightInfoComb.getAdtCabinFareList())) {
                        soldOut = false;
                        break;
                    }
                }
            }
        }
        return soldOut;
    }

    private QueryFlightFareResp dealFlightResponse(PtQueryFlightFareRequest queryFlightFareRequest, QueryFlightFareReq fareReq, String url,
                                                   AtomicBoolean useFareV30, AtomicBoolean useFareSimple, String channelCode, String ip, int ver, ClientInfo userAppVer) {
        QueryFlightFareResp resp;
        //主题卡
        ThemeCoupon themeCoupon = null;
        Map<String, ThemeCoupon> themeCouponMap = toThemeModelMap(handConfig.getThemeCabinLabel());
        //获取主题卡配置
        if (themeCouponMap.get(fareReq.getThemeCardType()) != null) {
            themeCoupon = themeCouponMap.get(fareReq.getThemeCardType());
        }
        if (themeCoupon != null) {
            if (CollectionUtils.isNotEmpty(queryFlightFareRequest.getSegCondList())) {
                ThemeCoupon finalThemeCoupon = themeCoupon;
                //比较航班与主题卡信息
                queryFlightFareRequest.getSegCondList().stream().forEach(flight -> {
                    //航班日期不在主题卡兑换时间范围之内
                    if (!DateUtils.compareDate(DateUtils.toDate(finalThemeCoupon.getFlightStartTime()),
                            DateUtils.toDate(finalThemeCoupon.getFlightEndTime()), DateUtils.toDate(flight.getFlightDate()))) {
                        queryFlightFareRequest.getSegCondList().remove(flight);
                    }
                });
            }
        }
        String headChannelCode = userAppVer.getHeadChannelCode();
        //是否设置特殊服务
        AtomicBoolean setService = new AtomicBoolean(false);
        //是否计算额外积分
        AtomicBoolean extraScore = new AtomicBoolean(false);
        List<String> packageCabinTypes = genPackageCabinTypes(fareReq, headChannelCode, ver, setService, extraScore);
        // 畅飞卡1.0展示逻辑
        // 判断是否展示儿童随心飞运价
        FareBusinessInfoEnum chdBusinessInfo = null;
        FareBusinessInfoEnum adtBusinessInfo = null;
        boolean showChdFreeCabin = false;// 是否展示儿童畅飞卡舱位
        boolean showAdtFreeCabin = false; // 是否展示成人畅飞卡舱位
        Date unlimitedFlyDateLimit = DateUtils.toDate(handConfig.getUnlimitedFlyDateLimit());
        Date departureTime = DateUtils.toDate(fareReq.getDepartureDate());
        boolean unlimitedFlyDateMatch = false;
        if (null != unlimitedFlyDateLimit && null != departureTime && departureTime.before(unlimitedFlyDateLimit)) {
            unlimitedFlyDateMatch = true;
        }
        boolean unlimitedFlyBegin = false;
        Date showUnlimitedFlyTimeBegin = DateUtils.toDate(handConfig.getShowUnlimitedFlyTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        if (null != showUnlimitedFlyTimeBegin && showUnlimitedFlyTimeBegin.before(new Date())) {
            unlimitedFlyBegin = true;
        }
        if (null != departureTime && DateUtils.durDays(new Date(), departureTime) >= handConfig.getFreeTicket5DayLimit()
                && !useFareV30.get() && unlimitedFlyDateMatch && unlimitedFlyBegin && HandlerConstants.ROUTE_TYPE_OW.equals(fareReq.getFlightType())
                && (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) || ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode))) {
            if (StringUtils.isNotBlank(fareReq.getFfpId())) {
                List<UnlimitedFlyBindRecord> childFlyBindRecords = null;
                try {
                    childFlyBindRecords = unlimitedFlyService.listChdBindRecord(fareReq.getChannelCode(), fareReq.getFfpId(), ip);
                } catch (Exception e) {
                    log.error("查询儿童畅飞卡绑定记录失败", e);
                }
                // 有绑定或者没过绑定有效期的展示随心飞
                if (CollectionUtils.isNotEmpty(childFlyBindRecords)) {
                    if (childFlyBindRecords.stream().anyMatch(childFlyBindRecord -> "Effective".equalsIgnoreCase(childFlyBindRecord.getBindingValidity()))) {
                        showChdFreeCabin = true;
                        packageCabinTypes.add(FareBasisEnum.CHD_UNLIMITED_FARE.getFareBasisCode());
                        chdBusinessInfo = childFlyBindRecords.stream().anyMatch(childFlyBindRecord -> "yes".equalsIgnoreCase(childFlyBindRecord.getBindStatus()))
                                ? FareBusinessInfoEnum.CHD_UNLIMITED_FLY_BOUND : FareBusinessInfoEnum.CHD_UNLIMITED_FLY_UNBOUND;
                    }
                }
                // 成人畅飞卡
                showAdtFreeCabin = true;
                List<UnlimitedFlyBindRecord> adtFlyCardBindRecords = null;
                try {
                    adtFlyCardBindRecords = unlimitedFlyService.listFlyCardBindRecord(fareReq.getChannelCode(), fareReq.getFfpId(), ip);
                } catch (Exception e) {
                    log.error("查询成人畅飞卡绑定记录失败", e);
                }
                if (CollectionUtils.isNotEmpty(adtFlyCardBindRecords)) {
                    // 购卡且已绑定
                    if (adtFlyCardBindRecords.stream().anyMatch(unlimitedFlyBindRecord -> "yes".equalsIgnoreCase(unlimitedFlyBindRecord.getBindStatus())
                            && !"yes".equals(unlimitedFlyBindRecord.getNoShowStatus()))) {
                        adtBusinessInfo = FareBusinessInfoEnum.ADT_UNLIMITED_FLY_BOUND;
                    } else if (adtFlyCardBindRecords.stream().anyMatch(unlimitedFlyBindRecord -> "yes".equalsIgnoreCase(unlimitedFlyBindRecord.getNoShowStatus()))) {
                        adtBusinessInfo = FareBusinessInfoEnum.NOSHOW_LIMIT;
                    } else {
                        adtBusinessInfo = FareBusinessInfoEnum.ADT_UNLIMITED_FLY_UNBOUND;
                    }
                } else {
                    if (showChdFreeCabin) {// 已购儿童卡未购成人卡
                        showAdtFreeCabin = false;
                    } else {
                        adtBusinessInfo = FareBusinessInfoEnum.ADT_UNLIMITED_FLY_NOT_BUY;
                    }
                }
            } else {
                showAdtFreeCabin = true;
            }
            if (DateUtils.durDays(new Date(), departureTime) < handConfig.getFreeTicket5DayLimit()) {
                chdBusinessInfo = FareBusinessInfoEnum.FIVE_DAYS_LIMIT;
                adtBusinessInfo = FareBusinessInfoEnum.FIVE_DAYS_LIMIT;
            }
        }
        if (showAdtFreeCabin) {
            packageCabinTypes.add(FareBasisEnum.ADT_UNLIMITED_FARE.getFareBasisCode());
        }
        // 畅飞卡2.0舱位展示逻辑
        boolean showUnlimitedCabin2 = false; //是否展示畅飞卡2.0舱位
        UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
        boolean unlimitedFly2DateMatch = false;
        Date now = new Date();
        Date unlimitedFly2DateBegin = DateUtils.toDate(unlimitedCard2Config.getFlightTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        Date unlimitedFly2DateEnd = DateUtils.toDate(unlimitedCard2Config.getFlightTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        if (null != unlimitedFly2DateBegin && null != unlimitedFly2DateEnd && null != departureTime
                && departureTime.getTime() <= unlimitedFly2DateEnd.getTime() && departureTime.getTime() >= unlimitedFly2DateBegin.getTime()) {
            unlimitedFly2DateMatch = true;
        }
        boolean unlimitedFly2Begin = false;
        Date unlimitedFly2TimeBegin = DateUtils.toDate(unlimitedCard2Config.getShowCabinTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        if (null != unlimitedFly2TimeBegin && unlimitedFly2TimeBegin.before(now)) {
            unlimitedFly2Begin = true;
        }
        if (!useFareV30.get() && unlimitedFly2DateMatch && unlimitedFly2Begin && HandlerConstants.ROUTE_TYPE_OW.equals(fareReq.getFlightType())
                && (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && ver >= 62100
                || ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode))) {
            showUnlimitedCabin2 = true;
            adtBusinessInfo = unlimitedFlyV2BusinessInfo(fareReq, departureTime, ip, unlimitedCard2Config);
            if (FareBusinessInfoEnum.ADT_PAY.equals(adtBusinessInfo)) {
                packageCabinTypes.add(FareBasisEnum.UNLIMITED_FARE_2_ADT_PAY.getFareBasisCode());
            } else if (FareBusinessInfoEnum.ADT_UNLIMITED_FLY_NOT_BUY.equals(adtBusinessInfo)) {
                // 未购买不展示兑换入口
            } else {
                packageCabinTypes.add(FareBasisEnum.UNLIMITED_FARE_2.getFareBasisCode());
            }
        }
        // 需要查询畅飞卡时查询X舱
        //主题卡查询需要查询
        boolean queryXcabin = showXcabin(fareReq);
        queryFlightFareRequest.setQueryXCabin(showAdtFreeCabin || showChdFreeCabin || showUnlimitedCabin2 || queryXcabin ? "Y" : "N");
        if (queryXcabin
                && FlightQueryTypeEnum.THEME_CARD.getType().equals(fareReq.getQueryType())
                && StringUtils.isNotBlank(fareReq.getThemeCardType())) {
            queryFlightFareRequest.setThemeCardList(Arrays.asList(fareReq.getThemeCardType()));
        }
        queryFlightFareRequest.setOriginalReqIP(ip);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = this.doPostClient(queryFlightFareRequest, url, headMap, handConfig.getReadTimeout(),
                handConfig.getConnectTimeout(), "Y".equals(handConfig.getShowAvlog()) ? LogLevelEnum.INFO : null);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
        }
        PtQueryFlightFareResponse res;
        try {
            res = (PtQueryFlightFareResponse) JsonUtil.jsonToBean(result.getResponse(), PtQueryFlightFareResponse.class);
            //res = (PtQueryFlightFareResponse) JsonUtil.jsonToBean(FileUtil.readFile("E:\\BJSOSA.json"), PtQueryFlightFareResponse.class);
            filterTransferFlights(fareReq.getQueryType(), fareReq.getSendCode(), fareReq.getArrCode(), res);
        } catch (Exception e) {
            log.error("{},{},航班查询IP:{},航班查询异常", MdcUtils.getRequestId(), JsonUtil.objectToJson(fareReq), ip, e);
            throw new OperationFailedException("航班查询出错!");
        }
        res.setInterFlag(StringUtils.isBlank(res.getInterFlag()) ? fareReq.getTripType() : res.getInterFlag());
        res.setRouteType(StringUtils.isBlank(res.getRouteType()) ? fareReq.getFlightType() : res.getRouteType());
        res.setCurrencyCode(StringUtils.isBlank(res.getCurrencyCode()) ? "CNY" : res.getCurrencyCode());
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(res.getResultCode())) {
            log.error("{},IP:{},会员卡号:{},查询参数:{},中台返回结果码{},描述:{}", MdcUtils.getRequestId(), userAppVer.getClientIp(), userAppVer.getFfpCardNo(), fareReq, res.getResultCode(), res.getErrorInfo());
        }
        // 查询广告信息，出错时不影响运价查询
        List<FlightAdvertisementDto> flightAdvertisementDtos = queryFareAdvertisements(headChannelCode, fareReq.getClientVersion());
        //联程运价，新版国际运价,国内组合运价
        if (useFareV30.get()) {
            String userNo = getChannelInfo(channelCode, "10");
            resp = AVObjectConvertV3.toFlightFareAddOnResponse(res, handConfig, flightAdvertisementDtos, localCacheService, fareReq.getQueryType(), userAppVer);
            // 非免票查询时设置服务信息
            boolean isFreeTicket = FlightQueryTypeEnum.FREE_TICKET.getType().equals(fareReq.getQueryType());
            boolean isAwardFlyFreeTicket = FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(fareReq.getQueryType()) || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(fareReq.getQueryType());
            boolean isYjYsTicket = FlightQueryTypeEnum.YJ_YS.getType().equals(fareReq.getQueryType());
            //非免票查询 只有 false，false 的情况才为true
            setService.set(!isFreeTicket && !isAwardFlyFreeTicket && !isYjYsTicket);
            //设置国际品牌运价
            if (HandlerConstants.TRIP_TYPE_I.equals(res.getInterFlag())) {
                PtPdmRequest ptPdmRequest = new PtPdmRequest(HandlerConstants.VERSION, channelCode, userNo);
                setBrandRight(fareReq.getQueryType(), resp, ptPdmRequest);
            }
            //2021-05-31 旅客类型 首页聚合查询旅客类型
            List<FlightInfoComb> transferFlightInfoList = resp.getTransferFlightInfoList();
            if (CollectionUtils.isNotEmpty(transferFlightInfoList) && CollectionUtils.isNotEmpty(queryFlightFareRequest.getPassengerType())) {
                transferFlightInfoList.forEach(flightInfoComb -> {
                    flightInfoComb.setPassengerType(queryFlightFareRequest.getPassengerType());
                });
            }
            //2021-11-24 处理留学生运价
            if (CollectionUtils.isNotEmpty(transferFlightInfoList) && FlightQueryTypeEnum.STUDENT.getType().equals(fareReq.getQueryType())) {
                for (FlightInfoComb flightInfoComb : transferFlightInfoList) {
                    if (CollectionUtils.isNotEmpty(flightInfoComb.getAdtCabinFareList())) {
                        for (CabinFare cabinFare : flightInfoComb.getAdtCabinFareList()) {
                            cabinFare.setCabinType(FlightQueryTypeEnum.STUDENT.getType());
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(transferFlightInfoList) && FlightQueryTypeEnum.THEME_CARD.getType().equals(fareReq.getQueryType())) {
                for (FlightInfoComb flightInfoComb : transferFlightInfoList) {
                    //主题卡目前只支持X舱
                    List<CabinFare>  adtCabinFareList = flightInfoComb.getAdtCabinFareList().stream().filter(cabinFare -> handConfig.getThemeCabin().equals(cabinFare.getCabinComb())).collect(Collectors.toList());
                    adtCabinFareList.forEach(cabinFare -> {
                        cabinFare.setThemeCardType(fareReq.getThemeCardType());
                        cabinFare.setThemeCabinInfo(fareReq.getThemeCardType(), FlightUtil.toThemeModelMap(handConfig.getThemeCabinLabel()));
                        cabinFare.setCabinType(PackageTypeEnum.THEME_CARD.getPackType());
                        cabinFare.setCabinLabelList2(new ArrayList<>());
                        cabinFare.setSortPriority(10);
                    });
                    flightInfoComb.setAdtCabinFareList(adtCabinFareList);
                }
            }
        } else {
            //航班运价结果转换
            FlightQueryBO flightQueryBO = new FlightQueryBO(fareReq.getQueryType(), fareReq.getThemeCardType());
            resp = AVObjectConvertV2.toFlightFareResponse(res, handConfig, templeteConfig, flightQueryBO, packageCabinTypes, flightAdvertisementDtos, localCacheService, userAppVer);
            resp.setFareBusinessInfo(chdBusinessInfo == null ? null : chdBusinessInfo.getCode());
            resp.setAdtUnlimitedCardMsg(adtBusinessInfo == null ? null : adtBusinessInfo.getCode());
        }
        resp.setFareCallSource(res.getFareCallSource());
        //航班特色处理 此块逻辑不影响正常结果
        if (setService.get()) {
            setService(queryFlightFareRequest, fareReq, resp, res, useFareV30, extraScore.get(), userAppVer, headChannelCode);
        }
        return resp;
    }

    /**
     * 奖励飞免票与主题卡免票 不支持中转航班，需要剔除中转数据
     *
     * @param queryType
     * @param sendCode  出发城市三字码
     * @param arrCode   到达城市三字码
     * @param res
     */
    private void filterTransferFlights(String queryType, String sendCode, String arrCode, PtQueryFlightFareResponse res) {
        //奖励飞免票与主题卡免票 过滤中转航班数据，
        if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(queryType)
                || FlightQueryTypeEnum.FREE_TICKET.getType().equals(queryType)
                || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(queryType)) {
            List flightInfoCombList = null;
            if (CollectionUtils.isNotEmpty(res.getFlightInfoCombList())) {
                flightInfoCombList = res.getFlightInfoCombList().stream().filter(flightInfoCombApi -> {
                    Map<String, List<V2FlightInfo>> flightMap = flightInfoCombApi.getFlightInfoList().stream().collect(Collectors.groupingBy(V2FlightInfo::getFlightDirection));
                    if (flightMap != null) {
                        for (Map.Entry<String, List<V2FlightInfo>> entry : flightMap.entrySet()) {
                            if (FlightDirection.GO.getCode().equals(entry.getKey())) {
                                List<V2FlightInfo> v2FlightInfoList = entry.getValue();
                                String depCity = v2FlightInfoList.get(0).getDepCity();
                                String arrCity = v2FlightInfoList.get(v2FlightInfoList.size() - 1).getArrCity();
                                //出发，到达与查询条件一致且航班组合中不存在-连接符
                                if ((sendCode + arrCode).equals(depCity + arrCity) && flightInfoCombApi.getFlightNoComb().indexOf("-") < 0) {
                                    return true;
                                }
                            }
                        }
                    }
                    return false;
                }).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(res.getFlightInfoCombList())) {
                res.setFlightInfoCombList(flightInfoCombList);
            }
        }
    }

    /**
     * 判断是否展示X舱
     *
     * @return
     */
    private boolean showXcabin(QueryFlightFareReq fareReq) {
        //主题卡查询
        if (FlightQueryTypeEnum.THEME_CARD.getType().equals(fareReq.getQueryType())) {
            return true;
        }
        return false;
    }

    /**
     * 获取提示信息
     *
     * @param fareReq
     * @param departureTime
     * @param ip
     * @param unlimitedCard2Config
     * @return
     */
    private FareBusinessInfoEnum unlimitedFlyV2BusinessInfo(QueryFlightFareReq fareReq, Date departureTime, String ip, UnlimitedCard2Config unlimitedCard2Config) {
        FareBusinessInfoEnum fareBusinessInfoEnum = null;
        // 未登陆
        if (StringUtils.isBlank(fareReq.getFfpId())) {
            return null;
        }
        List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = null;
        try {
            unlimitedFlyV2BindRecords = unlimitedFlyService.listFlyCard2BindRecord(fareReq.getChannelCode(), fareReq.getFfpId(), ip, fareReq.getFfpCardNo());
        } catch (Exception e) {
            log.error("查询畅飞卡2.0绑定记录失败", e);
        }
        if (CollectionUtils.isEmpty(unlimitedFlyV2BindRecords)) {
            return FareBusinessInfoEnum.ADT_UNLIMITED_FLY_NOT_BUY;
        }
        if (unlimitedFlyV2BindRecords.stream().allMatch(unlimitedFlyBindRecord -> "no".equalsIgnoreCase(unlimitedFlyBindRecord.getBindStatus()))) {
            return FareBusinessInfoEnum.ADT_UNLIMITED_FLY_UNBOUND;
        }
        if (unlimitedFlyV2BindRecords.stream().filter(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus())).
                allMatch(unlimitedFlyBindRecord -> "yes".equalsIgnoreCase(unlimitedFlyBindRecord.getNoShowStatus()))) {
            return FareBusinessInfoEnum.NOSHOW_LIMIT;
        }
        // 筛选绑定有效的绑定记录
        unlimitedFlyV2BindRecords = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                "yes".equalsIgnoreCase(bindRecord.getBindStatus()) && !"yes".equalsIgnoreCase(bindRecord.getNoShowStatus())).collect(Collectors.toList());
        // 是否是春节期间
        boolean springFestival = false;
        Date limitedDateBegin = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        Date limitedDateEnd = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        if (null != limitedDateBegin && null != limitedDateEnd
                && departureTime.getTime() <= limitedDateEnd.getTime() && departureTime.getTime() >= limitedDateBegin.getTime()) {
            springFestival = true;
        }
        if (springFestival) {
            // 成人春节畅飞卡
            List<UnlimitedFlyV2BindRecord> adtBindRecord = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                    VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(bindRecord.getResourceType()) &&
                            (StringUtils.isNotBlank(bindRecord.getAdultName()) || StringUtils.isNotBlank(bindRecord.getAdultElastName()))).collect(Collectors.toList());
            // 儿童春节畅飞卡
            List<UnlimitedFlyV2BindRecord> chdBindRecord = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                    VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(bindRecord.getResourceType()) &&
                            StringUtils.isNotBlank(bindRecord.getChildCnName()) || StringUtils.isNotBlank(bindRecord.getChildEnName())).collect(Collectors.toList());
            // 无春节畅飞卡不可购买春节票
            if (CollectionUtils.isEmpty(adtBindRecord) && CollectionUtils.isEmpty(chdBindRecord)) {
                return FareBusinessInfoEnum.SF_NOT_ALLOWED;
            }
            // 成人未购卡，儿童购卡
            if (CollectionUtils.isEmpty(adtBindRecord)) {
                fareBusinessInfoEnum = FareBusinessInfoEnum.ADT_PAY;
            } else {
                fareBusinessInfoEnum = FareBusinessInfoEnum.ADT_UNLIMITED_FLY_BOUND;
            }
        } else {
            List<UnlimitedFlyV2BindRecord> adtBindRecord = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                    StringUtils.isNotBlank(bindRecord.getAdultName()) || StringUtils.isNotBlank(bindRecord.getAdultElastName())).collect(Collectors.toList());
            /*List<UnlimitedFlyV2BindRecord> chdBindRecord = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                    StringUtils.isNotBlank(bindRecord.getChildCnName()) || StringUtils.isNotBlank(bindRecord.getChildEnName())).collect(Collectors.toList());*/
            // 成人未购卡，儿童购卡
            if (CollectionUtils.isEmpty(adtBindRecord)) {
                fareBusinessInfoEnum = FareBusinessInfoEnum.ADT_PAY;
            } else {
                fareBusinessInfoEnum = FareBusinessInfoEnum.ADT_UNLIMITED_FLY_BOUND;
            }
        }
        if (DateUtils.durDays(new Date(), departureTime) < unlimitedCard2Config.getFreeTicket3DayLimit()) {
            return FareBusinessInfoEnum.THREE_DAYS_LIMIT;
        }
        return fareBusinessInfoEnum;
    }

    private List<String> genPackageCabinTypes(QueryFlightFareReq fareReq, String headChannelCode, int version, AtomicBoolean setService, AtomicBoolean extraScore) {
        List<String> packageCabinTypes = Lists.newArrayList();
        if (StringUtils.isBlank(fareReq.getQueryType())) {
            setService.set(true);
            extraScore.set(true);
            fareReq.setQueryType(FlightQueryTypeEnum.PACKAGE_CABIN.getType());
        }
        if (FlightQueryTypeEnum.PACKAGE_CABIN.getType().equals(fareReq.getQueryType())) {
            packageCabinTypes.add(FareBasisEnum.BRAND_FARE.getFareBasisCode());
            packageCabinTypes.add(FareBasisEnum.COUPON_FAREBASIS.getFareBasisCode());
            packageCabinTypes.add(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode());
            packageCabinTypes.add(FareBasisEnum.MEMBER_FARE.getFareBasisCode());
            // 5.9版本新增青年特惠运价
            if ((ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode))
                    || ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)
                    || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode)) {
                packageCabinTypes.add(FareBasisEnum.YOUTH_FARE.getFareBasisCode());
                packageCabinTypes.add(FareBasisEnum.MULTI_DISCOUNT.getFareBasisCode());
                packageCabinTypes.add(FareBasisEnum.BAGGAGE_FARE.getFareBasisCode());
            }
            if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)
                    || ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)) {
                packageCabinTypes.add(FareBasisEnum.HIGH_REBATE.getFareBasisCode());
            }
            if ((ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && version >= 72200)
                    || ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)
                    || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode)) {
                packageCabinTypes.add(FareBasisEnum.DISNEY_FARE.getFareBasisCode());
            }
            if ((ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && version >= 74100)
                    || ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)
                    || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode)) {
                packageCabinTypes.add(FareBasisEnum.AIRPORT_TRANSFER_FARE.getFareBasisCode());
                packageCabinTypes.add(FareBasisEnum.FIRST_RIDE_MEMBER_FARE.getFareBasisCode());
            }
        }
        return packageCabinTypes;
    }

    /**
     * 查询广告信息
     *
     * @return
     */
    private List<FlightAdvertisementDto> queryFareAdvertisements(String headChannelCode, String clientVersion) {
        // 查询广告信息，出错时不影响运价查询
        List<FlightAdvertisementDto> flightAdvertisementDtos = Lists.newArrayList();
        try {
            String advertisementCache = apiRedisService.getData(RedisKeyConfig.FLIGHT_ADVERTISEMENT);
            if (StringUtils.isNotBlank(advertisementCache)) {
                flightAdvertisementDtos = JsonUtil.fromJson(advertisementCache, new TypeToken<List<FlightAdvertisementDto>>() {
                }.getType());
            } else {
                PictureRequest pictureRequest = new PictureRequest();
                pictureRequest.setChannelCode(headChannelCode);
                pictureRequest.setPicLocation("POSITION_AVFARE_ADV");
                pictureRequest.setClientVersion(clientVersion);
                pictureRequest.setVersionCode(String.valueOf(VersionNoUtil.toVerInt(clientVersion)));
                HttpResult advResult = doPostClient(pictureRequest, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_PICTURELIST);
                if (!advResult.isResult() || StringUtils.isEmpty(advResult.getResponse())) {
                    throw new OperationFailedException("查询广告信息网络出错");
                }
                BaseResp<List<PictureDto>> advResp = JsonUtil.fromJson(advResult.getResponse(), new TypeToken<BaseResp<List<PictureDto>>>() {
                }.getType());
                if (WSEnum.SUCCESS.getResultCode().equals(advResp.getResultCode())) {
                    flightAdvertisementDtos = advResp.getObjData().stream().map(pictureDto -> {
                        FlightAdvertisementDto flightAdvertisementDto = new FlightAdvertisementDto();
                        flightAdvertisementDto.setPictureDto(pictureDto);
                        if (StringUtils.isNotBlank(pictureDto.getAirlines())) {
                            flightAdvertisementDto.setAirlineList(Lists.newArrayList(pictureDto.getAirlines().split(",")));
                        } else {
                            flightAdvertisementDto.setAirlineList(Lists.newArrayList());
                        }
                        if (StringUtils.isNotBlank(pictureDto.getCabins())) {
                            flightAdvertisementDto.setCabinList(Lists.newArrayList(pictureDto.getCabins().split(",")));
                        }
                        return flightAdvertisementDto;
                    }).collect(Collectors.toList());
                    // 缓存广告信息缓存24小时
                    this.apiRedisService.putData(RedisKeyConfig.FLIGHT_ADVERTISEMENT, JsonUtil.objectToJson(flightAdvertisementDtos), 60 * 60 * 24L);
                } else {
                    log.error("查询广告信息失败, result=" + advResult.getResponse());
                }
            }
        } catch (Exception e) {
            log.error("查询运价广告出现异常", e);
        }
        return flightAdvertisementDtos;
    }

    /**
     * @param queryFlightFareRequest
     * @param fareReq
     * @param resp
     * @param res                    订单返回航班结果
     * @param useFareV30
     * @param extraScore
     * @param userAppVer             基础设备信息
     * @param headChannelCode        请求头渠道
     */
    private void setService(PtQueryFlightFareRequest queryFlightFareRequest, QueryFlightFareReq fareReq, QueryFlightFareResp resp,
                            PtQueryFlightFareResponse res, AtomicBoolean useFareV30, boolean extraScore, ClientInfo userAppVer, String headChannelCode) {

        //设置最低价缓存
        if (!isSoldOut(resp)) {
            try {//不影响结果
                List<FlightInfo> flightInfoList = resp.getFlightInfoList();
                setMinPriceCache(res.getCurrencyCode(), res.getRouteType(), resp.getFlightInfoList(), resp.getFareTaxInfoList(), fareReq, useFareV30.get());
                //设置航班推荐结果
                if (HandlerConstants.ROUTE_TYPE_OW.equals(fareReq.getFlightType()) && "Y".equals(handConfig.getUseFlightRecommend())) {
                    NearFlightRecommend flightRecommend = setNearFlight(fareReq, resp.getFlightInfoList(), useFareV30.get());
                    resp.setFlightRecommend(flightRecommend);
                }
                //设置航班标签
                setFlightLabel(flightInfoList, userAppVer, fareReq);
                //设置航班提醒内容
                setFlightReminder(fareReq.getSendCode(), fareReq.getArrCode(), resp, queryFlightFareRequest.getSegCondList(), fareReq.getQueryType());
                //设置是否是候补
                setFlightFareType(flightInfoList, resp.getRoutetype());
                //设置舱位退改费高低
                setReturnFeeDesc(flightInfoList);
                //会员专享积分计算展示
                if (extraScore && StringUtil.isNullOrEmpty(fareReq.getQueryType())) {
                    setActivityAdditionalScoreGive(resp.getFlightInfoList(), handConfig.getAdditionRuleId());
                }
            } catch (Exception e) {
                String logStr = JsonMapper.buildNormalMapper().toJson(queryFlightFareRequest);
                saveError("处理航班特色结果", System.currentTimeMillis() + "", userAppVer.getClientIp(), logStr, e);
            }
        } else {//售罄情况处理
            // 售罄也显示标签
            List<FlightInfo> flightInfoList = resp.getFlightInfoList();
            setFlightLabel(flightInfoList, userAppVer, fareReq);
            String key = "M" + res.getCurrencyCode() + res.getRouteType() + fareReq.getDepartureDate() + fareReq.getSendCode()
                    + fareReq.getArrCode();
            String key_copy = RedisKeyConfig.COMMON_FLIGHT_MIN_PRICE + fareReq.getChannelCode() + ":" + key;//此处是为了重新整合之前的低价
            String keyTax = RedisKeyConfig.COMMON_FLIGHT_TAXINFO + fareReq.getChannelCode() + ":" + key;//此处是为了重新整合之前的低价
            String keyRound = RedisKeyConfig.COMMON_FLIGHT_ROUND + fareReq.getChannelCode() + ":" + key;//此处往返低价
            //
            List<String> clearPriceCodeList = new ArrayList<>();
            clearPriceCodeList.add(UnifiedOrderResultEnum.SUCCESS.getResultCode());
            if(CollectionUtils.isNotEmpty(handConfig.getClearPriceCode())){
                clearPriceCodeList.addAll(handConfig.getClearPriceCode());
            }
            //调整为只有手机渠道进行缓存清理
            if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode)
                    && clearPriceCodeList.contains(res.getResultCode())) {
                apiRedisService.removeData(key);
                apiRedisService.removeData(key_copy);
                apiRedisService.removeData(keyTax);
                apiRedisService.removeData(keyRound);
                //查询的是往返航班,调换起始点清理
                if ("RT".equals(res.getRouteType())) {
                    key = "M" + res.getCurrencyCode() + res.getRouteType() + fareReq.getReturnDate() + fareReq.getArrCode() + fareReq.getSendCode();
                    key_copy = RedisKeyConfig.COMMON_FLIGHT_MIN_PRICE + fareReq.getChannelCode() + ":" + key;//此处是为了重新整合之前的低价
                    keyTax = RedisKeyConfig.COMMON_FLIGHT_TAXINFO + fareReq.getChannelCode() + ":" + key;//税费低价
                    apiRedisService.removeData(key);
                    apiRedisService.removeData(key_copy);
                    apiRedisService.removeData(keyTax);
                    apiRedisService.removeData(keyRound);
                }
                //清除国际往返低价日历
                if (HandlerConstants.ROUTE_TYPE_RT.equals(fareReq.getFlightType()) && HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType())) {
                    if (!StringUtil.isNullOrEmpty(fareReq.getReturnDate())) {
                        key = RedisKeyConfig.COMMON_FLIGHT_TAXINFO + fareReq.getChannelCode() + ":" + "M" + HandlerConstants.CURRENCY_CODE + fareReq.getFlightType() + fareReq.getDepartureDate() + "/" + fareReq.getReturnDate() + fareReq.getSendCode() + fareReq.getArrCode();
                        apiRedisService.removeData(key);
                    }
                }
                String redisKey = "";
                if (HandlerConstants.FLIGHT_DIRECTION_GO.equals(fareReq.getDirectType()) && HandlerConstants.ROUTE_TYPE_OW.equals(res.getRouteType())) {
                    redisKey = "H" + res.getCurrencyCode() + res.getRouteType() + fareReq.getDepartureDate() + fareReq.getSendCode() + fareReq.getArrCode();
                } else if (HandlerConstants.ROUTE_TYPE_RT.equals(res.getRouteType()) && StringUtils.isNotEmpty(fareReq.getReturnDate())) {
                    redisKey = "H" + res.getCurrencyCode() + res.getRouteType() + fareReq.getDepartureDate() + "/" + fareReq.getReturnDate() + fareReq.getSendCode() + fareReq.getArrCode();
                }
                apiRedisService.removeData(redisKey);
            }
            //设置航班推荐结果
            if (HandlerConstants.ROUTE_TYPE_OW.equals(fareReq.getFlightType())) {
                NearFlightRecommend flightRecommend = setNearFlight(fareReq, resp.getFlightInfoList(), useFareV30.get());
                resp.setFlightRecommend(flightRecommend);
            }
            //对于航班已售罄的情况，需要统一状态码为 10001 而非第三方状态码
            resp.setErrorInfo("当日无航班或航班已售磬");
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        }
    }

    /**
     * 设置国际品牌运价权益集合
     */
    private void setBrandRight(String queryType, QueryFlightFareResp resp, PtPdmRequest ptPdmRequest) {
        List<FlightInfoComb> transferFlightInfoList = resp.getTransferFlightInfoList();
        if (CollectionUtils.isNotEmpty(transferFlightInfoList)) {
            transferFlightInfoList.forEach(flightInfoComb -> {
                List<CabinFare> adtCabinFareList = flightInfoComb.getAdtCabinFareList();
                if (CollectionUtils.isNotEmpty(adtCabinFareList)) {
                    adtCabinFareList.forEach(cabinFare -> {
                        if (StringUtils.isNotBlank(cabinFare.getBrandCode())) {
                            String cabinClass = "C".equalsIgnoreCase(cabinFare.getCabinClass().substring(0, 1)) ? "J" : cabinFare.getCabinClass().substring(0, 1);
                            StringBuilder stringBuilder = new StringBuilder(cabinFare.getBrandCode() + cabinClass);
                            String brandCode = stringBuilder.toString();
                            if (FlightQueryTypeEnum.STUDENT.getType().equals(queryType) && StringUtils.isNotBlank(cabinFare.getTourCode())) {
                                brandCode = stringBuilder.append("_").append(cabinFare.getTourCode()).toString();
                            }
                            BrandRightQueryRequest brandRightQueryRequest = new BrandRightQueryRequest();
                            brandRightQueryRequest.setBrandRightCode(brandCode);
                            brandRightQueryRequest.setCabin(cabinFare.getCabinComb().substring(0, 1));
                            ptPdmRequest.setRequest(brandRightQueryRequest);
                            //处理国际品牌运价
                            cabinFare.setInterBrandRightInfos(genInterBrandRightInfos(brandCode, cabinFare, ptPdmRequest));
                            List<LabelInfo> cabinLabelInterBrand = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(cabinFare.getInterBrandRightInfos())) {
                                List<InterBrandRightInfo> collect = cabinFare.getInterBrandRightInfos().stream().filter(interBrandRightInfo -> StringUtils.isNotBlank(interBrandRightInfo.getState())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect)) {
                                    for (InterBrandRightInfo brandRightInfo : collect) {
                                        if ("服务".equals(brandRightInfo.getRightName()) && "是否包含".equals(brandRightInfo.getState())) {
                                            continue;
                                        }
                                        if (cabinLabelInterBrand.size() >= 3) {
                                            break;
                                        }
                                        LabelInfo labelInfo = new LabelInfo();
                                        labelInfo.setLabelName(brandRightInfo.getRightName());
                                        labelInfo.setPictureUrl("https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>");
                                        labelInfo.setLabelType("normal");
                                        cabinLabelInterBrand.add(labelInfo);
                                    }
                                }
                            }
                            cabinFare.setCabinLabelInterBrand(cabinLabelInterBrand);
                        }
                    });
                }
            });
        }
    }

    /**
     * 国际品牌运价权益列表处理
     *
     * @param
     */
    private List<InterBrandRightInfo> genInterBrandRightInfos(String brandCode, CabinFare cabinFare, PtPdmRequest<BrandRightQueryRequest> ptPdmRequest) {
        List<InterBrandRightInfo> interBrandRightInfos = new ArrayList<>();
        if (StringUtils.isNotBlank(brandCode) && ptPdmRequest != null) {
            PtPdmResponse<List<BrandRightQueryResponse>> response = orderService.queryBrandRight(ptPdmRequest, brandCode);
            if (response != null) {
                if (WSEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
                    //2021-05-10 国际品牌运价权益处理 查询统一订单获取国际品牌运价权益列表
                    List<BrandRightQueryResponse> brandRightQueryResponses = response.getResult();
                    if (CollectionUtils.isNotEmpty(brandRightQueryResponses)) {
                        BrandRightQueryResponse brandRightQueryResponse = brandRightQueryResponses.get(0);
                        if (brandRightQueryResponse != null && CollectionUtils.isNotEmpty(brandRightQueryResponse.getBrandRightDetail())) {
                            InterBrandRightInfo head = new InterBrandRightInfo();
                            head.setRightName("服务");
                            head.setState("是否包含");
                            interBrandRightInfos.add(head);
                            Map<String, BrandRightDto> brandRightMap = handConfig.getBrandRightDtoList().stream().collect(
                                    Collectors.toMap(x -> x.getRightCode() + ":" + x.getServiceState(), x -> x));
                            for (BrandRightDetailVo detailVo : brandRightQueryResponse.getBrandRightDetail()) {
                                if (BrandRightStateEnum.E.getState().equals(detailVo.getServiceState())) {
                                    continue;
                                }
                                InterBrandRightInfo brandRightInfo = new InterBrandRightInfo();
                                brandRightInfo.setRightCode(detailVo.getRightCode());
                                brandRightInfo.setRightName(detailVo.getRightName());
                                BrandRightDto brandRightDto = brandRightMap.get(detailVo.getRightCode() + ":" + detailVo.getServiceState());
                                if (StringUtils.isNotBlank(detailVo.getServiceState())) {
                                    if (brandRightDto != null) {
                                        brandRightInfo.setState(brandRightDto.getDescription());
                                    } else {
                                        brandRightInfo.setState("不享受");
                                    }
                                } else {
                                    if (detailVo.getQuantity() != null) {
                                        DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
                                        StringBuilder state = new StringBuilder();
                                        state.append(decimalFormat.format(detailVo.getQuantity()))
                                                .append(StringUtils.isNotBlank(detailVo.getSpec()) ? detailVo.getSpec() : "")
                                                .append(StringUtils.isNotBlank(detailVo.getServiceStateCon()) ? " " + detailVo.getServiceStateCon() + " " : "")
                                                .append(StringUtils.isNotBlank(detailVo.getUnit()) ? detailVo.getUnit() : "");
                                        brandRightInfo.setState(state.toString());
                                    } else {
                                        brandRightInfo.setState("");
                                    }
                                }
                                //积分权益重置
                                if ("06D".equalsIgnoreCase(brandRightInfo.getRightCode()) || "06N".equalsIgnoreCase(brandRightInfo.getRightCode())) {
                                    brandRightInfo.setState(Integer.toString(cabinFare.getScoreGive()));
                                }
                                //托运行李重置
                                if ("0C6".equalsIgnoreCase(brandRightInfo.getRightCode()) || "0GO".equalsIgnoreCase(brandRightInfo.getRightCode())) {
                                    brandRightInfo.setState(cabinFare.getCheckBaggeage());
                                }
                                interBrandRightInfos.add(brandRightInfo);
                            }
                        }
                    }
                }
            }

        }
        return interBrandRightInfos;
    }

    /**
     * 中转城市信息
     *
     * @param fareReq
     */
    private void queryFlightTranInfo(QueryFlightFareReq fareReq, PtQueryFlightFareRequest queryFlightFareRequest, String ip) {
        List<AirLineLabelDTO> airLineLabelDTOList = basicService.queryCacheFlightLine(fareReq.getSendCode(), fareReq.getArrCode(), fareReq.getChannelCode(), ip);
        if (!StringUtil.isNullOrEmpty(airLineLabelDTOList)) {
            List<String> tranCityList = new ArrayList<>();
            for (AirLineLabelDTO airLineLabelDTO : airLineLabelDTOList) {
                if (!StringUtil.isNullOrEmpty(airLineLabelDTO.getTransitCity())) {
                    String[] tranCitys = airLineLabelDTO.getTransitCity().split(",");
                    for (String tranCity : tranCitys) {
                        if (!tranCityList.contains(tranCity)) {
                            tranCityList.add(tranCity);
                        }
                    }
                }
            }
            if (!StringUtil.isNullOrEmpty(tranCityList)) {
                queryFlightFareRequest.setTransferCitys(tranCityList);
            }
        }
    }

    private boolean checkSign(QueryFlightFareReq fareReq) {
        String json = JsonMapper.buildNormalMapper().toJson(fareReq);
        TreeMap treeMap = JsonMapper.buildNormalMapper().fromJson(json, TreeMap.class);
        if (treeMap != null && !treeMap.isEmpty()) {
            String plaintext = "";
            Set<Map.Entry<String, String>> set = treeMap.entrySet();
            for (Map.Entry<String, String> mapEntry : set) {
                String key = mapEntry.getKey();
                String value = mapEntry.getValue();
                if (!StringUtil.isNullOrEmpty(key) && !"sign".equals(key) && value != null) {
                    plaintext += (key + value);
                }
            }
            plaintext = handConfig.getAvSecret() + plaintext + handConfig.getAvSecret();
            String ciphertext = EncoderHandler.encodeByMD5(plaintext).toLowerCase();
            if (!fareReq.getSign().equals(ciphertext)) {
                return false;
            }
        }
        return true;
    }

    //风控决策控制
    private boolean windControl(String headChannelCode, String headClientVer, String versionCode, String platForm, String ip, QueryFlightFareReq fareReq, HttpServletRequest request, QueryFlightFareResp resp) {
        //APP 请求头无版本信息的直接抛出
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
            if (StringUtil.isNullOrEmpty(headClientVer) || StringUtil.isNullOrEmpty(versionCode) || StringUtil.isNullOrEmpty(platForm)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setFlightInfoList(new ArrayList<>());
                resp.setErrorInfo("无法处理的请求");
                return false;
            }
        }
        //MWEB WEIXIN低于此版本的认为不合法
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode)) {
            if (StringUtil.isNullOrEmpty(headClientVer) || StringUtil.isNullOrEmpty(versionCode) || VersionNoUtil.toMVerInt(versionCode) < VersionNoUtil.toMVerInt(handConfig.getMwebOnlineVer())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setFlightInfoList(new ArrayList<>());
                resp.setErrorInfo("无法处理的请求");
                return false;
            }
        }
        CityInfoDto depCity = localCacheService.getLocalCity(fareReq.getSendCode());
        CityInfoDto arrCity = localCacheService.getLocalCity(fareReq.getArrCode());
        if (depCity == null || arrCity == null) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setFlightInfoList(new ArrayList<>());
            resp.setErrorInfo("无此城市信息");
            return false;
        }
        //航程类型修正处理
        String d = depCity.getIsInternational();
        String a = arrCity.getIsInternational();
        String tripType;
        if (HandlerConstants.FLIGHT_INTER_D.equals(d) && HandlerConstants.FLIGHT_INTER_D.equals(a)) {
            tripType = HandlerConstants.FLIGHT_INTER_D;
        } else {
            tripType = HandlerConstants.FLIGHT_INTER_I;
        }
        //根据配置确认是否放行风控检测
        if (StringUtils.isNotBlank(handConfig.getSwitchTongdunDorI()) && handConfig.getSwitchTongdunDorI().contains(tripType)) {
            return true;
        }
/*        if (HandlerConstants.TRIP_TYPE_D.equals(fareReq.getTripType()) && ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode)) {
            return true;
        }*/
        //生产与预发布环境判断设备指纹不允许为空
        if (EnvEnum.PRO.getEnv().equals(HandlerConstants.ENV) || EnvEnum.PRE.getEnv().equals(HandlerConstants.ENV)) {
            if (ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode)) {
                if ("Y".equals(handConfig.getWxappSwitch()) && StringUtil.isNullOrEmpty(fareReq.getBlackBox())) {
                    resp.setResultCode(WSEnum.NO_DEVICE_BLACKBOX.getResultCode());
                    resp.setFlightInfoList(new ArrayList<>());
                    resp.setErrorInfo(WSEnum.NO_DEVICE_BLACKBOX.getResultInfo());
                    return false;
                }
            } else {
                if (StringUtil.isNullOrEmpty(fareReq.getBlackBox())) {
                    resp.setResultCode(WSEnum.NO_DEVICE_BLACKBOX.getResultCode());
                    resp.setFlightInfoList(new ArrayList<>());
                    resp.setErrorInfo(WSEnum.NO_DEVICE_BLACKBOX.getResultInfo());
                    return false;
                }
            }
        }
        String userAgent = request.getHeader(HandlerConstants.CLIENT_USERAGENT);
        String cookie = request.getHeader(HandlerConstants.CLIENT_COOKIE);
        //同盾设备指纹来源
        String blackBoxFrom = request.getHeader(HandlerConstants.BLACKBOX_FROM);
        // 2020/01/31 暂时关闭微信小程序渠道的国际航班查询
        if (ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode)) {
            String openId = request.getHeader(HandlerConstants.CLIENT_HEADER_OPENID);
            if (StringUtil.isNullOrEmpty(openId)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("当日无航班或航班已售磬");
                return false;
            }
        }
        //同盾参数
        if ("Y".equals(handConfig.getUseTongDun())) {
            String multiProxy = IPUtil.isMultyProxy(request) ? "1" : "0";
            Map<String, String> params = FraudApiInvoker.createFlightQueryRiskControlParam(fareReq, headChannelCode,
                    platForm, ip, userAgent, cookie, multiProxy, depCity, arrCity, headClientVer, blackBoxFrom);
            if (params != null) {
                Antifraud antifraud = new Antifraud(fareReq.getFfpId(), fareReq.getFfpCardNo(), ip, params);
                FraudApiResponse fraudApiResponse = basicService.antifraud(headChannelCode, antifraud);
                if (fraudApiResponse.getSuccess()) {
                    if (fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                        resp.setResultCode(WSEnum.RISK_REJECT.getResultCode());
                        resp.setErrorInfo("查询过于频繁");
                        resp.setFlightInfoList(new ArrayList<>());
                        return false;
                    }
                    fareReq.setIp(StringUtils.isNotBlank(fraudApiResponse.getVirtualIp()) ? fraudApiResponse.getVirtualIp() : ip);
                    return true;
                }
            }
        } else {
            log.debug("航班查询请求未进行同盾验证，会员号：{}", fareReq.getFfpCardNo());
        }
        return true;
    }

    //风控决策控制
    private boolean riskControl(String channelCode, String headChannelCode, String headClientVer, String versionCode, String platForm,
                                String ip, QueryMultipleFlightFareRequest fareReq, HttpServletRequest request, QueryFlightFareResp resp) {
        boolean access = true;
        if (!StringUtil.isNullOrEmpty(fareReq.getFfpId())) {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(fareReq.getFfpId(), fareReq.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return false;
            }
        }
        // 验证请求是否被篡改
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && StringUtil.isNullOrEmpty(fareReq.getSign())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("请求参数不完整");
            return false;
        }
        //签名验证 FIXME 20200715 测试接口暂不加校验
//        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && !checkSign(fareReq)) {
//            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
//            resp.setErrorInfo("请求参数不正确");
//            return false;
//        }
        //验证IP
        if (!this.chkDayVisit(ip, HandlerConstants.AV_SOURCE, "")) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return false;
        }
        //APP 请求头无版本信息的直接抛出
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
            if (StringUtil.isNullOrEmpty(headClientVer) || StringUtil.isNullOrEmpty(versionCode) || StringUtil.isNullOrEmpty(platForm)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo("无法处理的请求");
                return false;
            }
        }
        //MWEB WEIXIN低于此版本的认为不合法
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode)) {
            if (StringUtil.isNullOrEmpty(headClientVer) || StringUtil.isNullOrEmpty(versionCode) || VersionNoUtil.toMVerInt(versionCode) < VersionNoUtil.toMVerInt(handConfig.getMwebOnlineVer())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo("无法处理的请求");
                return false;
            }
            if (StringUtil.isNullOrEmpty(fareReq.getBlackBox())) {
                resp.setResultCode(WSEnum.NO_DEVICE_BLACKBOX.getResultCode());
                resp.setErrorInfo(WSEnum.NO_DEVICE_BLACKBOX.getResultInfo());
                return false;
            }
        }
        //2021-02-07 金卡及以上会员不调用同盾风控
        int levelCode = 0;
        if (StringUtils.isNotBlank(fareReq.getFfpId()) && StringUtils.isNotBlank(fareReq.getFfpCardNo())) {
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberCacheService.queryMemberDetailInfo(request, fareReq.getFfpId(), fareReq.getFfpCardNo(), headChannelCode);
            if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
                levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
            }
        }
        for (MultipleFlightFareRequestSegment segment : fareReq.getSegments()) {
            //国际的第一段调用同盾
            if (HandlerConstants.TRIP_TYPE_I.equals(segment.getTripType())) {
                String userAgent = request.getHeader(HandlerConstants.CLIENT_USERAGENT);
                String cookie = request.getHeader(HandlerConstants.CLIENT_COOKIE);
                Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(channelCode, ip);
                //同盾参数
                if ("Y".equals(handConfig.getUseTongDun())
                        && FraudApiInvoker.needRiskControl(handConfig.getFraudEscapeConfig(), levelCode, ip)) {
                    String multiProxy = IPUtil.isMultyProxy(request) ? "1" : "0";
                    FraudApiResponse fraudApiResponse = FraudApiInvoker.flightQueryRiskControl(fareReq.getBlackBox(), segment.getSendCode(), segment.getArrCode(),
                            segment.getTripType(), fareReq.getFfpCardNo(), headChannelCode, platForm, ip, userAgent, cookie, multiProxy, cityInfoMap);
                    if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setErrorInfo("查询过于频繁");
                        return false;
                    }
                } else {
                    log.debug("多程航班查询请求未进行同盾验证，会员号：{}", fareReq.getFfpCardNo());
                }
            }
        }
        return access;
    }

    /**
     * 航班查询条件建立
     */
    private PtQueryFlightFareRequest createQueryFareRequest(String channelCode, String userNo, QueryFlightFareReq flghtFareQuery) {
        PtQueryFlightFareRequest flightFareReq = new PtQueryFlightFareRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                flghtFareQuery.getFlightType(),
                HandlerConstants.CURRENCY_CODE,
                HandlerConstants.LANG_CODE,
                new ArrayList<>(),
                handConfig.getAvReadRedis()
        );
        if (HandlerConstants.TRIP_TYPE_D.equals(flghtFareQuery.getTripType())) {
            flightFareReq.setFareSource(FareSourceEnum.HO.getFareSource());
        } else if (HandlerConstants.TRIP_TYPE_I.equals(flghtFareQuery.getTripType())) {
            flightFareReq.setFareSource(FareSourceEnum.SEARCH_ONE.getFareSource());
        }
        flightFareReq.setQueryType("0");
        String cabinCode = null;
        if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(flghtFareQuery.getQueryType())) {
            cabinCode = handConfig.getFreeTicketCabin();
        }
        if (FlightQueryTypeEnum.OPEN_FREE_TICKET.getType().equals(flghtFareQuery.getQueryType())) {
            //舱位选择
            cabinCode = handConfig.getOpenFreeTicketCabin();
        }
        if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(flghtFareQuery.getQueryType())) {
            //舱位选择
            cabinCode = handConfig.getAwardFlyFreeTicketCabin();
        }
        if (FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(flghtFareQuery.getQueryType())) {
            //舱位选择
            cabinCode = handConfig.getBusinessFlyFreeTicketCabin();
        }
        if (FlightQueryTypeEnum.THEME_CARD.getType().equals(flghtFareQuery.getQueryType())
                && HandlerConstants.TRIP_TYPE_I.equals(flghtFareQuery.getTripType())) {
            //舱位选择
            cabinCode = handConfig.getThemeCabin();
        }
        //留学生运价
        if (FlightQueryTypeEnum.STUDENT.getType().equals(flghtFareQuery.getQueryType())) {
            flightFareReq.setSpecialFareType(SpecialFareTypeEnum.STU.getCode());
        }
        //国内拥军价格
        if (FlightQueryTypeEnum.YJ_YS.getType().equals(flghtFareQuery.getQueryType())) {
            flightFareReq.setSpecialFareType(SpecialFareTypeEnum.YJ_YS.getCode());
        }
        String sendCode = flghtFareQuery.getSendCode();
        String sendAirportCode = flghtFareQuery.getSendAirportCode();
        String arrCode = flghtFareQuery.getArrCode();
        String arrAirportCode = flghtFareQuery.getArrAirportCode();
        //特殊城市转换
        Map<String, SpecialFareQuery> specialFareQueryMap = handConfig.getSpecialFareQueryMap();
        if (specialFareQueryMap != null) {
            SpecialFareQuery depSpecialFareQuery = specialFareQueryMap.get(sendCode);
            if (depSpecialFareQuery != null) {
                sendCode = depSpecialFareQuery.getCityCode();
                sendAirportCode = depSpecialFareQuery.getAirportCode();
                flghtFareQuery.setSendCode(sendCode);
                flghtFareQuery.setSendAirportCode(sendAirportCode);
            }
            SpecialFareQuery arrSpecialFareQuery = specialFareQueryMap.get(arrCode);
            if (arrSpecialFareQuery != null) {
                arrCode = arrSpecialFareQuery.getCityCode();
                arrAirportCode = arrSpecialFareQuery.getAirportCode();
                flghtFareQuery.setArrCode(arrCode);
                flghtFareQuery.setArrAirportCode(arrAirportCode);
            }
        }
        flightFareReq.getSegCondList().add(new Segment(0, FlightDirection.GO.getCode(), sendCode, arrCode,
                flghtFareQuery.getDepartureDate(), cabinCode, sendAirportCode, arrAirportCode));
        if (HandlerConstants.ROUTE_TYPE_RT.equals(flghtFareQuery.getFlightType())) {
            flightFareReq.getSegCondList().add(new Segment(1, FlightDirection.BACK.getCode(), arrCode, sendCode,
                    flghtFareQuery.getReturnDate(), cabinCode, arrAirportCode, sendAirportCode));
        }
        //运价查询增加查询乘客类型
        String passengerType = flghtFareQuery.getPassengerType();
        List<String> passengerTypes = new ArrayList<>();
        //留学生运价不需要处理乘客类型，默认为空即可
        if (!FlightQueryTypeEnum.STUDENT.getType().equals(flghtFareQuery.getQueryType())) {
            passengerTypes.add(CommonBaseConstants.PASSENGER_TYPE_ADT);
            if (StringUtils.isNotBlank(passengerType)) {
                if (CommonBaseConstants.PASSENGER_TYPE_INF.equals(passengerType)) {
                    passengerTypes.add(CommonBaseConstants.PASSENGER_TYPE_INF);
                } else if (CommonBaseConstants.PASSENGER_TYPE_CHD.equals(passengerType)) {
                    passengerTypes.add(CommonBaseConstants.PASSENGER_TYPE_CHD);
                } else if ("ALL".equals(passengerType)) {
                    passengerTypes.add(CommonBaseConstants.PASSENGER_TYPE_CHD);
                    passengerTypes.add(CommonBaseConstants.PASSENGER_TYPE_INF);
                }
            } else {
                passengerTypes.add(CommonBaseConstants.PASSENGER_TYPE_CHD);
                passengerTypes.add(CommonBaseConstants.PASSENGER_TYPE_INF);
            }
        }
        flightFareReq.setPassengerType(passengerTypes);
        flightFareReq.setChannelCustomerNo(flghtFareQuery.getFfpId());
        return flightFareReq;
    }

    /**
     * 设置航班查询的低价日历
     *
     * @param currencyCode 币种
     * @param routeType
     * @param flightList
     * @param taxInfoList  税费列表
     * @param fareReq      请求参数
     */
    private void setMinPriceCache(String currencyCode, String routeType, List<FlightInfo> flightList, List<FareTaxInfo> taxInfoList, QueryFlightFareReq fareReq, boolean useV30) {
        int cacheDay = 600;//默认10分钟
        String interFlag = fareReq.getTripType();
        String returnDate = fareReq.getReturnDate();
        //微信，微信小程序渠道国际低价日历  转MOBILE
        String channelCode = basicService.getChannelCode(fareReq.getChannelCode(), fareReq.getTripType());
        if (CollectionUtils.isNotEmpty(flightList)) {
            Double totalTax = 0.0;
            //成人税费总计
            FareTaxInfo fareTaxInfo = null;
            if (!StringUtil.isNullOrEmpty(taxInfoList)) {
                fareTaxInfo = taxInfoList.stream().filter(fareTax -> HandlerConstants.PASSENGER_TYPE_ADT.equals(fareTax.getPassengerType())).findFirst().orElse(null);
            }
            if (fareTaxInfo != null) {
                totalTax = fareTaxInfo.getCNTax() + fareTaxInfo.getYQTax() + fareTaxInfo.getQTax() + fareTaxInfo.getOtherTax();
            }
            //按照飞行方向分组
            Map<String, List<FlightInfo>> flightDirectionMap = flightList.stream().collect(Collectors.groupingBy(FlightInfo::getFlightDirection));
            //设置半年内的运价信息的缓存
            toConfigHalfYearPrice(currencyCode, routeType, flightList, fareReq);
            for (Map.Entry<String, List<FlightInfo>> entry : flightDirectionMap.entrySet()) {
                //遍历分组航班
                String key = "";
                String key_copy = "";
                String keyTax = "";
                String keyRound = "";
                Double minPrice = 0.0;
                Double minRoundPrice = 0.0;
                //航班列表中低价航班
                List<FlightInfo> flightInfoList = entry.getValue().stream().filter(f1 -> f1.getMinPrice() != null).collect(Collectors.toList());
                if (!StringUtil.isNullOrEmpty(flightInfoList)) {
                    //存在票价一致 税费不一致的情况
                    FlightInfo minFlight = flightInfoList.stream().min(Comparator.comparingDouble(FlightInfo::getMinPriceAndTax)).orElse(null);
                    if (minFlight != null) {
                        String sendCity = minFlight.getDepCity();
                        String arrCity = minFlight.getArrCity();
                        //后期逐步废弃此key的存储
                        key = "M" + currencyCode + routeType + minFlight.getFlightDate() + sendCity + arrCity;
                        if (useV30 && HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && HandlerConstants.TRIP_TYPE_I.equals(interFlag) && !StringUtil.isNullOrEmpty(returnDate)) {
                            key = "M" + currencyCode + routeType + minFlight.getFlightDate() + "/" + returnDate + minFlight.getDepCity() + minFlight.getArrCity();
                        }
                        key_copy = RedisKeyConfig.COMMON_FLIGHT_MIN_PRICE + channelCode + ":" + key;//此处是为了重新整合之前的低价
                        keyTax = RedisKeyConfig.COMMON_FLIGHT_TAXINFO + channelCode + ":" + key;
                        minPrice = minFlight.getMinPrice();
                        if (fareTaxInfo == null) {
                            totalTax = minFlight.getTotalTax();
                        }
                        cacheDay = DateUtils.dateDiffBySecond(minFlight.getFlightDate());
                    }
                }
                //往返特惠的低价日历
                List<FlightInfo> roundFlightInfoList = entry.getValue().stream().filter(f1 -> f1.getMinRoundPrice() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(roundFlightInfoList)) {
                    FlightInfo minFlight = roundFlightInfoList.stream().min(Comparator.comparingDouble(FlightInfo::getMinRoundPrice)).orElse(null);
                    if (minFlight != null) {
                        key = "M" + currencyCode + routeType + minFlight.getFlightDate() + minFlight.getDepCity() + minFlight.getArrCity();
                        keyRound = RedisKeyConfig.COMMON_FLIGHT_ROUND + channelCode + ":" + key;
                        minRoundPrice = minFlight.getMinRoundPrice();
                        cacheDay = DateUtils.dateDiffBySecond(minFlight.getFlightDate());
                    }
                }
                //同飞行方向中最低价
                if (minRoundPrice > 0) {
                    String minPriceStr = String.valueOf(minRoundPrice);
                    apiRedisService.replaceData(keyRound, NumberUtil.formatNumber(minPriceStr), cacheDay);
                }
                if (minPrice > 0) {
                    //默认不含税价
                    String minPriceStr = String.valueOf(minPrice);
                    //新的缓存目录
                    apiRedisService.replaceData(key_copy, NumberUtil.formatNumber(minPriceStr), cacheDay);
                    //含税价  国际税费只包含在去程
                    if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag) && "G".equals(entry.getKey())) {
                        minPriceStr = String.valueOf(minPrice + totalTax);
                        apiRedisService.replaceData(keyTax, NumberUtil.formatNumber(minPriceStr), cacheDay);
                    }
                } else if (minPrice == 0) {
                    apiRedisService.removeData(key_copy);
                    apiRedisService.removeData(keyRound);
                    if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag)) {
                        apiRedisService.removeData(keyTax);
                    }
                }
            }
        }
    }

    //设置半年内的运价信息的缓存
    private void toConfigHalfYearPrice(String currencyCode, String routeType, List<FlightInfo> flightList, QueryFlightFareReq fareReq) {
        if (CollectionUtils.isEmpty(flightList)) return;

        //按照飞行方向分组
        Map<String, List<FlightInfo>> flightDirectionMap = flightList.stream().collect(Collectors.groupingBy(FlightInfo::getFlightDirection));
        long cacheDay = 600L;//默认10分钟

        String tripType = fareReq.getTripType();// 国际国内标识 I-国际 D-国内
        String returnDate = fareReq.getReturnDate();// 返程日期
        String departureDate = fareReq.getDepartureDate();
        String sendCode = fareReq.getSendCode();
        String arrCode = fareReq.getArrCode();

        String key = "";

        for (Map.Entry<String, List<FlightInfo>> entry : flightDirectionMap.entrySet()) {

            if (HandlerConstants.FLIGHT_INTER_D.equals(tripType)) continue;

            if (StringUtils.isNotEmpty(entry.getValue().get(0).getFlightDate())) {
                cacheDay = DateUtils.dateDiffBySecond(entry.getValue().get(0).getFlightDate());
            }

            if (HandlerConstants.FLIGHT_DIRECTION_GO.equals(entry.getKey()) && HandlerConstants.ROUTE_TYPE_OW.equals(routeType)) {
                key = RedisKeyConfig.COMMON_FLIGHT_HALFYEAR + "H_" + currencyCode + "_" + routeType + "_" + departureDate + "_" + sendCode + "_" + arrCode;
            } else if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && StringUtils.isNotEmpty(returnDate)) {
                key = RedisKeyConfig.COMMON_FLIGHT_HALFYEAR + "H_" + currencyCode + "_" + routeType + "_" + departureDate + "/" + returnDate + "_" + sendCode + "_" + arrCode;
            }

            apiRedisService.replaceData(key, JSON.toJSONString(entry.getValue()), cacheDay);
        }

    }


    /**
     * 设置邻近航班推荐
     *
     * @param fareReq
     * @param flightInfoList
     * @return
     */
    private NearFlightRecommend setNearFlight(QueryFlightFareReq fareReq, List<FlightInfo> flightInfoList, boolean useFareV30) {

        try {
            List<FlightInfo> flightInfos = flightInfoList.stream()
                    .filter(f -> "G".equals(f.getFlightDirection()))
                    .filter(f -> f.getCarrierNo().startsWith("HO"))
                    .filter(f -> f.getMinPrice() != null)
                    .collect(Collectors.toList());
            /*if (StringUtil.isNullOrEmpty(flightInfos)) {
                return null;
            }*/
            //共飞城市设置
            //微信，微信小程序渠道国际低价日历  转MOBILE
            String channelCode = basicService.getChannelCode(fareReq.getChannelCode(), fareReq.getTripType());
            fareReq.setChannelCode(channelCode);
            Map<String, Object> param = new HashMap<>();
            param.put(CITY_CODE, fareReq.getSendCode());
            String key = RedisKeyConfig.COMMON_FLYING_CITY + ":" + fareReq.getSendCode() + ":" + fareReq.getTripType();
            String result = apiRedisService.getData(key);
            boolean isReadRedis = true;
            if (StringUtil.isNullOrEmpty(result)) {
                String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_COMMONCITY_QUERY;
                HttpResult httpResult = this.doPostClient(param, url, null, handConfig.getConnectTimeout(), handConfig.getReadTimeout());
                if (httpResult.isResult()) {
                    result = httpResult.getResponse();
                }
                isReadRedis = false;
            }
            if (!StringUtil.isNullOrEmpty(result)) {
                Map resultMap = JsonUtil.jsonToMap(result);
                if (resultMap != null && WSEnum.SUCCESS.getResultCode().equals(resultMap.get("resultCode"))) {
                    //默认存放一天 获取的是缓存数据不再存入redis
                    if (!isReadRedis) {
                        apiRedisService.putData(key, result, 60L * 60 * 24);
                    }
                    List<Map<String, String>> cityList = (List) resultMap.get("resultObject");
                    Map<String, Map> minMap = new HashMap<>();
                    for (Map map : cityList) {
                        String cityCode = (String) map.get(CITY_CODE);
                        //跳过出发城市相同的
                        if (fareReq.getSendCode().equals(cityCode)) {
                            continue;
                        }
                        String commokey = "M" + "CNY" + fareReq.getFlightType() + fareReq.getDepartureDate() + cityCode + fareReq.getArrCode();
                        String minKey = RedisKeyConfig.COMMON_FLIGHT_MIN_PRICE + fareReq.getChannelCode() + ":" + commokey;
                        if (useFareV30 && HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType())) {
                            minKey = RedisKeyConfig.COMMON_FLIGHT_TAXINFO + fareReq.getChannelCode() + ":" + commokey;
                        }
                        String minPrice = apiRedisService.getData(minKey);
                        if (!StringUtil.isNullOrEmpty(minPrice)) {
                            Double curMinPrice = Double.parseDouble(minPrice);
                            map.put(MIN_PRICE, minPrice);
                            if (minMap.isEmpty()) {
                                minMap.put("min", map);
                            } else {
                                if (curMinPrice < Double.parseDouble((String) minMap.get("min").get(MIN_PRICE))) {
                                    minMap.put("min", map);
                                }
                            }
                        }
                    }
                    if (!minMap.isEmpty()) {
                        Map<String, String> min = minMap.get("min");
                        Optional<FlightInfo> flightInfoOptional = flightInfos.stream().min(Comparator.comparingDouble(FlightInfo::getMinPrice));
                        if (flightInfoOptional.isPresent()) {
                            FlightInfo minFlightInfo = flightInfoOptional.get();//用户搜索的最低价航班
                            Double curMinPrice = minFlightInfo.getMinPrice();
                            if (HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType()) && useFareV30) {
                                curMinPrice = minFlightInfo.getMinPrice() + minFlightInfo.getTotalTax();
                            }
                            if (Double.parseDouble(min.get(MIN_PRICE)) < curMinPrice) {
                                NearFlightRecommend flightRecommend = new NearFlightRecommend();
                                BeanUtils.copyProperties(fareReq, flightRecommend);
                                flightRecommend.setDepCityCode(min.get(CITY_CODE));
                                flightRecommend.setDepCityName(min.get("cityName"));
                                flightRecommend.setArrCityCode(fareReq.getArrCode());
                                flightRecommend.setArrCityName(minFlightInfo.getArrCityName());
                                flightRecommend.setPrice(Double.parseDouble(min.get(MIN_PRICE)));
                                return flightRecommend;
                            }
                        } else {
                            //当查询航班没有时，展示配置的共飞城市中的最低价航线
                            NearFlightRecommend flightRecommend = new NearFlightRecommend();
                            BeanUtils.copyProperties(fareReq, flightRecommend);
                            flightRecommend.setDepCityCode(min.get(CITY_CODE));
                            flightRecommend.setDepCityName(min.get("cityName"));
                            flightRecommend.setArrCityCode(fareReq.getArrCode());
                            flightRecommend.setArrCityName(localCacheService.getLocalCity(fareReq.getArrCode()).getCityName());
                            flightRecommend.setPrice(Double.parseDouble(min.get(MIN_PRICE)));
                            return flightRecommend;
                        }
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("【设置共飞航线】行号：{}，异常信息：", e.getStackTrace()[0].getLineNumber(), e);
            return null;
        }
    }


    /**
     * 设置仓位退改费高低
     */
    private void setReturnFeeDesc(List<FlightInfo> flightInfoList) {
        flightInfoList.stream().forEach(flightInfo -> {
            flightInfo.getCabinFareList().stream().forEach(cabinFare -> {
                String returnFeeDesc = handConfig.getReturnFeeDescMap().get(cabinFare.getCabinCode());
                cabinFare.setReturnFeeDesc(returnFeeDesc);
            });
        });
    }


    /**
     * 设置是否是候补
     */
    private void setFlightFareType(List<FlightInfo> flightInfoList, String routeType) {
        flightInfoList.stream().forEach(flightInfo -> {
            if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && SystemConstants.WAIT.equals(flightInfo.getFlightFareType())) {
                List<CabinFare> cabinFareList = new ArrayList<>();
                flightInfo.setCabinFareList(cabinFareList);
                flightInfo.setFlightFareType("");
            }
        });
    }


    /**
     * 设置航班的特色标签
     *
     * @param flightInfoList
     * @param clientInfo
     */
    private void setFlightLabel(List<FlightInfo> flightInfoList, ClientInfo clientInfo, QueryFlightFareReq fareReq) {
        flightInfoList.parallelStream().forEach(flightInfo -> {
            dealFlightLabel(flightInfo, clientInfo, fareReq);
            //处理返程航班标签
            if (CollectionUtils.isNotEmpty(flightInfo.getFlightInfoReturnList())) {
                flightInfo.getFlightInfoReturnList().parallelStream().forEach(flightInfoBack -> {
                    dealFlightLabel(flightInfoBack, clientInfo, fareReq);
                });
            }
        });
    }


    //处理航班标签
    private void dealFlightLabel(FlightInfo flightInfo, ClientInfo clientInfo, QueryFlightFareReq fareReq) {
        if (flightInfo.getCarrierNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode())) {
            if (StringUtils.isNotBlank(flightInfo.getFareType())) {
                List<LabelInfo> labelsForReturn = new ArrayList<>();
                // wifi Label
                if (flightInfo.isWifiFlag() || "789".equals(flightInfo.getFType())) {
                    LabelInfo wifiLabel = new LabelInfo("机上WIFI", "", LabelTypeEnum.MAIN.getType());
                    wifiLabel.setPictureUrl("https://mediaws.juneyaoair.com/upload/icon/wifi.png");
                    labelsForReturn.add(wifiLabel);
                }
                if (FareBasisEnum.BUS_FARE.getFareBasisCode().equals(flightInfo.getThemeFlight())) {
                    if ("HKG".equals(flightInfo.getArrCity())) {
                        flightInfo.setArrCityName("中国香港全境");
                    }
                    if ("HKG".equals(flightInfo.getDepCity())) {
                        flightInfo.setDepCityName("中国香港全境");
                    }
                    LabelInfo busLabel = new LabelInfo("送包车券", handConfig.getBusFareLabelUrl(), LabelTypeEnum.ACTIVITY.getType());
                    LabelInfo cabinLabel = new LabelInfo("公务舱", "", LabelTypeEnum.NORMAL.getType());
                    labelsForReturn.add(busLabel);
                    labelsForReturn.add(cabinLabel);
                }
                //餐食标签
                Set<String> flightNoSet = flightAggrService.queryLowCarbon(clientInfo, flightInfo.getFlightDate(), flightInfo.getDepAirport(), flightInfo.getArrAirport());
                if (CollectionUtils.isNotEmpty(flightNoSet)) {
                    if (flightNoSet.stream().anyMatch(s -> s.equals(flightInfo.getFlightNo()))) {
                        List<LabelInfo> labelList = handConfig.getLabelInfo("lowCarbon");
                        if (CollectionUtils.isNotEmpty(labelList)) {
                            labelsForReturn.addAll(labelList);
                        }
                    }
                }
                // airline Labels
                List<LabelInfo> airLineLabels = getAirLineLabels(clientInfo.getChannelCode(), clientInfo.getClientIp(), flightInfo);
                labelsForReturn.addAll(airLineLabels);
                // return labels
                flightInfo.setLabelInfoList(labelsForReturn);
            }
        }
        // MU Flight
        if (StringUtils.isNotBlank(flightInfo.getCarrierNo()) && flightInfo.getCarrierNo().startsWith(AirCompanyEnum.MU.getAirCompanyCode())) {
            flightInfo.setSaleInfo("中国东方航空承运");
        }
        // 由新航线规则标签辅助配置
        routeLabelService.handByRouteLabel(flightInfo,clientInfo);
        // 设置 中转系统 行李直挂，联程值机  标签
        routeLabelService.setTransferLabel(flightInfo, fareReq);
    }


    /**
     * 返回航线标签
     *
     * @param channelCode
     * @param ip
     * @param flightInfo
     * @return
     */
    private List<LabelInfo> getAirLineLabels(String channelCode, String ip, FlightInfo flightInfo) {

        List<LabelInfo> airLineLabels = new ArrayList<>();
        // Airline label
        List<AirLineLabelDTO> airLineLabelDTOList = basicService.queryCacheFlightLine(flightInfo.getDepCity(), flightInfo.getArrCity(), flightInfo.getDepAirport(), flightInfo.getArrAirport(), channelCode, ip);

        //标签整合，匹配航班号与适用航班日期比对
        if (!StringUtil.isNullOrEmpty(airLineLabelDTOList)) {
            for (AirLineLabelDTO airLineLabelDTO : airLineLabelDTOList) {
                if (!StringUtil.isNullOrEmpty(airLineLabelDTO.getLabels())) {
                    //
                    List<LabelInfo> finalAirLineLabels = airLineLabels;
                    airLineLabelDTO.getLabels().forEach(t -> {

                        //
                        boolean existFlight = filterFlightNos(t.getFlightNos(), flightInfo.getFlightNo());
                        boolean existFlightDate = filterFlightDates(t.getFitFlightDateList(), flightInfo.getFlightDate(), t.getFloatingDate());
                        boolean existModel = filterAircraftModel(t.getApplicablModels() == null ? "" : t.getApplicablModels(), flightInfo.getFType());
                        boolean existTrans = false;

                        // match transfer
                        if (flightInfo.getTransferInfo() != null && flightInfo.getTransferInfo().getTransferAirPort() != null) {
                            if (airLineLabelDTO.getTransitAirport() != null) {
                                existTrans = airLineLabelDTO.getTransitAirport().equals(flightInfo.getTransferInfo().getTransferAirPort());
                            }
                        }
                        // 没有oneway，故仅判断直飞、中转
                        String labelFareTypeStr = this.addonRemarkToFareTypeStr(airLineLabelDTO.getAddonRemark());
                        // 匹配标签直飞、中转
                        boolean isDirectLabel = "Simple".equals(labelFareTypeStr);

                        // Simple
                        if (FareTypeEnum.SIMPLE.getFare().equals(flightInfo.getFareType())) {
                            if (isDirectLabel && existFlight && existFlightDate && existModel) {
                                LabelInfo labelInfoTemp = new LabelInfo(t.getLabelName(), t.getUrl(), t.getLabelType(), setLabelNum(t.getSequence()),t.getLabelImg());
                                finalAirLineLabels.add(labelInfoTemp);
                            }
                        } else {
                            // ADDON || SPA || ONEWAY
                            if (!isDirectLabel && existFlight && existFlightDate && existModel && existTrans) {
                                LabelInfo labelInfoTemp = new LabelInfo(t.getLabelName(), t.getUrl(), t.getLabelType(), setLabelNum(t.getSequence()),t.getLabelImg());
                                finalAirLineLabels.add(labelInfoTemp);
                            }
                        }
                    });
                }
            }
        }
        // 屏蔽 支付宝、微信 小程序渠道 标签链接跳转
        /*if (ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode)) {
            airLineLabels.forEach(v -> v.setLabelUrl(""));
        }*/

        // 升序排序
        if (!StringUtil.isNullOrEmpty(airLineLabels)) {
            airLineLabels = (airLineLabels.stream().sorted(Comparator.comparingInt(LabelInfo::getLabelNum)).collect(Collectors.toList()));
        }
        return airLineLabels;
    }

    private String addonRemarkToFareTypeStr(String addonRemark) {
        if (addonRemark.equals("N")) {
            return FareTypeEnum.SIMPLE.getFare();
        }
        if (addonRemark.equals("Y")) {
            return FareTypeEnum.ADDON.getFare();
        }
        if (addonRemark.equals("S")) {
            return FareTypeEnum.SPA.getFare();
        }
        return "";
    }

    /**
     * 航班标签-机型 是否符合
     *
     * @param labels
     * @param model
     * @return
     */
    private boolean filterAircraftModel(String labels, String model) {
        List<String> labelList = Arrays.asList(labels.split(","));
        if (StringUtil.isNullOrEmpty(labels)) {
            return true;
        } else {
            if (labelList.contains(model)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 匹配航班号是否符号条件
     *
     * @param flightNos
     * @return
     */
    private boolean filterFlightNos(List<String> flightNos, String flightNo) {
        if (StringUtil.isNullOrEmpty(flightNos)) {
            return true;
        } else {
            if (flightNos.contains(flightNo)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 匹配适应的航班日期
     *
     * @param fitFlightDateList
     * @param flightDateStr
     * @return
     */
    private boolean filterFlightDates(List<FitFlightDate> fitFlightDateList, String flightDateStr, String floatingDate) {
        // 适应航班日期、浮动生效范围 均未设定时返回
        if (CollectionUtils.isEmpty(fitFlightDateList) && Strings.isEmpty(floatingDate)) {
            return true;
        }

        boolean fitListBool = false;
        boolean floationgBool = false;

        try {

            // matching fit flight date
            //航班日期比对，包含首尾日期
            if (CollectionUtils.isNotEmpty(fitFlightDateList)) {
                for (FitFlightDate fitFlightDate : fitFlightDateList) {
                    Date startDate = DateUtils.toDate(fitFlightDate.getStartDate(), DateUtils.YYYY_MM_DD_PATTERN);
                    Date endDate = DateUtils.toDate(fitFlightDate.getEndDate(), DateUtils.YYYY_MM_DD_PATTERN);
                    Date flightDate = DateUtils.toDate(flightDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                    if (flightDate.getTime() >= startDate.getTime() && flightDate.getTime() <= endDate.getTime()) {
                        fitListBool = true;
                    }
                }
            }

            // match floating date
            // “近0天”至“近30天”，近30天的所有符合条件的航线上展示。
            if (Strings.isNotEmpty(floatingDate)) {
                String[] floatingDay = floatingDate.split("/");
                // check format
                if (new ArrayList<>(Arrays.asList(floatingDay)).stream().allMatch(NumberUtils::isDigits)) {
                    int forwardOffsetDay = Integer.parseInt(floatingDay[0]);
                    int backwardOffsetDay = Integer.parseInt(floatingDay[1]);
                    Date forwardDate = DateUtil.offsetDay(new Date(), forwardOffsetDay - 1);
                    Date backwardDate = DateUtil.offsetDay(new Date(), backwardOffsetDay);
                    Date flightDate = DateUtils.toDate(flightDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                    if (DateUtil.isIn(flightDate, forwardDate, backwardDate)) {
                        floationgBool = true;
                    }
                }
            }


        } catch (Exception e) {
            log.error("日志转换时间问题:{}", JsonUtil.objectToJson(fitFlightDateList), e);
        }
        return fitListBool || floationgBool;
    }


    /**
     * 留学生运价入口
     * 沿用原无限卡位置控制字段 "POSITION_AVFARE_CABIN"，实际处理留学生运价入口
     *
     * @param deptCode
     * @param arrCode
     * @param clientInfo
     * @return
     */
    private List<LabelInfo> setIntlStudentFareEntry(String deptCode, String arrCode, ClientInfo clientInfo) {
        // 获取留学生运价广告位 “POSITION_AVFARE_CABIN” （原无限卡）
        PictureRequest pictureRequest = new PictureRequest();
        pictureRequest.setChannelCode(clientInfo.getHeadChannelCode());
        pictureRequest.setPicLocation("POSITION_AVFARE_CABIN");
        pictureRequest.setPlatformInfo(clientInfo.getPlatform());
        pictureRequest.setClientVersion(clientInfo.getClientVersion());
        pictureRequest.setVersionCode(clientInfo.getVersionCode());
        List<PictureDto> pictureDtoList = basicService.getPictureList(pictureRequest);
        if (CollectionUtils.isNotEmpty(pictureDtoList)) {
            List<LabelInfo> labelInfoList = new ArrayList<>();
            for (PictureDto pictureDto : pictureDtoList) {
                // 未配置航线默认适用全航线 不认为是留学生运价
                if (StringUtils.isBlank(pictureDto.getAirlines())) {
                    LabelInfo labelInfo = convertoLabelInfo(pictureDto, false);
                    labelInfoList.add(labelInfo);
                } else {
                    // 匹配航线
                    if (matchFlightRoute(deptCode, arrCode, pictureDto.getAirlines())) {
                        LabelInfo labelInfo = convertoLabelInfo(pictureDto, true);
                        labelInfoList.add(labelInfo);
                    }
                }
            }
            return labelInfoList;
        }
        return new ArrayList<>();
    }

    private LabelInfo convertoLabelInfo(PictureDto pictureDto, boolean intlStd) {
        LabelInfo labelInfo = new LabelInfo();
        labelInfo.setLabelName(pictureDto.getTitle());
        labelInfo.setPictureUrl(pictureDto.getPicUrl());
        labelInfo.setLabelUrl(pictureDto.getUrl());
        labelInfo.setSubLabelDesc(pictureDto.getLittleTitle());
        labelInfo.setIntlStd(intlStd);
        return labelInfo;
    }


    /**
     * 根据起点和终点代码匹配航班路线
     * 此方法用于判断给定的起点和终点代码是否与PictureDto对象中的任一航班路线匹配
     * 航班路线格式为"SHA-PKX"，可能包含多个路线，以逗号分隔
     *
     * @param deptCode 起点代码
     * @param arrCode  终点代码
     * @param airlines 包含航班路线信息的字符串
     * @return 如果找到匹配的航班路线，则返回true；否则返回false
     */
    private static boolean matchFlightRoute(String deptCode, String arrCode, String airlines) {
        // SHA-PKX,PKX-SHA,SHA-PKX,PKX-SHA,PVG-HEL e.g.
        String[] airLines = airlines.split(",");
        // A-B 匹配 deptCode-arrCode 均满足，添加标签,返回
        for (String airLine : airLines) {
            String[] airLineArr = airLine.split("-");
            if (airLineArr.length == 2) {
                String deptCodeStr = airLineArr[0];
                String arrCodeStr = airLineArr[1];
                if (deptCode.equalsIgnoreCase(deptCodeStr) && arrCode.equalsIgnoreCase(arrCodeStr)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 航班重要提醒内容
     *
     * @param depCode 出发城市
     * @param arrCode 到达城市
     */
    private void setFlightReminder(String depCode, String arrCode, QueryFlightFareResp
            resp, List<Segment> segCondList, String queryType) {
        //2021-08-16 处理国际重要提示信息
        List<FlightReminderV2> flightReminderV2List = handConfig.getFlightReminderV2List();
        String interFlag = resp.getInterFlag();
        //消息提醒返回结果
        ArrayList<FlightReminder> flightReminders = new ArrayList<>();
        if (FlightQueryTypeEnum.OPEN_FREE_TICKET.getType().equals(queryType)) {
            resp.setFlightWarns(flightReminders);
            return;
        }
        if (CollectionUtils.isNotEmpty(flightReminderV2List)) {
            List<FlightReminderV2> flightReminderV2 = flightReminderV2List.stream().filter(flightReminderV21 -> interFlag.equals(flightReminderV21.getInterFlag())).collect(Collectors.toList());
            for (FlightReminderV2 reminderV2 : flightReminderV2) {
                if (null != reminderV2.getFlightReminder()) {
                    FlightReminder reminder = reminderV2.getFlightReminder();
                    boolean exist = false;
                    for (Segment segment : segCondList) {
                        String flightDate = segment.getFlightDate();
                        //2021-08-16 处理国际重要提示信息
                        Date begin = DateUtils.toDate(reminder.getFlightStartDate());
                        Date end = DateUtils.toDate(reminder.getFlightEndDate());
                        Date date = DateUtils.toDate(flightDate);
                        if (DateUtils.compareDate(begin, end, date) && !exist) {
                            flightReminders.add(reminder);
                            exist = true;
                        }
                    }
                }
            }
        }
        CityInfoDto depCityInfo = localCacheService.getLocalCity(depCode);
        ArrayList<CityWarnInfo> cityDeptWarnInfos = new ArrayList<>();
        if (depCityInfo != null && !StringUtil.isNullOrEmpty(depCityInfo.getCityWarnInfoList())) {
            for (CityWarnInfoDTO cityWarnInfoDTO : depCityInfo.getCityWarnInfoList()) {
                if (!StringUtil.isNullOrEmpty(cityWarnInfoDTO.getDepWarnContent())) {
                    //同一城市存在多条记录，现在出发和到达城市分开了
                    CityWarnInfo cityWarnInfo = new CityWarnInfo();
                    cityWarnInfo.setStartDate(cityWarnInfoDTO.getDepStartDate());
                    cityWarnInfo.setEndDate(cityWarnInfoDTO.getDepEndDate());
                    cityWarnInfo.setWarnTitle(cityWarnInfoDTO.getDepWarnTitle());
                    cityWarnInfo.setWarnContent(cityWarnInfoDTO.getDepWarnContent());
                    cityWarnInfo.setDepRouteType(cityWarnInfoDTO.getDepRouteType());
                    cityDeptWarnInfos.add(cityWarnInfo);
                }
            }
            if (CollectionUtils.isNotEmpty(cityDeptWarnInfos)) {
                for (CityWarnInfo ci : cityDeptWarnInfos) {
                    if (DateUtils.compareCurrentDate(ci.getStartDate() + " 00:00", ci.getEndDate() + " 23:59", DateUtils.YYYY_MM_DD_HH_MM_PATTERN)
                            && StringUtils.isNotBlank(ci.getDepRouteType()) && ci.getDepRouteType().contains(interFlag)) {
                        FlightReminder flightReminder = new FlightReminder();
                        flightReminder.setTitle(ci.getWarnTitle());
                        flightReminder.setReminderContent(ci.getWarnContent());
                        flightReminders.add(flightReminder);
                    }
                }
            }
        }


        CityInfoDto arrCityInfo = localCacheService.getLocalCity(arrCode);
        ArrayList<CityWarnInfo> cityArrWarnInfos = new ArrayList<>();
        if (arrCityInfo != null && !StringUtil.isNullOrEmpty(arrCityInfo.getCityWarnInfoList())) {
            for (CityWarnInfoDTO cityWarnInfoDTO : arrCityInfo.getCityWarnInfoList()) {
                if (!StringUtil.isNullOrEmpty(cityWarnInfoDTO.getArrWarnContent())) {
                    //同一城市存在多条记录，现在出发和到达城市分开了
                    CityWarnInfo cityWarnInfo = new CityWarnInfo();
                    cityWarnInfo.setStartDate(cityWarnInfoDTO.getArrStartDate());
                    cityWarnInfo.setEndDate(cityWarnInfoDTO.getArrEndDate());
                    cityWarnInfo.setWarnTitle(cityWarnInfoDTO.getArrWarnTitle());
                    cityWarnInfo.setWarnContent(cityWarnInfoDTO.getArrWarnContent());
                    cityWarnInfo.setArrRouteType(cityWarnInfoDTO.getArrRouteType());
                    cityArrWarnInfos.add(cityWarnInfo);
                }
            }
            if (CollectionUtils.isNotEmpty(cityArrWarnInfos)) {
                for (CityWarnInfo ci : cityArrWarnInfos) {
                    if (DateUtils.compareCurrentDate(ci.getStartDate() + " 00:00", ci.getEndDate() + " 23:59", DateUtils.YYYY_MM_DD_HH_MM_PATTERN)
                            && StringUtils.isNotBlank(ci.getArrRouteType()) && ci.getArrRouteType().contains(interFlag)) {
                        FlightReminder flightReminder = new FlightReminder();
                        flightReminder.setTitle(ci.getWarnTitle());
                        flightReminder.setReminderContent(ci.getWarnContent());
                        flightReminders.add(flightReminder);
                    }
                }
            }

        }
        resp.setFlightWarns(flightReminders);
    }

    //排序转换
    private int setLabelNum(String labelNum) {
        try {
            return StringUtil.isNullOrEmpty(labelNum) ? 0 : Integer.parseInt(labelNum);
        } catch (Exception e) {
            return 99;
        }

    }

    //会员专享营销规则设置
    private void setActivityAdditionalScoreGive(List<FlightInfo> flightInfoList, String ruleId) {
        if (StringUtil.isNullOrEmpty(flightInfoList)) {
            return;
        }
        List<MarketActivityRuleDetails> rules = null;
        String key = RedisKeyConfig.AV_OBJECT_CONVERT_V2_ACTIVITY + ruleId;
        String activityRuleStr = apiRedisService.getData(key);
        if (StringUtils.isNotBlank(activityRuleStr)) {
            rules = (List<MarketActivityRuleDetails>) JsonUtil.jsonToBean(activityRuleStr, new TypeToken<List<MarketActivityRuleDetails>>() {
            }.getType());
        } else {
            PtApiCRMRequest<MarketActivityRuleQueryRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
            Header header = new Header();
            header.setTimestamp(System.currentTimeMillis());
            header.setMemberId(-1L);
            ptApiCRMRequest.setHeader(header);
            ptApiCRMRequest.setChannel(HandlerConstants.M_CHANNEL_CODE);
            ptApiCRMRequest.setChannelPwd(HandlerConstants.M_CLIENT_PWD);
            ptApiCRMRequest.setData(new MarketActivityRuleQueryRequest(SingletonList.newSingletonList(ruleId)));
            PtCRMResponse<MarketActivityRuleQueryResponse> response = memberService.marketActivityRuleQuery(ptApiCRMRequest);
            if (response.isIsSuccess()) {
                rules = response.getData().getRuleDetails();
                if (CollectionUtils.isNotEmpty(rules)) {
                    MarketActivityRuleDetails ruleDetails = rules.get(0);
                    long saveTime = ruleDetails.getFlightEndDate() - new Date().getTime();
                    if (saveTime > 0) {
                        //默认存放30天
                        apiRedisService.putData(key, JsonUtil.objectToJson(rules),
                                saveTime > 3600L * 24 * 30 ? 3600L * 24 * 30 : saveTime);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(rules)) {
            rules.forEach(rule -> {
                List<String> applyClass = rule.getApplyClass();
                flightInfoList.stream().forEach(flightInfo -> {
                    Date flightDate = DateUtils.toDate(flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                    String airCode = flightInfo.getCarrierNo();
                    if (!StringUtil.isNullOrEmpty(flightInfo.getCabinFareList()) && airCode.startsWith(AirCompanyEnum.HO.getAirCompanyCode())
                            && null != flightDate && flightDate.getTime() >= rule.getFlightBeginDate() && flightDate.getTime() <= rule.getFlightEndDate()) {
                        flightInfo.getCabinFareList().forEach(cabinFare -> {
                            if (!StringUtil.isNullOrEmpty(applyClass)) {
                                if (applyClass.contains(cabinFare.getCabinCode())) {
                                    if ("P".equals(rule.getScoreType())) {
                                        // 百分比
                                        cabinFare.setAdditionalScoreGive(NumberUtil.doubleRoundUpInt(rule.getPromotionValue()
                                                .multiply(BigDecimal.valueOf(cabinFare.getScoreGive())).doubleValue()));
                                    } else if ("F".equals(rule.getScoreType())) {
                                        //固定值
                                        cabinFare.setAdditionalScoreGive(rule.getPromotionValue().intValue());
                                    }
                                }
                            } else {
                                if ("P".equals(rule.getScoreType())) {
                                    // 百分比
                                    cabinFare.setAdditionalScoreGive(NumberUtil.doubleRoundUpInt(rule.getPromotionValue()
                                            .multiply(BigDecimal.valueOf(cabinFare.getScoreGive())).doubleValue()));
                                } else if ("F".equals(rule.getScoreType())) {
                                    //固定值
                                    cabinFare.setAdditionalScoreGive(rule.getPromotionValue().intValue());
                                }
                            }
                        });
                    }
                });
            });
        }
    }

    /**
     * 根据机场过滤对应的航班信息
     *
     * @param sendCode
     * @param arrCode
     */
    private void filterFlightInfo(String sendCode, String arrCode, QueryFlightFareResp resp) {
        if ("PKX".equals(sendCode) || "PKX".equals(arrCode)) {
            if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode()) && !StringUtil.isNullOrEmpty(resp.getFlightInfoList())) {
                List newFlightInfoList = resp.getFlightInfoList().stream().filter(flightInfo ->
                        filter(sendCode, arrCode, flightInfo)
                ).collect(Collectors.toList());
                resp.setFlightInfoList(newFlightInfoList);
            }
        }
    }

    private boolean filter(String sendCode, String arrCode, FlightInfo flightInfo) {
        if (FlightDirection.GO.getCode().equals(flightInfo.getFlightDirection())) {
            return sendCode.equals(flightInfo.getDepAirport()) && arrCode.equals(flightInfo.getArrAirport());
        } else {
            return sendCode.equals(flightInfo.getArrAirport()) && arrCode.equals(flightInfo.getDepAirport());
        }
    }

    @RequestMapping(value = "queryUnusedUnlimitedFly", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询畅飞卡未出行航段数", notes = "查询畅飞卡未出行航段数")
    @InterfaceLog
    public BaseResp queryUnusedUnlimitedFly(@RequestBody BaseReq<UserInfoMust> req, HttpServletRequest request) {
        BaseResp<UnusedUnlimitedFlyResponse> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            UnusedUnlimitedFlyResponse response = new UnusedUnlimitedFlyResponse();
            List<UnlimitedFlyBindRecord> unlimitedFlyBindRecords = unlimitedFlyService.listFlyCardBindRecord(req.getChannelCode(), req.getRequest().getFfpId(), ip);
            if (CollectionUtils.isNotEmpty(unlimitedFlyBindRecords)) {
                Optional<UnlimitedFlyBindRecord> record = unlimitedFlyBindRecords.stream().filter(unlimitedFlyBindRecord ->
                        "yes".equalsIgnoreCase(unlimitedFlyBindRecord.getBindStatus())).findFirst();
                if (record.isPresent()) {
                    PtQueryUnlimitedFlyRequest ptQueryUnlimitedFlyRequest = new PtQueryUnlimitedFlyRequest("10",
                            req.getChannelCode(),
                            this.getChannelInfo(req.getChannelCode(), "10"),
                            req.getRequest().getFfpId(),
                            req.getRequest().getFfpCardNo(),
                            record.get().getVoucherNo());
                    HttpResult result = this.doPostClient(ptQueryUnlimitedFlyRequest, HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_UNLIMITED_FLY_ORDER, HttpUtil.getHeaderMap(ip, ""));
                    if (!result.isResult()) {
                        throw new NetworkException("网络错误，查询未出行畅飞卡订单失败");
                    }
                    PtQueryUpgradeCardResponse ptQueryUpgradeCardResponse = JsonUtil.fromJson(result.getResponse(), PtQueryUpgradeCardResponse.class);
                    if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptQueryUpgradeCardResponse.getResultCode())) {
                        throw new OperationFailedException("查询未出行畅飞卡订单失败");
                    }
                    response.setUnusedFlightNo(ptQueryUpgradeCardResponse.getCount());
                    // 2校验账户中升舱卡升舱的订单是否多于3张
                    if (4 <= response.getUnusedFlightNo()) {
                        response.setMessage("您已累计4段订座未使用航段请先使用再预订");
                    }
                } else {
                    response.setMessage("未绑定畅飞卡");
                }
            }
            resp.setObjData(response);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }


    @RequestMapping(value = "queryAdvertisements", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "queryAdvertisements", notes = "广告位查询")
    @InterfaceLog
    public BaseResp<Advertisments> queryAdvertisements(@RequestBody @Validated BaseReq<Advertisement> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<Advertisments> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        ClientInfo clientInfo = initClientInfo(request);
        try {
            //参数有效性检验
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS_925_2.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS_925_2.getResultInfo());
                return resp;
            }
            Advertisments advertisments = new Advertisments();

            String channelCode = req.getChannelCode();
            String headChannelCode = StringUtils.isNotEmpty(request.getHeader(HandlerConstants.TOKEN_CHANNELCODE))
                    ? request.getHeader(HandlerConstants.TOKEN_CHANNELCODE) : channelCode;

            Advertisement requestBody = req.getRequest();

            //1. 查询配置的酒店广告
            List<HotelProductRespDTO> hotelProductRespDTOS = basicService
                    .queryHotelProductList(headChannelCode, ip, req.getRequest().getLocation() == null ? "1" : req.getRequest().getLocation(), requestBody.getDepartureDate(), Collections.singletonList(requestBody.getArrCode()));
            // filter mismatched interFlag, retain null
            if (CollectionUtils.isNotEmpty(hotelProductRespDTOS)) {
                hotelProductRespDTOS = hotelProductRespDTOS.stream().filter(hotelProductRespDTO -> {
                    if (StringUtils.isBlank(hotelProductRespDTO.getInterFlag())) {
                        return true;
                    }
                    return hotelProductRespDTO.getInterFlag().equals(requestBody.getInterFlag());
                }).collect(Collectors.toList());
                // sort by sortNum desc, including null
                hotelProductRespDTOS.sort(Comparator.comparing(HotelProductRespDTO::getSortNum, Comparator.nullsLast(Comparator.reverseOrder())));
            }

            List<LabelInfo> flightAdvertisementList = new ArrayList<>();
            for (HotelProductRespDTO hotelProductRespDTO : hotelProductRespDTOS) {
                LabelInfo labelInfo = new LabelInfo();
                labelInfo.setPictureUrl(hotelProductRespDTO.getBannerUrl());
                labelInfo.setLabelName(hotelProductRespDTO.getTitle());
                labelInfo.setLabelDetail(hotelProductRespDTO.getSecondTitle());
                labelInfo.setLabelUrl(hotelProductRespDTO.getUrl());
                flightAdvertisementList.add(labelInfo);
            }

            // 留学生运价广告入口,仅限舱位列表页
            if ("3".equals(requestBody.getLocation())) {
                List<LabelInfo> intlStudentFareEntry = this.setIntlStudentFareEntry(requestBody.getDeptCode(), requestBody.getArrCode(), clientInfo);
                flightAdvertisementList.addAll(intlStudentFareEntry);
            }
            //
            advertisments.setFlightAdvertisement(flightAdvertisementList);
            resp.setObjData(advertisments);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;

        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }


}
