package com.juneyaoair.mobile.handler.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Sets;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FareBasisEnum;
import com.juneyaoair.appenum.coupon.CouponStateEnum;
import com.juneyaoair.appenum.coupon.CouponTypeEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.order.OrderCouponStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.coupon.request.MyCoupon;
import com.juneyaoair.baseclass.request.booking.PassengerInfo;
import com.juneyaoair.baseclass.request.coupons.*;
import com.juneyaoair.baseclass.request.multiDiscount.QueryMultiDiscountReq;
import com.juneyaoair.baseclass.response.av.FlightInfo;
import com.juneyaoair.baseclass.response.av.SegmentCabinInfo;
import com.juneyaoair.baseclass.response.coupons.*;
import com.juneyaoair.baseclass.salecoupon.response.SaleCoupon;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.flight.BaseResponse;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.coupon.CouponStyle;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.comm.SourceType;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.config.bean.TempleteConfig;
import com.juneyaoair.mobile.handler.controller.util.CouponObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.CouponUtil;
import com.juneyaoair.mobile.handler.service.ICouponService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.service.MobileMemberService;
import com.juneyaoair.mobile.handler.service.PdmService;
import com.juneyaoair.mobile.handler.util.ChannelUtils;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.*;
import com.juneyaoair.thirdentity.member.response.CrmPhoneInfo;
import com.juneyaoair.thirdentity.request.booking.PtPassengerInfo;
import com.juneyaoair.thirdentity.request.booking.PtSegmentInfo;
import com.juneyaoair.thirdentity.request.coupon.*;
import com.juneyaoair.thirdentity.request.multiDiscount.MultiDiscountRequest;
import com.juneyaoair.thirdentity.response.multiDiscount.MultiDiscountResponse;
import com.juneyaoair.thirdentity.salecoupon.request.PtSaleCouponGetRequest;
import com.juneyaoair.thirdentity.salecoupon.request.RedemEquityUnitsParam;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponGetResponse;
import com.juneyaoair.thirdentity.salecoupon.response.RedemEquityUnitsResult;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.VersionNoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by qinxiaoming on 2016-4-26.
 */

@RestController
@RequestMapping("/couponService")
@Api(value = "CouponController", tags = {"机票优惠券"})
public class CouponController extends BassController {
    @Autowired
    private TempleteConfig templeteConfig;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private LocalCacheService localCacheService;

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private CrmWSClient crmClient;

    @Autowired
    private ICouponService couponService;
    @Autowired
    private PdmService pdmService;
    @Autowired
    private MobileMemberService mobileMemberService;

    private final static int LIMITAPPVER = 5050000;
    private final static String SERVICE_NAME = "优惠券服务";

    //优惠活动券领取
    @RequestMapping(value = "/receiveCoupon", method = RequestMethod.POST)
    public ReceiveCouponResponse receiveCoupon(@RequestBody ReceiveCouponReq receiveReq, HttpServletRequest request) {
        String channelCode = receiveReq.getChannelCode();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        JsonMapper jsonMapper = JsonMapper.buildNormalMapper();
        String reqJson = jsonMapper.toJson(receiveReq);
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        ReceiveCouponResponse resp = new ReceiveCouponResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ReceiveCouponReq>> violations = validator.validate(receiveReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(receiveReq.getFfpId(), receiveReq.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        ChannelCodeEnum channelCodeEnum = ChannelCodeEnum.checkEnum(realChannelCode);
        if (channelCodeEnum == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("不正确的请求参数");
            return resp;
        }
        resp.setErrorInfo("系统升级维护中");
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        return resp;
    }

    //口令兑换领取优惠券(优惠券绑定)
    @RequestMapping(value = "/pwdExchange", method = RequestMethod.POST)
    public Object pwdExchange(@RequestBody ReceiveCouponReq receiveReq, HttpServletRequest request) {
        ActionBindCouponResponse resp = new ActionBindCouponResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ReceiveCouponReq>> violations = validator.validate(receiveReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(receiveReq.getFfpId(), receiveReq.getLoginKeyInfo(), receiveReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        // 判断优惠券兑换增加验证码输入是否正确 加上版本控制
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        // MOBILE 版本低于64000提示升级APP版本
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) < 64000) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("您的版本不支持，请升级app！");
            return resp;
        }
        if ((ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode))
                || (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= NumberUtils.toInt(handConfig.getMwebOnlineVer()))
                || (ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= NumberUtils.toInt(handConfig.getMiniOnlineVer()))) {
            if (StringUtils.isBlank(receiveReq.getCellPhone())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("优惠券兑换短信验证手机号不能为空");
                return resp;
            }
            if (StringUtils.isBlank(receiveReq.getVerifyCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("优惠券兑换验证码不能为空");
                return resp;
            }
            if (!checkVeriCode("SMS:" + receiveReq.getCellPhone() + SourceType.COUPON_EXCHANGE.getValue(), receiveReq.getVerifyCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("验证码错误或已经失效！");
                return resp;
            } else {
                // 验证成功，清除账号限制
                this.clearDayVisit("", receiveReq.getCellPhone(), SourceType.COUPON_EXCHANGE.getValue(), "");
            }
        }
        ActionBindCouponRequest bindCouponRequest = createActionBindCouponRequest(receiveReq, "CRM");
        HttpResult serviceResult = doPost(bindCouponRequest, HandlerConstants.URL_FARE + HandlerConstants.BIND_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (ActionBindCouponResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), ActionBindCouponResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    //领取成功
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else if ("9002".equals(resp.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("暂不可访问");
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询优惠券出错结果:" + JsonUtil.objectToJson(bindCouponRequest));
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }

    public boolean checkVeriCode(String key, String code) {
        String veryCodeCacheStr = apiRedisService.getData(key);
        log.info("优惠券兑换key：{}", key);
        log.info("优惠券兑换验证码：{}", veryCodeCacheStr);
        boolean flag = !(StringUtils.isBlank(code) || StringUtils.isBlank(veryCodeCacheStr) || !code.equals(veryCodeCacheStr));
        // 验证通过清除缓存
        if (flag) {
            apiRedisService.removeData(key);
        }
        return flag;
    }

    /**
     * 优惠活动券查询(APP已不使用此接口)
     *
     * @param couponActivityReq
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryCouponActivity", method = RequestMethod.POST)
    public CouponActivityResponse availQueryCouponActivity(@RequestBody QCouponActivityReq couponActivityReq, HttpServletRequest request) {
        CouponActivityResponse resp = new CouponActivityResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QCouponActivityReq>> violations = validator.validate(couponActivityReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        resp.setErrorInfo("系统升级维护中");
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        return resp;
    }

    //航班可使用优惠券查询  机票优惠券
    @ApiOperation(value = "航班可使用机票优惠券", notes = "航班可使用机票优惠券")
    @RequestMapping(value = "/availCouponsQuery", method = RequestMethod.POST)
    @InterfaceLog
    public AvailCouponsResponse availQueryCoupons(@RequestBody @Validated QueryCouponReq couponReq, BindingResult bindingResult, HttpServletRequest request) {
        AvailCouponsResponse resp = new AvailCouponsResponse();
        String ip = this.getClientIP(request);
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //校验参数
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponReq.getFfpId(), couponReq.getLoginKeyInfo(), couponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (SystemConstants.WAIT.equals(couponReq.getFlightFareType())){
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo("机票后补不支持优惠券");
            return resp;
        }
        resp = couponService.queryAvailCoupon(couponReq,resp,realChannelCode,ip,request);
        return resp;
    }

    //航班可使用优惠券查询  行李券
    @ApiOperation(value = "availQueryCoupons", notes = "航班可使用行李券")
    @RequestMapping(value = "/availCouponsQueryBG", method = RequestMethod.POST)
    @InterfaceLog
    @Deprecated
    public AvailCouponsResponse availQueryCouponsBG(@RequestBody @Validated QueryCouponReq couponReq,BindingResult bindingResult, HttpServletRequest request) {
        AvailCouponsResponse resp = new AvailCouponsResponse();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponReq.getFfpId(), couponReq.getLoginKeyInfo(), couponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        return couponService.queryAvailCouponBG(couponReq,resp);

    }



    //按状态查询优惠券  我的优惠券
    @ApiOperation(value = "查询我的账户中的优惠券数量", notes = "查询我的账户中的优惠券数量")
    @RequestMapping(value = "/couponCountQuery", method = RequestMethod.POST)
    public AvailCouponsResponse couponCountQuery(@RequestBody @Validated QueryCouponReq couponReq, BindingResult bindingResult, HttpServletRequest request) {
        AvailCouponsResponse resp = new AvailCouponsResponse();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponReq.getFfpId(), couponReq.getLoginKeyInfo(), couponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String timestamp = DateUtils.getCurrentTimeStr();
        CouponQueryRequest couponQueryRequest = CreateQueryCouponRequest(couponReq, couponReq.getChannelCode());
        couponQueryRequest.setSale(1);  //非可售
        HttpResult serviceResult = doPostClient(couponQueryRequest, HandlerConstants.New_URL_FARE + HandlerConstants.SUB_QUERY_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (AvailCouponsResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), AvailCouponsResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setTimeStamp(timestamp);
                    if (CollectionUtil.isEmpty(resp.getAvailCouponList())) {
                        resp.setAvailCouponList(new ArrayList<>());
                    }
                    if (StringUtil.isNullOrEmpty(couponReq.getClientVersionNo())) {//老旧的版本过滤第三方优惠券
                        List oldList = new ArrayList<>();
                        for (AvailCoupon availCoupon : resp.getAvailCouponList()) {
                            if (CouponTypeEnum.D.getType().equals(availCoupon.getCouponType()) || "T".equals(availCoupon.getCouponType())) {
                                oldList.add(availCoupon);
                                resp.setAvailCouponList(oldList);
                            }
                        }
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询优惠券出错结果:", e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }



    //按状态查询优惠券  我的优惠券
    @ApiOperation(value = "查询我的账户中的优惠券", notes = "查询我的账户中的优惠券")
    @RequestMapping(value = "/couponsQuery", method = RequestMethod.POST)
    public AvailCouponsResponse queryCoupons(@RequestBody @Validated QueryCouponReq couponReq, BindingResult bindingResult, HttpServletRequest request) {
        AvailCouponsResponse resp = new AvailCouponsResponse();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponReq.getFfpId(), couponReq.getLoginKeyInfo(), couponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String timestamp = DateUtils.getCurrentTimeStr();
        CouponQueryRequest couponQueryRequest = CreateQueryCouponRequest(couponReq, couponReq.getChannelCode());
        couponQueryRequest.setSale(1);  //非可售
        HttpResult serviceResult = doPostClient(couponQueryRequest, HandlerConstants.New_URL_FARE + HandlerConstants.SUB_QUERY_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (AvailCouponsResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), AvailCouponsResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setTimeStamp(timestamp);
                    if (resp.getAvailCouponList() == null) {
                        resp.setAvailCouponList(new ArrayList<>());
                    }
                    //2021-02-01 查询优惠券数量，更新首页会员中心的会员优惠券数量
                    if (CollectionUtils.isNotEmpty(resp.getAvailCouponList()) && CouponStateEnum.R.getState().equals(couponReq.getCouponState())) {
                        long secondDayDifference = DateUtils.getSecondDayDifference(new Date());
                        apiRedisService.putData(RedisKeyConfig.MEMBER_CENTER_MEMBER_COUPONTOTAL + couponReq.getFfpId(), serviceResult.getResponse(), secondDayDifference);//暂时存放一天
                    }
                    for (AvailCoupon availCoupon : resp.getAvailCouponList()) {
                        String sign = EncoderHandler.encodeByMD5(availCoupon.getCouponNo() + timestamp + HandlerConstants.ACCESSSECRET);
                        availCoupon.setSign(sign);
                        //优惠券基本属性渲染
                        String passengerType = availCoupon.getBookingLimit() == null ? null : availCoupon.getBookingLimit().getPassengerType();
                        String flightType = availCoupon.getBookingLimit() == null ? null : availCoupon.getBookingLimit().getFlightType();
                        CouponStyle  couponStyle= CouponUtil .creatCouponStyle(availCoupon);
                        couponStyle.setFlightType(flightType);
                        Coupon coupon = CouponUtil.initCouponStyle(couponStyle);
                        //打折券
                        if (CouponTypeEnum.D.getType().equals(availCoupon.getCouponType())) {
                            Double amount = 0.0;
                            // 是否儿童类型 设置优惠券抵扣类型
                            if (CommonBaseConstants.PASSENGER_TYPE_CHD.equals(passengerType)) {
                                availCoupon.setDiscountType(CommonBaseConstants.PASSENGER_TYPE_CHD + BigDecimal.valueOf(100 - availCoupon.getCouponRebate()).intValue());
                            }
                            if (!StringUtil.isNullOrEmpty(couponReq.getFlightInfoList())) {
                                for (FlightInfo flightInfo : couponReq.getFlightInfoList()) {
                                    amount += flightInfo.getCabinFareList().get(0).getPriceValue();
                                }
                                availCoupon.setCouponPrice((int) (amount - availCoupon.getCouponRebate() * amount));
                            } else {
                                availCoupon.setCouponRebate(100 - availCoupon.getCouponRebate());// 我的优惠券列表查询页免票显示100%
                            }
                        }
                        if (StringUtils.isNotBlank(availCoupon.getUsedStEndDt())) {
                            String[] useDates = availCoupon.getUsedStEndDt().split("~");
                            if (useDates.length == 2) {
                                availCoupon.setStartDate(useDates[0]);
                                availCoupon.setEndData(useDates[1]);
                            }
                        }
                        availCoupon.setIsGive("N");
                        availCoupon.setCouponStyle(coupon.getCouponCss());
                        availCoupon.setCouponName(coupon.getCouponName());
                        availCoupon.setCouponSourceName(coupon.getCouponTypeName());//优惠券类型名称
                        availCoupon.setIsGive(coupon.getIsGive());//表示是否可以转赠
                        availCoupon.setShowType(availCoupon.getShowType()==null?"coupon":availCoupon.getShowType());
                        if ("G".equals(availCoupon.getCouponState())) {//已赠送的需要有领取信息
                            availCoupon.setIsGive("N");
                            if (!StringUtil.isNullOrEmpty(availCoupon.getReveiceCardNo()) && !StringUtil.isNullOrEmpty(availCoupon.getReveiceTime())) {
                                String cardNO = availCoupon.getReveiceCardNo();
                                if (cardNO.length() > 4) {
                                    cardNO = cardNO.substring(cardNO.length() - 4, cardNO.length());
                                }
                                String msg = "用户****" + cardNO + "于" + availCoupon.getReveiceTime() + "领取";
                                availCoupon.setReceiveInfo(msg);
                            }
                        } else if (CouponStateEnum.R.getState().equals(availCoupon.getCouponState())) {
                            Calendar expiringDateLimit = Calendar.getInstance();
                            expiringDateLimit.add(Calendar.DATE, 5);
                            Date endDate = DateUtils.toDate(availCoupon.getEndData(), DateUtils.YYYY_MM_DD_PATTERN);
                            if (null != endDate) {
                                availCoupon.setExpiring(endDate.before(expiringDateLimit.getTime()));
                            }
                        }
                    }
                    //“已使用”优惠券排序逻辑 优惠券排列顺序以优惠券使用时间由近及远排列
                    if ("N".equals(couponReq.getCouponState())) {
                        resp.getAvailCouponList().sort((e1, e2) -> {
                            if (StringUtils.isNotBlank(e1.getUseDate()) && StringUtils.isNotBlank(e2.getUseDate())) {
                                return e2.getUseDate().compareTo(e1.getUseDate());
                            } else {
                                return StringUtils.isNotBlank(e2.getUseDate()) ? 1 : -1;
                            }
                        });
                    }

                    //  “已过期”优惠券排序逻辑 优惠券排列顺序优先级依次为：
                    // （若优惠券同级信息相同，则顺延至下一级）
                    // （1）优惠券失效时间由近及远；
                    // （2）优惠券生效时间由近及远；
                    // （3）优惠券金额由多到少；
                    // （4）优惠券领取时间由远及近；
                    if ("E".equals(couponReq.getCouponState())) {
                        List<AvailCoupon> result = resp.getAvailCouponList().stream()
                                .sorted(Comparator.comparing((AvailCoupon p) -> p.getEndData(), Comparator.reverseOrder())
                                        .thenComparing(AvailCoupon::getStartDate, Comparator.reverseOrder())
                                        .thenComparing(AvailCoupon::getCouponPrice, Comparator.reverseOrder())
                                        .thenComparing(AvailCoupon::getReveiceTime)).collect(Collectors.toList());
                        resp.setAvailCouponList(result);
                    }

                    if (StringUtil.isNullOrEmpty(couponReq.getClientVersionNo())) {//老旧的版本过滤第三方优惠券
                        List oldList = new ArrayList<>();
                        for (AvailCoupon availCoupon : resp.getAvailCouponList()) {
                            if (CouponTypeEnum.D.getType().equals(availCoupon.getCouponType()) || "T".equals(availCoupon.getCouponType())) {
                                oldList.add(availCoupon);
                                resp.setAvailCouponList(oldList);
                            }
                        }
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询优惠券出错结果:", e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }


    @ApiOperation(value = "查询可用优惠券-一单多券", notes = "查询可用优惠券-一单多券")
    @RequestMapping(value = "/couponsQueryV2", method = RequestMethod.POST)
    public AvailCouponsResponse couponsQueryV2(@RequestBody @Validated QueryCouponReq couponReq, BindingResult bindingResult, HttpServletRequest request) {
        AvailCouponsResponse resp = new AvailCouponsResponse();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponReq.getFfpId(), couponReq.getLoginKeyInfo(), couponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        CouponQueryRequest couponQueryRequest = CreateAvailQueryCouponRequest(couponReq, couponReq.getChannelCode());
        HttpResult serviceResult = doPostClient(couponQueryRequest, HandlerConstants.New_URL_FARE + HandlerConstants.SUB_QUERY_AVAIL_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (AvailCouponsResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), AvailCouponsResponse.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    for (AvailCoupon availCoupon : resp.getAvailCouponList()) {
                        if (availCoupon.getCouponType().equals(CouponTypeEnum.D.getType())) {
                            Double amount = 0.0;
                            for (FlightInfo flightInfo : couponReq.getFlightInfoList()) {
                                amount += flightInfo.getCabinFareList().get(0).getPriceValue();
                            }
                            availCoupon.setCouponPrice((int) (amount - availCoupon.getCouponRebate() * amount));
                        }
                        String flightType = availCoupon.getBookingLimit() == null ? "" : availCoupon.getBookingLimit().getFlightType();
                        CouponStyle  couponStyle= CouponUtil .creatCouponStyle(availCoupon);
                        couponStyle.setFlightType(flightType);
                        Coupon coupon = CouponUtil.initCouponStyle(couponStyle);
                        availCoupon.setCouponStyle(coupon.getCouponCss());
                        availCoupon.setCouponName(coupon.getCouponName());
                        availCoupon.setCouponSourceName(coupon.getCouponTypeName());
                        availCoupon.setPassengerInfoSuitList(availCoupon.getBookingLimit() == null ? null : availCoupon.getBookingLimit().getPassengerInfoSuitList());
                    }

                    // 仅返回可用优惠券
                    resp.setAvailCouponList(resp.getAvailCouponList().stream().filter(AvailCoupon::getIsAvailable).collect(Collectors.toList()));

                } else if (resp.getResultCode().equals("1003")) {//没有可用优惠券
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setAvailCouponList(new ArrayList<>());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询结果:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询优惠券出错结果:" + JsonUtil.objectToJson(couponQueryRequest));
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }




    /**
     * {@link com.juneyaoair.mobile.handler.controller.v2.V2MyRightCouponController#queryMyRightCoupons}
     *
     * @param couponReq
     * @param request
     * @return
     */
    @Deprecated
    @ApiOperation(value = "我的权益券", notes = "我的权益券")
    @RequestMapping(value = "/queryMyRightCoupons", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<AvailCoupon>> queryMyRightCoupons(@RequestBody @Validated BaseReq<MyCoupon> couponReq, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            MyCoupon myCoupon = couponReq.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(myCoupon.getFfpId(), myCoupon.getLoginKeyInfo(), couponReq.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            PtSaleCouponGetRequest ptSaleCouponGetRequest = CreateQueryRightCouponRequest(couponReq, couponReq.getChannelCode());
            ptSaleCouponGetRequest.setPageNo(1);
            ptSaleCouponGetRequest.setPageSize(100);
            //未使用的
            ptSaleCouponGetRequest.setCouponStates(OrderCouponStateEnum.Not.getStateCode());
            HttpResult serviceResult = doPost(ptSaleCouponGetRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_LIST);
            if (null != serviceResult && serviceResult.isResult()) {
                PtSaleCouponGetResponse couponsResp = (PtSaleCouponGetResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponGetResponse.class);
                if ("1001".equals(couponsResp.getResultCode())) {
                    if (StringUtil.isNullOrEmpty(couponsResp.getSaleCouponList())) {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setObjData(new ArrayList<AvailCoupon>());
                        return resp;
                    }
                    List<AvailCoupon> availCouponList = new LinkedList<>();
//                    List<String> couponNoList = new ArrayList<>();
                    for (SaleCoupon saleCoupon : couponsResp.getSaleCouponList()) {
                        AvailCoupon availCoupon = new AvailCoupon();
                        BeanUtils.copyProperties(saleCoupon, availCoupon);
                        availCoupon.setCouponActivityName(saleCoupon.getActivityName());
                        availCoupon.setUsedStEndDt(saleCoupon.getStartDate() + "至" + saleCoupon.getEndDate());
                        availCoupon.setCouponPrice((int) saleCoupon.getCouponPrice());
                        availCoupon.setIsGive("Y");
                        availCoupon.setCouponNo(saleCoupon.getCouponCode());
//                        couponNoList.add(saleCoupon.getCouponCode());
                        //二维码前端js生成
                        availCoupon.setShowQRcode(true);
                        //部分特性处理
                        showCouponNameBySource(availCoupon);
                        availCouponList.add(availCoupon);
                    }
//                    List<Map<String,String>> mapList = getRightCouponQrcodePath(couponNoList,request);
//                    for(Map map:mapList){
//                        for(AvailCoupon coupon : availCouponList){
//                            if(map.get("couponNo").equals(coupon.getCouponNo())){
//                                coupon.setQrcodeData((String)map.get("url"));
//                                coupon.setShowQRcode(true);
//                            }
//                        }
//                    }
                    resp.setObjData(availCouponList);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else if ("1003".equals(couponsResp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setObjData(new ArrayList<AvailCoupon>());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("查询结果:" + couponsResp.getErrorInfo());
                }
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("查询网络出错");
                return resp;
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    @ApiOperation(value = "我的失效权益券", notes = "我的失效权益券")
    @RequestMapping(value = "/queryMyInvalidRightCoupons", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<AvailCoupon>> queryMyInvalidRightCoupons(@RequestBody BaseReq<MyCoupon> couponReq, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(couponReq);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            MyCoupon myCoupon = couponReq.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(myCoupon.getFfpId(), myCoupon.getLoginKeyInfo(), couponReq.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            PtSaleCouponGetRequest ptSaleCouponGetRequest = CreateQueryRightCouponRequest(couponReq, couponReq.getChannelCode());
            ptSaleCouponGetRequest.setPageNo(1);
            ptSaleCouponGetRequest.setPageSize(100);
            //未使用的
            ptSaleCouponGetRequest.setCouponStates(OrderCouponStateEnum.Used.getStateCode() + "|" + OrderCouponStateEnum.Giving.getStateCode() + "|" + OrderCouponStateEnum.GiveAway.getStateCode() + "|" + OrderCouponStateEnum.Overdue.getStateCode());
            HttpResult serviceResult = doPost(ptSaleCouponGetRequest, HandlerConstants.URL_FARE + HandlerConstants.SALE_COUPON_LIST);
            if (null != serviceResult && serviceResult.isResult()) {
                PtSaleCouponGetResponse couponsResp = (PtSaleCouponGetResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), PtSaleCouponGetResponse.class);
                if ("1001".equals(couponsResp.getResultCode())) {
                    if (StringUtil.isNullOrEmpty(couponsResp.getSaleCouponList())) {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setObjData(new ArrayList<AvailCoupon>());
                        return resp;
                    }
                    List<AvailCoupon> availCouponList = new LinkedList<>();
                    for (SaleCoupon saleCoupon : couponsResp.getSaleCouponList()) {
                        AvailCoupon availCoupon = new AvailCoupon();
                        BeanUtils.copyProperties(saleCoupon, availCoupon);
                        availCoupon.setCouponActivityName(saleCoupon.getActivityName());
                        availCoupon.setUsedStEndDt(saleCoupon.getStartDate() + "至" + saleCoupon.getEndDate());
                        availCoupon.setCouponPrice((int) saleCoupon.getCouponPrice());
                        availCoupon.setIsGive("N");
                        availCoupon.setCouponNo(saleCoupon.getCouponCode());
                        OrderCouponStateEnum orderCouponState = OrderCouponStateEnum.getEnum(saleCoupon.getCouponState());
                        availCoupon.setCouponStateName(orderCouponState == null ? "" : orderCouponState.getDesc());
                        //部分特性处理
                        showCouponNameBySource(availCoupon);
                        availCouponList.add(availCoupon);
                    }
                    resp.setObjData(availCouponList);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else if ("1003".equals(couponsResp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setObjData(new ArrayList<AvailCoupon>());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("查询结果:" + couponsResp.getErrorInfo());
                }
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("查询网络出错");
                return resp;
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
    }

    //优惠券检验
    @ApiOperation(value = "checkCoupons", notes = "优惠券检验")
    @RequestMapping(value = "/couponsCheck", method = RequestMethod.POST)
    @InterfaceLog
    public CouponCheckResponse checkCoupons(@RequestBody @Validated QueryCouponReq couponReq, BindingResult bindingResult,HttpServletRequest request) {
        CouponCheckResponse resp = new CouponCheckResponse();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String clientVersion = request.getHeader(HandlerConstants.CLIENT_VERSION);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponReq.getFfpId(), couponReq.getLoginKeyInfo(), couponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (StringUtil.isNullOrEmpty(couponReq.getCouponNo())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("优惠券券号不可为空!");
            return resp;
        }
        if (StringUtil.isNullOrEmpty(couponReq.getCouponSource())) {
            couponReq.setCouponSource("HO");//默认查询机票券
        }
        boolean supportConn = false;
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && VersionNoUtil.toVerInt(clientVersion) >= LIMITAPPVER) {
            supportConn = true;
        }
        return couponService.couponsCheck(couponReq,resp,supportConn,ip,headChannelCode);
    }

    //多乘客优惠查询
    @ApiOperation(value = "queryMultiDiscount", notes = "多乘客优惠")
    @RequestMapping(value = "/queryMultiDiscount", method = RequestMethod.POST)
    public MultiDiscountResponse queryMultiDiscount(@RequestBody QueryMultiDiscountReq queryMultiDiscountReq) {
        String reqId = StringUtil.newGUID() + "_queryMultiDiscount";
        log.debug(reqId + "多乘客优惠查询请求参数:" + JsonUtil.objectToJson(queryMultiDiscountReq));
        MultiDiscountResponse resp = new MultiDiscountResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryMultiDiscountReq>> violations = validator.validate(queryMultiDiscountReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(queryMultiDiscountReq.getFfpId(), queryMultiDiscountReq.getLoginKeyInfo(), queryMultiDiscountReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //乘客人数处理 成人数 儿童数 婴儿数
        int adtLength = 0, chdLength = 0, infLength = 0;
        for (PassengerInfo passengerInfo : queryMultiDiscountReq.getPassengerInfoList()) {
            if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passengerInfo.getPassengerType())) {
                if (!("GM".equals(passengerInfo.getCertType()) || "JC".equals(passengerInfo.getCertType()))) { //排除特殊旅客
                    adtLength++;
                }
            }
        }
        String routeType = HandlerConstants.ROUTE_TYPE_OW;
        int goPriceADT = 0,   //去程成人价
                goPriceCHD = 0,   //去程儿童价
                goPriceINF = 0,   //去程婴儿价
                backPriceADT = 0, //返程成人价
                backPriceCHD = 0, //返程儿童价
                backPriceINF = 0; //返程婴儿价
        com.juneyaoair.baseclass.request.booking.FlightInfo flightInfoGo = null;
        com.juneyaoair.baseclass.request.booking.FlightInfo flightInfoBack = null;
        if (!StringUtil.isNullOrEmpty(queryMultiDiscountReq.getFlightInfoList())) {
            for (int i = 0; i < queryMultiDiscountReq.getFlightInfoList().size(); i++) {
                com.juneyaoair.baseclass.request.booking.FlightInfo flightInfo = queryMultiDiscountReq.getFlightInfoList().get(i);
                if ("G".equals(flightInfo.getFlightDirection())) {
                    flightInfoGo = flightInfo;
                } else if ("B".equals(flightInfo.getFlightDirection())) {
                    flightInfoBack = flightInfo;
                }
            }
        }
        String flightDateGo = "";
        String flightDateBack = "";
        String cabinCode = "";
        if (flightInfoGo != null) {
            flightDateGo = flightInfoGo.getFlightDate();
            if (StringUtil.isNullOrEmpty(flightDateGo)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("航班日期不能为空");
                return resp;
            }
            //取乘客对应的运价
            com.juneyaoair.baseclass.request.booking.CabinFare cabinFare = getCabinFare(flightInfoGo, HandlerConstants.PASSENGER_TYPE_ADT);
            if (null != cabinFare) {
                cabinCode = cabinFare.getCabinCode();
            }
            goPriceADT = calculatePrice(flightInfoGo, adtLength);  //去程成人总价
        }
        if (flightInfoBack != null) {
            flightDateBack = flightInfoBack.getFlightDate();
            routeType = HandlerConstants.ROUTE_TYPE_RT;
            if (StringUtil.isNullOrEmpty(flightInfoBack.getFlightDate())) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("返程航班日期不能为空");
                return resp;
            }
            com.juneyaoair.baseclass.request.booking.CabinFare cabinFare = getCabinFare(flightInfoBack, HandlerConstants.PASSENGER_TYPE_ADT);
            if (null != cabinFare) {
                cabinCode = cabinCode + "," + cabinFare.getCabinCode();
            }
            backPriceADT = calculatePrice(flightInfoBack, adtLength);
        }
        String userNo = getChannelInfo(queryMultiDiscountReq.getChannelCode(), "10");
        MultiDiscountRequest multiDiscountRequest = new MultiDiscountRequest(HandlerConstants.VERSION,
                queryMultiDiscountReq.getChannelCode(),
                userNo,
                adtLength,
                goPriceADT + backPriceADT,
                queryMultiDiscountReq.getInterFlag(),
                routeType,
                cabinCode,
                flightDateGo,
                flightDateBack);
        HttpResult serviceResult = doPost(multiDiscountRequest, HandlerConstants.URL_FARE + HandlerConstants.QUERY_MULTIDISCOUNT);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (MultiDiscountResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), MultiDiscountResponse.class);
                if (resp.getResultCode().equals("1001")) {
                    if (resp.getDiscountAmount() > 0) {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setErrorInfo("暂无优惠");
                    }
                } else {
                    resp.setErrorInfo("多乘客优惠查询结果:" + resp.getErrorInfo());
                }
                log.debug(reqId + "多乘客优惠查询结果" + JsonUtil.objectToJson(resp));
                return resp;
            } catch (Exception e) {
                log.error("多乘客优惠查询出错" + e.getMessage(), e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("多乘客优惠查询出错");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }

    //计算成人Y舱全价金额
    private int calculatePrice(com.juneyaoair.baseclass.request.booking.FlightInfo flightInfo, int passCount) {
        double price = flightInfo.getCabinFareList().get(0).getYPrice() * passCount;  //成人Y舱全价
        BigDecimal b = BigDecimal.valueOf(price);
        int total = b.setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
        return total;  //返回结果四舍五入至个位数
    }

    //取乘客运价
    private com.juneyaoair.baseclass.request.booking.CabinFare getCabinFare(com.juneyaoair.baseclass.request.booking.FlightInfo flightInfo, String passType) {
        for (com.juneyaoair.baseclass.request.booking.CabinFare cabinFare : flightInfo.getCabinFareList()) {
            if (passType.equals(cabinFare.getPassengerType())) {
                return cabinFare;
            }
        }
        return null;

    }

    /**
     * 查询我的可领优惠券
     *
     * @param queryCouponReq
     * @return
     */
    @RequestMapping(value = "/queryMyCouponActivty", method = RequestMethod.POST)
    @Deprecated
    public CouponActivityResponse queryMyCouponActivty(@RequestBody QueryCouponReq queryCouponReq) {
        CouponActivityResponse resp = new CouponActivityResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryCouponReq>> violations = validator.validate(queryCouponReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        resp.setErrorInfo("系统升级维护中");
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        return resp;
    }

    //优惠活动券查询 适用于4.0.40之后的版本 2017/11/30
    @RequestMapping(value = "/queryCouponActivities", method = RequestMethod.POST)
    public CouponActivityResponse queryCouponActivity(@RequestBody QCouponActivityReq couponActivityReq, HttpServletRequest request) {
        CouponActivityResponse resp = new CouponActivityResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QCouponActivityReq>> violations = validator.validate(couponActivityReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        resp.setErrorInfo("系统升级维护中");
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        return resp;
    }

    //积分兑换优惠券活动查询
    @ApiOperation(value = "queryScoreCoupon", notes = "积分兑换优惠券活动查询")
    @RequestMapping(value = "/queryScoreCoupon", method = RequestMethod.POST)
    public Object queryScoreCoupon(@RequestBody QCouponActivityReq couponActivityReq, HttpServletRequest request) {
        CouponActivityResponse resp = new CouponActivityResponse();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QCouponActivityReq>> violations = validator.validate(couponActivityReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        CouponActivityQueryRequest couponActivityQueryReq = CreateQueryCouponActivity(couponActivityReq, "CRM");
        String doMethod = HandlerConstants.URL_FARE + HandlerConstants.QUERY_SCORECOUPON;
        if ((!StringUtil.isNullOrEmpty(couponActivityReq.getFfpId())) && (!StringUtil.isNullOrEmpty(couponActivityReq.getFfpCardNo()))) {
            couponActivityQueryReq.setFfpId(couponActivityReq.getFfpId());
            couponActivityQueryReq.setFfpCardNo(couponActivityReq.getFfpCardNo());
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(couponActivityReq.getFfpId(), couponActivityReq.getLoginKeyInfo(), couponActivityReq.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
        }
        HttpResult serviceResult = doPost(couponActivityQueryReq, doMethod);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (CouponActivityResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), CouponActivityResponse.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    convertCoupon(resp.getCouponActivityList());//特殊处理
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询出错:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询优惠券出错结果:" + JsonUtil.objectToJson(couponActivityQueryReq));
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询出错返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }

    //积分兑换优惠券领取操作
    @RequestMapping(value = "/exchangeCoupons", method = RequestMethod.POST)
    public BaseResponse exchangeCoupons(@RequestBody ScoreCouponReq receiveReq, HttpServletRequest requst) {
        BaseResponse resp = new BaseResponse();
        String ip = "";
        if (StringUtil.isNullOrEmpty(receiveReq.getIp())) {
            ip = this.getClientIP(requst);
        } else {
            if (!this.checkToken(receiveReq.getIpToken(), receiveReq.getIp(), HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("非法的访问请求");
                return resp;
            } else {
                ip = receiveReq.getIp();
            }

        }
        String reqId = StringUtil.newGUID();//请求ID
        String channelCode = receiveReq.getChannelCode();
        log.info("请求ID:" + reqId + ",IP地址" + ip + ",兑换优惠券:" + JsonUtil.objectToJson(receiveReq));
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ScoreCouponReq>> violations = validator.validate(receiveReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        try {
            //活动验证
            CouponActivity couponActivity = checkActivity(receiveReq.getActivityNo(), "CRM");
            if (couponActivity == null) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("无此活动！");
                return resp;
            } else {
                if (couponActivity.getBuyPrice() == 0) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setErrorInfo("信息异常！");
                    return resp;
                }
            }
            //登录信息检验
            boolean flag = checkKeyInfo(String.valueOf(receiveReq.getFfpId()), receiveReq.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //判断账户的实名状态
            boolean accmflag = false;
            MemberInfoQueryResponseForClient memberInfoquery = crmClient.memberInfoquery(Long.valueOf(receiveReq.getFfpId()), receiveReq.getChannelCode(), getClientPwd(channelCode));
            if ("认证成功".equals(memberInfoquery.getMemberQueryInfo().getRealVerifyStatus())) {
                accmflag = true;
            }
            if (!accmflag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("该账户暂未实名请先进行实名认证!");
                return resp;
            }
            MemberQueryInfoType memberQueryInfo = memberInfoquery.getMemberQueryInfo();
            //消费密码验证
            VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(Long.valueOf(receiveReq.getFfpId()), receiveReq.getSalePassword(), channelCode, getClientPwd(channelCode));
            if (!clientResp.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(clientResp.getMessageHeader().getDescription());
                return resp;
            }
            //积分冻结
            ConnectInfoType connectInfoType = convertToConnectInfoType(memberQueryInfo);
            NonFlightInfoType nonFlightInfoType = convertToNonFlightInfoType(couponActivity);
            NonFlightRedeemApplyResponseForClient consumeResult = crmClient.doNonAirProApply(receiveReq.getFfpId(), receiveReq.getFfpCardNo(), channelCode, getClientPwd(channelCode), couponActivity.getBuyPrice(), connectInfoType, nonFlightInfoType);
            if (!"S000".equals(consumeResult.getMessageHeader().getErrorCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(consumeResult.getMessageHeader().getDescription());
                return resp;
            }
            log.info("请求ID:" + reqId + ",成功冻结" + couponActivity.getBuyPrice() + "积分");
            long redeemId = consumeResult.getRedeemID();//非航空兑奖记录编号
            //领取优惠券
            try {
                ReceiveCouponRequest receiveCouponRequest = createReceiveCouponReq(receiveReq, "CRM");
                HttpResult serviceResult = doPostClient(receiveCouponRequest, HandlerConstants.New_URL_FARE + HandlerConstants.SUB_RECEIVE_COUPON);
                if (null != serviceResult && serviceResult.isResult()) {
                    try {
                        resp = (BaseResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), BaseResponse.class);
                        if (resp.getResultCode().equals("1001")) {//成功的情况下扣减积分  其余情况解除积分的冻结状况
                            //领取成功
                            doConfirmOrCancel(redeemId, channelCode, "Confirm", reqId, resp);
                        } else {//领取失败
                            doConfirmOrCancel(redeemId, channelCode, "Cancel", reqId, resp);
                        }
                        return resp;
                    } catch (Exception e) {
                        log.error("领取优惠券出错结果:" + e.getMessage());
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setErrorInfo("返回结果空");
                        return resp;
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询网络出错");
                    doConfirmOrCancel(redeemId, channelCode, "Cancel", reqId, resp);
                    return resp;
                }
            } catch (Exception e) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("访问出错");
                doConfirmOrCancel(redeemId, channelCode, "Cancel", reqId, resp);
                return resp;
            }

        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("发生未知错误！");
            return resp;
        }
    }

    //赠送优惠券 生成赠送链接
    @ApiOperation(value = "APP赠送优惠券", notes = "APP赠送优惠券")
    @RequestMapping(value = "changeCoupon", method = RequestMethod.POST)
    public Object changeCoupon(@RequestBody ChangeCouponReq changeCouponReq, HttpServletRequest request) {
        ChangeCouponResponse resp = new ChangeCouponResponse();
        String reqId = StringUtil.newGUID() + "_changeCoupon";
        String ip = getClientIP(request);
        log.info("请求号:" + reqId + "IP地址:" + ip + "客户端提交参数:" + JsonUtil.objectToJson(changeCouponReq));
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ChangeCouponReq>> violations = validator.validate(changeCouponReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(changeCouponReq.getFfpId(), changeCouponReq.getLoginKeyInfo(), changeCouponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String shareDesc = templeteConfig.getShareDesc();
        if (StringUtil.isNullOrEmpty(shareDesc)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("暂无对应模板！");
            return resp;
        }
        //发起业务请求
        String userNo = getChannelInfo(changeCouponReq.getChannelCode(), "10");
        PtChangeCouponReq ptChangeCouponReq = CouponObjectConvert.toPtChangeCouponReq(changeCouponReq, userNo);
        HttpResult serviceResult = doPost(ptChangeCouponReq, HandlerConstants.URL_FARE + HandlerConstants.CHANGE_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (ChangeCouponResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), ChangeCouponResponse.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    //生成转赠链接
                    String url = HandlerConstants.MWEB_URL_NEW + HandlerConstants.WEB_RECEIVE_INVEREST_PAGE_URL_V3;
                    String couponCode = resp.getCouponCode();
                    String timestamp = DateUtils.getCurrentTimeStr();
                    String couponSource = resp.getCouponSource();
                    String sign = EncoderHandler.encodeByMD5(couponCode + timestamp + HandlerConstants.ACCESSSECRET);
                    String givename = "";
                    if (!StringUtil.isNullOrEmpty(changeCouponReq.getGiveMemberName()) && !"/".equals(changeCouponReq.getGiveMemberName())) {
                        givename = changeCouponReq.getGiveMemberName();
                    } else {
                        String cardNO = changeCouponReq.getFfpCardNo();
                        if (cardNO.length() > 4) {
                            cardNO = cardNO.substring(cardNO.length() - 4, cardNO.length());
                        }
                        givename = "用户" + cardNO;
                    }
                    // 小程序用分享信息
                    ShareCouponInfo shareCouponInfo = new ShareCouponInfo(couponCode, timestamp, couponSource, sign, givename);
                    resp.setShareCouponInfo(shareCouponInfo);
                    url = url + "?coupon=" + couponCode + "&timestamp=" + timestamp + "&couponSource=" + couponSource + "&sign=" + sign;
                    url = url + "&givename=" + givename;
                    resp.setErrorInfo(url);
                    shareDesc = shareDesc.replace("#COUPONNAME#", showCouponNameBySource(couponSource));
                    resp.setDesc(shareDesc);
                } else {
                    resp.setErrorInfo("赠送结果:" + resp.getErrorInfo());
                }
                log.info("请求号:" + reqId + "，响应结果:" + JsonUtil.objectToJson(resp));
                return resp;
            } catch (Exception e) {
                log.error("请求号:" + reqId + "，赠送出错" + e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("赠送返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }

    @ApiOperation(value = "APP取消赠送优惠券", notes = "APP取消赠送优惠券")
    @RequestMapping(value = "/cancelShareCoupon", method = RequestMethod.POST)
    public BaseResp cancelShareCoupon(@RequestBody ChangeCouponReq changeCouponReq, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_cancelShareCoupon";
        String ip = getClientIP(request);
        log.info("请求号:" + reqId + "IP地址:" + ip + "客户端提交参数:" + JsonUtil.objectToJson(changeCouponReq));
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ChangeCouponReq>> violations = validator.validate(changeCouponReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(changeCouponReq.getFfpId(), changeCouponReq.getLoginKeyInfo(), changeCouponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //请求取消赠送接口
        String userNo = getChannelInfo(changeCouponReq.getChannelCode(), "10");
        PtChangeCouponReq ptChangeCouponReq = CouponObjectConvert.toPtChangeCouponReq(changeCouponReq, userNo);
        HttpResult serviceResult = doPost(ptChangeCouponReq, HandlerConstants.URL_FARE + HandlerConstants.CANCEL_CHANGE_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                ChangeCouponResponse ptResp = (ChangeCouponResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), ChangeCouponResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptResp.getErrorInfo());
                }
                log.info("请求号:" + reqId + "，响应结果:" + JsonUtil.objectToJson(resp));
                return resp;
            } catch (Exception e) {
                log.error("请求号:" + reqId + "，赠送出错" + e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("取消赠送返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询网络出错");
            return resp;
        }
    }

    //赠送优惠券 生成赠送链接（微信）
    @RequestMapping(value = "/createReceiveUrlWeiXin", method = RequestMethod.POST)
    public Object createReceiveUrlWeiXin(@RequestBody ChangeCouponReq changeCouponReq, HttpServletRequest request) {
        ChangeCouponResponse resp = new ChangeCouponResponse();
        String reqId = StringUtil.newGUID() + "createReceiveUrlWeiXin";
        String ip = getClientIP(request);
        log.info("请求号:" + reqId + "IP地址:" + ip + "客户端提交参数:" + JsonUtil.objectToJson(changeCouponReq));
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ChangeCouponReq>> violations = validator.validate(changeCouponReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(changeCouponReq.getFfpId(), changeCouponReq.getLoginKeyInfo(), changeCouponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            //生成转赠链接
            String url = HandlerConstants.MWEB_URL_NEW + HandlerConstants.WEB_RECEIVE_INVEREST_PAGE_URL_V3;
            String couponCode = changeCouponReq.getCouponNo();
            String timestamp = DateUtils.getCurrentTimeStr();
            String couponSource = changeCouponReq.getCouponSource();
            String sign = EncoderHandler.encodeByMD5(couponCode + timestamp + HandlerConstants.ACCESSSECRET);
            String givename = "";
            if (!StringUtil.isNullOrEmpty(changeCouponReq.getGiveMemberName()) && !"/".equals(changeCouponReq.getGiveMemberName())) {
                givename = changeCouponReq.getGiveMemberName();
            } else {
                String cardNO = changeCouponReq.getFfpCardNo();
                if (cardNO.length() > 4) {
                    cardNO = cardNO.substring(cardNO.length() - 4, cardNO.length());
                }
                givename = "用户" + cardNO;
            }
            ShareCouponInfo shareCouponInfo = new ShareCouponInfo(couponCode, timestamp, couponSource, sign, givename);
            resp.setShareCouponInfo(shareCouponInfo);
            url = url + "?coupon=" + couponCode + "&timestamp=" + timestamp + "&couponSource=" + couponSource + "&sign=" + sign;
            url = url + "&givename=" + givename;
            resp.setErrorInfo(url);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            log.error("请求号:" + reqId + "，赠送出错" + e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("赠送返回结果空");
            return resp;
        }
        return resp;
    }

    //赠送优惠券 分享成功调用（微信）
    @RequestMapping(value = "/changeCouponWeiXin", method = RequestMethod.POST)
    public Object changeCouponWeiXin(@RequestBody ChangeCouponReq changeCouponReq, HttpServletRequest request) {
        ChangeCouponResponse resp = new ChangeCouponResponse();
        String reqId = StringUtil.newGUID() + "changeCouponWeiXin";
        String ip = getClientIP(request);
        log.info("请求号:" + reqId + "IP地址:" + ip + "客户端提交参数:" + JsonUtil.objectToJson(changeCouponReq));
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ChangeCouponReq>> violations = validator.validate(changeCouponReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(changeCouponReq.getFfpId(), changeCouponReq.getLoginKeyInfo(), changeCouponReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //发起业务请求
        String userNo = getChannelInfo(changeCouponReq.getChannelCode(), "10");
        PtChangeCouponReq ptChangeCouponReq = CouponObjectConvert.toPtChangeCouponReq(changeCouponReq, userNo);
        HttpResult serviceResult = doPost(ptChangeCouponReq, HandlerConstants.URL_FARE + HandlerConstants.CHANGE_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (ChangeCouponResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), ChangeCouponResponse.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("赠送优惠券错误");
                }
            } catch (Exception e) {
                log.error("请求号:" + reqId + "，赠送出错" + e.getMessage(), e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("赠送返回结果空");
                return resp;
            }
        }
        return resp;
    }


    //领取赠送的优惠券
    @ApiOperation(value = "领取优惠券", notes = "领取优惠券")
    @RequestMapping(value = "/receiveChangeCoupon", method = RequestMethod.POST)
    public Object receiveChangeCoupon(@RequestBody ReceiveChangeCouponRequest receiveChangeCouponRequest, HttpServletRequest request) {
        ReceiveChangeCouponResponse resp = new ReceiveChangeCouponResponse();
        String reqId = StringUtil.newGUID() + "_receiveChangeCoupon";
        String ip;
        if (StringUtil.isNullOrEmpty(receiveChangeCouponRequest.getIp())) {
            ip = this.getClientIP(request);
        } else {
            ip = receiveChangeCouponRequest.getIp();
            if (!this.checkToken(receiveChangeCouponRequest.getIpToken(), ip, HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo("未被授权的访问！");
                return resp;
            }
        }
        log.info("请求号:" + reqId + "IP地址:" + ip + "客户端提交参数:" + JsonUtil.objectToJson(receiveChangeCouponRequest));
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<ReceiveChangeCouponRequest>> violations = validator.validate(receiveChangeCouponRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证码检验
        if (!this.checkVeriCode("SMS", receiveChangeCouponRequest.getMobileNo(), SourceType.ACTIVITY_SOURECE.getValue(), receiveChangeCouponRequest.getMobileCode())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("验证码错误");
            return resp;
        }
        //签名检验
        String source = receiveChangeCouponRequest.getCouponCode() + receiveChangeCouponRequest.getTimestamp() + HandlerConstants.ACCESSSECRET;
        String code = EncoderHandler.encodeByMD5(source);
        if (!receiveChangeCouponRequest.getSign().equals(code)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("信息验证错误");
            return resp;
        }
        //手机号验证是否注册  查询卡号等账户信息
        String ffpCardNo = "";
        String ffpId = "";
        String memberName = receiveChangeCouponRequest.getMobileNo();
        GetMemberInfoResponseForClient memberInfoResponseForClient = crmClient.getMemberInfo(receiveChangeCouponRequest.getMobileNo(), QueryMemberDataType.MOBILE.value(), HandlerConstants.M_CHANNEL_CODE, HandlerConstants.M_CLIENT_PWD);
        if (memberInfoResponseForClient != null) {
            if ("S000".equals(memberInfoResponseForClient.getMessageHeader().getErrorCode())) {
                MemberQueryInfoType memberQueryInfoType = memberInfoResponseForClient.getMemberQueryInfo();
                if (memberQueryInfoType != null && !StringUtil.isNullOrEmpty(memberQueryInfoType.getMemberID()) && !StringUtil.isNullOrEmpty(String.valueOf(memberQueryInfoType.getID()))) {
                    ffpId = String.valueOf(memberQueryInfoType.getID());
                    ffpCardNo = memberQueryInfoType.getMemberID();
                    CustomerInfoType customerInfo = memberQueryInfoType.getCustomerInfo();
                    if (customerInfo != null && !StringUtil.isNullOrEmpty(customerInfo.getCLastName()) && !StringUtil.isNullOrEmpty(customerInfo.getCFirstName())) {
                        memberName = customerInfo.getCLastName() + customerInfo.getCFirstName();
                    }
                }
            } else {
                //会员信息不存在，根据手机号快速注册
                MemberB2CRegistResponseForClient memberB2CRegistResponseForClient = crmClient.fastRegist(receiveChangeCouponRequest.getMobileNo(), receiveChangeCouponRequest.getMobileNo(), HandlerConstants.M_CHANNEL_CODE, HandlerConstants.M_CLIENT_PWD);
                if (null != memberB2CRegistResponseForClient) {
                    if ("S000".equals(memberB2CRegistResponseForClient.getMessageHeader().getErrorCode())) {
                        ffpId = String.valueOf(memberB2CRegistResponseForClient.getID());
                        ffpCardNo = memberB2CRegistResponseForClient.getMemberID();
                    } else {
                        log.info("请求号:" + reqId + "手机号注册会员失败，手机号：" + receiveChangeCouponRequest.getMobileNo() + "," + JsonUtil.objectToJson(memberB2CRegistResponseForClient));
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setErrorInfo(memberB2CRegistResponseForClient.getMessageHeader().getDescription());
                        return resp;
                    }
                } else {
                    log.info("请求号:" + reqId + "手机号注册会员失败，手机号：" + receiveChangeCouponRequest.getMobileNo());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("注册请求结果为空！");
                    return resp;
                }
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("请求异常稍后再试！");
            return resp;
        }
        //发起业务请求  执行领券的业务操作
        if (StringUtil.isNullOrEmpty(ffpId) || StringUtil.isNullOrEmpty(ffpCardNo)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("账户异常！");
            return resp;
        }
        String channelCode = receiveChangeCouponRequest.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        PtReceiveChangeCouponRequest ptReceiveChangeCouponRequest = CouponObjectConvert.toPtReceiveChangeCouponRequest(receiveChangeCouponRequest, ffpId, ffpCardNo, memberName, channelCode, userNo);
        HttpResult serviceResult = doPost(ptReceiveChangeCouponRequest, HandlerConstants.URL_FARE + HandlerConstants.RECEIVE_CHANGE_COUPON);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (ReceiveChangeCouponResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), ReceiveChangeCouponResponse.class);
                if (resp.getResultCode().equals("1001")) {
                    apiRedisService.removeData("SMS:" + receiveChangeCouponRequest.getMobileNo() + SourceType.ACTIVITY_SOURECE.getValue());
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    if (resp.getErrorInfo().indexOf("未使用数量超过5张") > -1) {
                        resp.setResultCode("9999");
                        resp.setErrorInfo(resp.getErrorInfo());
                    } else {
                        resp.setErrorInfo("领取结果:" + resp.getErrorInfo());
                    }
                }
                log.info("请求号:" + reqId + "，响应结果:" + JsonUtil.objectToJson(resp));
                return resp;
            } catch (Exception e) {
                log.error("请求号:" + reqId + "，领取出错" + e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("领取返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }

    //我的优惠券查询条件
    private CouponQueryRequest CreateQueryCouponRequest(QueryCouponReq couponReq, String channelCode) {
        String userNo = getChannelInfo(channelCode, "10");
        List<PtSegmentInfo> segmentInfoList = new ArrayList<>();
        String couponState = couponReq.getCouponState();
        if (CouponStateEnum.R.getState().equals(couponReq.getCouponState())) {//已领取
            couponState = CouponStateEnum.R.getState() + "," + CouponStateEnum.C.getState();
        } else if (CouponStateEnum.N.getState().equals(couponReq.getCouponState())) {//已使用
            couponState = CouponStateEnum.N.getState() + "," + CouponStateEnum.G.getState();
        }
        CouponQueryRequest couponQueryReq = new CouponQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                couponReq.getFfpId(),
                couponReq.getFfpCardNo(),
                couponState,
                segmentInfoList
        );
        couponQueryReq.setCouponSource(couponReq.getCouponSource());
        return couponQueryReq;
    }


    //航班查询中优惠券查询条件
    private CouponQueryRequest CreateAvailQueryCouponRequest(QueryCouponReq couponReq, String channelCode) {
        String userNo = ChannelUtils.getChannelInfo(channelCode, "10");
        List<PtSegmentInfo> segmentInfoList = new ArrayList<>();
        CouponQueryRequest couponQueryReq = new CouponQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                couponReq.getFfpId(),
                couponReq.getFfpCardNo(),
                "R",
                segmentInfoList
        );
        couponQueryReq.setScene(couponReq.getScene());
        couponQueryReq.setCouponSource(couponReq.getCouponSource());
        //航班处理
        if (CollectionUtils.isNotEmpty(couponReq.getFlightInfoList())) {
            for (int i = 0; i < couponReq.getFlightInfoList().size(); i++) {
                FlightInfo flightInfo = couponReq.getFlightInfoList().get(i);
                PtSegmentInfoCoupon segmentInfo = new PtSegmentInfoCoupon();
                BeanUtils.copyProperties(flightInfo, segmentInfo);
                if (StringUtil.isNullOrEmpty(flightInfo.getStopNumber())) {
                    segmentInfo.setStopNumber(0);
                } else {
                    segmentInfo.setStopNumber(Integer.parseInt(flightInfo.getStopNumber()));
                }
                // 国内联程航班  拥军属航班
                if (flightInfo.getCabinFareList().get(0).getCabinClass().contains("-")
                        || flightInfo.getCabinFareList().get(0).getCabinClass().contains("/")
                        || flightInfo.getCabinFareList().get(0).getFareBasis().endsWith(FareBasisEnum.YJYS_FARE.getFareBasisCode())) {
                    String[] cabinCodes = flightInfo.getCabinFareList().get(0).getCabinComb().replace("/", "-").split("-");
                    String[] cabinClass = flightInfo.getCabinFareList().get(0).getCabinClass().replace("/", "-").split("-");
                    if (cabinCodes.length > i) {
                        segmentInfo.setCabin(cabinCodes[i]);
                    }
                    if (cabinClass.length > i) {
                        segmentInfo.setCabinClass(cabinClass[i]);
                    }
                } else {
                    segmentInfo.setCabin(StringUtils.isNotBlank(flightInfo.getCabinFareList().get(0).getCabinCode())?
                            flightInfo.getCabinFareList().get(0).getCabinCode():flightInfo.getCabinFareList().get(0).getCabinComb());
                    segmentInfo.setCabinClass(flightInfo.getCabinFareList().get(0).getCabinClass());
                }
                //处理成人舱位中转联程的价格 2021-10-15
                segmentInfo.setAdultTicketPrice(getAdtCabinFarePriceValue(flightInfo));
                com.juneyaoair.baseclass.response.av.CabinFare[] chdinfCabins = flightInfo.getCabinCHDINFFareList();
                if (chdinfCabins != null && chdinfCabins.length > 0) {
                    for (com.juneyaoair.baseclass.response.av.CabinFare cabinFare : chdinfCabins) {
                        //处理儿童婴儿舱位中转联程的价格 2021-10-15
                        if (HandlerConstants.PASSENGER_TYPE_INF.equals(cabinFare.getPassengerType())) {
                            segmentInfo.setInfTicketPrice(getChdInFCabinFarePriceValue(flightInfo, cabinFare));
                        }
                        if (HandlerConstants.PASSENGER_TYPE_CHD.equals(cabinFare.getPassengerType())) {
                            segmentInfo.setChildTicketPrice(getChdInFCabinFarePriceValue(flightInfo, cabinFare));
                        }
                    }
                }
                segmentInfo.setTourCode(CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())?
                        flightInfo.getCabinFareList().get(0).getTourCode():  flightInfo.getCabinOLDFareList().get(0).getTourCode());
                segmentInfo.setFareBasis(CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())?
                        flightInfo.getCabinFareList().get(0).getFareBasis():flightInfo.getCabinOLDFareList().get(0).getFareBasis());
                segmentInfo.setYPrice(null == flightInfo.getCabinFareList().get(0).getYPrice() ? 0 : flightInfo.getCabinFareList().get(0).getYPrice());
                segmentInfoList.add(segmentInfo);
            }
        }
        couponQueryReq.setSegmentInfoList(segmentInfoList);
        //乘客处理
        List<PtPassengerInfo> ptPassengerInfoList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(couponReq.getPassengerInfoList())) {
            for (PassengerInfo passengerInfo : couponReq.getPassengerInfoList()) {
                PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
                BeanUtils.copyProperties(passengerInfo, ptPassengerInfo);
                ptPassengerInfoList.add(ptPassengerInfo);
            }
            couponQueryReq.setPassengerInfoList(ptPassengerInfoList);
        }
        return couponQueryReq;
    }

    ////处理儿童婴儿舱位中转联程的价格 2021-10-15
    private Double getChdInFCabinFarePriceValue(FlightInfo flightInfo, com.juneyaoair.baseclass.response.av.CabinFare cabinFare) {
        Double chdInFTicketPrice = cabinFare.getPriceValue();
        if (StringUtils.isNotBlank(cabinFare.getPriceValueComb())) {
            SegmentCabinInfo chdInfCabinInfo =null;
            if (CollectionUtils.isNotEmpty(cabinFare.getSegmentCabinInfos())){
                chdInfCabinInfo = cabinFare.getSegmentCabinInfos().stream().filter(segmentCabinInfo ->
                        flightInfo.getDepCity().equals(segmentCabinInfo.getDepCity())
                                && segmentCabinInfo.getArrCity().equals(flightInfo.getArrCity())).findFirst().orElse(null);
            }
            if (chdInfCabinInfo != null) {
                chdInFTicketPrice = chdInfCabinInfo.getPriceValue();
            }
        }
        return chdInFTicketPrice;
    }

    //处理成人舱位中转联程的价格 2021-10-15
    private Double getAdtCabinFarePriceValue(FlightInfo flightInfo) {
        Double adultTicketPrice = flightInfo.getCabinFareList().get(0).getPriceValue();
        if (StringUtils.isNotBlank(flightInfo.getCabinFareList().get(0).getPriceValueComb())) {
            SegmentCabinInfo cabinInfo = flightInfo.getCabinFareList().get(0).getSegmentCabinInfos().stream().filter(segmentCabinInfo ->
                    flightInfo.getDepCity().equals(segmentCabinInfo.getDepCity())
                            && segmentCabinInfo.getArrCity().equals(flightInfo.getArrCity())).findFirst().orElse(null);
            if (cabinInfo != null) {
                adultTicketPrice = cabinInfo.getPriceValue();
            }
        }
        return adultTicketPrice;
    }

    //权益券查询条件
    private PtSaleCouponGetRequest CreateQueryRightCouponRequest(BaseReq<MyCoupon> couponReq, String channelCode) {
        MyCoupon myCoupon = couponReq.getRequest();
        String userNo = getChannelInfo(channelCode, "10");
        PtSaleCouponGetRequest couponQueryReq = new PtSaleCouponGetRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                myCoupon.getFfpId(),
                myCoupon.getFfpCardNo()
        );
        return couponQueryReq;
    }

    //优惠活动券查询条件
    private CouponActivityQueryRequest CreateQueryCouponActivity(QCouponActivityReq couponReq, String channelCode) {
        String userNo = getChannelInfo(channelCode, "10");
        CouponActivityQueryRequest couponActivityQueryRequest = new CouponActivityQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo
        );
        return couponActivityQueryRequest;
    }

    //积分优惠活动券查询条件(领取校验时使用)
    private CouponActivityQueryRequest CreateQueryCouponActivity(String channelCode) {
        String userNo = getChannelInfo(channelCode, "10");
        CouponActivityQueryRequest couponActivityQueryRequest = new CouponActivityQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo
        );
        return couponActivityQueryRequest;
    }

    //积分优惠活动券领取条件
    private ReceiveCouponRequest createReceiveCouponReq(ReceiveCouponReq receiveReq, String channelCode) {
        String userNo = getChannelInfo(channelCode, "10");
        ReceiveCouponRequest receiveCouponRequest = new ReceiveCouponRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                receiveReq.getFfpId(),
                receiveReq.getFfpCardNo(),
                receiveReq.getActivityNo()
        );

        return receiveCouponRequest;
    }

    //领取积分优惠券领取条件
    private ReceiveCouponRequest createReceiveCouponReq(ScoreCouponReq receiveReq, String channelCode) {
        String userNo = getChannelInfo(channelCode, "10");
        ReceiveCouponRequest receiveCouponRequest = new ReceiveCouponRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                receiveReq.getFfpId(),
                receiveReq.getFfpCardNo(),
                receiveReq.getActivityNo()
        );
        return receiveCouponRequest;
    }

    //优惠券部分字段转换
    private void convertCoupon(List<CouponActivity> couponActivityList) {
        if (!StringUtil.isNullOrEmpty(couponActivityList)) {
            for (CouponActivity couponActivity : couponActivityList) {
                CouponStyle couponStyle = getCouponStyle(couponActivity);
                Coupon coupon = CouponUtil.initCouponStyle(couponStyle);
                couponActivity.setCouponStyle(coupon.getCouponCss());
                couponActivity.setCouponName(coupon.getCouponName());
                couponActivity.setCouponSourceName(coupon.getCouponTypeName());
            }
        }
    }

    private @NotNull CouponStyle getCouponStyle(CouponActivity couponActivity) {
        CouponStyle couponStyle =new CouponStyle();
        couponStyle.setCouponSource(couponActivity.getCouponSource());
        couponStyle.setCouponType(couponActivity.getCouponType());
        couponStyle.setCouponPrice( couponActivity.getCouponPrice());
        couponStyle.setCouponRebate(couponActivity.getCouponRebate());
        couponStyle.setCouponActivityName(couponActivity.getCouponName());
        couponStyle.setShowType("");
        couponStyle.setCouponState("N");
        return couponStyle;
    }

    //积分操作确认状态
    private void doConfirmOrCancel(long redeemId, String channelCode, String confirmOrCancel, String reqId, BaseResponse resp) {
        String desc = "";
        if ("Confirm".equals(confirmOrCancel)) {
            desc = "兑换优惠券";
        } else {
            desc = "取消优惠券兑换";
        }
        try {
            NonFlightRedeemConfirmOrCancelResponseForClient nonFlightRedeemConfirmOrCancelResponseForClient = crmClient.doNonAirProConfirmOrCancel(redeemId, channelCode, desc, "", getClientPwd(channelCode), ConfirmOrCancelType.fromValue(confirmOrCancel));
            if ("S000".equals(nonFlightRedeemConfirmOrCancelResponseForClient.getMessageHeader().getErrorCode())) {
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    log.info(reqId + desc + ",交易流水号:" + redeemId);
                } else if (resp.getResultCode().equals("50001")) { //优惠券已领完
                    resp.setResultCode("20001");
                } else if (resp.getResultCode().equals("50002")) { // 已领过优惠券
                    resp.setResultCode("20002");
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("领取结果:" + resp.getErrorInfo());
                }
            } else {
                log.info(reqId + desc + ",交易流水号:" + redeemId + ",操作失败");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(nonFlightRedeemConfirmOrCancelResponseForClient.getMessageHeader().getDescription());
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
        }
    }

    //封装联系信息
    private ConnectInfoType convertToConnectInfoType(MemberQueryInfoType memberQueryInfoType) {
        ConnectInfoType connectInfoType = new ConnectInfoType();
        String name = "HO";//联系人信息
        String certNum = "";//联系人证件号
        String telephone = "95520";//联系电话
        if (memberQueryInfoType != null) {
            CustomerInfoType infoType = memberQueryInfoType.getCustomerInfo();
            List<CustomerContactInfoType> customerContactInfoTypeList = memberQueryInfoType.getCustomerContactInfo();
            if (infoType != null) {
                name = infoType.getCLastName() + infoType.getCFirstName();//中文姓名
                certNum = getCertNumber(memberQueryInfoType.getCustomerCertificateInfo(), CertificateType.IDCard);//证件信息
            }
            if (!StringUtil.isNullOrEmpty(customerContactInfoTypeList)) {
                for (CustomerContactInfoType cust : customerContactInfoTypeList) {
                    String custType = cust.getContactType().value().toUpperCase();
                    if (custType.equals("MOBILE")) {
                        telephone = cust.getContactValue();
                    }
                }
            }
            if (StringUtil.isNullOrEmpty(name)) {
                name = "HO";
            }
            if (StringUtil.isNullOrEmpty(certNum)) {
                certNum = "HO95520";
            }
            if (StringUtil.isNullOrEmpty(telephone)) {
                telephone = "95520";
            }
            connectInfoType.setConnectName(name);
            connectInfoType.setTelephone(telephone);
            connectInfoType.setIDCardNo(certNum);
        } else {
            connectInfoType.setConnectName("HO");
            connectInfoType.setTelephone("HO95520");
            connectInfoType.setIDCardNo("95520");
        }
        return connectInfoType;
    }

    //封装非航班商品信息
    private NonFlightInfoType convertToNonFlightInfoType(CouponActivity couponActivity) {
        NonFlightInfoType nonFlightInfoType = new NonFlightInfoType();
        Date date = new Date();
        if (StringUtil.isNullOrEmpty(couponActivity.getActivityStartDate())) {
            nonFlightInfoType.setBeginDate(DateUtils.getDateStringAll(date));
        } else {
            nonFlightInfoType.setBeginDate(couponActivity.getActivityStartDate());
        }
        if (StringUtil.isNullOrEmpty(couponActivity.getActivityEndDate())) {
            nonFlightInfoType.setEndDate(DateUtils.getDateStringAll(date));
        } else {
            nonFlightInfoType.setEndDate(couponActivity.getActivityEndDate());
        }
        nonFlightInfoType.setCount(1);
        nonFlightInfoType.setProductType("IAWTV");
        nonFlightInfoType.setProductName("IAWTV_PRODUCT");
        return nonFlightInfoType;
    }

    //比对活动信息
    private CouponActivity checkActivity(String activityNo, String channelCode) {
        CouponActivity activity = null;
        CouponActivityResponse resp = new CouponActivityResponse();
        CouponActivityQueryRequest couponActivityQueryReq = CreateQueryCouponActivity(channelCode);
        String doMethod = HandlerConstants.URL_FARE + HandlerConstants.QUERY_SCORECOUPON;
        HttpResult serviceResult = doPost(couponActivityQueryReq, doMethod);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (CouponActivityResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), CouponActivityResponse.class);
                if (resp.getResultCode().equals("1001")) {
                    if (!StringUtil.isNullOrEmpty(resp.getCouponActivityList())) {
                        for (CouponActivity temp : resp.getCouponActivityList()) {
                            if (activityNo.equals(temp.getActivityNo())) {
                                activity = temp;//找到相应的活动信息
                                break;
                            }

                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询积分优惠券活动出错!" + e.getMessage());
            }
        }
        return activity;
    }

    //优惠券兑换条件
    private ActionBindCouponRequest createActionBindCouponRequest(ReceiveCouponReq receiveReq, String channelCode) {
        String userNo = getChannelInfo(channelCode, "10");
        ActionBindCouponRequest req = new ActionBindCouponRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                receiveReq.getFfpId(),
                receiveReq.getFfpCardNo(),
                receiveReq.getActivityNo()
        );
        return req;
    }

    //取crm接口客户端密码
    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    /**
     * 根据证件类型获取证件号
     *
     * @param customerCertificateInfoTypeList 证件列表一览
     * @param certType                        证件类型
     * @return 证件号
     */
    private String getCertNumber(List<CustomerCertificateInfoType> customerCertificateInfoTypeList, CertificateType certType) {
        String certNumber = null;
        for (CustomerCertificateInfoType certInfo : customerCertificateInfoTypeList) {
            if (certInfo.getCertType().equals(certType)) {
                certNumber = certInfo.getCertNumber();
                break;
            }
        }
        return certNumber;
    }

    /**
     * 优惠券名称处理
     *
     * @param availCoupon 返回的优惠券对象
     * @return
     */
    private void showCouponNameBySource(AvailCoupon availCoupon) {
        String type;
        double couponPrice = availCoupon.getCouponPrice();
        switch (availCoupon.getCouponSource()) {
            case "HOBG": {
                type = "行李额度兑换券-" + (int) couponPrice + "KG";
                break;
            }
            case "HOLO": {
                type = "贵宾休息室券";
                break;
            }
           /* case "HOUP": {
                type = "升舱兑换券";
                break;
            }*/
            default: {
                type = availCoupon.getCouponActivityName();
                break;
            }
        }
        availCoupon.setCouponName(type);
    }

    /**
     * 赠送优惠券名称处理
     *
     * @param couponSource
     * @return
     */
    private String showCouponNameBySource(String couponSource) {
        String couponName;
        switch (couponSource) {
            case "HOBG": {
                couponName = "行李额度兑换券";
                break;
            }
            case "HOLO": {
                couponName = "贵宾休息室券";
                break;
            }
            case "HOUP": {
                couponName = "升舱兑换券";
                break;
            }
            default: {
                couponName = "优惠券";
                break;
            }
        }
        return couponName;
    }

    @ApiOperation(value = "券码兑换")
    @RequestMapping(value = "/externalExchangeCoupon", method = RequestMethod.POST)
    public BaseResp<ExchangeCouponResult> externalExchangeCoupon(@RequestBody @Validated BaseReq<ExchangeCouponParam> exchangeCouponParamBase, HttpServletRequest request) {
        BaseResp<ExchangeCouponResult> result = new BaseResp<>();
        try {
            ExchangeCouponParam exchangeCouponParam = exchangeCouponParamBase.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(exchangeCouponParam.getFfpId(), exchangeCouponParam.getLoginKeyInfo(), exchangeCouponParamBase.getChannelCode());
            if (!flag) {
                throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), WSEnum.ERROR_CHK_ERROR.getResultInfo());
            }
            // 基于会员卡号获取会员手机号
            CrmPhoneInfo memberPhone = mobileMemberService.getMemberPhone(exchangeCouponParam.getFfpCardNo(), exchangeCouponParamBase.getChannelCode(), request);
            // 验证验证码
            if (!checkVeriCode(RedisKeyConfig.SMS_REDIS + memberPhone.getPhone() + SourceType.EXTERNAL_EXCHANGE_COUPON.getValue(), exchangeCouponParam.getVerifyCode())) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), "验证码错误或已经失效！");
            } else {
                // 验证成功，清除账号限制
                this.clearDayVisit("", memberPhone.getPhone(), SourceType.EXTERNAL_EXCHANGE_COUPON.getValue(), "");
            }
            // 券码兑换
            RedemEquityUnitsParam redemEquityUnitsParam = new RedemEquityUnitsParam();
            redemEquityUnitsParam.setVersion(HandlerConstants.VERSION);
            redemEquityUnitsParam.setChannelCode(exchangeCouponParamBase.getChannelCode());
            redemEquityUnitsParam.setFfpId(exchangeCouponParam.getFfpId());
            redemEquityUnitsParam.setFfpCardNo(exchangeCouponParam.getFfpCardNo());
            redemEquityUnitsParam.setApprovalCode(exchangeCouponParam.getApprovalCode());
            RedemEquityUnitsResult redemEquityUnitsResult = pdmService.redemEquityUnits(redemEquityUnitsParam);
            Set<String> productTypeSet = Sets.newHashSet();
            Set<String> LiveThemeProductTypeSet= Sets.newHashSet();
            if (null != redemEquityUnitsResult && CollectionUtils.isNotEmpty(redemEquityUnitsResult.getVoucherNoList())) {
                for (RedemEquityUnitsResult.VoucherNo voucherNo : redemEquityUnitsResult.getVoucherNoList()){
                    productTypeSet.add(voucherNo.getProductType());
                    LiveThemeProductTypeSet.add(voucherNo.getLiveThemeProductType());
                }
            }
            ExchangeCouponResult exchangeCouponResult = new ExchangeCouponResult();
            exchangeCouponResult.setProductTypeSet(productTypeSet);
            exchangeCouponResult.setLiveThemeProductTypeSet(LiveThemeProductTypeSet);
            result.success(exchangeCouponResult, WSEnum.SUCCESS.getResultCode(), WSEnum.SUCCESS.getResultInfo());
        } catch (CommonException ce) {
            log.error("券码兑换失败，请求ID：{} 异常信息：", MdcUtils.getRequestId(), ce);
            result.fail(ce.getResultCode(), ce.getErrorMsg());
        }
        return result;
    }
}
