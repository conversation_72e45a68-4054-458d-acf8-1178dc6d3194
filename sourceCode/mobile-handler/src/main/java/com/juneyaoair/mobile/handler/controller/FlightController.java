package com.juneyaoair.mobile.handler.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FlightQueryTypeEnum;
import com.juneyaoair.appenum.av.PackageTypeEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.flight.PeriodEnum;
import com.juneyaoair.appenum.flight.PeriodTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.av.common.SpecialFareQuery;
import com.juneyaoair.baseclass.basicsys.request.FlightExistDateReqDTO;
import com.juneyaoair.baseclass.basicsys.response.AirLineInfoDepCityDto;
import com.juneyaoair.baseclass.basicsys.response.AirLineLabelDTO;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.basicsys.response.FlightExistDateDTO;
import com.juneyaoair.baseclass.common.base.ThemeCoupon;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.BasicBaseReq;
import com.juneyaoair.baseclass.common.request.RequestData;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.ResponseCode;
import com.juneyaoair.baseclass.common.response.ResponseData;
import com.juneyaoair.baseclass.common.response.RouteCalendarBaseResp;
import com.juneyaoair.baseclass.flight.common.CityAirportInfo;
import com.juneyaoair.baseclass.insurance.apollo.InsuranceConfig;
import com.juneyaoair.baseclass.insurance.apollo.PaperworkEntity;
import com.juneyaoair.baseclass.request.RouteCalendarReq;
import com.juneyaoair.baseclass.request.av.QueryMinPriceReq;
import com.juneyaoair.baseclass.request.baggageExcess.BaggageExcessSegmentInfo;
import com.juneyaoair.baseclass.request.baggageExcess.QueryBaggageExcessReq;
import com.juneyaoair.baseclass.request.insure.QueryFlightInsureReq;
import com.juneyaoair.baseclass.request.lounge.QueryLoungeReq;
import com.juneyaoair.baseclass.request.tax.QueryTaxReq;
import com.juneyaoair.baseclass.request.wifi.QueryWifiReq;
import com.juneyaoair.baseclass.response.av.*;
import com.juneyaoair.baseclass.response.baggageExcess.QueryBaggageExcessResp;
import com.juneyaoair.baseclass.response.insure.*;
import com.juneyaoair.baseclass.response.lounge.LoungeInfo;
import com.juneyaoair.baseclass.response.lounge.QueryLoungeResp;
import com.juneyaoair.baseclass.response.tax.QueryTaxResp;
import com.juneyaoair.baseclass.response.wifi.QueryWifiResp;
import com.juneyaoair.baseclass.response.wifi.WifiQuery;
import com.juneyaoair.baseclass.theme.*;
import com.juneyaoair.exception.RequestParamErrorException;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.comm.service.impl.GlobalRedisServiceImpl;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.dto.AirLineDepCityDto;
import com.juneyaoair.mobile.core.bean.dto.AirLineDeptAirPortDto;
import com.juneyaoair.mobile.core.service.cityandairport.ICityAirportService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.InsuranceService;
import com.juneyaoair.mobile.handler.controller.util.AVObjectConvert;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IThemeCardService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.thirdentity.request.BaggageExcess.QueryBaggageExcessRequest;
import com.juneyaoair.thirdentity.request.BaggageExcess.QueryBaggageExcessSegmentInfo;
import com.juneyaoair.thirdentity.request.freeticket.request.AwardFlyPeriodRequest;
import com.juneyaoair.thirdentity.request.freeticket.response.AwardFlyPeriodResponse;
import com.juneyaoair.thirdentity.request.freeticket.response.FreeCalendar;
import com.juneyaoair.thirdentity.request.lounge.LoungeQueryRequest;
import com.juneyaoair.thirdentity.request.tax.TaxQueryRequest;
import com.juneyaoair.thirdentity.request.wifi.WifiQueryRequest;
import com.juneyaoair.thirdentity.response.baggageExcess.QueryBaggageExcessResponse;
import com.juneyaoair.thirdentity.response.lounge.LoungeQuery;
import com.juneyaoair.thirdentity.response.lounge.LoungeQueryResponse;
import com.juneyaoair.thirdentity.response.tax.TaxQueryResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by qinxiaoming on 2016-4-13.
 */
@Slf4j
@RequestMapping("/flight")
@RestController
@Api(value = "航班服务相关操作", tags = {"航班服务相关操作"})
public class FlightController extends BassController {

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    @Autowired
    private GlobalRedisServiceImpl globalRedisService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private ICityAirportService airportInfoService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private IThemeCardService themeCardService;


    //航班保险查询
    @ApiOperation(value = "航班保险查询", notes = "航班保险查询")
    @RequestMapping(value = "/flightInsure", method = RequestMethod.POST)
    public QueryFlightInsureResp queryFlightFare(@RequestBody @Validated QueryFlightInsureReq flightInsureReq, BindingResult bindingResult, HttpServletRequest request) {
        QueryFlightInsureResp resp = new QueryFlightInsureResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        if (CollectionUtils.isEmpty(flightInsureReq.getInsureSegList())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo("航线数据不可为空");
            return resp;
        }
        //5.6版本以后的保险
        String versionCodeStr = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String channelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //请求头中无数据
        if (StringUtils.isEmpty(channelCode)) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925_1.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925_1.getResultInfo());
            return resp;
        }
        if (SystemConstants.WAIT.equals(flightInsureReq.getFlightFareType())) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925_1.getResultCode());
            resp.setErrorInfo("机票候补不支持航班保险查询");
            return resp;
        }
        if (StringUtils.isEmpty(versionCodeStr)) {
            versionCodeStr = "0";
        }
        /*int versionCode = Integer.parseInt(versionCodeStr);
        //app5.6及以后启用新保险
        if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(channelCode)) {
            // 6.1.2版本若关闭了新版购保流程，则不展示保险
            if (versionCode == 61200 && !"Y".equalsIgnoreCase(handConfig.getIsNewProcessInsurance())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setInsureList(Lists.newArrayList());
                return resp;
            }
            List<InsureInfo> insureInfos = insuranceService.queryOptionalInsurabce(flightInsureReq, getClientIP(request));
            resp.setInsureList(insureInfos);
        }*/
        //M站及微信12100以后启用
        if (!ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(channelCode)) {
            List<InsureInfo> insureInfos = insuranceService.queryOptionalInsurabce(flightInsureReq, getClientIP(request));
            resp.setInsureList(insureInfos);
        }

        //20220927 新增广告弹窗
        if (CollectionUtils.isNotEmpty(resp.getInsureList())) {
            Optional<InsuranceConfig> optionalInsuranceConfig = handConfig.getInsuranceConfigList().stream().filter(el -> el.getRouteType().equals(flightInsureReq.getTripType())).findFirst();
            if (optionalInsuranceConfig.isPresent()) {
                //默认选择第一个保险
                if (StringUtils.isNotEmpty(resp.getInsureList().get(0).getInsId())) {
                    WinkConfiguration winkConfiguration = new WinkConfiguration();
                    PopUpConfiguration popUpConfiguration = new PopUpConfiguration();

                    BeanUtils.copyNotNullProperties(optionalInsuranceConfig.get().getWinkConfig(), winkConfiguration);
                    BeanUtils.copyNotNullProperties(optionalInsuranceConfig.get().getPopUpConfig(), popUpConfiguration);
                    popUpConfiguration.setDefaultInsuranceId(resp.getInsureList().get(0).getInsId());
                    popUpConfiguration.setPdfUrl(resp.getInsureList().get(0).getInsDescPdfURL());
                    List<PaperworkEntity> paperworkContent = optionalInsuranceConfig.get().getPopUpConfig().getPaperworkContent();
                    List<String> entityContentList = paperworkContent.stream().sorted(Comparator.comparing(PaperworkEntity::getEntityId)).map(PaperworkEntity::getEntityContent).collect(Collectors.toList());
                    popUpConfiguration.setPaperworkContent(entityContentList);

                    InsuranceConfiguration insuranceConfiguration = new InsuranceConfiguration();
                    insuranceConfiguration.setWinkConfiguration(winkConfiguration);
                    insuranceConfiguration.setPopUpConfiguration(popUpConfiguration);
                    resp.setInsuranceConfiguration(insuranceConfiguration);
                }
            }
        }
        resp.setNewProcessInsurance("Y".equalsIgnoreCase(handConfig.getIsNewProcessInsurance()));
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //航班辅助产品（贵宾休息室）
    @ApiOperation(value = "贵宾休息室", notes = "贵宾休息室")
    @RequestMapping(value = "/flightLounge", method = RequestMethod.POST)
    public QueryLoungeResp queryFlightLounge(@RequestBody QueryLoungeReq loungeReq, HttpServletRequest request) {
        QueryLoungeResp resp = new QueryLoungeResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryLoungeReq>> violations = validator.validate(loungeReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(loungeReq.getFfpId(), loungeReq.getLoginKeyInfo(), loungeReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        LoungeQueryRequest loungeQueryRequest = CreateQueryLoungeRequest(loungeReq);
        HttpResult serviceResult = doPost(loungeQueryRequest, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_LOUNGE);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                LoungeQueryResponse serviceRes = (LoungeQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), LoungeQueryResponse.class);
                List<LoungeInfo> loungeQueryList = new ArrayList<LoungeInfo>();//返回前端的List
                if (null != serviceRes.getLoungeQueryList() && serviceRes.getLoungeQueryList().size() > 0) {
                    for (LoungeQuery loungeQuery : serviceRes.getLoungeQueryList()) {
                        LoungeInfo loungeInfo = new LoungeInfo();
                        BeanUtils.copyNotNullProperties(loungeQuery, loungeInfo);
                        if ("Y".equals(loungeQueryRequest.getIsSingleBuy()) && 1 == loungeQuery.getSingleValidityDay()) {
                            loungeInfo.setLoungeType("zhidingri");
                        }
                        //默认选中逻辑
                        if (!StringUtil.isNullOrEmpty(loungeReq.getCabinClass()) && "Y".equals(loungeReq.getCabinClass())) {//选择的舱位等级
                            loungeInfo.setDefCheck("Y");
                        }
                        loungeQueryList.add(loungeInfo);
                    }
                }
                resp.setLoungeQueryList(loungeQueryList);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            } catch (Exception e) {
                log.error("查询休息室出错结果:" + JsonUtil.objectToJson(loungeQueryRequest));
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(NETWORK_ERROR_MESSAGE);
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(NETWORK_ERROR_MESSAGE);
            return resp;
        }

    }

    //航班辅助产品（WIFI）
    @ApiOperation(value = "随身WIFI", notes = "WIFI")
    @RequestMapping(value = "/flightWifi", method = RequestMethod.POST)
    public QueryWifiResp queryFlightWifi(@RequestBody QueryWifiReq wifiReq, HttpServletRequest request) {
        QueryWifiResp resp = new QueryWifiResp();
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        resp.setErrorInfo("无可销售的wifi");
        return resp;
    }

    /**
     * WIFI产品查询
     *
     * @param wifiReq
     * @param request
     * @return
     */
    @ApiOperation(value = "initFlightWifi", notes = "WIFI信息产品")
    @RequestMapping(value = "initWifi", method = RequestMethod.POST)
    public QueryWifiResp initFlightWifi(@RequestBody QueryWifiReq wifiReq, HttpServletRequest request) {
        QueryWifiResp resp = new QueryWifiResp();
        try {
            //默认取还件日期
            Calendar calendar = Calendar.getInstance();
            Date curDate, takeDate, returnDate, takeDateMo, returnDateMo;
            curDate = calendar.getTime();//当前时间
            int hour = calendar.get(Calendar.HOUR_OF_DAY);//获取当前的小时数
            if (hour >= 11) {
                takeDate = DateUtils.addOrLessDay(curDate, 2);//取件日期+2
                returnDate = DateUtils.addOrLessDay(curDate, 4);//还件日期+4
                takeDateMo = DateUtils.addOrLessDay(curDate, 8);//澳门日期+8
                returnDateMo = DateUtils.addOrLessDay(curDate, 10);//澳门还件日期+10
            } else {
                takeDate = DateUtils.addOrLessDay(curDate, 1);//取件日期+1
                returnDate = DateUtils.addOrLessDay(curDate, 3);//还件日期+3
                takeDateMo = DateUtils.addOrLessDay(curDate, 7);//澳门日期+7
                returnDateMo = DateUtils.addOrLessDay(curDate, 9);//澳门还件日期+9
            }
            String format = "yyyy-MM-dd";
            String startMin = " 11:00:00";
            String endMin = " 23:59:59";
            String takeDateStr = DateUtils.convertDate2Str(takeDate, format) + startMin;
            String returnDateStr = DateUtils.convertDate2Str(returnDate, format) + endMin;
            String takeDateMoStr = DateUtils.convertDate2Str(takeDateMo, format) + startMin;
            String returnDateMoStr = DateUtils.convertDate2Str(returnDateMo, format) + endMin;
            //国际
            AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "JP", takeDateStr, returnDateStr);
            AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "KR", takeDateStr, returnDateStr);
            AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "TH", takeDateStr, returnDateStr);
            if (checkClient(wifiReq.getClientVersion())) {//4.0.40之后的版本
                AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "PH", takeDateStr, returnDateStr);
                AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "RU", takeDateStr, returnDateStr);
                AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "SG", takeDateStr, returnDateStr);
            }


            //港澳台
            AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "TW", takeDateStr, returnDateStr);
            AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "HK", takeDateStr, returnDateStr);
            AddWifiToDicByCountry(resp, wifiReq.getChannelCode(), "MO", takeDateMoStr, returnDateMoStr);
            //国家名称处理
            if (!StringUtil.isNullOrEmpty(resp.getWifiQueryList())) {
                for (WifiQuery wifiQuery : resp.getWifiQueryList()) {
                    wifiQuery.setCountryName(countryCodeToName(wifiQuery.getCountryNo()));
                }
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            log.error("WIFI产品查询异常：" + e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(NETWORK_ERROR_MESSAGE);

        }
        return resp;
    }

    private void AddWifiToDicByCountry(QueryWifiResp resp, String channelCode, String countryNo, String takeDate, String
            returnDate) {
        //参数构造
        String userNo = getChannelInfo(channelCode, "10");
        WifiQueryRequest wifiQueryRequest = new WifiQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo
        );
        wifiQueryRequest.setCountryNo(countryNo);
        wifiQueryRequest.setTakeDate(takeDate);
        wifiQueryRequest.setReturnDate(returnDate);
        try {
            HttpResult serviceResult = doPost(wifiQueryRequest, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_WIFI);
            if (null != serviceResult && serviceResult.isResult()) {
                try {
                    QueryWifiResp temp = (QueryWifiResp) JsonUtil.jsonToBean(serviceResult.getResponse(),
                            QueryWifiResp.class);
                    if (temp.getResultCode().equals("1001")) {
                        if (!StringUtil.isNullOrEmpty(temp.getWifiQueryList())) {
                            if (StringUtil.isNullOrEmpty(resp.getWifiQueryList())) {
                                resp.setWifiQueryList(temp.getWifiQueryList());
                            } else {
                                resp.getWifiQueryList().addAll(temp.getWifiQueryList());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("结果转换出错:" + serviceResult.getResponse());
                }
            }
        } catch (Exception e) {
            log.error("查询wifi出错结果:" + JsonUtil.objectToJson(wifiQueryRequest));
        }
    }

    //国家代码转换为国家名称
    private String countryCodeToName(String code) {
        String name = "";
        switch (code) {
            case "JP":
                name = "日本";
                break;
            case "KR":
                name = "韩国";
                break;
            case "TH":
                name = "泰国";
                break;
            case "TW":
                name = "中国台湾";
                break;
            case "HK":
                name = "中国香港";
                break;
            case "MO":
                name = "中国澳门";
                break;
            case "RU":
                name = "俄罗斯";
                break;
            case "PH":
                name = "菲律宾";
                break;
            case "SG":
                name = "新加坡";
                break;
            default:
                name = "";
                break;
        }
        return name;
    }

    //计算税费
    @ApiOperation(value = "国际税费查询", notes = "国际税费查询")
    @RequestMapping(value = "/flightInternatTax", method = RequestMethod.POST)
    public QueryTaxResp queryFlightInternatTax(@RequestBody QueryTaxReq taxReq, HttpServletRequest request) {
        QueryTaxResp resp = new QueryTaxResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryTaxReq>> violations = validator.validate(taxReq);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(taxReq.getFfpId(), taxReq.getLoginKeyInfo(), taxReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        TaxQueryRequest taxQueryRequest = CreateQueryTaxRequest(taxReq);
        HttpResult serviceResult = doPost(taxQueryRequest, HandlerConstants.URL_FARE + HandlerConstants.QUERY_INTERNAT_TAX);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                TaxQueryResponse taxResponse = (TaxQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), TaxQueryResponse.class);
                if (taxResponse.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setPassengerTaxList(AVObjectConvert.toFlightTaxResponse(taxQueryRequest.getPassengerTypeList(), taxResponse));
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询出错:" + resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询tax出错结果:" + JsonUtil.objectToJson(taxQueryRequest));
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(NETWORK_ERROR_MESSAGE);
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(NETWORK_ERROR_MESSAGE);
            return resp;
        }
    }

    //最低价查询
    @InterfaceLog
    @ApiOperation(value = "低价日历查询", notes = "低价日历查询")
    @RequestMapping(value = "periodMinPrice", method = RequestMethod.POST)
    public QueryMinPriceResp getPeriodMinPrice(@RequestBody @Validated QueryMinPriceReq queryMinPrice, BindingResult bindingResult, HttpServletRequest request) {
        QueryMinPriceResp resp = new QueryMinPriceResp();
        //基本参数检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        //特殊城市转换
        Map<String, SpecialFareQuery> specialFareQueryMap = handConfig.getSpecialFareQueryMap();
        if (specialFareQueryMap != null) {
            SpecialFareQuery depSpecialFareQuery = specialFareQueryMap.get(queryMinPrice.getDepCity());
            if (depSpecialFareQuery != null) {
                queryMinPrice.setDepCity(depSpecialFareQuery.getCityCode());
            }
            SpecialFareQuery arrSpecialFareQuery = specialFareQueryMap.get(queryMinPrice.getArrCity());
            if (arrSpecialFareQuery != null) {
                queryMinPrice.setArrCity(arrSpecialFareQuery.getCityCode());
            }
        }
        //新版本接口控制
        FlightDirection flightDirection = FlightDirection.getFlightDirection(queryMinPrice.getFlightDirection());
        if (flightDirection == null) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("飞行方向参数不正确");
            return resp;
        }
        //类型为单程，方向为Back是错误的，纠正方向为G
        if (HandlerConstants.ROUTE_TYPE_OW.equals(queryMinPrice.getFlightType()) && FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection())) {
            queryMinPrice.setFlightDirection(FlightDirection.GO.getCode());
        }
        //判断是国内航线
        String internationalFlag = checkInternationalFlag(queryMinPrice.getDepCity(), queryMinPrice.getArrCity());
        //国内税费强制置为false
        if (HandlerConstants.TRIP_TYPE_D.equals(internationalFlag)) {
            queryMinPrice.setTaxFlag(false);
        }
        String startDateStr = queryMinPrice.getDepartureDate();
        //返程时需要更换时间
        if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection()) && !StringUtil.isNullOrEmpty(queryMinPrice.getReturnDate())) {
            startDateStr = queryMinPrice.getReturnDate();
        }
        // 微信，微信小程序渠道国际低价日历  转MOBILE
        String channelCode = basicService.getChannelCode(queryMinPrice.getChannelCode(), internationalFlag);
        queryMinPrice.setChannelCode(channelCode);
        /**
         * 奖励飞AWARD_FLY_FREE_TICKET专属数据查询，调用远程接口进行查询。因为没有最低价格，所有不需要对数据进行统一数据处理，可以直接返回
         * 下面的奖励飞查询是使用旧版本定时任务，现在使用新版本，直接查询远程接口
         */
        Map<String, FlightQueryTypeDto> flightQueryTypeMap = handConfig.getFlightQueryTypeMap();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        FlightQueryTypeDto flightQueryTypeDto = flightQueryTypeMap.get(queryMinPrice.getFlightQueryType());
        if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())) {
            //判断如果显示条数为999999，就显示假数据
            if (handConfig.getAwareFlyPeriodQueryDay().equals(999999)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                List<MinPrice> priceList1 = new ArrayList<>();
                for (int i = 0; i < 100; i++) {
                    MinPrice minPrice = new MinPrice();
                    minPrice.setAvailableBook(true);
                    Date date = DateUtils.addOrLessDay(new Date(), i);
                    minPrice.setDateStr(simpleDateFormat.format(date));
                    minPrice.setMinPriceMonthly(false);
                    String week = DateUtils.getWeek(date);
                    minPrice.setWeekStr(week);
                    minPrice.setPriceStr("0");
                    priceList1.add(minPrice);
                }
                resp.setPriceList(priceList1);
                return resp;
            } else {
                return findAwareFlyFreeTicketByRequest(queryMinPrice, request);
            }
        }
        int days = 7;
        Date startDate;//开始时间 //周取前后三天，月取查询日开始 日历取今天开始
        if (PeriodTypeEnum.Monthly.getPeriodType().equals(queryMinPrice.getPeriodType()) || PeriodTypeEnum.ThirtyInDays.getPeriodType().equals(queryMinPrice.getPeriodType())) {//Monthly当月最低价，30InDays 30天内最低价
            days = 30;
            startDate = DateUtils.toDate(startDateStr, "yyyy-MM-dd");
        } else if (PeriodTypeEnum.Calendar.getPeriodType().equals(queryMinPrice.getPeriodType())) {
            days = 500;
            startDate = DateUtils.toDate(DateUtils.getCurrentDateStr(), "yyyy-MM-dd");
        } else if (PeriodTypeEnum.Fifteen.getPeriodType().equals(queryMinPrice.getPeriodType())) {
            days = 15;
            startDate = DateUtils.addOrLessDay(DateUtils.toDate(startDateStr, "yyyy-MM-dd"), addDays(startDateStr, -7));
        } else if (PeriodTypeEnum.Weekly.getPeriodType().equals(queryMinPrice.getPeriodType())) {
            startDate = DateUtils.addOrLessDay(DateUtils.toDate(startDateStr, "yyyy-MM-dd"), addDays(startDateStr, -3));
        } else if (PeriodTypeEnum.HalfYear.getPeriodType().equals(queryMinPrice.getPeriodType())) {
            // 半年取查询日开始
            days = 183;
            startDate = DateUtils.toDate(startDateStr, "yyyy-MM-dd");
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
        String startDateR = "";
        String endDate = "";
        String cabinCode = "";
        if (StringUtils.isNotBlank(queryMinPrice.getFfpCardNo()) && StringUtils.isNotBlank(queryMinPrice.getFfpId())
                && StringUtils.isNotBlank(queryMinPrice.getActivityNo())) {
            RequestData requestData = creatActivityInfoRequestData(queryMinPrice, ip);
            ResponseData<QueryActivityInfoResp> responseData = themeCardService.queryActivityInfo(requestData);
            if (ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
                QueryActivityInfoResp queryActivityInfoResp = responseData.getData();
                ProductRuleLimitInfo productRuleLimitInfo = queryActivityInfoResp.getRuleLimit();
                FlightDateLimitInfo flightDateLimit = productRuleLimitInfo.getFlightDateLimit();
                List<String> cabinLimits = productRuleLimitInfo.getCabinLimits();
                if (flightDateLimit != null) {
                    List<DateLimitInfo> dateLimitInfos = flightDateLimit.getSuits();
                    if (CollectionUtils.isNotEmpty(dateLimitInfos)) {
                        DateLimitInfo dateLimitInfo = dateLimitInfos.get(0);
                        startDateR = simpleDateFormat.format(dateLimitInfo.getStartDate());
                        endDate = simpleDateFormat.format(dateLimitInfo.getEndDate());
                        if (CollectionUtils.isNotEmpty(cabinLimits)) {
                            cabinCode =cabinLimits.stream().collect(Collectors.joining(","));
                        }
                    }
                }
            } else {
                queryMinPrice.setActivityNo("");
            }
        } else {
            queryMinPrice.setActivityNo("");
        }
        if (flightQueryTypeDto != null && StringUtils.isBlank(queryMinPrice.getActivityNo())) {
            Date newDate = new Date();
            startDateR = simpleDateFormat.format(newDate);
            Date date = DateUtils.addOrLessDay(newDate, flightQueryTypeDto.getDays());
            endDate = simpleDateFormat.format(date);
            cabinCode = flightQueryTypeDto.getCabinCode();
        }
        if (StringUtils.isNotBlank(startDateR) && StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(cabinCode)) {
            String depCity=queryMinPrice.getDepCity();
            String arrCity=queryMinPrice.getArrCity();
            List<MinPrice> priceList1 = new ArrayList<>();
            if (FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection())){
                depCity=queryMinPrice.getArrCity();
                arrCity=queryMinPrice.getDepCity();
            }
            Map<String, Boolean> routeCalendarMap = routeCalendarList(depCity, arrCity, startDateR, endDate, cabinCode);
            for (int i = 0; i < days; i++) {
                MinPrice minPrice = new MinPrice();
                minPrice.setAvailableBook(true);
                Date date2 = DateUtils.addOrLessDay(startDate, i);
                minPrice.setDateStr(simpleDateFormat.format(date2));
                minPrice.setMinPriceMonthly(false);
                minPrice.setActivityDate(routeCalendarMap.get(minPrice.getDateStr()) != null);
                String week = DateUtils.getWeek(date2);
                Boolean availableBook = routeCalendarMap.get(minPrice.getDateStr()) == null ? false : routeCalendarMap.get(minPrice.getDateStr());
                minPrice.setAvailableBook(availableBook);
                minPrice.setWeekStr(week);
                minPrice.setPriceStr("0");
                priceList1.add(minPrice);
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setPriceList(priceList1);
            return resp;
        }
        //主题卡
        ThemeCoupon themeCoupon = null;
        Map<String, ThemeCoupon> themeCouponMap = toThemeModelMap(handConfig.getThemeCabinLabel());
        //获取主题卡配置
        if (themeCouponMap != null && themeCouponMap.get(queryMinPrice.getFlightQueryType()) != null) {
            themeCoupon = themeCouponMap.get(queryMinPrice.getFlightQueryType());
        }
        List<MinPrice> priceList = new ArrayList<>();
        //redis key处理 设置批量键值
        List<String> keyList = new LinkedList<>();
        Date keyStartDate = startDate;
        //判断是否返回价格数据
        boolean showPriceFlag = true;
        if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType())
                && HandlerConstants.TRIP_TYPE_I.equals(queryMinPrice.getInterFlag())
                && FlightDirection.GO.getCode().equals(queryMinPrice.getFlightDirection())) {
            showPriceFlag = false;
        }
        //如果是国际去程查询，则不展示价格
        for (int k = 1; k <= days; k++) {
            String currentDate = DateUtils.dateToString(keyStartDate, "yyyy-MM-dd");
            String key = "M" + queryMinPrice.getCurrencyCode() + queryMinPrice.getFlightType() + currentDate + queryMinPrice.getDepCity() + queryMinPrice.getArrCity();
            //国内 返程需后台调换起始终点
            if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && HandlerConstants.TRIP_TYPE_D.equals(queryMinPrice.getInterFlag()) && FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection())) {
                key = "M" + queryMinPrice.getCurrencyCode() + queryMinPrice.getFlightType() + currentDate + queryMinPrice.getArrCity() + queryMinPrice.getDepCity();
            }
            //5.5国际往返为打包价格
            if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && HandlerConstants.TRIP_TYPE_I.equals(queryMinPrice.getInterFlag())) {
                if (FlightDirection.GO.getCode().equals(queryMinPrice.getFlightDirection())) {
                    //国际往返查询时只关注第一段有无航班，所以此处直接查询单程的缓存结果
                    key = "M" + queryMinPrice.getCurrencyCode() + HandlerConstants.ROUTE_TYPE_OW + currentDate + queryMinPrice.getDepCity() + queryMinPrice.getArrCity();
                    //当去程大于返程日期时，需要+2处理
                    if (StringUtils.isNotBlank(queryMinPrice.getReturnDate())) {
                        key = "M" + queryMinPrice.getCurrencyCode() + queryMinPrice.getFlightType() + currentDate + "/" + queryMinPrice.getReturnDate() + queryMinPrice.getDepCity() + queryMinPrice.getArrCity();
                        Date returnDate = DateUtils.toDate(queryMinPrice.getReturnDate(), "yyyy-MM-dd");
                        if (returnDate != null && keyStartDate.getTime() > returnDate.getTime()) {
                            String returnDateStr = DateUtils.dateToString(DateUtils.addOrLessDay(keyStartDate, +2), "yyyy-MM-dd");
                            key = "M" + queryMinPrice.getCurrencyCode() + queryMinPrice.getFlightType() + currentDate + "/" + returnDateStr + queryMinPrice.getDepCity() + queryMinPrice.getArrCity();
                        }
                    } /*else {
                        //首页选择出发日期
                        key = "";
                    }*/
                } else if (FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection())) {
                    //返程日期时查询
                    key = "M" + queryMinPrice.getCurrencyCode() + queryMinPrice.getFlightType() + queryMinPrice.getDepartureDate() + "/" + currentDate + queryMinPrice.getDepCity() + queryMinPrice.getArrCity();
                }
            }
            String finalKey = RedisKeyConfig.COMMON_FLIGHT_MIN_PRICE + queryMinPrice.getChannelCode() + ":" + key;
            if (queryMinPrice.isTaxFlag()) {
                finalKey = RedisKeyConfig.COMMON_FLIGHT_TAXINFO + queryMinPrice.getChannelCode() + ":" + key;
            } else {
                if (PackageTypeEnum.ROUND_TRIP_PRIVILEGES.getPackType().equals(queryMinPrice.getCabinType())) {
                    finalKey = RedisKeyConfig.COMMON_FLIGHT_ROUND + queryMinPrice.getChannelCode() + ":" + key;
                }
            }
            if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType()) || themeCoupon != null) {
                String cityCode = FlightDirection.GO.getCode().equals(queryMinPrice.getFlightDirection()) ?
                        queryMinPrice.getDepCity() + queryMinPrice.getArrCity() : queryMinPrice.getArrCity() + queryMinPrice.getDepCity();
                finalKey = RedisKeyConfig.COMMON_FREE_TICKET_FLIGHT + currentDate + cityCode;
            }
            //奖励飞专属redis key
            if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())) {
                String cityCode = FlightDirection.GO.getCode().equals(queryMinPrice.getFlightDirection()) ?
                        queryMinPrice.getDepCity() + queryMinPrice.getArrCity() : queryMinPrice.getArrCity() + queryMinPrice.getDepCity();
                finalKey = RedisKeyConfig.COMMON_AWARD_FLY_FREE_TICKET + currentDate + cityCode;
            }
            keyList.add(finalKey);
            keyStartDate = DateUtils.addOrLessDay(keyStartDate, 1);
        }
        //批量获取低价缓存
        List<String> priceStrList;
        if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType()) || themeCoupon != null) {
            priceStrList = globalRedisService.getDataValueBatch(keyList);
        }
        //获取奖励飞免票,通过定时任务来获取
        else if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())) {
            priceStrList = globalRedisService.getDataValueBatch(keyList);
        } else {
            priceStrList = apiRedisService.getDataValueBatch(keyList);
        }
        Date valueStartDate = startDate;
        //组装缓存低价
        if (CollectionUtils.isNotEmpty(priceStrList)) {
            for (int d = 0; d < days; d++) {
                String currentDate = DateUtils.dateToString(valueStartDate, "yyyy-MM-dd");
                String price = priceStrList.get(d);
                //国内往返 无价格时取单程价格
                if (!PackageTypeEnum.ROUND_TRIP_PRIVILEGES.getPackType().equals(queryMinPrice.getCabinType())
                        && !FlightQueryTypeEnum.FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())
                        && !FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())
                        && themeCoupon == null) {
                    if (StringUtil.isNullOrEmpty(price) && HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && HandlerConstants.TRIP_TYPE_D.equals(internationalFlag)) {
                        String owKey = "M" + queryMinPrice.getCurrencyCode() + HandlerConstants.ROUTE_TYPE_OW + currentDate + queryMinPrice.getDepCity() + queryMinPrice.getArrCity();
                        //国内返程需切换
                        if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && HandlerConstants.TRIP_TYPE_D.equals(queryMinPrice.getInterFlag()) && FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection())) {
                            owKey = "M" + queryMinPrice.getCurrencyCode() + HandlerConstants.ROUTE_TYPE_OW + currentDate + queryMinPrice.getArrCity() + queryMinPrice.getDepCity();
                        }
                        owKey = RedisKeyConfig.COMMON_FLIGHT_MIN_PRICE + queryMinPrice.getChannelCode() + ":" + owKey;
                        price = apiRedisService.getData(owKey);
                    }
                }
                MinPrice minprice = new MinPrice();
                minprice.setDateStr(currentDate);
                minprice.setWeekStr(DateUtils.getWeekStrNoZhou(valueStartDate));
                minprice.setPriceStr(StringUtils.isBlank(price) ? "" : price);
                priceList.add(minprice);
                valueStartDate = DateUtils.addOrLessDay(valueStartDate, 1);
            }
        }
        if ((PeriodTypeEnum.Monthly.getPeriodType().equals(queryMinPrice.getPeriodType())
                || PeriodTypeEnum.Calendar.getPeriodType().equals(queryMinPrice.getPeriodType())
                || PeriodTypeEnum.Fifteen.getPeriodType().equals(queryMinPrice.getPeriodType()))
                && !FlightQueryTypeEnum.FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())
                && !FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())
                && themeCoupon == null) {
            //标识当月最低价
            priceList = markIsMinPriceMonthly(priceList);
            //判断当前日期是否有航班 国际判断，国内默认都是可点击
            if (HandlerConstants.TRIP_TYPE_I.equalsIgnoreCase(internationalFlag) && PeriodTypeEnum.Calendar.getPeriodType().equals(queryMinPrice.getPeriodType())) {
                matchFlightDate(priceList, queryMinPrice, ip, showPriceFlag);
            }
        } else if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType()) || themeCoupon != null) {
            Date maxDate = DateUtils.addOrLessDay(startDate, handConfig.getMaxDays());
            if (themeCoupon != null) {
                maxDate = DateUtils.addOrLessDay(startDate, handConfig.getThemeBuyDayLimit());
                ThemeCoupon finalThemeCoupon = themeCoupon;
                priceList.forEach(price -> {
                    if (!DateUtils.compareDate(DateUtils.toDate(finalThemeCoupon.getFlightStartTime()), DateUtils.toDate(finalThemeCoupon.getFlightEndTime()), DateUtils.toDate(price.getDateStr()))) {
                        price.setAvailableBook(false);
                    }
                });
            }
            final Date finalMaxDate = maxDate;
            Date finalStartDate = startDate;
            priceList.forEach(price -> {
                // 免票时存舱位,剩余座位量 格式为 X,2
                if (StringUtils.isNotBlank(price.getPriceStr()) && price.getPriceStr().contains(",")) {
                    String cabinAmount = price.getPriceStr().split(",")[1];
                    Date day = DateUtils.toDate(price.getDateStr());
                    //免票限制可选日期为三日
                    if (handConfig.getMaxDays() != 0 && day.compareTo(finalStartDate) >= 0 && day.compareTo(finalMaxDate) < 0) {
                        price.setAvailableBook(false);
                    } else {
                        price.setAvailableBook("A".equals(cabinAmount) || NumberUtils.toInt(cabinAmount) > 0);
                    }
                } else {
                    price.setAvailableBook(false);
                }
            });
        }
        //奖励飞价格封装
        else if (FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())) {
            Date maxDate = DateUtils.addOrLessDay(startDate, 3);
            Date finalStartDate1 = startDate;
            priceList.forEach(price -> {
                // 免票时存舱位,剩余座位量 格式为 I,1
                if (StringUtils.isNotBlank(price.getPriceStr()) && price.getPriceStr().contains(",")) {
                    String cabinAmount = price.getPriceStr().split(",")[1];
                    Date day = DateUtils.toDate(price.getDateStr());
                    //免票限制可选日期为三日
                    if (day.compareTo(finalStartDate1) >= 0 && day.compareTo(maxDate) < 0) {
                        price.setAvailableBook(false);
                    } else {
                        price.setAvailableBook("A".equals(cabinAmount) || NumberUtils.toInt(cabinAmount) > 0);
                    }
                } else {
                    price.setAvailableBook(false);
                }
            });
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setPriceList(priceList);
        return resp;
    }

    private RequestData creatActivityInfoRequestData(QueryMinPriceReq queryMinPrice, String ip) {
        QueryActivityInfoReq queryActivityInfoReq1 = new QueryActivityInfoReq();
        queryActivityInfoReq1.setActivityNo(queryMinPrice.getActivityNo());
        queryActivityInfoReq1.setProductType(queryMinPrice.getProductType());
        RequestData requestData = RequestData.builder()
                .channelNo(ChannelCodeEnum.MOBILE.getChannelCode())
                .ffpId(queryMinPrice.getFfpId())
                .ffpNo(queryMinPrice.getFfpCardNo())
                .originIp(ip)
                .data(queryActivityInfoReq1).build();
        return requestData;
    }


    //最低价查询
    @ApiOperation(value = "航班半年运价查询", notes = "国际航班半年运价查询")
    @RequestMapping(value = "toGainHalfYearPrice", method = RequestMethod.POST)
    @InterfaceLog
    public QueryHalfYearPriceResp toGainHalfYearPrice(@RequestBody @Validated QueryMinPriceReq queryMinPrice, BindingResult bindingResult, HttpServletRequest request) {
        QueryHalfYearPriceResp resp = new QueryHalfYearPriceResp();
        //基本参数检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //特殊城市转换
        Map<String, SpecialFareQuery> specialFareQueryMap = handConfig.getSpecialFareQueryMap();
        if (specialFareQueryMap != null) {
            SpecialFareQuery depSpecialFareQuery = specialFareQueryMap.get(queryMinPrice.getDepCity());
            if (depSpecialFareQuery != null) {
                queryMinPrice.setDepCity(depSpecialFareQuery.getCityCode());
            }
            SpecialFareQuery arrSpecialFareQuery = specialFareQueryMap.get(queryMinPrice.getArrCity());
            if (arrSpecialFareQuery != null) {
                queryMinPrice.setArrCity(arrSpecialFareQuery.getCityCode());
            }
        }
        //新版本接口控制
        FlightDirection flightDirection = FlightDirection.getFlightDirection(queryMinPrice.getFlightDirection());
        if (flightDirection == null) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("飞行方向参数不正确");
            return resp;
        }
        //类型为单程，方向为Back是错误的，纠正方向为G
        if (HandlerConstants.ROUTE_TYPE_OW.equals(queryMinPrice.getFlightType()) && FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection())) {
            queryMinPrice.setFlightDirection(FlightDirection.GO.getCode());
        }
        //判断是国内航线
        String internationalFlag = checkInternationalFlag(queryMinPrice.getDepCity(), queryMinPrice.getArrCity());
        if (HandlerConstants.TRIP_TYPE_D.equals(internationalFlag)) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setErrorInfo("暂不支持国内航班查询");
            return resp;
        }

        if (!PeriodEnum.HalfYear.getPeriodType().equals(queryMinPrice.getPeriodType())) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setErrorInfo("只支持查询半年内的国际票信息");
            return resp;
        }

        String startDateStr = queryMinPrice.getDepartureDate();
        //返程时需要更换时间
        if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection()) && !StringUtil.isNullOrEmpty(queryMinPrice.getReturnDate())) {
            startDateStr = queryMinPrice.getReturnDate();
        }
        int days = 183;
        Date startDate = DateUtils.addOrLessDay(DateUtils.toDate(startDateStr, "yyyy-MM-dd"), 1);
        //redis key处理 设置批量键值
        List<String> keyList = new LinkedList<>();
        Date keyStartDate = startDate;
        for (int k = 1; k <= days; k++) {
            String currentDate = DateUtils.dateToString(keyStartDate, "yyyy-MM-dd");
            String finalKey = "";
            if (HandlerConstants.ROUTE_TYPE_OW.equals(queryMinPrice.getFlightType())) {
                finalKey = RedisKeyConfig.COMMON_FLIGHT_HALFYEAR + "H_" + queryMinPrice.getCurrencyCode() + "_" + queryMinPrice.getFlightType() + "_" + currentDate + "_" + queryMinPrice.getDepCity() + "_" + queryMinPrice.getArrCity();
            } else if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && org.apache.commons.lang3.StringUtils.isNotEmpty(queryMinPrice.getReturnDate())) {
                String returnDateStr = DateUtils.dateToString(DateUtils.addOrLessDay(keyStartDate, +2), "yyyy-MM-dd");
                finalKey = RedisKeyConfig.COMMON_FLIGHT_HALFYEAR + "H_" + queryMinPrice.getCurrencyCode() + "_" + queryMinPrice.getFlightType() + "_" + currentDate + "/" + returnDateStr + "_" + queryMinPrice.getDepCity() + "_" + queryMinPrice.getArrCity();
            }
            keyList.add(finalKey);
            keyStartDate = DateUtils.addOrLessDay(keyStartDate, 1);
        }

        List<String> priceStrList = apiRedisService.getDataValueBatch(keyList);
        priceStrList.removeAll(Collections.singleton(null));

        Map<String, List<com.juneyaoair.baseclass.response.av.FlightInfo>> flightInfoMap = new TreeMap<>();
        LinkedHashMap<String, List<com.juneyaoair.baseclass.response.av.FlightInfo>> sortedMap = new LinkedHashMap<>(
        );
        if (CollectionUtils.isNotEmpty(priceStrList)) {
            for (String s : priceStrList) {
                List<com.juneyaoair.baseclass.response.av.FlightInfo> flightInfos = JSON.parseObject(s, new TypeReference<List<com.juneyaoair.baseclass.response.av.FlightInfo>>() {
                });
                if (!StringUtil.isNullOrEmpty(flightInfos.get(0).getFlightDate())) {
                    flightInfoMap.put(flightInfos.get(0).getFlightDate(), flightInfos);
                }
            }
            sortedMap = flightInfoMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(
                    Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldVal, newVal) -> oldVal,
                            LinkedHashMap::new
                    )
            );
        }
        List<com.juneyaoair.baseclass.response.av.FlightInfo> flightInfos = new ArrayList<>();
        List<com.juneyaoair.baseclass.response.av.FlightInfo> flightInfosBack = new ArrayList<>();

        if (!sortedMap.isEmpty()) {
            for (Map.Entry<String, List<com.juneyaoair.baseclass.response.av.FlightInfo>> entry : sortedMap.entrySet()
            ) {
                flightInfos = entry.getValue();
                if (CollectionUtils.isNotEmpty(flightInfos)) {
                    break;
                }
            }
        }

        if (CollectionUtils.isNotEmpty(flightInfos)) {
            List<com.juneyaoair.baseclass.response.av.FlightInfo> collect = flightInfos.stream().sorted(Comparator.comparing(com.juneyaoair.baseclass.response.av.FlightInfo::getMinPrice, Comparator.nullsLast(Double::compareTo))).collect(Collectors.toList());
            com.juneyaoair.baseclass.response.av.FlightInfo flightInfo = collect.get(0);
            flightInfosBack.add(flightInfo);
        }

        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setFlightInfoList(flightInfosBack);
        return resp;
    }

    /**
     * 通过远程请求查询 奖励飞免票，特价日历
     *
     * @param queryMinPrice
     * @param request
     * @return
     */
    private QueryMinPriceResp findAwareFlyFreeTicketByRequest(QueryMinPriceReq queryMinPrice, HttpServletRequest request) {
        QueryMinPriceResp resp = new QueryMinPriceResp();
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_FREE_CALENDAR;
        String headClientVer = request.getHeader(HandlerConstants.CLIENT_VERSION);
        //封装参数
        AwardFlyPeriodRequest awardFlyPeriodRequest = new AwardFlyPeriodRequest();
        awardFlyPeriodRequest.setChannelCode(queryMinPrice.getChannelCode());
        awardFlyPeriodRequest.setVersion(headClientVer);
        //2021年10月13日 N,I取消三天限制
        awardFlyPeriodRequest.setStartQueryDate(DateUtils.convertDate2Str(new Date(), "yyyy-MM-dd"));
        //通过apollo进行配置
        awardFlyPeriodRequest.setQueryDay(handConfig.getAwareFlyPeriodQueryDay());
        awardFlyPeriodRequest.setDepCity(queryMinPrice.getDepCity());
        awardFlyPeriodRequest.setArrCity(queryMinPrice.getArrCity());
        //通过apollo进行配置
        awardFlyPeriodRequest.setReadRedis(handConfig.getAwareFlyPeriodUserRedis());
        awardFlyPeriodRequest.setFareSource("Searchone");
        if (FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(queryMinPrice.getFlightQueryType())) {
            awardFlyPeriodRequest.setCabin(handConfig.getBusinessFlyFreeTicketCabin());
        }
        /**
         * 发送请求
         */
        HttpResult result = this.doPostClient(awardFlyPeriodRequest, url);
        if (!result.isResult()) {
            resp = new QueryMinPriceResp();
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("远程接口调用错误");
            String reqJson = JsonUtil.objectToJson(awardFlyPeriodRequest);
            log.info("奖励飞远程接口调用错误,url:{},请求内容{}", url, reqJson);
            return resp;
        }
        AwardFlyPeriodResponse response = (AwardFlyPeriodResponse) JsonUtil.jsonToBean(result.getResponse(), AwardFlyPeriodResponse.class);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
            resp = new QueryMinPriceResp();
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("远程接口返回结果错误");
            log.info("奖励飞远程接口返回结果错误,url:{},响应内容：{}", url, result.getResponse());
            return resp;
        }
        //封装响应参数
        List<MinPrice> priceList = new ArrayList<>();
        List<FreeCalendar> freeCalendarList = response.getFreeCalendarList();
        //将list转成map用作返回参数
        Map<String, FreeCalendar> freeCalendarMap = freeCalendarList.stream()
                .collect(Collectors.toMap(FreeCalendar::getDateStr, a -> a));
        //生成360条，从今天开始算
        for (int i = 0; i < 360; i++) {
            Date date = DateUtils.addOrLessDay(new Date(), i);
            String dateStr = DateUtils.convertDateToString(date, "yyyy-MM-dd");
            FreeCalendar freeCalendar = freeCalendarMap.get(dateStr);
            if (freeCalendar == null) {
                freeCalendar = new FreeCalendar();
                freeCalendar.setDateStr(dateStr);
                freeCalendar.setDvailableBook(false);
            }
            MinPrice minPrice = new MinPrice();
            minPrice.setDateStr(freeCalendar.getDateStr());
            minPrice.setAvailableBook(freeCalendar.getDvailableBook());
            minPrice.setMinPriceMonthly(false);
            minPrice.setPriceStr(freeCalendar.getDvailableBook() ? "0" : null);
            minPrice.setWeekStr(DateUtils.getWeek(date));
            priceList.add(minPrice);
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setPriceList(priceList);
        return resp;
    }

    /**
     * 航线兑换日历
     *
     * @param depCity
     * @param arrCity
     * @param startDate
     * @param endDate
     * @param cabinType
     * @return
     */
    private Map<String, Boolean> routeCalendarList(String depCity, String arrCity, String startDate, String endDate, String cabinType) {
        Map<String, Boolean> map = new HashMap<>();
        RouteCalendarReq routeCalendarReq = new RouteCalendarReq();
        routeCalendarReq.setArrCity(arrCity);
        routeCalendarReq.setDepCity(depCity);
        routeCalendarReq.setStartDate(startDate);
        routeCalendarReq.setEndDate(endDate);
        routeCalendarReq.setCabinType(cabinType);
        HttpResult httpResult = this.doPostClient(routeCalendarReq, HandlerConstants.BASIC_INFO_URL + HandlerConstants.ROUTE_CALENDAR_LIST);
        if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
            log.info("查询航线兑换日历,网络错误!");
            return map;
        }
        RouteCalendarBaseResp resp = JSON.parseObject(httpResult.getResponse(), new TypeToken<RouteCalendarBaseResp>() {
        }.getType());
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            log.info("查询航线兑换日历,请求内容:{},响应内容{}", JsonUtil.objectToJson(routeCalendarReq), JsonUtil.objectToJson(resp));
            return map;
        } else {
            List<RouteCalendarDto> routeCalendarList = resp.getObjData();
            if (CollectionUtils.isNotEmpty(routeCalendarList)) {
                map = routeCalendarList.stream()
                        .collect((Collectors.toMap(x -> x.getDateStr(), x -> x.getAvailable())));
                return map;
            }
        }
        return map;
    }

    /**
     * 匹配当日有无航班
     *
     * @param priceList
     * @param queryMinPrice
     * @param ip
     * @param showPriceFlag 是否返回价格信息
     */
    private void matchFlightDate(List<MinPrice> priceList, QueryMinPriceReq queryMinPrice, String ip, boolean showPriceFlag) {
        List<String> flightDates = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(priceList)) {
            priceList.stream().forEach(minPrice -> {
                flightDates.add(minPrice.getDateStr());
            });
        }
        String depCity = queryMinPrice.getDepCity();
        String arrCity = queryMinPrice.getArrCity();
        //往返 返程需要调换起始点查询
        if (HandlerConstants.ROUTE_TYPE_RT.equals(queryMinPrice.getFlightType()) && FlightDirection.BACK.getCode().equals(queryMinPrice.getFlightDirection())) {
            depCity = queryMinPrice.getArrCity();
            arrCity = queryMinPrice.getDepCity();
        }
        //查询航线信息
        List<AirLineLabelDTO> airLineLabelDTOList = basicService.queryCacheFlightLine(depCity, arrCity, queryMinPrice.getChannelCode(), ip);
        //航线是否存在标记
        boolean isExist = false;
        if (CollectionUtils.isNotEmpty(airLineLabelDTOList)) {
            isExist = true;
        }
        //所有的航班信息
        //1.判断直达是否有航班
        //2.判断addon航班
        //3.判断spa航班
        List<FlightExistDateDTO> flightExistDateDTOList;
        if (isExist) {
            BasicBaseReq<FlightExistDateReqDTO> basicBaseReq = new BasicBaseReq<>(HandlerConstants.BASIC_INFO_VERSION, queryMinPrice.getChannelCode(), ip);
            FlightExistDateReqDTO flightExistDateReqDTO = FlightExistDateReqDTO.builder().depCityCode(depCity).arrCityCode(arrCity).build();
            basicBaseReq.setRequest(flightExistDateReqDTO);
            flightExistDateDTOList = basicService.queryFlightInfoExistByCity(basicBaseReq);
        } else {
            flightExistDateDTOList = new ArrayList<>();

        }
        //日期价格缓存与航班信息组合判断
        if (CollectionUtils.isNotEmpty(priceList)) {
            for (MinPrice minPrice : priceList) {
                String curMinPriceDate = minPrice.getDateStr();
                if (isExist) {
                    boolean isHas = true;
                    if (CollectionUtils.isNotEmpty(flightExistDateDTOList)) {
                        //判断当前日期是否有航班
                        FlightExistDateDTO flightExistDateDTO = flightExistDateDTOList.stream().filter(flightExistDateTemp -> curMinPriceDate.equals(flightExistDateTemp.getDate())).findFirst().orElse(null);
                        if (flightExistDateDTO != null) {
                            isHas = flightExistDateDTO.isExistFlight();
                        }
                    }
                    minPrice.setAvailableBook(isHas || StringUtils.isNotBlank(minPrice.getPriceStr()));
                    if (!showPriceFlag) {
                        minPrice.setPriceStr("");
                    }
                } else {
                    minPrice.setAvailableBook(false);
                }
            }
            //容错处理，如果都是不可点击的默认恢复成可点状态 null代表数据有问题
            boolean isAllUnavailable = priceList.stream().allMatch(minPrice -> !minPrice.isAvailableBook());
            if (airLineLabelDTOList == null || isAllUnavailable) {
                for (MinPrice minPrice : priceList) {
                    minPrice.setAvailableBook(true);
                }
            }
            if (CollectionUtils.isNotEmpty(priceList)) {
                priceList.stream().filter(minPrice -> !minPrice.isAvailableBook()).forEach(minPrice -> minPrice.setPriceStr(""));
            }
        }
    }

    //计算当前日期与选择的日期偏移量
    private int addDays(String chooseDate, int day) {
        Date curDate = DateUtils.toDate(DateUtils.getCurrentDateStr(), "yyyy-MM-dd");
        Date depDate = DateUtils.toDate(chooseDate, "yyyy-MM-dd");
        int diff = DateUtils.dateDiff(depDate, curDate);
        if (diff <= 0) {//表示选择的日期在当前时间之前
            return Math.abs(diff);
        } else {
            int absoluteValue = Math.abs(day);
            if (diff >= absoluteValue) {
                return day;
            } else {
                return -diff;
            }
        }
    }

    //月最低价解析
    private List<MinPrice> markIsMinPriceMonthly(List<MinPrice> minPriceList) {
        List<MinPrice> retList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        //初始化第一个月第一天的月份
        Date firstDate = DateUtils.toDate(minPriceList.get(0).getDateStr());
        calendar.setTime(firstDate);
        double firstMonth = calendar.get(Calendar.MONTH) + 1d;
        double firstYear = calendar.get(Calendar.YEAR);
        double firstPrice = minPriceList.get(0).getPriceStr().equals("") ? 0 : Double.parseDouble(minPriceList.get(0).getPriceStr());
        try {
            if (CollectionUtils.isNotEmpty(minPriceList)) {
                List<MinPrice> cellList = new ArrayList<>();
                for (int i = 0; i < minPriceList.size(); i++) {
                    MinPrice minPrice = minPriceList.get(i);
                    Date date = DateUtils.toDate(minPrice.getDateStr());
                    calendar.setTime(date);
                    int month = calendar.get(Calendar.MONTH) + 1;
                    int year = calendar.get(Calendar.YEAR);
                    double priceNow = minPrice.getPriceStr().equals("") ? 0 : Double.parseDouble(minPrice.getPriceStr());
                    //拆分minPriceList
                    if (month == firstMonth && year == firstYear) {
                        cellList.add(minPrice);
                        if (i == minPriceList.size() - 1) {//不跨月时遍历至最后一个时，也需要标记当月的最低价
                            findMinPrice(cellList, firstPrice);
                            retList.addAll(cellList);
                        }
                    } else {
                        //跨月份时标记上个月的最低价
                        --i;
                        if (null != cellList && cellList.size() > 0) {
                            findMinPrice(cellList, firstPrice);
                            retList.addAll(cellList);
                            cellList.clear();
                        }

                        firstMonth = month;
                        firstYear = year;
                        firstPrice = priceNow;
                    }
                }
            }
        } catch (Exception e) {
            log.error("markIsMinPriceMonthly ERROR:" + e.getMessage(), e);
            return minPriceList;
        }
        return retList;
    }


    private void findMinPrice(List<MinPrice> cellList, double firstPrice) {
        for (MinPrice cellPrice : cellList) {
            double price = cellPrice.getPriceStr().equals("") ? 0 : Double.parseDouble(cellPrice.getPriceStr());
            if ((firstPrice == 0 && price != 0) || (firstPrice > price && price != 0)) {
                firstPrice = price;
            }
        }
        //得到最低价金额
        if (firstPrice != 0) {
            for (int j = 0; j < cellList.size(); j++) {
                double price2 = cellList.get(j).getPriceStr().equals("") ? 0 : Double.parseDouble(cellList.get(j).getPriceStr());
                if (firstPrice == price2) {
                    cellList.get(j).setMinPriceMonthly(true);
                }
            }
        }
    }


    /**
     * 根据城市信息判别是否国内国际航班
     *
     * @param depCity 出发城市
     * @param arrCity 到达城市
     * @return
     */
    private String checkInternationalFlag(String depCity, String arrCity) {
        String flag = HandlerConstants.TRIP_TYPE_D;
        CityInfoDto depCityInfo = localCacheService.getLocalCity(depCity);
        CityInfoDto arrCityInfo = localCacheService.getLocalCity(arrCity);
        if (depCityInfo == null || arrCityInfo == null) {
            return flag;
        } else {
            if (HandlerConstants.TRIP_TYPE_I.equals(depCityInfo.getIsInternational()) ||
                    HandlerConstants.TRIP_TYPE_I.equals(arrCityInfo.getIsInternational())) {
                flag = HandlerConstants.TRIP_TYPE_I;
            }
            return flag;
        }
    }

    /**
     * @param request
     * @return
     * @Deprecated {@link com.juneyaoair.mobile.handler.controller.FlightController#getFlightLine5_0}
     */
    @Deprecated
    @ApiOperation(value = "航线查询(废弃)", notes = "航线查询废弃")
    @RequestMapping(value = "/flightLine", method = RequestMethod.POST)
    public List<AirLineInfoDepCityDto> getFlightLine(HttpServletRequest request) {
        List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = basicService.selectAllAirline();
        return airLineInfoDepCityDtoList;
    }

    //航线查询2
    @ApiOperation(value = "航线查询2", notes = "航线查询2")
    @RequestMapping(value = "/new/flightLine", method = RequestMethod.POST)
    public BaseResp getFlightLine5_0(@RequestBody @Validated BaseReq req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        try {
            resp.setChannelCode(req.getChannelCode());
            //切换至基础服务的航线查询方法
            List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = basicService.selectAllAirline();
            if (!StringUtil.isNullOrEmpty(airLineInfoDepCityDtoList)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(airLineInfoDepCityDtoList);
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
            }
            return resp;
        } catch (Exception e) {
            log.error("航线查询2查询失败", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    //航线机场查询
    @ApiOperation(value = "getFlightLineAirPort", notes = "航线机场查询")
    @RequestMapping(value = "/getFlightLineAirPort", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<AirLineDeptAirPortDto>> getFlightLineAirPort(@RequestBody BaseReq req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            List<AirLineDeptAirPortDto> airLineDeptAirPortDtoList = airportInfoService.queryAirLineDeptAirPort(true);
            if (!StringUtil.isNullOrEmpty(airLineDeptAirPortDtoList)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(airLineDeptAirPortDtoList);
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    //查询热门城市
    @ApiOperation(value = "queryHotCity", notes = "热门城市航线")
    @RequestMapping(value = "queryHotCity", method = RequestMethod.POST)
    public BaseResp<List<AirLineDepCityDto>> queryHotCity(HttpServletRequest request, @RequestBody BaseReq req) {
        BaseResp<List<AirLineDepCityDto>> resp = new BaseResp<>();
        try {
            List<AirLineDepCityDto> list = airportInfoService.queryHotCity();
            resp.setChannelCode(req.getChannelCode());
            if (!StringUtil.isNullOrEmpty(list)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(list);
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    /**
     * AIRPORT_INFO  版本控制
     *
     * @param req
     * @param request
     * @return
     */
    @ApiOperation(value = "城市，机场搜索", notes = "城市，机场搜索")
    @RequestMapping(value = "searchCityAirportInfo", method = RequestMethod.POST)
    public BaseResp<List<CityAirportInfo>> searchCityAirportInfo(@RequestBody BaseReq req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String ip = this.getClientIP(request);
        try {
            this.validateRequest(req);
            BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, realChannelCode, ip);
            List<CityAirportInfo> cityAirportInfoList = basicService.searchCityAirportInfo(basicBaseReq, RedisKeyConfig.CACHE_CITY_AIRPORT);
            resp.setObjData(cityAirportInfoList);
            if (!StringUtil.isNullOrEmpty(cityAirportInfoList)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
            }
        } catch (RequestParamErrorException paramErrorException) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
        } catch (Exception e) {
            log.error("城市，机场搜索查询失败", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    /*
     * 休息室查询
     * @param flightInsureReq
     * @return
     */
    private LoungeQueryRequest CreateQueryLoungeRequest(QueryLoungeReq queryLoungeReq) {
        String channelCode = queryLoungeReq.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        LoungeQueryRequest loungeQueryRequest = new LoungeQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                queryLoungeReq.getAirportCode(),
                queryLoungeReq.getFlightDate(),
                queryLoungeReq.getIsSingleBuy(),
                queryLoungeReq.getFfpId(),
                queryLoungeReq.getFfpCardNo()
        );
        loungeQueryRequest.setCabin(queryLoungeReq.getCabin());
        return loungeQueryRequest;
    }

    private TaxQueryRequest CreateQueryTaxRequest(QueryTaxReq taxReq) {
        String channelCode = taxReq.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        List<String> passList = new ArrayList<>();
        passList.add("ADT");
        passList.add("CHD");
        passList.add("INF");
        TaxQueryRequest taxQueryRequest = new TaxQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                HandlerConstants.CURRENCY_CODE,
                passList,
                taxReq.getSegmentInfoList()
        );
        return taxQueryRequest;
    }

    //逾重行李查询
    @ApiOperation(value = "逾重行李产品", notes = "逾重行李产品")
    @RequestMapping(value = "queryBaggageExcess", method = RequestMethod.POST)
    public QueryBaggageExcessResp queryBaggageExcess(@RequestBody QueryBaggageExcessReq queryBaggageExcessReq, HttpServletRequest request) {
        QueryBaggageExcessResp response = new QueryBaggageExcessResp();
        String reqId = StringUtil.newGUID() + "_queryBaggageExcess";
        log.info("请求号:{},客户端提交参数:{}", reqId, JsonMapper.buildNonNullMapper().toJson(queryBaggageExcessReq));
        String ip = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryBaggageExcessReq>> violations = validator.validate(queryBaggageExcessReq);
        if (null != violations && violations.size() > 0) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(queryBaggageExcessReq.getFfpId(), queryBaggageExcessReq.getLoginKeyInfo(), queryBaggageExcessReq.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        //行李产品查询
        QueryBaggageExcessRequest queryBaggageExcessRequest = CreateQueryBaggageExcessRequest(queryBaggageExcessReq);
        HttpResult serviceResult = doPost(queryBaggageExcessRequest, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_BAGGAGEEXCESS);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                QueryBaggageExcessResponse serviceRes = (QueryBaggageExcessResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), QueryBaggageExcessResponse.class);
                if (serviceRes.getResultCode().equals("1001")) {
                    response.setSegmentInfoList(serviceRes.getSegmentInfoList());
                    /*if (queryBaggageExcessReq.getClientVersion() == null) {
                        if (StringUtil.isNullOrEmpty(baggageExcessList)) {  //4.1.04 出现的BUG处理
                            BaggageExcess baggageExcess = new BaggageExcess();
                            baggageExcess.setTitle("暂无");
                            baggageExcess.setExcessAmount(0.0);
                            baggageExcessList.add(baggageExcess);
                            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                            response.setResultCode(WSEnum.SUCCESS.getResultCode());
                            response.setBaggageExcessCouponList(new ArrayList<>());
                            log.info("请求号:" + reqId + "，响应结果:" + JsonUtil.objectToJson(response));
                            return response;
                        }
                    }*/
                    response.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                    response.setResultCode(WSEnum.SUCCESS.getResultInfo());
                    //行李优惠券查询
                    //List<BaggageExcess> baggageExcessList = serviceRes.getSegmentInfoList().get(0).getBaggageExcessList();//在售逾重行李产品
                    //新版行李优惠券查询
                    /*if (!StringUtil.isNullOrEmpty(baggageExcessList)) {
                        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = createCouponProductGetRequestDto(queryBaggageExcessReq);
                        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_MY_PRODUCT_V2;
                        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                        String result = this.invokePost(ptCouponProductGetRequestDto, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                        if (!StringUtil.isNullOrEmpty(result)) {
                            PtCouponProductGetResponseDto ptCouponProductGetResponseDto = (PtCouponProductGetResponseDto) JsonUtil.jsonToBean(result, PtCouponProductGetResponseDto.class);
                            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductGetResponseDto.getResultCode())) {
                                List<AvailCoupon> availCouponList = RightCouponConvert.formatBaggegeCouponList(ptCouponProductGetResponseDto.getVouchers());
                                if(!StringUtil.isNullOrEmpty(availCouponList)){
                                    List<BaggageExcessCouponInfo> baggageExcessCouponInfoList = new ArrayList<>();
                                    BaggageExcessCouponInfo baggageExcessCouponInfo = null;
                                    //遍历行李券，将产品信息赋给券
                                    for(int i=0;i<availCouponList.size();i++){
                                        AvailCoupon availCoupon = availCouponList.get(i);
                                        if(availCoupon.getOverweight() != 0){
                                            for(BaggageExcess baggageExcess:baggageExcessList){
                                                if(baggageExcess.getOverweight() != 0 && baggageExcess.getOverweight() == availCoupon.getOverweight() && baggageExcess.getUnit().equals(availCoupon.getUnit())){
                                                    baggageExcessCouponInfo = new BaggageExcessCouponInfo();
                                                    BeanUtils.copyNotNullProperties(availCoupon,baggageExcessCouponInfo);
                                                    //行李产品
                                                    baggageExcessCouponInfo.setBaggageExcessInfo(baggageExcess);
                                                    baggageExcessCouponInfo.setDepAirport(baggageExcess.getDepAirport());
                                                    baggageExcessCouponInfo.setArrAirport(baggageExcess.getArrAirport());
                                                    baggageExcessCouponInfoList.add(baggageExcessCouponInfo);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    response.setBaggageExcessCouponList(baggageExcessCouponInfoList);
                                }else{
                                    response.setBaggageExcessCouponList(new ArrayList<>());
                                }
                            }else{
                                response.setBaggageExcessCouponList(new ArrayList<>());
                            }
                        }
                    }*/
                    //如果没有行李产品则不做优惠券查询
                    /*if(!StringUtil.isNullOrEmpty(baggageExcessList)){
                        //行李券
                        CouponQueryRequest couponQueryRequest = CreateAvailQueryCouponRequest(queryBaggageExcessReq, queryBaggageExcessReq.getChannelCode());
                        HttpResult couponResult = doPost(couponQueryRequest, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_AVAIL_COUPON);
                        if(couponResult!=null&&couponResult.isResult()){
                            AvailCouponsResponse availQueryCouponsResponse = (AvailCouponsResponse)JsonUtil.jsonToBean(couponResult.getResponse(),AvailCouponsResponse.class);
                            if(availQueryCouponsResponse.getResultCode().equals(UnifiedOrderResultEnum.SUCCESS.getResultCode())){
                                if(null != availQueryCouponsResponse.getAvailCouponList() &&availQueryCouponsResponse.getAvailCouponList().size()>0){
                                    List<AvailCoupon> availCouponList = availQueryCouponsResponse.getAvailCouponList();
                                    List<BaggageExcessCouponInfo> baggageExcessCouponInfoList = new ArrayList<BaggageExcessCouponInfo>();
                                    BaggageExcessCouponInfo baggageExcessCouponInfo = null;
                                    //遍历行李券，将产品信息赋给券
                                    for(int i=0;i<availCouponList.size();i++){
                                        AvailCoupon availCoupon = availCouponList.get(i);
                                        if(availCoupon.getCouponPrice() != 0){
                                            for(BaggageExcess baggageExcess:baggageExcessList){
                                                if(baggageExcess.getOverweight() != 0 && baggageExcess.getOverweight() == availCoupon.getCouponPrice()){
                                                    baggageExcessCouponInfo = new BaggageExcessCouponInfo();
                                                    Coupon coupon =  CouponUtil.initCouponStyle(availCoupon.getCouponSource(), availCoupon.getCouponType(), availCoupon.getCouponPrice(), availCoupon.getCouponRebate(), availCoupon.getCouponState(), availCoupon.getCouponActivityName());
                                                    availCoupon.setCouponName(coupon.getCouponName());
                                                    BeanUtils.copyNotNullProperties(availCoupon,baggageExcessCouponInfo);
                                                    //行李产品
                                                    baggageExcessCouponInfo.setBaggageExcessInfo(baggageExcess);
                                                    baggageExcessCouponInfo.setDepAirport(baggageExcess.getDepAirport());
                                                    baggageExcessCouponInfo.setArrAirport(baggageExcess.getArrAirport());
                                                    baggageExcessCouponInfoList.add(baggageExcessCouponInfo);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    response.setBaggageExcessCouponList(baggageExcessCouponInfoList);
                                }else{
                                    response.setBaggageExcessCouponList(new ArrayList<>());
                                }
                            }else{
                                response.setBaggageExcessCouponList(new ArrayList<>());
                            }
                        }
                    }*/
                }
            } catch (Exception e) {
                log.error("查询逾重行李出错结果:{}，请求参数：{}", e, JsonUtil.objectToJson(queryBaggageExcessRequest));
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo(NETWORK_ERROR_MESSAGE);
                log.info("请求号:" + reqId + "，响应结果:" + JsonUtil.objectToJson(response));
                return response;
            }
        }
        log.info("请求号:" + reqId + "，响应结果:" + JsonUtil.objectToJson(response));
        return response;
    }
    /*
     * 逾重行李查询
     * @param
     * @return
     */

    private QueryBaggageExcessRequest CreateQueryBaggageExcessRequest(QueryBaggageExcessReq queryBaggageExcessReq) {
        String channelCode = queryBaggageExcessReq.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        List<QueryBaggageExcessSegmentInfo> segmentInfoList = new ArrayList<QueryBaggageExcessSegmentInfo>();
        for (BaggageExcessSegmentInfo info : queryBaggageExcessReq.getSegmentInfoList()) {
            QueryBaggageExcessSegmentInfo queryInfo = new QueryBaggageExcessSegmentInfo();
            queryInfo.setArrAirport(info.getArrAirport());
            queryInfo.setCabin(info.getCabin());
            queryInfo.setDepAirport(info.getDepAirport());
            queryInfo.setDepDateTime(info.getDepDateTime());
            queryInfo.setArrAirName(info.getArrCityName());
            queryInfo.setDepAirName(info.getDepCityName());
            queryInfo.setFlightNo(info.getFlightNo());
            queryInfo.setSegNO(info.getSegNO());
            queryInfo.setArrAirport(info.getArrAirport());
            queryInfo.setDepAirport(info.getDepAirport());
            segmentInfoList.add(queryInfo);
        }
        QueryBaggageExcessRequest queryBaggageExcessRequest = new QueryBaggageExcessRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                segmentInfoList
        );
        return queryBaggageExcessRequest;
    }

    /**
     * 主题卡集合
     *
     * @param themeStr
     * @return
     */
    public static Map<String, ThemeCoupon> toThemeModelMap(String themeStr) {
        try {
            Type type = new TypeToken<Map<String, ThemeCoupon>>() {
            }.getType();
            return (Map<String, ThemeCoupon>) JsonUtil.jsonToMap(themeStr, type);
        } catch (Exception e) {
            return null;
        }
    }

}
