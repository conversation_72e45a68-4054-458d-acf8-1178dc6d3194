package com.juneyaoair.mobile.handler.service;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.baseclass.lowCarbon.LowCarbonResult;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.external.service.LowCarbonService;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.MdcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/30 18:38
 */
@Slf4j
@Service
public class FlightAggrService {
    @Autowired
    private LowCarbonService lowCarbonService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService redisService;

    public Set<String> queryLowCarbon(ClientInfo clientInfo, String flightDate, String depAirportCode, String arrAirportCode) {
        String key = RedisKeyConfig.createLowCarbonKey(flightDate);
        Map<String, String> strMap = new HashMap<>();
        try {
            if (redisService.existKey(key)) {
                Object obj = redisService.getHashData(key, depAirportCode + arrAirportCode);
                if (obj != null) {
                    return JsonUtil.fromJson(obj.toString(), new TypeToken<Set<String>>() {
                    });
                }
            } else {
                List<LowCarbonResult.FlightInfo> flightInfoList = lowCarbonService.queryLowCarbon(clientInfo, flightDate);
                Map<String, Set<String>> map = new HashMap<>();
                if (CollectionUtils.isNotEmpty(flightInfoList)) {
                    //进行机场分组处理
                    map = flightInfoList.stream()
                            .filter(Objects::nonNull)
                            .filter(f -> StringUtils.isNotBlank(f.getDepAirportCode()))
                            .filter(f -> StringUtils.isNotBlank(f.getArrAirportCode()))
                            // 新增flatMap展开航班号列表
                            .flatMap(flightInfo -> flightInfo.getFlightNoList().stream()
                                    .map(flightNo -> new AbstractMap.SimpleEntry<>(
                                            flightInfo.getDepAirportCode() + flightInfo.getArrAirportCode(),
                                            flightNo
                                    )))
                            .collect(Collectors.groupingBy(
                                    Map.Entry::getKey,
                                    Collectors.mapping(Map.Entry::getValue, Collectors.toSet())
                            ));
                    Map<String, String> finalStrMap = strMap;
                    map.forEach((k, v) -> {
                        finalStrMap.put(k, JsonUtil.objectToJson(v));
                    });
                }
                //将查询结果缓存至redis中,不关心是否有数据都要缓存
                saveCache(key,strMap);
                Set<String> set = map.get(depAirportCode + arrAirportCode);
                if (set == null) {
                    set = new HashSet<>();
                }
                return set;
            }
        } catch (Exception e) {
            log.error("{}:餐食标签处理异常:", MdcUtils.getRequestId(), e);
            //异常情况也进行缓存
            saveCache(key,strMap);
        }
        return new HashSet<>();
    }

    private void saveCache(String key,Map<String, String> strMap){
        //将查询结果缓存至redis中,不关心是否有数据都要缓存
        if(MapUtils.isEmpty(strMap)){
            strMap = new HashMap<>();
            strMap.put("NONE",JsonUtil.objectToJson(new HashSet<>()));
        }
        redisService.setHashData(key, strMap, 60 * 10L);
    }
}
