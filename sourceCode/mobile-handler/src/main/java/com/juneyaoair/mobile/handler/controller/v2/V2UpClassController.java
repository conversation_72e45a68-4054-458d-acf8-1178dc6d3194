package com.juneyaoair.mobile.handler.controller.v2;

import com.alibaba.fastjson.JSONObject;
import com.geetest.geeguard.sdk.GeetestLib;
import com.google.common.collect.Lists;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.ContactTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.appenum.ticket.TicketQueryTypeEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.newcoupon.req.protocol.OrderCouponDto;
import com.juneyaoair.baseclass.prepayment.request.QueryCouponInfoRequest;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.booking.CabinFare;
import com.juneyaoair.baseclass.request.booking.PassengerInfo;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.baseclass.response.booking.TicketBookingResp;
import com.juneyaoair.baseclass.response.coupons.AvailCoupon;
import com.juneyaoair.baseclass.ticket.resp.PassengerInfoDto;
import com.juneyaoair.baseclass.unlimit.UpgradeCardV2Config;
import com.juneyaoair.baseclass.upclass.AvailbleCabinInfo;
import com.juneyaoair.baseclass.upclass.UpFlightInfo;
import com.juneyaoair.baseclass.upclass.UpPriceSearch;
import com.juneyaoair.baseclass.upclass.UpclassInfoSearch;
import com.juneyaoair.baseclass.upclass.common.FlightInfo;
import com.juneyaoair.baseclass.upclass.common.PassSegInfo;
import com.juneyaoair.baseclass.upclass.common.SegmentShow;
import com.juneyaoair.baseclass.upclass.request.IUpClassFlightInfo;
import com.juneyaoair.baseclass.upclass.request.UpClassBookOrderReq;
import com.juneyaoair.baseclass.upclass.request.UpClassOrderReq;
import com.juneyaoair.baseclass.upclass.response.UpTicketInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmRequestConvert;
import com.juneyaoair.mobile.handler.controller.util.IdentityInfoUtil;
import com.juneyaoair.mobile.handler.controller.v2.util.CabinUpObjectConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.controller.v2.util.RightCouponConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.service.bean.country.TCountryDTO;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.VerifyConsumePasswdResponseForClient;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.av.comm.Fare;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyV2BindRecord;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberBasicInfoSoaModel;
import com.juneyaoair.thirdentity.member.response.MemberContactSoaModel;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.booking.PtTicketBookingReq;
import com.juneyaoair.thirdentity.request.booking.PtTicketOrderInfo;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.IdentityInfo;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.speedRefund.CabinNumInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.thirdentity.salecoupon.request.PtCouponProductGetRequestDto;
import com.juneyaoair.thirdentity.salecoupon.response.PtCouponProductGetResponseDto;
import com.juneyaoair.thirdentity.ticket.request.PtTicketDigestRequestDto;
import com.juneyaoair.thirdentity.ticket.response.PtDetrDigestInfoDto;
import com.juneyaoair.thirdentity.ticket.response.PtTicketListInfoResponse;
import com.juneyaoair.thirdentity.upclass.Segment;
import com.juneyaoair.thirdentity.upclass.UpgradeCabinFare;
import com.juneyaoair.thirdentity.upclass.request.PtTicketUpgradeFeeInfo;
import com.juneyaoair.thirdentity.upclass.request.PtTicketUpgradeFeeRequest;
import com.juneyaoair.thirdentity.upclass.request.TicketUpgradeCabinInfoDto;
import com.juneyaoair.thirdentity.upclass.request.TicketUpgradeCabinInfoRequest;
import com.juneyaoair.thirdentity.upclass.response.PtTicketUpgradeFeeResponse;
import com.juneyaoair.thirdentity.upclass.response.TicketUpgradeCabinInfoResponse;
import com.juneyaoair.thirdentity.upclass.response.UpAVFlightInfo;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/11  14:15.
 */
@RequestMapping("/v2/up")
@RestController
@Api(value = "V2UpClassController", description = "升舱服务（JAVA接口）")
public class V2UpClassController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private CrmWSClient crmClient;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private OrderManage orderManage;
    @Autowired
    private IUnlimitedFlyService unlimitedFlyService;
    @Autowired
    private IUnlimitCouponService unlimitCouponService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private GeetestService geetestService;

    @Autowired
    private IThemeCardService themeCardService;

    private static final String[] SUPPORT_UP_PASSTYPE = {CommonBaseConstants.IBE_PASSENGER_TYPE_ADT, CommonBaseConstants.IBE_PASSENGER_TYPE_CHD};


    @InterfaceLog
    @ApiOperation(value = "查询升舱信息【支持往返】", notes = "查询升舱信息【支持往返】")
    @RequestMapping(value = "/queryUpTicketInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<UpTicketInfo>> queryUpTicketInfo(@RequestBody @Validated BaseReq<UpclassInfoSearch> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        UpclassInfoSearch upclassInfo = req.getRequest();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(upclassInfo.getFfpId(), upclassInfo.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //不使用极验验证操作的
        List<String> channelList = Arrays.asList(ChannelCodeEnum.WXAPP.getChannelCode()
                , ChannelCodeEnum.MP_ALIPAY.getChannelCode()
        );
        boolean isSign = StringUtils.isNotBlank(upclassInfo.getSign());
        if (!channelList.contains(headChannelCode) && !isSign) {
            GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
            HashMap<String, String> param = new HashMap<>();
            param.put("user_id", ip); //网站用户id  设备号
            param.put("client_type", upclassInfo.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
            param.put("ip_address", ip); //传输用户请求验证时所携带的IP
            Geetest geetest = new Geetest(upclassInfo.getGeetest_challenge(), upclassInfo.getGeetest_validate(), upclassInfo.getGeetest_seccode());
            geetestService.validateMd5(gtSdk, geetest, param);
        }
        String sign = EncoderHandler.encodeBySHA1(upclassInfo.getTicketNo() + upclassInfo.getFlightNo() + upclassInfo.getFlightDate() + HandlerConstants.DEFAULT_TOKEN);
        if (isSign && !sign.equals(upclassInfo.getSign())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }
        // 升舱卡处理
        if (upclassInfo.isUpClassCard()) {
            List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = this.unlimitedFlyService.listUpClassCardV2BindRecord(req.getChannelCode(), req.getRequest().getFfpId(), ip, req.getRequest().getFfpCardNo());
            if (CollectionUtils.isEmpty(unlimitedFlyV2BindRecords)) {
                resp.setResultCode(WSEnum.UPCLASSCARD_NOT_PURCHASED.getResultCode());
                resp.setResultInfo(WSEnum.UPCLASSCARD_NOT_PURCHASED.getResultInfo());
                return resp;
            }
            if (unlimitedFlyV2BindRecords.stream().noneMatch(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus()))) {
                resp.setResultCode(WSEnum.UPCLASSCARD_UNBIND.getResultCode());
                resp.setResultInfo(WSEnum.UPCLASSCARD_UNBIND.getResultInfo());
                return resp;
            }
        }
        String passName = upclassInfo.getPassName().toUpperCase();
        JSONObject tags = new JSONObject();
        tags.put("IP", ip);
        tags.put("FfpCardNo", upclassInfo.getFfpCardNo());
        tags.put("ChannelCode", headChannelCode);
        MetricLogUtil.saveMetricLog("升舱-客票提取", tags, new BigDecimal(1));
        TicketListInfoResponse ticketListInfoResponse = queryTicket(req.getChannelCode(), upclassInfo.getTicketNo(), passName, ip);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ticketListInfoResponse.getErrorInfo());
            return resp;
        }
        if (CollectionUtils.isEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("客票信息有误，请先确认行程信息");
            return resp;
        }
        List<PtIBETicketInfo> ptIBETicketInfoList = ticketListInfoResponse.getIBETicketInfoList();
        //用作判断值机过滤
        List<PtIBETicketInfo> ptIBETicketInfoListWithCheckedIn = ticketListInfoResponse.getIBETicketInfoList();
        //保留可用OPEN FOR USE的客票
        ptIBETicketInfoList = ptIBETicketInfoList.stream()
                .filter(this::checkTicketStatus)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ptIBETicketInfoList)) {
            //判断机票状态是否为值机装填，如果是则返回取消值机信息
            ptIBETicketInfoListWithCheckedIn = ptIBETicketInfoListWithCheckedIn.stream()
                    .filter(this::isCheckedInStatus)
                    .collect(Collectors.toList());
            //不为空，代表有值机的，提示取消值机
            if (!StringUtil.isNullOrEmpty(ptIBETicketInfoListWithCheckedIn)) {
                resp.setResultCode(WSEnum.UPCLASSCARD_CHECKED_IN.getResultCode());
                resp.setResultInfo(WSEnum.UPCLASSCARD_CHECKED_IN.getResultInfo());
                return resp;
            }
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("抱歉，客票状态异常，请联系95520");
            return resp;
        }


        List<PtIBETicketInfo> filterList = new ArrayList<>();
        //姓名过滤
        for (PtIBETicketInfo ptIBETicketInfo : ptIBETicketInfoList) {
            String patternStr = passName + PatternCommon.PASS_NAME_REGEX;//正则表达式
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
            if (matcher.matches()) {
                filterList.add(ptIBETicketInfo);
            }
        }
        if (StringUtil.isNullOrEmpty(filterList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("客票信息有误，请先确认行程信息");
            return resp;
        }
        //行程匹配检验
        if (StringUtils.isAnyBlank(upclassInfo.getFlightNo())) {
            //if (StringUtils.isBlank(upclassInfo.getFlightNo())) {
            throw new IllegalArgumentException("服务升级中");
        }
        boolean check = false;
        AtomicReference<String> depCity= new AtomicReference<>("");
        AtomicReference<String> arrCity= new AtomicReference<>("");
        for (PtIBETicketInfo ptIBETicketInfo : filterList) {
            check = ptIBETicketInfo.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> {
                if (upclassInfo.getFlightNo().equals(ptSegmentInfo.getFlightNo())
                ) {
                    AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode());
                    AirPortInfoDto depAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode());
                    depCity.set(depAirPort.getCityCode());
                    arrCity.set(arrAirPort.getCityCode());
                    return true;
                } else {
                    return false;
                }
            });
            if (check) {
                break;
            }
        }
        if (!check) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("暂未查询到可线上升舱的客票，请致电吉祥航空客服热线95520咨询");
            return resp;
        }
        //全部为无陪乘客
        boolean checkPass = filterList.stream().allMatch(ticket -> CommonBaseConstants.IBE_PASSENGER_TYPE_UNACCOMPANIED.equals(ticket.getPassengerType()));
        if (checkPass) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("无陪儿童暂不支持线上升舱");
            return resp;
        }
        checkPass = filterList.stream().allMatch(ticket -> CommonBaseConstants.IBE_PASSENGER_TYPE_INF.equals(ticket.getPassengerType()));
        if (checkPass) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("婴儿票暂不支持线上升舱");
            return resp;
        }

        checkPass = filterList.stream().allMatch(ticket -> StringUtils.isNotBlank(ticket.getTourCode()) && ticket.getTourCode().contains("GROUP"));
        if (checkPass) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您的团队客票所预定的航班暂时不支持升舱");
            return resp;
        }
        List<PtIBETicketInfo> groupTicketInfoList = filterList.stream()
                .filter(ticket -> ticket.getSegmentInfoList()
                        .stream()
                        .anyMatch(ptSegmentInfo ->!StringUtil.isNullOrEmpty(ptSegmentInfo.getRate())&& ptSegmentInfo.getRate().indexOf("GV") > -1)
        ).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(groupTicketInfoList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您的团队客票所预定的航班暂时不支持升舱");
            return resp;
        }

        //目前支持升舱的为成人 儿童 且成人不能携带婴儿
        List<String> supportUpPassTypeList = Arrays.asList(SUPPORT_UP_PASSTYPE);
        filterList = filterList.stream().filter(ticket -> supportUpPassTypeList.contains(ticket.getPassengerType())
                && StringUtils.isBlank(ticket.getTicketNoInf())).collect(Collectors.toList());
        if (StringUtil.isNullOrEmpty(filterList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("婴儿票或携带婴儿的成人票暂不支持线上升舱");
            return resp;
        }
        //IT票暂不支持升舱
        boolean itTicket = filterList.stream().allMatch(ticket -> ticket.isIT());
        if (itTicket) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("此客票暂不支持线上升舱，请联系95520！");
            return resp;
        } else {
            filterList = filterList.stream().filter(ticket -> !ticket.isIT()).collect(Collectors.toList());
        }
        //非人民币支付暂不支持升舱
        boolean checkCurrency = filterList.stream().allMatch(ticket -> !HandlerConstants.CURRENCY_CODE.equals(ticket.getCurrencyType()));
        if (checkCurrency) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("非人民币支付暂不支持线上升舱，请联系95520！");
            return resp;
        } else {
            filterList = filterList.stream().filter(ticket -> HandlerConstants.CURRENCY_CODE.equals(ticket.getCurrencyType())).collect(Collectors.toList());
        }

        // 检查是否使用宠物票
        List<PtIBETicketInfo> ptYpetTicketInfoList = filterList.stream()
                .filter(ticket -> StringUtils.isNotBlank(ticket.getSigningInfo()) &&
                        (ticket.getSigningInfo().equals("YPET") || ticket.getSigningInfo().equals("JPET")))
                .collect(Collectors.toList());
        if (!StringUtil.isNullOrEmpty(ptYpetTicketInfoList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您的客票为宠物票，不支持升舱，如有疑问请致电95520");
            return resp;
        }
        List<PtIBETicketInfo> themTicketInfoList = filterList.stream().filter(ticket ->
                ticket.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> "THEM".equals(ptSegmentInfo.getFareBasic()))
        ).collect(Collectors.toList());
        //多次卡不支持升舱
        if (CollectionUtils.isNotEmpty(themTicketInfoList) && themTicketInfoList.size() == 1) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您的客票为多次卡兑换，不支持升舱，如有疑问请致电95520");
            return resp;
        }


        List<OrderCouponDto> orderCouponDtoList = themeCardService.queryTicketRedeem(upclassInfo.getTicketNo(), depCity.get(),arrCity.get());
        if (CollectionUtils.isNotEmpty(orderCouponDtoList)) {
            //临期次卡
            List<OrderCouponDto> expiringCard = orderCouponDtoList.stream().filter(orderCouponDto -> VoucherTypesEnum.EXPIRING_CARD.getCode().equals(orderCouponDto.getCouponSource())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(expiringCard)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您的客票为临期次卡，不支持升舱，如有疑问请致电95520");
                return resp;
            }
        }

        List<UpTicketInfo> upTicketInfoList = dealUpTicketList(filterList, handConfig, upclassInfo, req.getChannelCode(),
                ip, supportUpgradeCard(headChannelCode, NumberUtils.toInt(req.getVersionCode())));
        if (CollectionUtils.isEmpty(upTicketInfoList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("客票信息有误，请先确认行程信息");
            return resp;
        }
        //升舱券处理
        upTicketInfoList = upTicketInfoList.stream().map(upTicketInfo -> queryUpCoupon(upTicketInfo, req.getChannelCode(), ip, upclassInfo.getFfpId(), upclassInfo.getFfpCardNo())).collect(Collectors.toList());
        //处理预付费行李,选座,是否购买校验
        String userNo = this.getChannelInfo(req.getChannelCode(), "10");
        handlerCouponTicketInfoList(ip, req.getChannelCode(), userNo, upTicketInfoList);
        //处理客票隐私信息
        handPrivacyInfo(upTicketInfoList);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(upTicketInfoList);
        return resp;
    }

    /**
     * 处理客票隐私信息
     *
     * @param upTicketInfoList
     */
    private void handPrivacyInfo(List<UpTicketInfo> upTicketInfoList) {
        upTicketInfoList.parallelStream().forEach(upTicketInfo -> {
                    PassengerInfo passengerInfo = upTicketInfo.getPassengerInfoList().get(0);
                    String certNo = passengerInfo.getCertNo();
                    passengerInfo.setCertNo(SensitiveInfoHider.hideMiddleSensitiveInfo(certNo));
                    //客票对应的敏感信息缓存10分钟
                    apiRedisService.putData(RedisKeyConfig.createUpIdInfo(upTicketInfo.getTicketNo()), certNo, 60 * 10L);
                }
        );
    }

    private void handlerCouponTicketInfoList(String ip, String channelCode, String userNo, List<UpTicketInfo> ticketInfoList) {
        QueryCouponInfoRequest queryCouponInfoRequest = new QueryCouponInfoRequest();
        queryCouponInfoRequest.setRequestIp(ip);
        queryCouponInfoRequest.setChannelCode(channelCode);
        queryCouponInfoRequest.setUserNo(userNo);
        queryCouponInfoRequest.setVersion("10");
        for (UpTicketInfo upTicketInfo : ticketInfoList) {
            handlerCoupon(queryCouponInfoRequest, upTicketInfo);
        }
    }

    private void handlerCoupon(QueryCouponInfoRequest queryCouponInfoRequest, UpTicketInfo ticketInfo) {
        if (StringUtil.isNullOrEmpty(ticketInfo.getTicketNo())) {
            return;
        }
        queryCouponInfoRequest.setTicketNoList(Collections.singletonList(ticketInfo.getTicketNo()));
        String payCouponDesc = orderManage.isPayCoupon(queryCouponInfoRequest);
        if (!StringUtil.isNullOrEmpty(payCouponDesc)) {
            payCouponDesc = payCouponDesc + "，需要单独申请退款，确认继续升舱吗？";
            ticketInfo.setPayCouponInfoDesc(payCouponDesc);
        }
    }


    /**
     * 判断使用升舱卡版本
     *
     * @param headChannelCode
     * @param versionCode
     * @return 0 : 不可使用升舱卡，1：可用升舱卡2020；2：可用升舱卡2021；
     */
    public static int supportUpgradeCard(String headChannelCode, int versionCode) {
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
            if (versionCode >= 62300) {
                return 2;
            } else if (versionCode >= 59600) {
                return 1;
            }
            return 0;
        } else if (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode)) {
            return 2;
        } else {
            return 0;
        }
    }

    //客票状态过滤
    private boolean checkTicketStatus(PtIBETicketInfo ptIBETicketInfo) {
        return ptIBETicketInfo.getSegmentInfoList().stream()
                .anyMatch(ptSegmentInfo -> HandlerConstants.OPEN_FOR_USE.equals(ptSegmentInfo.getTicketStatus()));
    }

    /**
     * 判断是否已值机
     *
     * @param ptIBETicketInfo
     * @return true 已值机，false 未值机
     */
    private boolean isCheckedInStatus(PtIBETicketInfo ptIBETicketInfo) {
        return ptIBETicketInfo.getSegmentInfoList().stream()
                .anyMatch(ptSegmentInfo -> HandlerConstants.CHECKED_IN.equals(ptSegmentInfo.getTicketStatus()));
    }

    @ApiOperation(value = "查询升舱差价", notes = "查询升舱差价")
    @RequestMapping(value = "/queryUpPrice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<Map<String, Object>> queryUpPrice(@RequestBody @Validated BaseReq<UpPriceSearch> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        UpPriceSearch upPriceSearch = req.getRequest();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(upPriceSearch.getFfpId(), upPriceSearch.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //信息验签
        UpFlightInfo upFlightInfo = upPriceSearch.getUpPriceSegList().get(0);
        String sign = EncoderHandler.encodeBySHA1(upFlightInfo.createSinaParam() + HandlerConstants.DEFAULT_TOKEN);
        if (!sign.equals(upFlightInfo.getSign())) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("抱歉，航班信息异常，请联系95520");
            return resp;
        }
        try {
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            // 港澳台地区
            boolean HKMCTWRegion = false;
            if (HandlerConstants.TRIP_TYPE_I.equals(upFlightInfo.getInterFlag()) &&
                    (handConfig.getHKMCTWRegions().contains(upFlightInfo.getDeptCityCode()) || handConfig.getHKMCTWRegions().contains(upFlightInfo.getArrCityCode()))) {
                HKMCTWRegion = true;
            }
            // 升舱旅客只有一位
            PassengerInfo passengerInfo = upPriceSearch.getUpPriceSegList().get(0).getPassengerInfoList().get(0);
            // 是否使用无限升舱卡
            boolean unlimitedUpClassAvailable = false;
            //缓存五分钟，是否可用2021版无限升舱卡
            String key = RedisKeyConfig.UNLIMITED_UPGRADE + upPriceSearch.getFfpId() + ":" + upPriceSearch.getTicketNo().replaceAll("-", "");
            String unlimitedUpFlag = apiRedisService.getData(key);
            if (StringUtils.isNotBlank(unlimitedUpFlag) && "Y".equals(unlimitedUpFlag)) {
                unlimitedUpClassAvailable = true;
            } else {
                int supportUnlimitCard = supportUpgradeCard(headChannelCode, NumberUtils.toInt(req.getVersionCode()));
                if (supportUnlimitCard > 0 && !handConfig.getFreeTicketCabin().equals(upFlightInfo.getCabin())) {
                    UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                    Date flightTime = DateUtils.toDate(upFlightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                    // 2021版升舱卡 只能升国内舱位
                    if (supportUnlimitCard >= 2) {
                        boolean upClassCardAvailable = false;
                        try {
                            upClassCardAvailable = orderManage.unlimitedUpClassAvailable(upPriceSearch.getFfpId(),
                                    upPriceSearch.getFfpCardNo(), req.getChannelCode(), this.getChannelInfo(req.getChannelCode(), "10"),
                                    this.getClientPwd(req.getChannelCode()), passengerInfo.getCertType(), passengerInfo.getCertNo(), ip, HKMCTWRegion, passengerInfo.getPassengerName());

                        } catch (Exception e) {
                            log.error("查询无限升舱卡出现异常", e);
                        }
                        if (upClassCardAvailable) {
                            // 在升舱卡时间范围内
                            Date unusableTimeBegin = DateUtils.toDate(upgradeCardV2Config.getUnusableTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                            Date unusableTimeEnd = DateUtils.toDate(upgradeCardV2Config.getUnusableTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                            // 不在春运期间可用升舱卡
                            if (null != unusableTimeBegin && null != unusableTimeEnd && (flightTime.getTime() <= unusableTimeBegin.getTime()
                                    || flightTime.getTime() >= unusableTimeEnd.getTime())) {
                                unlimitedUpClassAvailable = true;
                            }
                        }
                    }
                }
            }
            // 国际票查询指定舱位
            if (useInterUpClass(request, upFlightInfo.getInterFlag())) {
                TicketUpgradeCabinInfoRequest cabinInfoRequest = this.createUpgradeCabinRequest(upPriceSearch,
                        req.getChannelCode(), unlimitedUpClassAvailable ? handConfig.getUnlimitedUpClassCabin() : handConfig.getSupportUpCouponCabinI());
                HttpResult httpResult = this.doPostClient(cabinInfoRequest, HandlerConstants.URL_FARE_API + HandlerConstants.BOOK_QUERY_UPGRADE_FLIGHT_INFO, headMap);
                if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
                    throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
                }
                TicketUpgradeCabinInfoResponse response = JsonUtil.fromJson(httpResult.getResponse(), TicketUpgradeCabinInfoResponse.class);
                if (UnifiedOrderResultEnum.CHECK_5026.getResultCode().equals(response.getResultCode())) {
                    throw new OperationFailedException("抱歉，暂无可升舱座位");
                } else if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
                    throw new OperationFailedException(response.getErrorInfo());
                }
                AtomicBoolean unlimitedUpClass = new AtomicBoolean(unlimitedUpClassAvailable);
                HashMap<String, Object> resultMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(response.getTicketUpgradeCabinInfoDtos())) {
                    List<AvailbleCabinInfo> availableCabinInfoList = this.dealRClass(response.getTicketUpgradeCabinInfoDtos(),
                            upFlightInfo, upPriceSearch.getUpPriceSegList().get(0).getPassengerInfoList().get(0).getPassengerType(), unlimitedUpClass);
                    resultMap.put("aClassInfoList", availableCabinInfoList);
                    resultMap.put("unlimitedUpClass", unlimitedUpClass.get());
                    String warnMessage = this.warnMessage(req.getChannelCode(), ip, req.getRequest().getTicketNo());
                    if (StringUtils.isNotBlank(warnMessage)) {
                        resultMap.put("warnMessage", warnMessage);
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setResultInfo("暂无可升舱的座位");
                }
                resp.setObjData(resultMap);
            } else {
                //查询升舱差价
                PtTicketUpgradeFeeRequest ptTicketUpgradeFeeRequest = createTicketUpFee(upPriceSearch, req.getChannelCode());
                String url = HandlerConstants.URL_FARE_API + HandlerConstants.CAL_TICKET_UPGRADE_FEE;
                HttpResult httpResult = this.doPostClient(ptTicketUpgradeFeeRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                if (httpResult.isResult()) {
                    if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("升舱费用查询返回结果为空！");
                    } else {
                        PtTicketUpgradeFeeResponse ptTicketUpgradeFeeResponse;
                        try {
                            ptTicketUpgradeFeeResponse = (PtTicketUpgradeFeeResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtTicketUpgradeFeeResponse.class);
                        } catch (Exception e) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("小吉开小差啦，请联系95520或稍后再试");
                            return resp;
                        }
                        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptTicketUpgradeFeeResponse.getResultCode())) {
                            //航班信息补充处理
                            completeFlightInfo(ptTicketUpgradeFeeRequest, ptTicketUpgradeFeeResponse, upFlightInfo);
                            Map<String, Object> resultMap = new HashMap<>();
                            AtomicBoolean unlimitedUpClass = new AtomicBoolean(unlimitedUpClassAvailable);
                            List<AvailbleCabinInfo> availableCabinInfoList = dealAClass(ptTicketUpgradeFeeResponse, upFlightInfo,
                                    ptTicketUpgradeFeeRequest.getTicketUpgradeFeeInfo().get(0).getPassengerType(), unlimitedUpClass);
                            //头等舱位信息
                            resultMap.put("aClassInfoList", availableCabinInfoList);
                            resultMap.put("unlimitedUpClass", unlimitedUpClass.get());
                            String warnMessage = this.warnMessage(req.getChannelCode(), ip, req.getRequest().getTicketNo());
                            if (StringUtils.isNotBlank(warnMessage)) {
                                resultMap.put("warnMessage", warnMessage);
                            }
                            if (StringUtil.isNullOrEmpty(availableCabinInfoList)) {
                                resp.setObjData(resultMap);
                                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                                resp.setResultInfo("暂无可升舱的座位");
                                return resp;
                            }
                            resp.setObjData(resultMap);
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(ptTicketUpgradeFeeResponse.getErrorInfo());
                        }
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(httpResult.getResponse());
                }
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    /**
     * 查询下单提示信息
     *
     * @param channelCode
     * @param ip
     * @param ticketNo
     * @return
     */
    private String warnMessage(String channelCode, String ip, String ticketNo) {
        StringBuilder message = new StringBuilder();
        List<PassengerInfoDto> passengerInfoDtos = orderService
                .queryPassengersByTicketNo(channelCode, this.getChannelInfo(channelCode, "10"), ip, ticketNo);
        if (CollectionUtils.isNotEmpty(passengerInfoDtos)) {
            //升舱的旅客
            Optional<PassengerInfoDto> upPassenger = passengerInfoDtos.stream().filter(passengerInfoDto ->
                    ticketNo.equals(passengerInfoDto.getTicketNo())).findFirst();
            upPassenger.ifPresent(passengerInfoDto -> {
                PassengerTypeEnum passengerTypeEnum = PassengerTypeEnum.valueOf(passengerInfoDto.getPassengerType());
                if (PassengerTypeEnum.CHD.equals(passengerTypeEnum)) {
                    // 儿童升舱，提示信息
                    message.append("此订单包含儿童旅客，需成人陪同，成人或儿童单独升舱可能导致儿童无法出行！");
                } else if (PassengerTypeEnum.ADT.equals(passengerTypeEnum)
                        && passengerInfoDtos.stream().anyMatch(passenger -> !PassengerTypeEnum.ADT.getPassType().equals(passenger.getPassengerType()))) {
                    // 成人升舱，若订单中有儿童或者婴儿，提示信息
                    message.append("此订单包含儿童旅客，需成人陪同，成人或儿童单独升舱可能导致儿童无法出行！");
                }
                // 婴儿不可升舱
            });
        }
        return message.toString();
    }

    @ApiOperation(value = "生成升舱订单【支持往返】", notes = "生成升舱订单【支持往返】")
    @RequestMapping(value = "/bookUpClassOrderV2", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp bookUpClassOrderV2(@RequestBody @Validated BaseReq<UpClassOrderReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        UpClassOrderReq upClassBookOrderReq = req.getRequest();
        if (upClassBookOrderReq == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("缺少请求参数");
            return resp;
        }
        String info = apiRedisService.getData(RedisKeyConfig.createUpIdInfo(upClassBookOrderReq.getTicketNo()));
        if (StringUtils.isBlank(info)) {
            resp.setResultCode(WSEnum.OPERATION_TIMEOUT_50016.getResultCode());
            resp.setResultInfo("操作超时，请返回上一层级重新查询");
            return resp;
        }
        List<PassengerInfo> passengerInfoList = upClassBookOrderReq.getUpTicketInfo().getPassengerInfoList();
        for (PassengerInfo passengerInfo : passengerInfoList) {
            passengerInfo.setCertNo(info);
        }
        if (HandlerConstants.FLIGHT_INTER_D.equals(upClassBookOrderReq.getUpTicketInfo().getInterFlag())
                && CollectionUtils.isEmpty(upClassBookOrderReq.getUpAVFlightInfoList())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("升舱航段信息不可为空");
            return resp;
        }
        if (HandlerConstants.FLIGHT_INTER_I.equals(upClassBookOrderReq.getUpTicketInfo().getInterFlag())
                && CollectionUtils.isEmpty(upClassBookOrderReq.getIUpClassFlightInfoList())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("升舱航段信息不可为空");
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(upClassBookOrderReq.getFfpId(), upClassBookOrderReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String channelCode = req.getChannelCode();
        UpTicketInfo upTicketInfo = upClassBookOrderReq.getUpTicketInfo();
        boolean upFlag = upTicketInfo.getSegmentShowList().stream().anyMatch(segmentShow -> segmentShow.isSelectUpFlag());
        if (!upFlag) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("确认升舱段！");
            return resp;
        }
        //检验升舱券是否重复使用
        Map<String, Long> codeMap = upTicketInfo.getSegmentShowList().stream().collect(Collectors.groupingBy(segmentShow -> groupByCode(segmentShow), Collectors.counting()));
        if (codeMap != null && !codeMap.isEmpty()) {
            for (Map.Entry<String, Long> entry : codeMap.entrySet()) {
                if (!StringUtil.isNullOrEmpty(entry.getKey()) && entry.getValue() > 1) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(entry.getKey() + "不可重复使用！");
                    return resp;
                }
            }
        }
        //国际国内使用升舱券的舱位
        String supportCoupon = "";
        BigDecimal outCouponOrderAmount = BigDecimal.ZERO;
        if (HandlerConstants.TRIP_TYPE_I.equals(upTicketInfo.getInterFlag())) {
            for (SegmentShow seg : upTicketInfo.getSegmentShowList()) {
                if (seg.isSelectUpFlag() && StringUtil.isNullOrEmpty(seg.getUpCouponCode())) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("国际票请联系95520升舱");
                    return resp;
                }
            }
        } else {
            //计算剔除升舱券后的费用
            outCouponOrderAmount = upClassBookOrderReq.getUpAVFlightInfoList().stream().map(upAVFlightInfo ->
                    sumOrderAmount(upAVFlightInfo, upTicketInfo.getSegmentShowList(), true)).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (UpAVFlightInfo upAVFlightInfo : upClassBookOrderReq.getUpAVFlightInfoList()) {
                for (SegmentShow seg : upTicketInfo.getSegmentShowList()) {
                    if (seg.isSelectUpFlag() && seg.getFlightNo().equals(upAVFlightInfo.getFlightNo())) {
                        List<UpgradeCabinFare> upgradeCabinFareList = upAVFlightInfo.getUpgradeCabinFareList();
                        if (StringUtil.isNullOrEmpty(upgradeCabinFareList)) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("小吉开小差啦，请联系95520或稍后再试");
                            return resp;
                        }
                        supportCoupon = seg.isUnlimitedUpClass() ? handConfig.getUnlimitedUpClassCabin() : handConfig.getSupportUpCouponCabin();
                        String upCabinCode = upAVFlightInfo.getUpgradeCabinFareList().get(0).getCabinCode();
                        if (supportCoupon.contains(upCabinCode)) {
                            if (StringUtil.isNullOrEmpty(seg.getUpCouponCode())) {
                                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                resp.setResultInfo("小吉开小差啦，请联系95520或稍后再试");
                                return resp;
                            }
                        } else {
                            if (!StringUtil.isNullOrEmpty(seg.getUpCouponCode())) {
                                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                                resp.setResultInfo("该舱位不支持使用升舱券" + upCabinCode);
                                return resp;
                            }
                        }
                    }
                }
            }
            //APP5.7以及M WEIXIN WXAPP走新的积分验证服务
            String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
            String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
            //积分 消费密码验证
            if (upClassBookOrderReq.getScore() > 0) {
                boolean newCheck = false;
                if (ChannelCodeEnum.MWEB.getChannelCode().equals(realChannelCode)) {
                    newCheck = true;
                }
                //小程序的暂不进行小额密码
                if (ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode)) {
                    newCheck = false;
                }
                if (ChannelCodeEnum.MOBILE.getChannelCode().equals(realChannelCode) && NumberUtil.stringToInt(versionCode) >= 57000) {
                    newCheck = true;
                }
                //积分金额不可大于升舱费用
                if (upClassBookOrderReq.getScore() > outCouponOrderAmount.doubleValue()) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("积分金额有误！");
                    return resp;
                }
                if (newCheck) {
                    BaseResp checkResp = orderService.checkFreeScoreLimit(upClassBookOrderReq.getFfpCardNo(), upClassBookOrderReq.getFfpId(),
                            channelCode, getChannelInfo(channelCode, "40"), upClassBookOrderReq.getScore(), upClassBookOrderReq.getSalePwd(), request);
                    if (!WSEnum.SUCCESS.getResultCode().equals(checkResp.getResultCode())) {
                        resp.setResultCode(checkResp.getResultCode());
                        resp.setResultInfo(checkResp.getResultInfo());
                        return resp;
                    }
                } else {
                    //消费密码
                    String patternStr = PatternCommon.SALE_P_W_D;
                    Pattern pattern = Pattern.compile(patternStr);
                    Matcher matcher = pattern.matcher(upClassBookOrderReq.getSalePwd());
                    if (!matcher.matches()) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("消费密码为六位数字");
                        return resp;
                    }
                    VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(Long.valueOf(upClassBookOrderReq.getFfpId()),
                            upClassBookOrderReq.getSalePwd(), channelCode, getClientPwd(channelCode));
                    if (!"S000".equals(clientResp.getMessageHeader().getErrorCode())) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(clientResp.getMessageHeader().getDescription());
                        return resp;
                    }
                }
            }
        }

        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                , MemberDetailRequestItemsEnum.CONTACTINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CrmRequestConvert.buildMemberDetailReq(upClassBookOrderReq.getFfpCardNo(),
                upClassBookOrderReq.getFfpId(), ip, channelCode, getClientPwd(channelCode), items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
        if (ptCRMResponse.getCode() != 0) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("获取用户信息异常！");
            return resp;
        }
        //封装升舱订单请求参数
        String userNo = this.getChannelInfo(req.getChannelCode(), "10");
        PtTicketBookingReq ptTicketBookingReq;
        String url;
        if (useInterUpClass(request, upClassBookOrderReq.getUpTicketInfo().getInterFlag())) {
            // 国际升舱订单
            ptTicketBookingReq = createIUpgradeOrderReqV2(req.getChannelCode(), userNo, upClassBookOrderReq, ptCRMResponse.getData(), outCouponOrderAmount.doubleValue());
            url = HandlerConstants.URL_FARE_API + HandlerConstants.BOOK_TICKET_INTER_UPGRADE;
        } else {
            // 国内升舱订单
            ptTicketBookingReq = createUpgradeOrderReqV2(req.getChannelCode(), userNo, upClassBookOrderReq, ptCRMResponse.getData(), outCouponOrderAmount.doubleValue());
            url = HandlerConstants.URL_FARE_API + HandlerConstants.BOOK_TICKET_UPGRADE;
        }
        ptTicketBookingReq.setOrderRequestIp(ip);
        ptTicketBookingReq.setCouponCheckFlag("new");
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = this.doPostClient(ptTicketBookingReq, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("小吉开小差啦，请联系95520或稍后再试");
            } else {
                TicketBookingResp ticketBookingResp;
                try {
                    ticketBookingResp = (TicketBookingResp) JsonUtil.jsonToBean(httpResult.getResponse(), TicketBookingResp.class);
                } catch (Exception e) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("小吉开小差啦，请联系95520或稍后再试");
                    log.error(COMMON_LOG_WITH_RESP_INFO, reqId, ip, httpResult.getResponse());
                    return resp;
                }
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketBookingResp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    Map<String, Object> orderMap = new HashMap<>();
                    orderMap.put("payState", false);
                    orderMap.put("orderNo", ticketBookingResp.getOrderNO());
                    orderMap.put("channelOrderNo", ticketBookingResp.getChannelOrderNo());
                    //如果是全积分或者升舱券抵扣则同步调用0元支付 6.1.0版本虚拟支付移动至统一订单侧
                    if ((upClassBookOrderReq.getScore() == outCouponOrderAmount.doubleValue()) || outCouponOrderAmount.doubleValue() == 0) {
                        orderMap.put("payState", true);
                    }
                    resp.setObjData(orderMap);
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ticketBookingResp.getErrorInfo());
                }
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(httpResult.getResponse());
        }
        return resp;
    }

    /**
     * 是否使用新国际升舱
     *
     * @param request
     * @return
     */
    private boolean useInterUpClass(HttpServletRequest request, String interFlag) {
        String realChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        int version = VersionNoUtil.toMVerInt(versionCode);
        boolean interUpClass = false;//是否使用新一套国际升舱
        if ((ChannelCodeEnum.MOBILE.getChannelCode().equals(realChannelCode) && version >= 58100)
                || (ChannelCodeEnum.MWEB.getChannelCode().equals(realChannelCode))) {
            interUpClass = true;
        }
        return interUpClass && HandlerConstants.FLIGHT_INTER_I.equals(interFlag);
    }

    //分组字段处理
    private String groupByCode(SegmentShow segmentShow) {
        if (segmentShow.isSelectUpFlag()) {
            if (StringUtil.isNullOrEmpty(segmentShow.getUpCouponCode()) || segmentShow.isUnlimitedUpClass()) {
                return "";
            } else {
                return segmentShow.getUpCouponCode();
            }
        } else {
            return "";
        }

    }

    /**
     * 计算升舱订单总价
     *
     * @param upAVFlightInfo
     * @param segmentShowList
     * @param outCouponPrice  是否排除优惠券抵金额
     * @return
     */
    private BigDecimal sumOrderAmount(UpAVFlightInfo upAVFlightInfo, List<SegmentShow> segmentShowList, boolean outCouponPrice) {
        BigDecimal total = new BigDecimal("0");
        for (SegmentShow segmentShow : segmentShowList) {
            if (segmentShow.isSelectUpFlag() && segmentShow.getFlightNo().equals(upAVFlightInfo.getFlightNo())) {
                BigDecimal cabinAmount = upAVFlightInfo.getUpgradeCabinFareList().stream().map(upgradeCabinFare -> sumCabinAmount(upgradeCabinFare)).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (StringUtil.isNullOrEmpty(segmentShow.getUpCouponCode())) {
                    total = total.add(cabinAmount);
                }
            }
        }
        return total;
    }

    //计算舱位的升舱费用  票面差额+手续费
    private BigDecimal sumCabinAmount(UpgradeCabinFare upgradeCabinFare) {
        return new BigDecimal(upgradeCabinFare.getPriceDiff() + upgradeCabinFare.getUpgradeFee() + "");
    }

    /**
     * 升舱客票结果转换
     *
     * @param filterList
     * @return
     */
    private List<UpTicketInfo> dealUpTicketList(List<PtIBETicketInfo> filterList, HandConfig handConfig, UpclassInfoSearch upclassInfoSearch, String channelCode, String ip, int supportUnlimitCard) {
        String ffpId = upclassInfoSearch.getFfpId();
        String ffpCardNo = upclassInfoSearch.getFfpCardNo();
        List<UpTicketInfo> upTicketInfoList = new ArrayList<>();
        String classSet = handConfig.getCabinClass();
        filterList.forEach(ibe -> {
            UpTicketInfo upTicketInfo = new UpTicketInfo();
            upTicketInfo.setUpAble(true);
            List<SegmentShow> segmentShowList = new ArrayList<>();
            List<PassengerInfo> passengerInfoList = ticketInfoToPass(ibe, localCacheService);
            //航班原始运价信息
            List<FlightInfo> flightInfoList = ticketInfoToFlight(ibe, passengerInfoList.get(0).getPassengerType());
            upTicketInfo.setTicketNo(ibe.getTicketNo());
            upTicketInfo.setInterFlag(ibe.getInterFlag());
            //起点与终点一致认为其是往返客票
            int segCount = ibe.getSegmentInfoList().size();
            upTicketInfo.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
            if (ibe.getOrgCity().equals(ibe.getDstCity())) {
                upTicketInfo.setRouteType(HandlerConstants.ROUTE_TYPE_RT);
            }
            upTicketInfo.setPassengerInfoList(passengerInfoList);
            upTicketInfo.setFlightInfoList(flightInfoList);
            AtomicBoolean containInter = new AtomicBoolean(false);
            AtomicBoolean containDomestic = new AtomicBoolean(false);
            AtomicInteger segmentSize = new AtomicInteger(0);
            //展示航段处理
            ibe.getSegmentInfoList().forEach(ptSegmentInfo -> {
                boolean international = false;// 是否是国际
                segmentSize.incrementAndGet();
                AirPortInfoDto deptAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode());
                AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode());
                if (HandlerConstants.TRIP_TYPE_I.equals(deptAirPort.getIsInternational()) || HandlerConstants.TRIP_TYPE_I.equals(arrAirPort.getIsInternational())) {
                    international = true;
                    containInter.set(true);
                } else {
                    containDomestic.set(true);
                }

                SegmentShow segmentShow = new SegmentShow();
                segmentShow.setSegNo(ptSegmentInfo.getSegmentIndex() - 1);
                BeanUtils.copyNotNullProperties(ptSegmentInfo, segmentShow);
                segmentShow.setPnrNo(ptSegmentInfo.getPnrNo());
                segmentShow.setIfUp(ptSegmentInfo.isOperation());
                segmentShow.setNotUpReason(ptSegmentInfo.getIsOperationReason());
                String deptDate = separateTime(ptSegmentInfo.getDepTime())[0];
                String arrDate = separateTime(ptSegmentInfo.getArrTime())[0];
                segmentShow.setFlightDate(deptDate);
                segmentShow.setFlightArrDate(arrDate);
                segmentShow.setFlightWeek(getWeekByDate(segmentShow.getFlightDate()));
                segmentShow.setFlightDirection(FlightDirection.GO.getCode());
                if (HandlerConstants.ROUTE_TYPE_RT.equals(upTicketInfo.getRouteType()) && ptSegmentInfo.getSegmentIndex() > (segCount / 2)) {
                    segmentShow.setFlightDirection(FlightDirection.BACK.getCode());
                }
                segmentShow.setDeptTime(separateTime(ptSegmentInfo.getDepTime())[1]);
                segmentShow.setArrTime(separateTime(ptSegmentInfo.getArrTime())[1]);
                segmentShow.setDeptAirPortCode(ptSegmentInfo.getDepAirportCode());
                segmentShow.setDeptAirPortName(deptAirPort.getAirPortName());
                segmentShow.setDeptCityCode(deptAirPort.getCityCode());
                segmentShow.setDeptCityName(deptAirPort.getCityName());
                segmentShow.setArrAirPortCode(ptSegmentInfo.getArrAirportCode());
                segmentShow.setArrAirPortName(arrAirPort != null ? arrAirPort.getAirPortName() : "");
                segmentShow.setArrCityCode(arrAirPort != null ? arrAirPort.getCityCode() : "");
                segmentShow.setArrCityName(arrAirPort != null ? arrAirPort.getCityName() : "");
                segmentShow.setDeptTerminal(ptSegmentInfo.getDepAirportTerminal());
                segmentShow.setArrTerminal(ptSegmentInfo.getArrAirportTerminal());
                // 港澳台地区
                boolean HKMCTWRegion = handConfig.getHKMCTWRegions().contains(segmentShow.getDeptAirPortCode())
                        || handConfig.getHKMCTWRegions().contains(segmentShow.getArrAirPortCode());
                PassengerInfo passengerInfo = upTicketInfo.getPassengerInfoList().get(0);
                boolean unlimitedUpClass = false;
                if (supportUnlimitCard > 0 && !handConfig.getFreeTicketCabin().equals(segmentShow.getCabin())) {
                    UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                    Date flightTime = DateUtils.toDate(segmentShow.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                    // 2021版升舱卡
                    // 团队票、积分全额抵扣机票、特殊服务客票不支持升舱卡升舱
//                    boolean ticketSupportUpgrade = !"Group".equalsIgnoreCase(ibe.getEticketMold()) && !ibe.isAllDeduction() && !ibe.isSpecialService();
                    boolean ticketSupportUpgrade = !"Group".equalsIgnoreCase(ibe.getEticketMold()) && !ibe.isSpecialService();
                    if (supportUnlimitCard >= 2) {
                        boolean upClassCardAvailable = false;
                        try {
                            upClassCardAvailable = orderManage.unlimitedUpClassAvailable(ffpId, ffpCardNo, channelCode,
                                    this.getChannelInfo(channelCode, "10"), this.getClientPwd(channelCode), passengerInfo.getCertType(),
                                    passengerInfo.getCertNo(), ip, HKMCTWRegion, passengerInfo.getPassengerName());
                        } catch (ServiceException e) {
                            segmentShow.setUnlimitedCardFlightOver3(true);
                        } catch (Exception e) {
                            log.error("查询是否可用无限升舱卡出现异常", e);
                        }
                        if (upClassCardAvailable) {
                            if (!international) {
                                Date unusableTimeBegin = DateUtils.toDate(upgradeCardV2Config.getUnusableTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                                Date unusableTimeEnd = DateUtils.toDate(upgradeCardV2Config.getUnusableTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                                if (!ticketSupportUpgrade) {
                                    segmentShow.setIfUp(false);
                                    segmentShow.setNotUpReason("您查询的航班不支持用升舱卡升舱");
                                } else if (null != unusableTimeBegin && null != unusableTimeEnd && (flightTime.getTime() <= unusableTimeBegin.getTime()
                                        || flightTime.getTime() >= unusableTimeEnd.getTime())) {
                                    unlimitedUpClass = true;
                                    //缓存五分钟，是否可用2021版无限升舱卡
                                    String key = RedisKeyConfig.UNLIMITED_UPGRADE + ffpId + ":" + ibe.getTicketNo().replaceAll("-", "");
                                    String unlimitedUpFlag = apiRedisService.getData(key);
                                    if (StringUtils.isBlank(unlimitedUpFlag)) {
                                        apiRedisService.putData(key, "Y", 5 * 60L);//暂时五分钟
                                    }
                                } else {
                                    segmentShow.setIfUp(false);
                                    segmentShow.setNotUpReason("您查询的航班不支持用升舱卡升舱");
                                }
                            } else {
                                segmentShow.setIfUp(false);
                                segmentShow.setNotUpReason("您查询的航班不支持用升舱卡升舱");
                            }
                        }
                    }
                }
                //免票，常客兑换票，中转客票不支持改期升舱
                String notAllowedChangeCabin = unlimitedUpClass ? handConfig.getUnlimitedUpNotAllowedCabin() : handConfig.getNotAllowedChangeCabin();
                segmentShow.setUnlimitedUpClass(unlimitedUpClass);
                //yyyy-MM-dd HH:mm
                segmentShow.setFlightTime(DateUtils.calDuration(ptSegmentInfo.getDepTime(), "8", ptSegmentInfo.getArrTime(), "8"));
                //yyyy-MM-dd
                segmentShow.setDays(DateUtils.diffDays(deptDate, "8", arrDate, "8"));
                segmentShow.setPriceValue(ptSegmentInfo.getTicketPrice());
                //暂无经停信息
                segmentShow.setStopCity("");
                segmentShow.setStopCityName("");
                segmentShow.setCabinClassName(CommonUtil.showCabinClassName(CommonUtil.getCabinClassByCabinCode(ptSegmentInfo.getCabin(), classSet)));
                //免票航班只支持升舱券升舱
                boolean isFreeTicket = ptSegmentInfo.getCabin().contains(handConfig.getFreeTicketCabin());
                if (isFreeTicket && HandlerConstants.FLIGHT_INTER_D.equals(upTicketInfo.getInterFlag()) && CollectionUtils.isNotEmpty(ptSegmentInfo.getCabinList())) {
                    List<CabinNumInfo> cabinList = ptSegmentInfo.getCabinList().stream().filter(cabinNumInfo ->
                            handConfig.getSupportUpCouponCabin().contains(cabinNumInfo.getCabin())).collect(Collectors.toList());
                    segmentShow.setCabinList(cabinList);
                } else {
                    segmentShow.setCabinList(ptSegmentInfo.getCabinList());
                }
                //多次卡不支持升舱
                if ("THEM".equals(ptSegmentInfo.getFareBasic())) {
                    upTicketInfo.setUpAble(false);
                    segmentShow.setIfUp(false);
                }
                if ("公务舱位已售罄".equals(segmentShow.getNotUpReason()) && HandlerConstants.FLIGHT_INTER_I.equals(upTicketInfo.getInterFlag())) {
                    segmentShow.setNotUpReason("公务舱已售罄");
                } else if (isFreeTicket && CollectionUtils.isEmpty(segmentShow.getCabinList())) {
                    segmentShow.setIfUp(false);
                    segmentShow.setNotUpReason("公务舱(" + handConfig.getSupportUpCouponCabin() + ")舱位已售罄");
                }
                com.juneyaoair.mobile.mongo.entity.FlightInfo flightInfo = queryFlightInfo(deptDate, segmentShow.getFlightNo(),
                        segmentShow.getDeptAirPortCode(), segmentShow.getArrAirPortCode());
                if (null != flightInfo) {
                    Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
                    AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, flightInfo.getPlanType());
                    segmentShow.setPlaneType(aircraftModel == null ? "" : aircraftModel.getRemark());
                }
                if (upTicketInfo.isUpAble()) {
                    if (StringUtils.isNotBlank(notAllowedChangeCabin) && notAllowedChangeCabin.contains(ptSegmentInfo.getCabin())) {
                        segmentShow.setIfUp(false);
                        segmentShow.setNotUpReason("该舱位不支持线上升舱！");
                    }
                    if (!"HO".equals(ptSegmentInfo.getAirline())) {
                        segmentShow.setIfUp(false);
                        segmentShow.setNotUpReason("暂不支持吉祥航班以外的航班升舱");
                    }
                } else {
                    segmentShow.setIfUp(false);
                    segmentShow.setNotUpReason(upTicketInfo.getNotUpReason());
                }
                segmentShowList.add(segmentShow);
            });
            // SPA / ADDON
            if ((HandlerConstants.ROUTE_TYPE_OW.equals(upTicketInfo.getRouteType()) && segmentSize.get() > 1 && containInter.get())
                    || (HandlerConstants.ROUTE_TYPE_RT.equals(upTicketInfo.getRouteType()) && segmentSize.get() > 2 && containInter.get())) {
                upTicketInfo.setUpAble(false);
                upTicketInfo.setNotUpReason("国际联程票不支持线上升舱");
            }
            //各类不可升舱限制条件
            checkNotSupport(upTicketInfo, ibe, segmentShowList.get(0).isUnlimitedUpClass());
            upTicketInfo.setSegmentShowList(segmentShowList);
            if (upTicketInfo.isUpAble()) {
                //判断是否存在允许升舱的航段，有的情况展示升舱按钮
                boolean ifup = segmentShowList.stream().anyMatch(SegmentShow::isIfUp);
                upTicketInfo.setUpAble(ifup);
                if (!ifup) {
                    SegmentShow notUpSegmentShow = segmentShowList.stream().filter(segmentShow -> !segmentShow.isIfUp()).findFirst().orElse(null);
                    if (notUpSegmentShow != null) {
                        upTicketInfo.setNotUpReason(notUpSegmentShow.getNotUpReason());
                    }
                }
            }
            String sign = EncoderHandler.encodeBySHA1(upTicketInfo.createSinaParam() + HandlerConstants.DEFAULT_TOKEN);
            upTicketInfo.setSign(sign);
            upTicketInfoList.add(upTicketInfo);
        });
        return upTicketInfoList;
    }

    //获取航班信息
    private com.juneyaoair.mobile.mongo.entity.FlightInfo queryFlightInfo(String flightDate, String flightNo, String depAirportCode, String arrAirportCode) {
        com.juneyaoair.mobile.mongo.entity.FlightInfo flightInfo = new com.juneyaoair.mobile.mongo.entity.FlightInfo();
        flightInfo.setFlightDate(flightDate);
        flightInfo.setDepAirport(depAirportCode);
        flightInfo.setArrAirport(arrAirportCode);
        flightInfo.setFlightNo(flightNo);
        List<com.juneyaoair.mobile.mongo.entity.FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
        if (CollectionUtils.isNotEmpty(flightInfoList)) {
            return flightInfoList.get(0);
        }
        return null;
    }


    //检验客票不支持升舱的情况
    private void checkNotSupport(UpTicketInfo upTicketInfo, PtIBETicketInfo ibe, boolean unlimitedUpClass) {
        boolean teamFlag = FlightUtil.judgeTeamTicket(ibe.getSigningInfo());//团队票
        if (teamFlag) {
            upTicketInfo.setNotUpReason("团队票不支持升舱");
            upTicketInfo.setUpAble(false);
            return;
        }
        if (!unlimitedUpClass && !StringUtil.isNullOrEmpty(ibe.getExchangeInfo())) {
            upTicketInfo.setNotUpReason("已改期升舱客票不支持升舱");
            upTicketInfo.setUpAble(false);
            return;
        }
        //多客票时多航段暂不支持升舱
        if (!StringUtil.isNullOrEmpty(ibe.getFollowTicketNo())) {
            upTicketInfo.setNotUpReason("联程航班暂不支持线上升舱");
            upTicketInfo.setUpAble(false);
            return;
        }
        //单程航班多于一段 也不支持升舱
        if ((!ibe.getOrgCity().equals(ibe.getDstCity())) && ibe.getSegmentInfoList().size() > 1) {
            upTicketInfo.setNotUpReason("联程航班暂不支持线上升舱");
            upTicketInfo.setUpAble(false);
            return;
        }
        //一个客票航段数多于2段的，暂不支持升舱
        if (ibe.getSegmentInfoList().size() > 2) {
            upTicketInfo.setNotUpReason("联程航班暂不支持线上升舱");
            upTicketInfo.setUpAble(false);
            return;
        }
//        if(HandlerConstants.TRIP_TYPE_I.equals(ibe.getInterFlag())&&ibe.getSegmentInfoList().size()>1){
//            upTicketInfo.setNotUpReason("国际往返航班暂不支持线上升舱，请联系95520办理");
//            upTicketInfo.setUpAble(false);
//            return;
//        }
        boolean boardFlag = ibe.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> HandlerConstants.BOARDED.equals(ptSegmentInfo.getTicketStatus()));
        if (boardFlag) {
            upTicketInfo.setNotUpReason("您已办理登机，请联系乘务员办理升舱");
            upTicketInfo.setUpAble(false);
            return;
        }
        boolean checkInFlag = ibe.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> HandlerConstants.CHECKED_IN.equals(ptSegmentInfo.getTicketStatus()));
        if (checkInFlag) {
            upTicketInfo.setNotUpReason("您已办理值机，请联系乘务员办理升舱");
            upTicketInfo.setUpAble(false);
            return;
        }
    }

    /**
     * 创建升舱查询费用请求参数
     *
     * @param upPriceSearch
     * @param channelCode
     * @return
     */
    private PtTicketUpgradeFeeRequest createTicketUpFee(UpPriceSearch upPriceSearch, String channelCode) {
        String userNo = this.getChannelInfo(channelCode, "10");
        //目前只是单段航段处理
        UpFlightInfo upFlightInfo = upPriceSearch.getUpPriceSegList().get(0);
        PassengerInfo passengerInfo = upFlightInfo.getPassengerInfoList().get(0);
        PtTicketUpgradeFeeRequest ptTicketUpgradeFeeRequest = new PtTicketUpgradeFeeRequest(HandlerConstants.VERSION, channelCode, userNo, HandlerConstants.CURRENCY_CODE, HandlerConstants.LANG_CODE);
        ptTicketUpgradeFeeRequest.setRouteType(upFlightInfo.getRouteType());
        ptTicketUpgradeFeeRequest.setInterFlag(upFlightInfo.getInterFlag());
        //如果存在非OPEN的客票,国内往返查询单程运价
        if (HandlerConstants.ROUTE_TYPE_RT.equals(upFlightInfo.getRouteType()) && HandlerConstants.TRIP_TYPE_D.equals(upFlightInfo.getInterFlag())) {
            boolean notOpen = upFlightInfo.getFlightInfoList().stream().anyMatch(flightInfo -> !HandlerConstants.OPEN_FOR_USE.equals(flightInfo.getTicketStatus()));
            if (notOpen) {
                ptTicketUpgradeFeeRequest.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
            }
        }
        //非必传项
        ptTicketUpgradeFeeRequest.setRandCode("");
        List<PtTicketUpgradeFeeInfo> ticketUpgradeFeeInfoList = new ArrayList<>();
        upFlightInfo.getFlightInfoList().stream().forEach(flightInfo -> {
            PtTicketUpgradeFeeInfo ticketUpgradeFeeInfo = new PtTicketUpgradeFeeInfo();
            if (flightInfo.getFlightNo().equals(upFlightInfo.getFlightNo())) {
                ticketUpgradeFeeInfo.setUpgradeFlag(true);
            }
            if (HandlerConstants.OPEN_FOR_USE.equals(flightInfo.getTicketStatus())) {
                ticketUpgradeFeeInfo.setFlightNo(flightInfo.getFlightNo());
                ticketUpgradeFeeInfo.setPassengerType(passengerInfo.getPassengerType());
                ticketUpgradeFeeInfo.setDepAirport(flightInfo.getDepAirport());
                ticketUpgradeFeeInfo.setArrAirport(flightInfo.getArrAirport());
                ticketUpgradeFeeInfo.setCabin(flightInfo.getCabinCode());
                ticketUpgradeFeeInfo.setTicketPrice(flightInfo.getPriceValue());
                ticketUpgradeFeeInfo.setYQTax(flightInfo.getYQTax());
                ticketUpgradeFeeInfo.setCNTax(flightInfo.getCNTax());
                ticketUpgradeFeeInfo.setDepDateTime(flightInfo.getDepDateTime());
                upFlightInfo.getSegmentList().stream().forEach(segment -> {
                    if (segment.getDepCity().equals(flightInfo.getDepCity()) && segment.getArrCity().equals(flightInfo.getArrCity())) {
                        List<Segment> segmentList = new ArrayList<>();
                        segmentList.add(segment);
                        ticketUpgradeFeeInfo.setSegCondList(segmentList);
                    }
                });
                ticketUpgradeFeeInfoList.add(ticketUpgradeFeeInfo);
            }
        });
        ptTicketUpgradeFeeRequest.setTicketUpgradeFeeInfo(ticketUpgradeFeeInfoList);
        return ptTicketUpgradeFeeRequest;
    }

    /**
     * 创建查询舱位信息请求参数
     *
     * @param upPriceSearch
     * @param channelCode
     * @return
     */
    private TicketUpgradeCabinInfoRequest createUpgradeCabinRequest(UpPriceSearch upPriceSearch,
                                                                    String channelCode, String cabinCode) {
        TicketUpgradeCabinInfoRequest cabinInfoRequest = new TicketUpgradeCabinInfoRequest();
        cabinInfoRequest.setChannelCode(channelCode);
        cabinInfoRequest.setVersion("20");
        cabinInfoRequest.setRandCode(StringUtil.newGUID());
        cabinInfoRequest.setUserNo(this.getChannelInfo(channelCode, "10"));
        cabinInfoRequest.setLangCode(HandlerConstants.LANG_CODE);
        //目前只是单段航段处理
        UpFlightInfo upFlightInfo = upPriceSearch.getUpPriceSegList().get(0);
        TicketUpgradeCabinInfoDto ticketUpgradeCabinInfoDto = new TicketUpgradeCabinInfoDto();
        ticketUpgradeCabinInfoDto.setFlightNo(upFlightInfo.getFlightNo());
        ticketUpgradeCabinInfoDto.setFlightDate(upFlightInfo.getFlightDate());
        ticketUpgradeCabinInfoDto.setDepAirport(upFlightInfo.getDeptAirPortCode());
        ticketUpgradeCabinInfoDto.setDepCity(upFlightInfo.getDeptCityCode());
        ticketUpgradeCabinInfoDto.setDepDateTime(upFlightInfo.getFlightDate() + " " + upFlightInfo.getDeptTime());
        ticketUpgradeCabinInfoDto.setArrAirport(upFlightInfo.getArrAirPortCode());
        ticketUpgradeCabinInfoDto.setArrCity(upFlightInfo.getArrCityCode());
        ticketUpgradeCabinInfoDto.setArrDateTime(upFlightInfo.getFlightArrDate() + " " + upFlightInfo.getArrTime());
        ticketUpgradeCabinInfoDto.setCurrencyCode(HandlerConstants.CURRENCY_CODE);
        ticketUpgradeCabinInfoDto.setQueryCabin(cabinCode);
        cabinInfoRequest.setTicketUpgradeCabinInfos(Collections.singletonList(ticketUpgradeCabinInfoDto));
        return cabinInfoRequest;
    }

    /**
     * 国内往返时一段已使用的情况下，需补充信息
     *
     * @param ptTicketUpgradeFeeRequest
     * @param ptTicketUpgradeFeeResponse
     * @param upFlightInfo
     */
    private void completeFlightInfo(PtTicketUpgradeFeeRequest ptTicketUpgradeFeeRequest, PtTicketUpgradeFeeResponse ptTicketUpgradeFeeResponse, UpFlightInfo upFlightInfo) {
        if (HandlerConstants.TRIP_TYPE_D.equals(upFlightInfo.getInterFlag()) && HandlerConstants.ROUTE_TYPE_RT.equals(upFlightInfo.getRouteType())) {
            List<UpAVFlightInfo> upAVFlightInfoListNew = new ArrayList<>();
            List<UpAVFlightInfo> upAVFlightInfoList = ptTicketUpgradeFeeResponse.getFlightInfoList();
            List<FlightInfo> flightInfoList = upFlightInfo.getFlightInfoList();
            flightInfoList.stream().forEach(flightInfo -> {
                UpAVFlightInfo upAVFlightInfoTemp = upAVFlightInfoList.stream().filter(upAVFlightInfo -> upAVFlightInfo.getFlightNo().equals(flightInfo.getFlightNo())).findFirst().orElse(null);
                if (upAVFlightInfoTemp == null) {
                    upAVFlightInfoTemp = new UpAVFlightInfo();
                    BeanUtils.copyProperties(flightInfo, upAVFlightInfoTemp);
                }
                upAVFlightInfoListNew.add(upAVFlightInfoTemp);

            });
            ptTicketUpgradeFeeResponse.setFlightInfoList(upAVFlightInfoListNew);
        }
    }

    /**
     * 处理头等舱位信息
     * UpFlightInfo  表示选择的升舱段
     * passType 乘客类型
     */
    private List<AvailbleCabinInfo> dealAClass(PtTicketUpgradeFeeResponse ptTicketUpgradeFeeResponse, UpFlightInfo upFlightInfo, String passType,
                                               AtomicBoolean unlimitedUpClass) {
        List<AvailbleCabinInfo> availbleCabinInfoList = new ArrayList<>();
        List<UpAVFlightInfo> flightInfoList = ptTicketUpgradeFeeResponse.getFlightInfoList();
        if (StringUtil.isNullOrEmpty(flightInfoList)) {
            return availbleCabinInfoList;
        }
        //遍历升舱查询结果（往返时航班为往返段信息）
        for (UpAVFlightInfo upAVFlightInfo : flightInfoList) {
            boolean isFreeTicket = upFlightInfo.getCabin().contains(handConfig.getFreeTicketCabin());
            //表示为升舱航段
            if (upFlightInfo.getFlightNo().equals(upAVFlightInfo.getFlightNo())) {
                String supportUpCoupon;
                if (unlimitedUpClass.get()) {
                    supportUpCoupon = handConfig.getUnlimitedUpClassCabin();
                } else if (HandlerConstants.TRIP_TYPE_I.equals(upFlightInfo.getInterFlag())) {
                    supportUpCoupon = handConfig.getSupportUpCouponCabinI();
                } else {
                    supportUpCoupon = handConfig.getSupportUpCouponCabin();
                    // 免票兑换票只允许使用升舱券升舱
                    if (isFreeTicket) {
                        upAVFlightInfo.setUpgradeCabinFareList(upAVFlightInfo.getUpgradeCabinFareList().stream().filter(upgradeCabinFare ->
                                handConfig.getSupportUpCouponCabin().contains(upgradeCabinFare.getCabinCode())
                        ).collect(Collectors.toList()));
                    }
                }
                if (CollectionUtils.isNotEmpty(upAVFlightInfo.getUpgradeCabinFareList())) {
                    boolean containUnlimitedUpClassCabin = false;
                    for (UpgradeCabinFare upgradeCabinFare : upAVFlightInfo.getUpgradeCabinFareList()) {
                        if (upgradeCabinFare.getPassengerType().equals(passType) && supportUpCoupon.contains(upgradeCabinFare.getCabinCode())) {
                            containUnlimitedUpClassCabin = true;
                            break;
                        }
                    }
                    // 如果没有无限升舱卡可以升的舱位，则使用普通升舱
                    if (!containUnlimitedUpClassCabin) {
                        unlimitedUpClass.set(false);
                        if (HandlerConstants.TRIP_TYPE_I.equals(upFlightInfo.getInterFlag())) {
                            supportUpCoupon = handConfig.getSupportUpCouponCabinI();
                        } else {
                            supportUpCoupon = handConfig.getSupportUpCouponCabin();
                            // 免票兑换票只允许使用升舱券升舱
                            if (isFreeTicket) {
                                upAVFlightInfo.setUpgradeCabinFareList(upAVFlightInfo.getUpgradeCabinFareList().stream().filter(upgradeCabinFare ->
                                        handConfig.getSupportUpCouponCabin().contains(upgradeCabinFare.getCabinCode())
                                ).collect(Collectors.toList()));
                            }
                        }
                    }
                    for (UpgradeCabinFare upgradeCabinFare : upAVFlightInfo.getUpgradeCabinFareList()) {
                        if (upgradeCabinFare.getPassengerType().equals(passType)) {
                            if (unlimitedUpClass.get() && !supportUpCoupon.contains(upgradeCabinFare.getCabinCode())) {
                                // 无限升舱卡只升J舱
                                continue;
                            }
                            AvailbleCabinInfo availbleCabinInfo = new AvailbleCabinInfo();
                            availbleCabinInfo.setPassType(passType);
                            availbleCabinInfo.setCabinCode(upgradeCabinFare.getCabinCode());
                            availbleCabinInfo.setCabinName("公务舱");
                            if (HandlerConstants.TRIP_TYPE_I.equals(upFlightInfo.getInterFlag())) {
                                availbleCabinInfo.setCabinName("公务舱");
                            }
                            if ("P".equals(upgradeCabinFare.getCabinCode())) {
                                availbleCabinInfo.setCabinName("超级头等舱");
                            } else if ("F".equals(upgradeCabinFare.getCabinCode())) {
                                availbleCabinInfo.setCabinName("头等舱");
                            }
                            double serviceFee = upgradeCabinFare.getUpgradeFee() == null ? 0 : upgradeCabinFare.getUpgradeFee();
                            double priceDiff = upgradeCabinFare.getPriceDiff() == null ? 0 : upgradeCabinFare.getPriceDiff();
                            availbleCabinInfo.setServiceFee(serviceFee);
                            availbleCabinInfo.setPriceDiff(priceDiff);
                            //总价
                            availbleCabinInfo.setUpgradeFee(serviceFee + priceDiff);
                            availbleCabinInfo.setUpAVFlightInfoList(flightInfoList);
                            //升舱支持的方式  现金支付是默认的支持方式
                            availbleCabinInfo.setSupportCash(true);
                            if (supportUpCoupon.contains(upgradeCabinFare.getCabinCode())) {
                                availbleCabinInfo.setSupportCash(false);
                                availbleCabinInfo.setSupportUpCoupon(true);
                                availbleCabinInfo.setPriority(0);
                            }
                            String num=   upgradeCabinFare.getCabinNumber();
                            num= "A".equals(num)?"10":num;
                            availbleCabinInfo.setCabinNum(num);
                            availbleCabinInfoList.add(availbleCabinInfo);
                        }
                    }
                }
            }
        }

        //遍历可升舱舱位 取出对应价格信息
        for (AvailbleCabinInfo availbleCabinInfo : availbleCabinInfoList) {
            List<UpAVFlightInfo> upAVFlightInfoListNew = new ArrayList<>();
            for (UpAVFlightInfo upAVFlightInfo : availbleCabinInfo.getUpAVFlightInfoList()) {
                UpAVFlightInfo tempFlightInfo = new UpAVFlightInfo();
                BeanUtils.copyProperties(upAVFlightInfo, tempFlightInfo);
                //升舱运价处理  只匹配升舱段
                if (upFlightInfo.getFlightNo().equals(upAVFlightInfo.getFlightNo())) {
                    for (UpgradeCabinFare upgradeCabinFare : upAVFlightInfo.getUpgradeCabinFareList()) {
                        //匹配的头等舱
                        if (availbleCabinInfo.getCabinCode().equals(upgradeCabinFare.getCabinCode()) && upgradeCabinFare.getPassengerType().equals(availbleCabinInfo.getPassType())) {
                            List<UpgradeCabinFare> upgradeCabinFareList = new ArrayList<>();
                            upgradeCabinFareList.add(upgradeCabinFare);
                            tempFlightInfo.setUpgradeCabinFareList(upgradeCabinFareList);
                            Map<String, Fare> fareMap = new HashMap<>();
                            Fare fare = upAVFlightInfo.getFareDic().get(upgradeCabinFare.getFareKey());
                            fareMap.put(upgradeCabinFare.getFareKey(), fare);
                            tempFlightInfo.setFareDic(fareMap);
                        }
                    }
                }
                upAVFlightInfoListNew.add(tempFlightInfo);
            }
            availbleCabinInfo.setUpAVFlightInfoList(upAVFlightInfoListNew);
        }
        AvailbleCabinInfo availbleCabin = availbleCabinInfoList.stream()
                .filter(availbleCabinInfo -> "D".equals(availbleCabinInfo.getCabinCode()))
                .findFirst()
                .orElse(null);
        if (availbleCabin ==null) {
            AvailbleCabinInfo availbleCabinInfo = getAvailbleCabinInfo();
            availbleCabinInfoList.add(availbleCabinInfo);
        }
        //价格排序
        availbleCabinInfoList.sort(Comparator.comparing(AvailbleCabinInfo::getPriority).thenComparing(AvailbleCabinInfo::getUpgradeFee));
        return availbleCabinInfoList;
    }

    private  AvailbleCabinInfo getAvailbleCabinInfo() {
        AvailbleCabinInfo  availbleCabinInfo=new AvailbleCabinInfo();
        availbleCabinInfo.setCabinName("公务舱");
        availbleCabinInfo.setCabinCode("D");
        availbleCabinInfo.setPassType("ADT");
        availbleCabinInfo.setCabinNum("0");
        availbleCabinInfo.setPriceDiff(0);
        availbleCabinInfo.setSupportUpCoupon(true);
        availbleCabinInfo.setUpgradeFee(0);
        availbleCabinInfo.setPriority(10);
        availbleCabinInfo.setUnlimitedUpClass(false);
        return availbleCabinInfo;
    }

    private List<AvailbleCabinInfo> dealRClass(List<TicketUpgradeCabinInfoDto> ticketUpgradeCabinInfoDtos,
                                               UpFlightInfo upFlightInfo, String passengerType, AtomicBoolean unlimitedUpClass) {
        List<AvailbleCabinInfo> availableCabinInfoList = Lists.newArrayList();
        for (TicketUpgradeCabinInfoDto ticketUpgradeCabinInfoDto : ticketUpgradeCabinInfoDtos) {
            AvailbleCabinInfo availbleCabinInfo = new AvailbleCabinInfo();
            availbleCabinInfo.setPassType(passengerType);
            availbleCabinInfo.setCabinCode(ticketUpgradeCabinInfoDto.getQueryCabin());
            availbleCabinInfo.setCabinName("公务舱");
            //升舱支持的方式  国际升舱只支持
            availbleCabinInfo.setSupportCash(false);
            availbleCabinInfo.setSupportUpCoupon(true);
            availbleCabinInfo.setUnlimitedUpClass(unlimitedUpClass.get());
            availbleCabinInfo.setPriceDiff(1200);// 20200708港澳台地区默认差价1200
            availableCabinInfoList.add(availbleCabinInfo);
        }
        return availableCabinInfoList;
    }

    //处理乘客信息
    private List<PassengerInfo> ticketInfoToPass(PtIBETicketInfo ticketInfo, LocalCacheService localCacheService) {
        List<PassengerInfo> passengerInfoList = new ArrayList<>();
        PassengerInfo pass = new PassengerInfo();
        String passengerType = HandlerConstants.PASSENGER_TYPE_ADT;
        if (CommonBaseConstants.IBE_PASSENGER_TYPE_ADT.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_ADT;
        } else if (CommonBaseConstants.IBE_PASSENGER_TYPE_CHD.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_CHD;
        }
        pass.setPassengerNO(0);
        pass.setPassengerName(ticketInfo.getPassengerName());
        pass.setPassengerType(passengerType);
        pass.setcNTax(ticketInfo.getCN());
        pass.setyQTax(ticketInfo.getYQ());
        //默认取NI(国内护照也是NI类型)，若无则取第一个证件
        IdentityInfo identityInfo = IdentityInfoUtil.getIdentityInfo(ticketInfo.getIdentityInfoList());
        if (identityInfo != null) {
            List<String> countryCode = Stream.of(identityInfo.Nationality, identityInfo.BelongCountry).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(countryCode)) {
                List<TCountryDTO> localCountryList = localCacheService.getLocalCountry(countryCode);
                if (CollectionUtils.isNotEmpty(localCountryList)) {
                    if (StringUtils.isNotBlank(identityInfo.Nationality)) {
                        pass.setNationalityName(localCountryList.stream().filter(i -> identityInfo.Nationality.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.countryName).orElse(null));
                        pass.setNationalityEName(localCountryList.stream().filter(i -> identityInfo.Nationality.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.englishName).orElse(null));
                    }
                    if (StringUtils.isNotBlank(identityInfo.BelongCountry)) {
                        pass.setBelongCountryName(localCountryList.stream().filter(i -> identityInfo.BelongCountry.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.countryName).orElse(null));
                        pass.setBelongCountryEName(localCountryList.stream().filter(i -> identityInfo.BelongCountry.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.englishName).orElse(null));
                    }
                }
            }
            pass.setNationality(identityInfo.getNationality());
            pass.setCertType(identityInfo.getIdType());
            pass.setCertNo(identityInfo.getIdNo());
            pass.setBirthdate(identityInfo.getBirthdate());
        }
        pass.setPricePaid(ticketInfo.getTotalAmount() - ticketInfo.getYQ() - ticketInfo.getCN());
        pass.setIsBuyInsurance("N");
        passengerInfoList.add(pass);
        return passengerInfoList;
    }

    /**
     * 处理时间 将yyyy-MM-dd HH:mm
     *
     * @param time
     * @return
     */
    private String[] separateTime(String time) {
        String[] timeStr = new String[2];
        if (time.length() >= 15) {
            timeStr[0] = time.substring(0, 10);
            timeStr[1] = time.substring(11, 16);
        } else {
            timeStr[0] = "";
            timeStr[1] = "";
        }
        return timeStr;
    }

    /**
     * 根据日期返回周几
     *
     * @return
     */
    private String getWeekByDate(String dateStr) {
        try {
            Date date = DateUtils.toDate(dateStr);
            return DateUtils.getWeekStr(date);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取客票信息
     *
     * @param channelCode
     * @param ticketNo
     * @return
     */
    private TicketListInfoResponse queryTicket(String channelCode, String ticketNo, String passName, String clientIp) {
        String userNo = getChannelInfo(channelCode, "10");
        String certType = CertNoUtil.getCertTypeByCertNo(ticketNo);
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        TicketListInfoResponse ticketListInfoResponse;
        //票号查询
        if ("TN".equals(certType)) {
            TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, channelCode, userNo);
            ticketInfoRequest.setQueryType(TicketQueryTypeEnum.UPGRADE.getCode());
            ticketInfoRequest.setCertType("TN");
            ticketInfoRequest.setTicketNo(ticketNo);
            ticketInfoRequest.setPassengerName(passName);
            ticketListInfoResponse = orderManage.getTicketInfo(ticketInfoRequest, headMap);
        } else {
            //如果是证件参数采取证件查询过滤
            if ("NI".equals(certType)) {
                ticketListInfoResponse = queryTicketByCert(channelCode, userNo, ticketNo, passName, certType, headMap);
            } else {
                ticketListInfoResponse = queryTicketByCert(channelCode, userNo, ticketNo, passName, "PP", headMap);
                if (UnifiedOrderResultEnum.ERROR_CERT_TYPE.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                    ticketListInfoResponse = queryTicketByCert(channelCode, userNo, ticketNo, passName, "ID", headMap);
                }
            }
            if (UnifiedOrderResultEnum.ERROR_CERT_TYPE.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                ticketListInfoResponse = queryTicketByCert(channelCode, userNo, ticketNo, passName, "UU", headMap);
            }
        }
        return ticketListInfoResponse;
    }

    /**
     * 证件号查询客票信息
     *
     * @param channelCode
     * @param userNo
     * @param ticketNo
     * @param passName
     * @param certType
     * @return
     */
    private TicketListInfoResponse queryTicketByCert(String channelCode, String userNo, String ticketNo, String passName, String certType, Map<String, String> headMap) {
        TicketListInfoResponse ticketListInfoResponse = new TicketListInfoResponse();
        PtTicketDigestRequestDto ptTicketDigestRequestDto = PtTicketDigestRequestDto.builder()
                .CertType(certType)
                .CertNo(ticketNo)
                .PassengerName(passName)
                .ChannelCode(channelCode)
                .Version(HandlerConstants.VERSION)
                .SegmentStatusList(Arrays.asList("OPEN", "OPEN FOR USE"))
                .build();
        //对于证件类型不正确，
        PtTicketListInfoResponse ptTicketListInfoResponse = orderManage.getTicketDigestInfo(ptTicketDigestRequestDto, headMap);
        if (UnifiedOrderResultEnum.ERROR_CERT_TYPE.getResultCode().equals(ptTicketListInfoResponse.getResultCode())) {
            ticketListInfoResponse.fail(ptTicketListInfoResponse.getResultCode(), ptTicketListInfoResponse.getErrorInfo());
            return ticketListInfoResponse;
        }
        //姓名过滤
        List<PtDetrDigestInfoDto> filterList = new ArrayList<>();
        for (PtDetrDigestInfoDto ptIBETicketInfo : ptTicketListInfoResponse.getDetrDigestInfoDtoList()) {
            String patternStr = passName + PatternCommon.PASS_NAME_REGEX;//正则表达式
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(ptIBETicketInfo.getName());
            if (matcher.matches()) {
                filterList.add(ptIBETicketInfo);
            }
        }
        if (CollectionUtils.isEmpty(filterList)) {
            ticketListInfoResponse.success(UnifiedOrderResultEnum.SUCCESS.getResultCode(), UnifiedOrderResultEnum.SUCCESS.getResultInfo(), null);
            return ticketListInfoResponse;
        }
        List<PtDetrDigestInfoDto> ptDetrDigestInfoDtoList = filterList;
        List<Future<List<PtIBETicketInfo>>> futureList = new ArrayList<>(filterList.size());
        //遍历处理客票信息,此处为了加快响应速度，改造为异步执行
        for (PtDetrDigestInfoDto ptDetrDigestInfoDto : ptDetrDigestInfoDtoList) {
            Callable<List<PtIBETicketInfo>> callable = () -> {
                TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, channelCode, userNo);
                ticketInfoRequest.setQueryType(TicketQueryTypeEnum.UPGRADE.getCode());
                ticketInfoRequest.setCertType("TN");
                ticketInfoRequest.setTicketNo(ptDetrDigestInfoDto.getTktNo());
                ticketInfoRequest.setPassengerName(passName);
                TicketListInfoResponse callTicketListInfoResponse = orderManage.getTicketInfo(ticketInfoRequest, headMap);
                return callTicketListInfoResponse.getIBETicketInfoList();
            };
            Future<List<PtIBETicketInfo>> future = taskExecutor.submit(callable);
            futureList.add(future);
        }
        //遍历获取结果
        List<PtIBETicketInfo> allPtIBETicketInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(futureList)) {
            for (Future<List<PtIBETicketInfo>> future : futureList) {
                try {
                    List<PtIBETicketInfo> ptIBETicketInfoList = future.get(20L, TimeUnit.SECONDS);
                    if (CollectionUtils.isNotEmpty(ptIBETicketInfoList)) {
                        allPtIBETicketInfoList.addAll(ptIBETicketInfoList);
                    }
                } catch (Exception e) {
                    log.error("提取客票存在异常:", e);
                }
            }
        }
        ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.SUCCESS.getResultCode());
        ticketListInfoResponse.setErrorInfo(UnifiedOrderResultEnum.SUCCESS.getResultInfo());
        ticketListInfoResponse.setIBETicketInfoList(allPtIBETicketInfoList);
        return ticketListInfoResponse;
    }

    /**
     * 升舱订单请求V2
     *
     * @param channelCode
     * @param userNo
     * @param upClassOrderReq
     * @param ptMemberDetail
     * @param outCouponOrderAmount
     * @return
     */
    private PtTicketBookingReq createUpgradeOrderReqV2(String channelCode, String userNo, UpClassOrderReq upClassOrderReq, PtMemberDetail ptMemberDetail, double outCouponOrderAmount) {
        UpTicketInfo upTicketInfo = upClassOrderReq.getUpTicketInfo();
        PassengerInfo passengerInfo = upTicketInfo.getPassengerInfoList().get(0);
        String passType = passengerInfo.getPassengerType();
        String pnr = upTicketInfo.getSegmentShowList().get(0).getPnrNo();
        //公用信息
        PtTicketBookingReq ptTicketBookingReq = new PtTicketBookingReq(HandlerConstants.VERSION, channelCode, userNo, HandlerConstants.CURRENCY_CODE, HandlerConstants.LANG_CODE);
        ptTicketBookingReq.setChannelCode(channelCode);
        ptTicketBookingReq.setChannelOrderNo(upClassOrderReq.getChannelOrderNo());
        ptTicketBookingReq.setChannelCustomerNo(upClassOrderReq.getFfpId());
        ptTicketBookingReq.setRouteType(upTicketInfo.getRouteType());
        ptTicketBookingReq.setInterFlag(upTicketInfo.getInterFlag());
        ptTicketBookingReq.setCurrencyCode(HandlerConstants.CURRENCY_CODE);
        ptTicketBookingReq.setLangCode("CN");
        ptTicketBookingReq.setTicketOrderSort("Upgrade");
        ptTicketBookingReq.setTicketOrderType("Person");
        //机票子订单，第一段为客票的原有航段信息
        //处理原有的航班信息
        List<FlightInfo> flightInfoList = upTicketInfo.getFlightInfoList();
        List<PassengerInfo> passengerInfoList = upTicketInfo.getPassengerInfoList();
        String redisKey = RedisKeyConfig.createTicketCacheKey(TicketQueryTypeEnum.UPGRADE.getCode(), upTicketInfo.getTicketNo());
        String result = apiRedisService.getData(redisKey);
        if (StringUtils.isBlank(result)) {
            throw new CommonException(WSEnum.OPERATION_TIMEOUT_50016.getResultCode(), "升舱操作超时，请退出当前页重新查询");
        }
        TicketListInfoResponse ticketListInfoResponse = JsonUtil.fromJson(result, TicketListInfoResponse.class);
        List<IdentityInfo> identityInfoList = ticketListInfoResponse.getIBETicketInfoList().get(0).getIdentityInfoList();
        //儿童，婴儿，国际票需要出生日期,身份证的直接获取，护照类型的暂不知道
        for (PassengerInfo pass : passengerInfoList) {
            Optional<IdentityInfo> optional = identityInfoList.stream().filter(info -> info.getIdNo().equals(pass.getCertNo())).findFirst();
            if (optional.isPresent()) {
                IdentityInfo identityInfo = optional.get();
                pass.setBirthdate(identityInfo.getBirthdate());
                pass.setSex(identityInfo.getSex());
                pass.setNationality(identityInfo.getNationality());
                pass.setBelongCountry(identityInfo.getBelongCountry());
                pass.setCertValidity(identityInfo.getCertValidity());
            }
            if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(pass.getCertType()) && Pattern.matches(PatternCommon.ID_NUMBER, pass.getCertNo())) {
                    pass.setBirthdate(CertUtil.certNoToDate(pass.getCertNo()));
                }
            }
        }
        //第二段为升舱的客票信息,不进行升舱段也要保存此子订单中
        List<FlightInfo> flightInfoListUp = createUpFlightInfoV2(upClassOrderReq, passType);
        //客票信息缺少部分信息，信息同步
        syncFlightInfoOld(flightInfoList, flightInfoListUp);
        List<PtTicketOrderInfo> tOrderList = createNormalTicketOrderInfo(upTicketInfo.getTicketNo(), upTicketInfo.getInterFlag(), flightInfoList, passengerInfoList, pnr, passType, ptMemberDetail);
        //升舱航段信息
        List<PtTicketOrderInfo> upTicketOrderInfoList = createUpgradeTicketOrderInfo(upClassOrderReq.getScore(),
                upTicketInfo.getTicketNo(), upTicketInfo.getInterFlag(), flightInfoListUp, passengerInfoList, pnr, passType, ptMemberDetail);
        tOrderList.addAll(upTicketOrderInfoList);
        // 联系人信息
        if (upClassOrderReq.getContactInfo() != null) {
            tOrderList.forEach(info -> {
                info.setLinker(upClassOrderReq.getContactInfo().getUserName());
                info.setLinkerTelphone(upClassOrderReq.getContactInfo().getMobile());
                info.setLinkerHandphone(upClassOrderReq.getContactInfo().getMobile());
            });
        }
        ptTicketBookingReq.setTicketOrderInfoList(tOrderList);
        Integer useScoreTotal = 0;
        for (PtTicketOrderInfo ptTicketOrderInfo : tOrderList) {
            useScoreTotal = useScoreTotal + ptTicketOrderInfo.getUseScoreTotal();
        }
        ptTicketBookingReq.setUseScoreTotal(useScoreTotal);
        //支付金额 如果有积分减去部分积分信息
        ptTicketBookingReq.setPayAmount(outCouponOrderAmount - useScoreTotal);
        return ptTicketBookingReq;
    }

    /**
     * 国际升舱下单请求参数
     */
    private PtTicketBookingReq createIUpgradeOrderReqV2(String channelCode, String userNo, UpClassOrderReq upClassOrderReq, PtMemberDetail ptMemberDetail, double outCouponOrderAmount) {
        UpTicketInfo upTicketInfo = upClassOrderReq.getUpTicketInfo();
        PassengerInfo passengerInfo = upTicketInfo.getPassengerInfoList().get(0);
        String passType = passengerInfo.getPassengerType();
        String pnr = upTicketInfo.getSegmentShowList().get(0).getPnrNo();
        //公用信息
        PtTicketBookingReq ptTicketBookingReq = new PtTicketBookingReq(HandlerConstants.VERSION, channelCode, userNo, HandlerConstants.CURRENCY_CODE, HandlerConstants.LANG_CODE);
        ptTicketBookingReq.setChannelCode(channelCode);
        ptTicketBookingReq.setChannelOrderNo(upClassOrderReq.getChannelOrderNo());
        ptTicketBookingReq.setChannelCustomerNo(upClassOrderReq.getFfpId());
        ptTicketBookingReq.setRouteType(upTicketInfo.getRouteType());
        ptTicketBookingReq.setInterFlag(upTicketInfo.getInterFlag());
        ptTicketBookingReq.setCurrencyCode(HandlerConstants.CURRENCY_CODE);
        ptTicketBookingReq.setLangCode("CN");
        ptTicketBookingReq.setTicketOrderSort("Upgrade");
        ptTicketBookingReq.setTicketOrderType("Person");
        //机票子订单，第一段为客票的原有航段信息
        //处理原有的航班信息
        List<FlightInfo> flightInfoList = upTicketInfo.getFlightInfoList();
        List<PassengerInfo> passengerInfoList = upTicketInfo.getPassengerInfoList();
        String redisKey = RedisKeyConfig.createTicketCacheKey(TicketQueryTypeEnum.UPGRADE.getCode(), upTicketInfo.getTicketNo());
        String result = apiRedisService.getData(redisKey);
        if (StringUtils.isBlank(result)) {
            throw new CommonException(WSEnum.OPERATION_TIMEOUT_50016.getResultCode(), "升舱操作超时，请退出当前页重新查询");
        }
        TicketListInfoResponse ticketListInfoResponse = JsonUtil.fromJson(result, TicketListInfoResponse.class);
        List<IdentityInfo> identityInfoList = ticketListInfoResponse.getIBETicketInfoList().get(0).getIdentityInfoList();
        //儿童，婴儿，国际票需要出生日期,身份证的直接获取，护照类型的暂不知道
        for (PassengerInfo pass : passengerInfoList) {
            Optional<IdentityInfo> optional = identityInfoList.stream().filter(info -> info.getIdNo().equals(pass.getCertNo())).findFirst();
            if (optional.isPresent()) {
                IdentityInfo identityInfo = optional.get();
                pass.setBirthdate(identityInfo.getBirthdate());
                pass.setSex(identityInfo.getSex());
                pass.setNationality(identityInfo.getNationality());
                pass.setBelongCountry(identityInfo.getBelongCountry());
                pass.setCertValidity(identityInfo.getCertValidity());
            }
            if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(pass.getCertType()) && Pattern.matches(PatternCommon.ID_NUMBER, pass.getCertNo())) {
                    pass.setBirthdate(CertUtil.certNoToDate(pass.getCertNo()));
                }
            }
        }
        //第二段为升舱的客票信息,不进行升舱段也要保存此子订单中
        List<FlightInfo> flightInfoListUp = createIUpFlightInfoV2(upClassOrderReq, passType);
        //第一个子订单 原有航班信息
        List<PtTicketOrderInfo> tOrderList = createNormalTicketOrderInfo(upTicketInfo.getTicketNo(), upTicketInfo.getInterFlag(),
                flightInfoList, passengerInfoList, pnr, passType, ptMemberDetail);
        //第二个子订单 升舱航段信息
        List<PtTicketOrderInfo> upTicketOrderInfoList = createIUpgradeTicketOrderInfo(upClassOrderReq.getScore(),
                upTicketInfo.getTicketNo(), upTicketInfo.getInterFlag(), flightInfoListUp, passengerInfoList,
                pnr, passType, ptMemberDetail, upTicketInfo.getSegmentShowList());
        tOrderList.addAll(upTicketOrderInfoList);
        // 联系人信息
        if (upClassOrderReq.getContactInfo() != null) {
            tOrderList.forEach(info -> {
                info.setLinker(upClassOrderReq.getContactInfo().getUserName());
                info.setLinkerTelphone(upClassOrderReq.getContactInfo().getMobile());
                info.setLinkerHandphone(upClassOrderReq.getContactInfo().getMobile());
            });
        }
        ptTicketBookingReq.setTicketOrderInfoList(tOrderList);
        Integer useScoreTotal = 0;
        for (PtTicketOrderInfo ptTicketOrderInfo : tOrderList) {
            useScoreTotal = useScoreTotal + ptTicketOrderInfo.getUseScoreTotal();
        }
        ptTicketBookingReq.setUseScoreTotal(useScoreTotal);
        //支付金额 如果有积分减去部分积分信息
        ptTicketBookingReq.setPayAmount(outCouponOrderAmount - useScoreTotal);
        return ptTicketBookingReq;
    }

    /**
     * 创建一个包含原有航班信息的子订单
     *
     * @return
     */
    private List<PtTicketOrderInfo> createNormalTicketOrderInfo(String ticketNo, String interFlag, List<FlightInfo> flightInfoList,
                                                                List<PassengerInfo> passengerInfoList, String pnr, String passType, PtMemberDetail ptMemberDetail) {
        PassSegInfo passSegInfo = createPassSegInfo(ptMemberDetail, ticketNo, passengerInfoList.get(0).getPassengerName());
        passSegInfo.setPassengerInfoList(passengerInfoList);
        passSegInfo.setFlightInfoList(flightInfoList);
        passSegInfo.setInterFlag(interFlag);
        return CabinUpObjectConvert.ToTicketOrderInfoList(passSegInfo, passType, pnr, "NORMAL");//机票订单信息列表
    }

    /**
     * 新的升舱子订单
     *
     * @return
     */
    private List<PtTicketOrderInfo> createUpgradeTicketOrderInfo(int score, String ticketNo, String interFlag, List<FlightInfo> flightInfoList,
                                                                 List<PassengerInfo> passengerInfoList, String pnr, String passType, PtMemberDetail ptMemberDetail) {
        PassSegInfo passSegInfo = createPassSegInfo(ptMemberDetail, ticketNo, passengerInfoList.get(0).getPassengerName());
        passSegInfo.setPassengerInfoList(passengerInfoList);
        passSegInfo.setFlightInfoList(flightInfoList);
        passSegInfo.setInterFlag(interFlag);
        passSegInfo.setUseScoreTotal(0);
        if (score > 0) {
            passSegInfo.setUseScoreTotal(score);
        }
        return CabinUpObjectConvert.ToTicketOrderInfoUpList(passSegInfo, passType, pnr, "UPGRADE");//机票订单信息列表
    }

    /**
     * 新的升舱子订单
     * 国际
     *
     * @return
     */
    private List<PtTicketOrderInfo> createIUpgradeTicketOrderInfo(int score, String ticketNo, String interFlag,
                                                                  List<FlightInfo> flightInfoList, List<PassengerInfo> passengerInfoList, String pnr, String passType,
                                                                  PtMemberDetail ptMemberDetail, List<SegmentShow> segmentShowList) {
        PassSegInfo passSegInfo = createPassSegInfo(ptMemberDetail, ticketNo, passengerInfoList.get(0).getPassengerName());
        passSegInfo.setPassengerInfoList(passengerInfoList);
        passSegInfo.setFlightInfoList(flightInfoList);
        passSegInfo.setInterFlag(interFlag);
        passSegInfo.setUseScoreTotal(0);
        return CabinUpObjectConvert.toITicketOrderInfoUpList(passSegInfo, passType, pnr, "UPGRADE", segmentShowList, handConfig);//机票订单信息列表
    }

    //创建基本请求类
    private PassSegInfo createPassSegInfo(PtMemberDetail ptMemberDetail, String ticketNo, String passName) {
        PassSegInfo passSegInfo = new PassSegInfo();
        MemberBasicInfoSoaModel memberQueryInfoType = ptMemberDetail.getBasicInfo();
        String name = memberQueryInfoType.getCLastName() + memberQueryInfoType.getCFirstName();
        if (StringUtil.isNullOrEmpty(name)) {//会员姓名为空的情况下赋值乘客姓名
            name = passName;
        }
        passSegInfo.setLinker(name);
        String phone = "";
        if (!StringUtil.isNullOrEmpty(ptMemberDetail.getContactInfo())) {
            for (MemberContactSoaModel cust : ptMemberDetail.getContactInfo()) {
                int custType = cust.getContactType();
                if (ContactTypeEnum.MOBILE.getCode() == custType) {
                    phone = cust.getContactNumber();
                }
            }
        }
        passSegInfo.setLinkerHandphone(phone);
        passSegInfo.setFfpId(String.valueOf(memberQueryInfoType.getMemberId()));
        passSegInfo.setSfCardNo("HO" + memberQueryInfoType.getCardNO());
        passSegInfo.setFfpCardType("Member");
        passSegInfo.setTicketNo(ticketNo);
        return passSegInfo;
    }

    //处理客票的航段信息  //根据客票生成的默认信息
    private List<FlightInfo> ticketInfoToFlight(PtIBETicketInfo ibeTicketInfo, String passType) {
        List<FlightInfo> flightInfoList = new ArrayList<>();
        int i = 0;
        //国内航班采取机建，燃油平分方式
        double cn = 0;
        double yq = 0;
        if (HandlerConstants.TRIP_TYPE_D.equals(ibeTicketInfo.getInterFlag())) {
            int segCnt = ibeTicketInfo.getSegmentInfoList().size();
            cn = ibeTicketInfo.getCN() / (double) segCnt;
            yq = ibeTicketInfo.getYQ() / (double) segCnt;
        }
        String classSet = handConfig.getCabinClass();
        //起点与终点一致认为其是往返客票
        int segCount = ibeTicketInfo.getSegmentInfoList().size();
        String routeType = HandlerConstants.ROUTE_TYPE_OW;
        if (ibeTicketInfo.getOrgCity().equals(ibeTicketInfo.getDstCity())) {
            routeType = HandlerConstants.ROUTE_TYPE_RT;
        }
        for (PtSegmentInfo seg : ibeTicketInfo.getSegmentInfoList()) {
            AirPortInfoDto deptAirPort = localCacheService.getLocalAirport(seg.getDepAirportCode());
            AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(seg.getArrAirportCode());
            List<CabinFare> cabinFareList = new ArrayList<>();
            FlightInfo flightInfo = new FlightInfo();
            BeanUtils.copyProperties(seg, flightInfo);
            flightInfo.setFlightDirection(FlightDirection.GO.getCode());
            if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && seg.getSegmentIndex() > (segCount / 2)) {
                flightInfo.setFlightDirection(FlightDirection.BACK.getCode());
            }
            flightInfo.setId(String.valueOf(i));
            flightInfo.setFlightDate(separateTime(seg.getDepTime())[0]);
            flightInfo.setDepDateTime(seg.getDepTime());
            flightInfo.setArrDateTime(seg.getArrTime());
            flightInfo.setDepCity(deptAirPort.getCityCode());
            flightInfo.setArrCity(arrAirPort.getCityCode());
            flightInfo.setDepAirport(seg.getDepAirportCode());
            flightInfo.setArrAirport(seg.getArrAirportCode());
            flightInfo.setCabinCode(seg.getCabin());
            flightInfo.setPriceValue(seg.getTicketPrice());
            flightInfo.setYQTax(yq);
            flightInfo.setCNTax(cn);
            //拼造舱位价格
            CabinFare cabinFare = new CabinFare();
            cabinFare.setID("0");
            cabinFare.setCabinCode(seg.getCabin());
            cabinFare.setCabinClass(CommonUtil.getCabinClassByCabinCode(seg.getCabin(), classSet));
            cabinFare.setPassengerType(passType);
            if (HandlerConstants.TRIP_TYPE_I.equals(ibeTicketInfo.getInterFlag())) {
                flightInfo.setYQTax(-1);
                flightInfo.setCNTax(-1);
                cabinFare.setCNTax(-1);
                cabinFare.setYQTax(-1);
            }
            if (HandlerConstants.TRIP_TYPE_D.equals(ibeTicketInfo.getInterFlag())) {
                cabinFare.setCNTax(cn);
                cabinFare.setYQTax(yq);
            }
            cabinFare.setPriceValue(seg.getTicketPrice());//处理价格
            cabinFare.setRSP(cabinFare.getPriceValue());
            cabinFareList.add(cabinFare);
            cabinFareToFlight(cabinFareList, flightInfo, passType);
            flightInfoList.add(flightInfo);
            i++;
        }
        return flightInfoList;
    }

    //处理舱位价格
    private FlightInfo cabinFareToFlight(List<CabinFare> cabinFareList, FlightInfo flightInfo, String passType) {
        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passType)) {
            flightInfo.setCabinFareList(cabinFareList);
        } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passType)) {
            flightInfo.setCabinGMJCFareList(cabinFareList);
        } else {
            flightInfo.setCabinCHDINFFareList(cabinFareList);
        }
        return flightInfo;
    }

    /**
     * 生成升舱的航段信息
     *
     * @return
     */
    private List<FlightInfo> createUpFlightInfo(UpClassBookOrderReq upClassBookOrderReq, String passType) {
        UpFlightInfo upFlightInfo = upClassBookOrderReq.getUpFlightInfo();
        List<UpAVFlightInfo> upAVFlightInfoList = upFlightInfo.getUpAVFlightInfoList();
        List<FlightInfo> flightInfoListUp = new ArrayList<>();
        upAVFlightInfoList.stream().forEach(flightInfo -> {
            boolean upFlag = flightInfo.getFlightNo().equals(upFlightInfo.getFlightNo()) ? true : false;
            FlightInfo temp = formatFlightInfo(flightInfo, upFlag, passType, upFlightInfo.getUpCouponCode());
            flightInfoListUp.add(temp);
        });
        return flightInfoListUp;
    }

    /**
     * 构建新的航段信息
     *
     * @return
     */
    private List<FlightInfo> createUpFlightInfoV2(UpClassOrderReq upClassOrderReq, String passType) {
        UpTicketInfo upTicketInfo = upClassOrderReq.getUpTicketInfo();
        List<SegmentShow> segmentShowList = upTicketInfo.getSegmentShowList();
        List<UpAVFlightInfo> upAVFlightInfoList = upClassOrderReq.getUpAVFlightInfoList();
        List<FlightInfo> flightInfoListUp = new ArrayList<>();
        upAVFlightInfoList.forEach(flightInfo -> {
            //判断是否升舱航段
            SegmentShow segmentShow = segmentShowList.stream().filter(seg -> seg.getFlightNo().equals(flightInfo.getFlightNo())).findFirst().orElse(null);
            boolean upFlag = false;
            if (segmentShow != null && segmentShow.isSelectUpFlag()) {
                upFlag = true;
            }
            FlightInfo temp = formatFlightInfo(flightInfo, upFlag, passType, segmentShow.getUpCouponCode());
            flightInfoListUp.add(temp);
        });
        return flightInfoListUp;
    }

    /**
     * 创建国际升舱航段信息
     */
    private List<FlightInfo> createIUpFlightInfoV2(UpClassOrderReq upClassOrderReq, String passType) {
        UpTicketInfo upTicketInfo = upClassOrderReq.getUpTicketInfo();
        List<SegmentShow> segmentShowList = upTicketInfo.getSegmentShowList();
        List<IUpClassFlightInfo> iUpClassFlightInfo = upClassOrderReq.getIUpClassFlightInfoList();
        List<FlightInfo> flightInfoListUp = new ArrayList<>();
        upTicketInfo.getFlightInfoList().stream().forEach(flightInfo -> {
            //判断是否升舱航段
            SegmentShow segmentShow = segmentShowList.stream().filter(seg -> seg.getFlightNo().equals(flightInfo.getFlightNo()))
                    .findFirst().orElse(null);
            boolean upFlag = false;
            String cabinCode;
            if (segmentShow.isSelectUpFlag()) {
                upFlag = true;
                Optional<IUpClassFlightInfo> optional = iUpClassFlightInfo.stream().filter(upClass -> upClass.getFlightNo().equals(flightInfo.getFlightNo())).findFirst();
                if (optional.isPresent()) {
                    cabinCode = optional.get().getCabinCode();
                } else {
                    throw new OperationFailedException("升舱舱位信息不正确");
                }
            } else {
                cabinCode = segmentShow.getCabin();
            }
            FlightInfo flight = upTicketInfo.getFlightInfoList().stream()
                    .filter(flight1 -> flight1.getFlightNo().equals(flightInfo.getFlightNo())).findFirst().orElse(null);
            FlightInfo temp = new FlightInfo();
            BeanUtils.copyNotNullProperties(flight, temp);
            temp.setCabinCode(cabinCode);
            temp.setUpgradeFlag(upFlag);
            flightInfoListUp.add(temp);
        });
        return flightInfoListUp;
    }

    /**
     * 封装基础的FlightInfo
     *
     * @param flightInfo
     * @param upFlag
     * @param passType
     * @return
     */
    private FlightInfo formatFlightInfo(UpAVFlightInfo flightInfo, boolean upFlag, String passType, String couponCode) {
        FlightInfo temp = new FlightInfo();
        BeanUtils.copyProperties(flightInfo, temp);
        //升舱航班
        if (upFlag) {
            List<CabinFare> cabinFareList = new ArrayList<>();
            CabinFare cabinFare = new CabinFare();
            UpgradeCabinFare upgradeCabinFare = flightInfo.getUpgradeCabinFareList().get(0);
            Fare fare = flightInfo.getFareDic().get(upgradeCabinFare.getFareID());
            BeanUtils.copyProperties(upgradeCabinFare, cabinFare);
            BeanUtils.copyProperties(fare, cabinFare);
            cabinFare.setFareID(fare.getFareID());
            cabinFareList.add(cabinFare);
            if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passType)) {
                temp.setCabinFareList(cabinFareList);
            } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passType)) {
                temp.setCabinCHDINFFareList(cabinFareList);
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passType)) {
                temp.setCabinGMJCFareList(cabinFareList);
            }
            temp.setCabinCode(upgradeCabinFare.getCabinCode());
            temp.setUpgradeFlag(true);
            temp.setUpgradeTicketPrice(upgradeCabinFare.getPriceDiff());//票面差价
            //使用升舱券
            if (!StringUtil.isNullOrEmpty(couponCode)) {
                temp.setUpCouponCode(couponCode);
            }
        }
        return temp;
    }

    /**
     * 部分航班信息同步
     * flightInfoList  历史航段
     * flightInfoList_up  升舱航段
     *
     * @param
     * @return
     */
    private void syncFlightInfoOld(List<FlightInfo> flightInfoList, List<FlightInfo> flightInfoListUp) {
        for (FlightInfo flightInfoUp : flightInfoListUp) {
            for (FlightInfo flightInfo : flightInfoList) {
                if (flightInfoUp.getFlightNo().equals(flightInfo.getFlightNo())) {
                    flightInfo.setFType(flightInfoUp.getFType());
                    flightInfo.setCarrierNo(flightInfoUp.getCarrierNo());
                    flightInfo.setMealCode(flightInfoUp.getMealCode());
                    //如果不是升舱段，需将新的运价信息用的老的代替，以消除不升舱的价格差价问题
                    if (flightInfoUp.getUpgradeFlag() == null || !flightInfoUp.getUpgradeFlag()) {
                        flightInfoUp.setCabinFareList(flightInfo.getCabinFareList());
                        flightInfoUp.setCabinGMJCFareList(flightInfo.getCabinGMJCFareList());
                        flightInfoUp.setCabinCHDINFFareList(flightInfo.getCabinCHDINFFareList());
                        flightInfoUp.setUpgradeFlag(false);
                    }
                }
            }
        }
    }

    /**
     * @param channelCode
     * @return
     */
    private UpTicketInfo queryUpCoupon(UpTicketInfo upTicketInfo, String channelCode, String ip, String ffpId, String ffpCardNo) {
        //客票允许升舱的情况下才执行此操作
        if (upTicketInfo.isUpAble()) {
            Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(channelCode, ip);
            Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(channelCode, ip);
            List<SegmentShow> segmentShowList = upTicketInfo.getSegmentShowList().stream().map(segmentShow -> {
                if (upTicketInfo.isUpAble() && segmentShow.isIfUp()) {
                    //需使用升舱券的舱位
                    String upCouponCabin = "";
                    if (HandlerConstants.TRIP_TYPE_I.equals(upTicketInfo.getInterFlag())) {
                        upCouponCabin = segmentShow.isUnlimitedUpClass() ? handConfig.getUnlimitedUpClassCabin() : handConfig.getSupportUpCouponCabinI();
                    } else {
                        upCouponCabin = handConfig.getSupportUpCouponCabin();
                    }
                    //标记是否需要查询升舱券信息 默认查询
                    boolean flag = true;
                    if (CollectionUtils.isNotEmpty(segmentShow.getCabinList())) {
                        if (HandlerConstants.TRIP_TYPE_I.equals(upTicketInfo.getInterFlag())) {
                            boolean cabinEnough = false;
                            for (CabinNumInfo cabinNumInfo : segmentShow.getCabinList()) {
                                if (upCouponCabin.contains(cabinNumInfo.getCabin())) {
                                    cabinEnough = true;
                                    break;
                                }
                            }
                            if (!cabinEnough) {
                                segmentShow.setIfUp(false);
                                segmentShow.setNotUpReason("公务舱(" + upCouponCabin + ")舱位已售罄");
                            }
                        }
                        if (StringUtil.isNullOrEmpty(upCouponCabin)) {
                            flag = false;
                            segmentShow.setCouponNum(9999);
                        } else {
                            // 普通升舱
                            if (HandlerConstants.TRIP_TYPE_I.equals(upTicketInfo.getInterFlag()) && !segmentShow.isUnlimitedUpClass()) {
                                segmentShow.setCabinList(segmentShow.getCabinList().stream().filter(cabinNumInfo ->
                                        handConfig.getSupportUpCouponCabinI().equals(cabinNumInfo.getCabin())).collect(Collectors.toList()));
                            }
                            for (CabinNumInfo cabinNumInfo : segmentShow.getCabinList()) {
                                //检验当前剩余的舱位是否包含自费升舱舱位  自费舱位权益券数量无限制
                                if (!upCouponCabin.contains(cabinNumInfo.getCabin())) {
                                    flag = false;
                                    segmentShow.setCouponNum(9999);
                                    break;
                                }
                            }
                        }
                    } else {
                        //没有返回具体的舱位明细时默认不查询券信息
                        flag = false;
                        segmentShow.setCouponNum(9999);
                        if (HandlerConstants.TRIP_TYPE_I.equals(upTicketInfo.getInterFlag())) {
                            flag = true;
                            segmentShow.setCouponNum(0);
                        }
                    }
                    //不包含自费舱位
                    if (flag) {
                        UpFlightInfo upFlightInfo = new UpFlightInfo();
                        BeanUtils.copyProperties(segmentShow, upFlightInfo);
                        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = CabinUpObjectConvert.createCouponProductGetRequestDto(upFlightInfo, channelCode, ffpId, ffpCardNo);
                        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_MY_PRODUCT_V2;
                        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                        HttpResult httpResult = this.doPostClient(ptCouponProductGetRequestDto, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                        if (httpResult.isResult() && !StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                            try {
                                PtCouponProductGetResponseDto ptCouponProductGetResponseDto =
                                        (PtCouponProductGetResponseDto) JsonUtil.jsonToBean(httpResult.getResponse(), PtCouponProductGetResponseDto.class);
                                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductGetResponseDto.getResultCode())) {
                                    List<AvailCoupon> availCouponList = RightCouponConvert.formatAvailCouponList("R",
                                            ptCouponProductGetResponseDto.getVouchers(), cityInfoMap, airPortInfoMap, handConfig);
                                    segmentShow.setCouponNum(availCouponList.size());
                                    // 非无限升舱卡需校验升舱券
                                    if (!segmentShow.isUnlimitedUpClass()) {
                                        //是否存在正常的升舱券
                                        Optional<AvailCoupon> optional = availCouponList.stream().filter(availCoupon -> !availCoupon.isUnlimitedUpClass()).findFirst();
                                        //不存在普通升舱券
                                        if (!optional.isPresent()) {
                                            segmentShow.setBuyCoupon(true);
                                            segmentShow.setIfUp(false);
                                            segmentShow.setNotUpReason(segmentShow.isUnlimitedCardFlightOver3() ?
                                                    "抱歉！您的账户中已存在3段使用无限升舱卡升舱航段，请在使用一段航程后再办理其他升舱业务。"
                                                    : "该航班仅支持使用升舱券办理升舱，请先购买升舱券");
                                        }
                                    }
                                } else {
                                    if (ptCouponProductGetResponseDto.getCount() <= 0) {
                                        segmentShow.setBuyCoupon(true);
                                        segmentShow.setIfUp(false);
                                        segmentShow.setNotUpReason("您查询的客票仅支持使用升舱券升舱，您还未购买升舱券，请先购买后再升舱");
                                    }
                                }
                            } catch (Exception e) {
                                log.error("【升舱-查询可用升舱券数量出现异常】", e);
                            }
                        }
                    }
                }
                return segmentShow;
            }).collect(Collectors.toList());
            //判断是否存在允许升舱的航段，有的情况展示升舱按钮
            boolean ifup = segmentShowList.stream().anyMatch(SegmentShow::isIfUp);
            upTicketInfo.setUpAble(ifup);
            if (!ifup) {
                segmentShowList.stream().filter(segmentShow -> !segmentShow.isIfUp()).findFirst()
                        .ifPresent(notUpSegmentShow -> upTicketInfo.setNotUpReason(notUpSegmentShow.getNotUpReason()));
            }
            upTicketInfo.setSegmentShowList(segmentShowList);
        }
        return upTicketInfo;
    }

    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    /**
     * 升舱卡校验
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "upgradeCardCheck", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "升舱卡校验", notes = "升舱卡校验")
    @InterfaceLog
    public BaseResp upgradeCardCheck(@RequestBody BaseReq<UserInfoMust> req, HttpServletRequest request) {
        BaseResp<String> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = this.unlimitedFlyService.listUpClassCardV2BindRecord(req.getChannelCode(), req.getRequest().getFfpId(), ip, req.getRequest().getFfpCardNo());
            if (CollectionUtils.isEmpty(unlimitedFlyV2BindRecords)) {
                resp.setResultCode(WSEnum.UPCLASSCARD_NOT_PURCHASED.getResultCode());
                resp.setResultInfo(WSEnum.UPCLASSCARD_NOT_PURCHASED.getResultInfo());
                return resp;
            }
            if (unlimitedFlyV2BindRecords.stream().noneMatch(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus()))) {
                resp.setResultCode(WSEnum.UPCLASSCARD_UNBIND.getResultCode());
                resp.setResultInfo(WSEnum.UPCLASSCARD_UNBIND.getResultInfo());
                return resp;
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

}
