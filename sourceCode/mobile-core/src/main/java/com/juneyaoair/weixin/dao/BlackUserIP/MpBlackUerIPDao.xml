<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="MpBlackUerIPDao">

<!--auto generated Code-->
    <resultMap id="AllColumnMap" type="com.juneyaoair.weixin.bean.BlackUserIP.MpBlackUerIP">
        <result column="id" property="id"/>
        <result column="ip" property="ip"/>
        <result column="mark" property="mark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="last_update_user_id" property="lastUpdateUserId"/>
        <result column="last_update_user_name" property="lastUpdateUserName"/>
    </resultMap>

<!--auto generated Code-->
    <sql id="all_column">
        id,
        ip,
        mark,
        create_time,
        create_user_id,
        create_user_name,
        last_update_time,
        last_update_user_id,
        last_update_user_name
    </sql>
    <sql id="all_column_log">
        id,
        blackUserId,
        ip,
        method,
        urlPath,
        createTime
    </sql>

<!--auto generated Code-->
    <insert id="insert" parameterType="blackUserIP">
        INSERT INTO mp_black_uer_ip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null"> id, </if>
            <if test="ip != null"> ip, </if>
            <if test="mark != null"> mark, </if>
            create_time,
            <if test="createUserId != null"> create_user_id, </if>
            <if test="createUserName != null"> create_user_name, </if>
            <if test="lastUpdateTime != null"> last_update_time, </if>
            <if test="lastUpdateUserId != null"> last_update_user_id, </if>
            <if test="lastUpdateUserName != null"> last_update_user_name, </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null"> #{id}, </if>
            <if test="ip != null"> #{ip}, </if>
            <if test="mark != null"> #{mark}, </if>
           sysdate,
            <if test="createUserId != null"> #{createUserId}, </if>
            <if test="createUserName != null"> #{createUserName}, </if>
            <if test="lastUpdateTime != null"> #{lastUpdateTime}, </if>
            <if test="lastUpdateUserId != null"> #{lastUpdateUserId}, </if>
            <if test="lastUpdateUserName != null"> #{lastUpdateUserName}, </if>
        </trim>
    </insert>

<!--auto generated Code-->
    <insert id="insertList" parameterType="blackUserIP">
        INSERT INTO mp_black_uer_ip(
        <include refid="all_column"/>
        )VALUES
        <foreach collection="pojos" item="pojo" index="index" separator=",">
            (
            #{id},
            #{ip},
            #{mark},
            #{createTime},
            #{createUserId},
            #{createUserName},
            #{lastUpdateTime},
            #{lastUpdateUserId},
            #{lastUpdateUserName}
            )
        </foreach>
    </insert>

<!--auto generated Code-->
    <update id="update" parameterType="blackUserIP">
        UPDATE mp_black_uer_ip
        <set>
            <if test="id != null"> id = #{id}, </if>
            <if test="ip != null"> ip = #{ip}, </if>
            <if test="mark != null"> mark = #{mark}, </if>
            <if test="lastUpdateTime != null"> last_update_time = sysdate, </if>
            <if test="lastUpdateUserId != null"> last_update_user_id = #{lastUpdateUserId}, </if>
            <if test="lastUpdateUserName != null"> last_update_user_name = #{lastUpdateUserName}, </if>
        </set>
         WHERE id = #{id}
    </update>

<!--auto generated Code-->
    <select id="select" resultMap="AllColumnMap" parameterType="blackUserIP">
        SELECT <include refid="all_column"/>
        FROM mp_black_uer_ip
        <where>
            <if test="id != null"> AND id = #{id} </if>
            <if test="ip != null"> AND ip = #{ip} </if>
            <if test="mark != null"> AND mark = #{mark} </if>
            <if test="createTime != null">
                AND create_time &gt;= #{createTimeS}
                and create_time &lt;= #{createTimeE}
            </if>
            <if test="createUserId != null"> AND create_user_id = #{createUserId} </if>
            <if test="createUserName != null"> AND create_user_name = #{createUserName} </if>
            <if test="lastUpdateTime != null">
                AND last_update_time &gt;= #{lastUpdateTimeS}
                AND last_update_time &lt;= #{lastUpdateTimeE}
            </if>
            <if test="lastUpdateUserId != null"> AND last_update_user_id = #{lastUpdateUserId} </if>
            <if test="lastUpdateUserName != null"> AND last_update_user_name = #{lastUpdateUserName} </if>
        </where>

    </select>

<!--&lt;!&ndash;auto generated Code&ndash;&gt;-->
    <!--<delete id="delete">-->
        <!--DELETE FROM mp_black_uer_ip where id = #{id}-->
    <!--</delete>-->

    <!--auto generated Code-->
    <insert id="insertLog" parameterType="blackUserVisitLog">
        INSERT INTO MP_BLACK_USER_VISIT_LOGS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null"> id, </if>
            <if test="blackUserId != null"> BLACK_USER_ID, </if>
            <if test="ip != null"> IP, </if>
            <if test="method != null"> METHOD, </if>
            <if test="urlPath != null"> URL_PATH, </if>
             CREATE_TIME
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null"> #{id}, </if>
            <if test="blackUserId != null"> #{blackUserId}, </if>
            <if test="ip != null"> #{ip}, </if>
            <if test="method != null"> #{method}, </if>
            <if test="urlPath != null"> #{urlPath}, </if>
             sysdate
        </trim>
    </insert>

    <select id="selectLogs" parameterType="blackUserVisitLog" resultType="blackUserVisitLog">
        SELECT <include refid="all_column_log"/>
        FROM mp_black_uer_ip
        <where>
            <if test="id != null"> AND id = #{id} </if>
            <if test="blackUserId != null"> AND BLACK_USER_ID = #{blackUserId} </if>
            <if test="ip != null"> AND IP = #{ip} </if>
            <if test="method != null"> AND METHOD = #{method} </if>
            <if test="urlPath != null"> AND URL_PATH = #{urlPath} </if>
            <if test="createTime != null">
                AND createTime &gt;= #{createTimeS}
                AND createTime &lt;= #{createTimeE}
            </if>
        </where>
    </select>
</mapper>
