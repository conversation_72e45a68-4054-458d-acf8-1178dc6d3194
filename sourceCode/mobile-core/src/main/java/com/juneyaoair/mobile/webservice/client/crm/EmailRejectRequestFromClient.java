
package com.juneyaoair.mobile.webservice.client.crm;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>EmailRejectRequestFromClient complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="EmailRejectRequestFromClient"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MessageHeader" type="{JuneyaoFFP2013.Service.Schema}RequestMessageHeaderType" minOccurs="0"/&gt;
 *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="member_id" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Reject_type" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EmailRejectRequestFromClient", propOrder = {
    "messageHeader",
    "id",
    "memberId",
    "rejectType"
})
public class EmailRejectRequestFromClient {

    @XmlElement(name = "MessageHeader")
    protected RequestMessageHeaderType messageHeader;
    @XmlElement(name = "ID")
    protected String id;
    @XmlElement(name = "member_id")
    protected String memberId;
    @XmlElement(name = "Reject_type")
    protected String rejectType;

    /**
     * 获取messageHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link RequestMessageHeaderType }
     *     
     */
    public RequestMessageHeaderType getMessageHeader() {
        return messageHeader;
    }

    /**
     * 设置messageHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link RequestMessageHeaderType }
     *     
     */
    public void setMessageHeader(RequestMessageHeaderType value) {
        this.messageHeader = value;
    }

    /**
     * 获取id属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getID() {
        return id;
    }

    /**
     * 设置id属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setID(String value) {
        this.id = value;
    }

    /**
     * 获取memberId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * 设置memberId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMemberId(String value) {
        this.memberId = value;
    }

    /**
     * 获取rejectType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRejectType() {
        return rejectType;
    }

    /**
     * 设置rejectType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRejectType(String value) {
        this.rejectType = value;
    }

}
