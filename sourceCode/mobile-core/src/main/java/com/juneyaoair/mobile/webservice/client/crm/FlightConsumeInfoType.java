
package com.juneyaoair.mobile.webservice.client.crm;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>FlightConsumeInfoType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="FlightConsumeInfoType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MemberID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="CardType" type="{JuneyaoFFP2013.Service.Schema}RedeemCardType"/&gt;
 *         &lt;element name="RedeemType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="PnrCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="PassengerFlightInfo" type="{JuneyaoFFP2013.Service.Schema}PassengerFlightInfoType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="RedeemMiles" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="Telephone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="RedeemModel" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FlightConsumeInfoType", propOrder = {
    "memberID",
    "cardType",
    "redeemType",
    "pnrCode",
    "passengerFlightInfo",
    "redeemMiles",
    "telephone",
    "redeemModel"
})
public class FlightConsumeInfoType {

    @XmlElement(name = "MemberID")
    protected String memberID;
    @XmlElement(name = "CardType", required = true)
    @XmlSchemaType(name = "string")
    protected RedeemCardType cardType;
    @XmlElement(name = "RedeemType")
    protected String redeemType;
    @XmlElement(name = "PnrCode")
    protected String pnrCode;
    @XmlElement(name = "PassengerFlightInfo")
    protected List<PassengerFlightInfoType> passengerFlightInfo;
    @XmlElement(name = "RedeemMiles")
    protected long redeemMiles;
    @XmlElement(name = "Telephone")
    protected String telephone;
    @XmlElement(name = "RedeemModel")
    protected String redeemModel;

    /**
     * 获取memberID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMemberID() {
        return memberID;
    }

    /**
     * 设置memberID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMemberID(String value) {
        this.memberID = value;
    }

    /**
     * 获取cardType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link RedeemCardType }
     *     
     */
    public RedeemCardType getCardType() {
        return cardType;
    }

    /**
     * 设置cardType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link RedeemCardType }
     *     
     */
    public void setCardType(RedeemCardType value) {
        this.cardType = value;
    }

    /**
     * 获取redeemType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRedeemType() {
        return redeemType;
    }

    /**
     * 设置redeemType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRedeemType(String value) {
        this.redeemType = value;
    }

    /**
     * 获取pnrCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPnrCode() {
        return pnrCode;
    }

    /**
     * 设置pnrCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPnrCode(String value) {
        this.pnrCode = value;
    }

    /**
     * Gets the value of the passengerFlightInfo property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the passengerFlightInfo property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPassengerFlightInfo().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PassengerFlightInfoType }
     * 
     * 
     */
    public List<PassengerFlightInfoType> getPassengerFlightInfo() {
        if (passengerFlightInfo == null) {
            passengerFlightInfo = new ArrayList<PassengerFlightInfoType>();
        }
        return this.passengerFlightInfo;
    }

    /**
     * 获取redeemMiles属性的值。
     * 
     */
    public long getRedeemMiles() {
        return redeemMiles;
    }

    /**
     * 设置redeemMiles属性的值。
     * 
     */
    public void setRedeemMiles(long value) {
        this.redeemMiles = value;
    }

    /**
     * 获取telephone属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelephone() {
        return telephone;
    }

    /**
     * 设置telephone属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelephone(String value) {
        this.telephone = value;
    }

    /**
     * 获取redeemModel属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRedeemModel() {
        return redeemModel;
    }

    /**
     * 设置redeemModel属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRedeemModel(String value) {
        this.redeemModel = value;
    }

}
