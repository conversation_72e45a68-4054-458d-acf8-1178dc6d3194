
package com.juneyaoair.mobile.webservice.client.crm;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.juneyaoair.mobile.webservice.client.crm package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _BC2MW001Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_001Request");
    private final static QName _MW2BC001Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_001Response");
    private final static QName _BC2MW002Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_002Request");
    private final static QName _MW2BC002Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_002Response");
    private final static QName _BC2MW003Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_003Request");
    private final static QName _MW2BC003Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_003Response");
    private final static QName _BC2MW004Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_004Request");
    private final static QName _MW2BC004Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_004Response");
    private final static QName _BC2MW005Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_005Request");
    private final static QName _MW2BC005Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_005Response");
    private final static QName _BC2MW006Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_006Request");
    private final static QName _MW2BC006Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_006Response");
    private final static QName _BC2MW007Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_007Request");
    private final static QName _MW2BC007Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_007Response");
    private final static QName _BC2MW008Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_008Request");
    private final static QName _MW2BC008Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_008Response");
    private final static QName _BC2MW009Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_009Request");
    private final static QName _MW2BC009Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_009Response");
    private final static QName _BC2MW010Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_010Request");
    private final static QName _MW2BC010Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_010Response");
    private final static QName _BC2MW011Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_011Request");
    private final static QName _MW2BC011Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_011Response");
    private final static QName _BC2MW012Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_012Request");
    private final static QName _MW2BC012Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_012Response");
    private final static QName _BC2MW013Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_013Request");
    private final static QName _MW2BC013Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_013Response");
    private final static QName _BC2MW014Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_014Request");
    private final static QName _MW2BC014Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_014Response");
    private final static QName _BC2MW015Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_015Request");
    private final static QName _MW2BC015Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_015Response");
    private final static QName _BC2MW016Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_016Request");
    private final static QName _MW2BC016Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_016Response");
    private final static QName _BC2MW017Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_017Request");
    private final static QName _MW2BC017Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_017Response");
    private final static QName _BC2MW018Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_018Request");
    private final static QName _MW2BC018Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_018Response");
    private final static QName _BC2MW019Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_019Request");
    private final static QName _MW2BC019Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_019Response");
    private final static QName _BC2MW020Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_020Request");
    private final static QName _MW2BC020Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_020Response");
    private final static QName _BC2MW021Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_021Request");
    private final static QName _MW2BC021Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_021Response");
    private final static QName _BC2MW022Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_022Request");
    private final static QName _MW2BC022Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_022Response");
    private final static QName _BC2MW023Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_023Request");
    private final static QName _MW2BC023Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_023Response");
    private final static QName _BC2MW024Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_024Request");
    private final static QName _MW2BC024Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_024Response");
    private final static QName _BC2MW025Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_025Request");
    private final static QName _MW2BC025Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_025Response");
    private final static QName _BC2MW026Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_026Request");
    private final static QName _MW2BC026Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_026Response");
    private final static QName _BC2MW027Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_027Request");
    private final static QName _MW2BC027Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_027Response");
    private final static QName _BC2MW028Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_028Request");
    private final static QName _MW2BC028Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_028Response");
    private final static QName _BC2MW029Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_029Request");
    private final static QName _MW2BC029Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_029Response");
    private final static QName _BC2MW030Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_030Request");
    private final static QName _MW2BC030Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_030Response");
    private final static QName _BC2MW031Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_031Request");
    private final static QName _MW2BC031Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_031Response");
    private final static QName _BC2MW032Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_032Request");
    private final static QName _MW2BC032Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_032Response");
    private final static QName _BC2MW033Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_033Request");
    private final static QName _MW2BC033Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_033Response");
    private final static QName _BC2MW034Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_034Request");
    private final static QName _MW2BC034Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_034Response");
    private final static QName _BC2MW035Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_035Request");
    private final static QName _MW2BC035Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_035Response");
    private final static QName _BC2MW036Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_036Request");
    private final static QName _MW2BC036Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_036Response");
    private final static QName _BC2MW037Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_037Request");
    private final static QName _MW2BC037Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_037Response");
    private final static QName _BC2MW038Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_038Request");
    private final static QName _MW2BC038Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_038Response");
    private final static QName _BC2MW039Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_039Request");
    private final static QName _MW2BC039Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_039Response");
    private final static QName _BC2MW040Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_040Request");
    private final static QName _MW2BC040Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_040Response");
    private final static QName _BC2MW041Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_041Request");
    private final static QName _MW2BC041Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_041Response");
    private final static QName _BC2MW042Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_042Request");
    private final static QName _MW2BC042Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_042Response");
    private final static QName _BC2MW043Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_043Request");
    private final static QName _MW2BC043Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_043Response");
    private final static QName _BC2MW046Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_046Request");
    private final static QName _MW2BC046Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_046Response");
    private final static QName _BC2MW047Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_047Request");
    private final static QName _MW2BC047Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_047Response");
    private final static QName _BC2MW048Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_048Request");
    private final static QName _MW2BC048Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_048Response");
    private final static QName _BC2MW049Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_049Request");
    private final static QName _MW2BC049Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_049Response");
    private final static QName _BC2MW050Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_050Request");
    private final static QName _MW2BC050Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_050Response");
    private final static QName _BC2MW051Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_051Request");
    private final static QName _MW2BC051Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_051Response");
    private final static QName _BC2MW052Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_052Request");
    private final static QName _MW2BC052Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_052Response");
    private final static QName _BC2MW053Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_053Request");
    private final static QName _MW2BC053Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_053Response");
    private final static QName _BC2MW054Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_054Request");
    private final static QName _MW2BC054Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_054Response");
    private final static QName _BC2MW055Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_055Request");
    private final static QName _MW2BC055Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_055Response");
    private final static QName _BC2MW056Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_056Request");
    private final static QName _MW2BC056Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_056Response");
    private final static QName _BC2MW057Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_057Request");
    private final static QName _MW2BC057Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_057Response");
    private final static QName _BC2MW058Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_058Request");
    private final static QName _MW2BC058Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_058Response");
    private final static QName _BC2MW059Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_059Request");
    private final static QName _MW2BC059Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_059Response");
    private final static QName _BC2MW060Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_060Request");
    private final static QName _MW2BC060Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_060Response");
    private final static QName _BC2MW061Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_061Request");
    private final static QName _MW2BC061Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_061Response");
    private final static QName _BC2MW062Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_062Request");
    private final static QName _MW2BC062Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_062Response");
    private final static QName _BC2MW063Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_063Request");
    private final static QName _MW2BC063Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_063Response");
    private final static QName _BC2MW064Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_064Request");
    private final static QName _MW2BC064Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_064Response");
    private final static QName _BC2MW065Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_065Request");
    private final static QName _MW2BC065Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_065Response");
    private final static QName _BC2MW066Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_066Request");
    private final static QName _MW2BC066Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_066Response");
    private final static QName _BC2MW067Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_067Request");
    private final static QName _MW2BC067Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_067Response");
    private final static QName _BC2MW068Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_068Request");
    private final static QName _MW2BC068Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_068Response");
    private final static QName _BC2MW069Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_069Request");
    private final static QName _MW2BC069Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_069Response");
    private final static QName _BC2MW070Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_070Request");
    private final static QName _MW2BC070Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_070Response");
    private final static QName _BC2MW071Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_071Request");
    private final static QName _MW2BC071Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_071Response");
    private final static QName _BC2MW072Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_072Request");
    private final static QName _MW2BC072Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_072Response");
    private final static QName _BC2MW073Request_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "BC2MW_073Request");
    private final static QName _MW2BC073Response_QNAME = new QName("JuneyaoFFP2013.Service.Schema", "MW2BC_073Response");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.juneyaoair.mobile.webservice.client.crm
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link MemberRegistRequestFromClient }
     * 
     */
    public MemberRegistRequestFromClient createMemberRegistRequestFromClient() {
        return new MemberRegistRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberRegistResponseForClient }
     * 
     */
    public MemberRegistResponseForClient createMemberRegistResponseForClient() {
        return new MemberRegistResponseForClient();
    }

    /**
     * Create an instance of {@link MemberRegistMostRequestFromClient }
     * 
     */
    public MemberRegistMostRequestFromClient createMemberRegistMostRequestFromClient() {
        return new MemberRegistMostRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberRegistMostResponseForClient }
     * 
     */
    public MemberRegistMostResponseForClient createMemberRegistMostResponseForClient() {
        return new MemberRegistMostResponseForClient();
    }

    /**
     * Create an instance of {@link MemberLoginRequestFromClient }
     * 
     */
    public MemberLoginRequestFromClient createMemberLoginRequestFromClient() {
        return new MemberLoginRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberLoginResponseForClient }
     * 
     */
    public MemberLoginResponseForClient createMemberLoginResponseForClient() {
        return new MemberLoginResponseForClient();
    }

    /**
     * Create an instance of {@link B2CLoginRequestFromClient }
     * 
     */
    public B2CLoginRequestFromClient createB2CLoginRequestFromClient() {
        return new B2CLoginRequestFromClient();
    }

    /**
     * Create an instance of {@link B2CLoginResponseForClient }
     * 
     */
    public B2CLoginResponseForClient createB2CLoginResponseForClient() {
        return new B2CLoginResponseForClient();
    }

    /**
     * Create an instance of {@link MemberInfoQueryRequestFromClient }
     * 
     */
    public MemberInfoQueryRequestFromClient createMemberInfoQueryRequestFromClient() {
        return new MemberInfoQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberInfoQueryResponseForClient }
     * 
     */
    public MemberInfoQueryResponseForClient createMemberInfoQueryResponseForClient() {
        return new MemberInfoQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberAddressInfoUpdateRequestFromClient }
     * 
     */
    public MemberAddressInfoUpdateRequestFromClient createMemberAddressInfoUpdateRequestFromClient() {
        return new MemberAddressInfoUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberAddressInfoUpdateResponseForClient }
     * 
     */
    public MemberAddressInfoUpdateResponseForClient createMemberAddressInfoUpdateResponseForClient() {
        return new MemberAddressInfoUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link MemberContactInfoUpdateRequestFromClient }
     * 
     */
    public MemberContactInfoUpdateRequestFromClient createMemberContactInfoUpdateRequestFromClient() {
        return new MemberContactInfoUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberContactInfoUpdateResponseForClient }
     * 
     */
    public MemberContactInfoUpdateResponseForClient createMemberContactInfoUpdateResponseForClient() {
        return new MemberContactInfoUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link MemberRebuildPasswordRequestFromClient }
     * 
     */
    public MemberRebuildPasswordRequestFromClient createMemberRebuildPasswordRequestFromClient() {
        return new MemberRebuildPasswordRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberRebuildPasswordResponseForClient }
     * 
     */
    public MemberRebuildPasswordResponseForClient createMemberRebuildPasswordResponseForClient() {
        return new MemberRebuildPasswordResponseForClient();
    }

    /**
     * Create an instance of {@link MemberChangePasswordRequestFromClient }
     * 
     */
    public MemberChangePasswordRequestFromClient createMemberChangePasswordRequestFromClient() {
        return new MemberChangePasswordRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberChangePasswordResponseForClient }
     * 
     */
    public MemberChangePasswordResponseForClient createMemberChangePasswordResponseForClient() {
        return new MemberChangePasswordResponseForClient();
    }

    /**
     * Create an instance of {@link MemberAccountQueryRequestFromClient }
     * 
     */
    public MemberAccountQueryRequestFromClient createMemberAccountQueryRequestFromClient() {
        return new MemberAccountQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberAccountQueryResponseForClient }
     * 
     */
    public MemberAccountQueryResponseForClient createMemberAccountQueryResponseForClient() {
        return new MemberAccountQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MileageRetroRequestFromClient }
     * 
     */
    public MileageRetroRequestFromClient createMileageRetroRequestFromClient() {
        return new MileageRetroRequestFromClient();
    }

    /**
     * Create an instance of {@link MileageRetroResponseForClient }
     * 
     */
    public MileageRetroResponseForClient createMileageRetroResponseForClient() {
        return new MileageRetroResponseForClient();
    }

    /**
     * Create an instance of {@link PurchaseMileageApplyRequestFromClient }
     * 
     */
    public PurchaseMileageApplyRequestFromClient createPurchaseMileageApplyRequestFromClient() {
        return new PurchaseMileageApplyRequestFromClient();
    }

    /**
     * Create an instance of {@link PurchaseMileageApplyResponseForClient }
     * 
     */
    public PurchaseMileageApplyResponseForClient createPurchaseMileageApplyResponseForClient() {
        return new PurchaseMileageApplyResponseForClient();
    }

    /**
     * Create an instance of {@link PurchaseMileageConfirmOrCancelRequestFromClient }
     * 
     */
    public PurchaseMileageConfirmOrCancelRequestFromClient createPurchaseMileageConfirmOrCancelRequestFromClient() {
        return new PurchaseMileageConfirmOrCancelRequestFromClient();
    }

    /**
     * Create an instance of {@link PurchaseMileageConfirmOrCancelResponseForClient }
     * 
     */
    public PurchaseMileageConfirmOrCancelResponseForClient createPurchaseMileageConfirmOrCancelResponseForClient() {
        return new PurchaseMileageConfirmOrCancelResponseForClient();
    }

    /**
     * Create an instance of {@link VerifyConsumePasswdRequestFromClient }
     * 
     */
    public VerifyConsumePasswdRequestFromClient createVerifyConsumePasswdRequestFromClient() {
        return new VerifyConsumePasswdRequestFromClient();
    }

    /**
     * Create an instance of {@link VerifyConsumePasswdResponseForClient }
     * 
     */
    public VerifyConsumePasswdResponseForClient createVerifyConsumePasswdResponseForClient() {
        return new VerifyConsumePasswdResponseForClient();
    }

    /**
     * Create an instance of {@link FlightConsumeApplyRequestFromClient }
     * 
     */
    public FlightConsumeApplyRequestFromClient createFlightConsumeApplyRequestFromClient() {
        return new FlightConsumeApplyRequestFromClient();
    }

    /**
     * Create an instance of {@link FlightConsumeApplyResponseForClient }
     * 
     */
    public FlightConsumeApplyResponseForClient createFlightConsumeApplyResponseForClient() {
        return new FlightConsumeApplyResponseForClient();
    }

    /**
     * Create an instance of {@link FlightConsumeConfirmOrCancelRequestFromClient }
     * 
     */
    public FlightConsumeConfirmOrCancelRequestFromClient createFlightConsumeConfirmOrCancelRequestFromClient() {
        return new FlightConsumeConfirmOrCancelRequestFromClient();
    }

    /**
     * Create an instance of {@link FlightConsumeConfirmOrCancelResponseForClient }
     * 
     */
    public FlightConsumeConfirmOrCancelResponseForClient createFlightConsumeConfirmOrCancelResponseForClient() {
        return new FlightConsumeConfirmOrCancelResponseForClient();
    }

    /**
     * Create an instance of {@link FlightRedeemRuleRequestFromClient }
     * 
     */
    public FlightRedeemRuleRequestFromClient createFlightRedeemRuleRequestFromClient() {
        return new FlightRedeemRuleRequestFromClient();
    }

    /**
     * Create an instance of {@link FlightRedeemRuleResponseForClient }
     * 
     */
    public FlightRedeemRuleResponseForClient createFlightRedeemRuleResponseForClient() {
        return new FlightRedeemRuleResponseForClient();
    }

    /**
     * Create an instance of {@link FlightRedeemApplyRequestFromClient }
     * 
     */
    public FlightRedeemApplyRequestFromClient createFlightRedeemApplyRequestFromClient() {
        return new FlightRedeemApplyRequestFromClient();
    }

    /**
     * Create an instance of {@link FlightRedeemApplyResponseForClient }
     * 
     */
    public FlightRedeemApplyResponseForClient createFlightRedeemApplyResponseForClient() {
        return new FlightRedeemApplyResponseForClient();
    }

    /**
     * Create an instance of {@link FlightRedeemConfirmOrCancelRequestFromClient }
     * 
     */
    public FlightRedeemConfirmOrCancelRequestFromClient createFlightRedeemConfirmOrCancelRequestFromClient() {
        return new FlightRedeemConfirmOrCancelRequestFromClient();
    }

    /**
     * Create an instance of {@link NonFlightRedeemRuleRequestFromClient }
     * 
     */
    public NonFlightRedeemRuleRequestFromClient createNonFlightRedeemRuleRequestFromClient() {
        return new NonFlightRedeemRuleRequestFromClient();
    }

    /**
     * Create an instance of {@link NonFlightRedeemRuleResponseForClient }
     * 
     */
    public NonFlightRedeemRuleResponseForClient createNonFlightRedeemRuleResponseForClient() {
        return new NonFlightRedeemRuleResponseForClient();
    }

    /**
     * Create an instance of {@link NonFlightRedeemApplyRequestFromClient }
     * 
     */
    public NonFlightRedeemApplyRequestFromClient createNonFlightRedeemApplyRequestFromClient() {
        return new NonFlightRedeemApplyRequestFromClient();
    }

    /**
     * Create an instance of {@link NonFlightRedeemApplyResponseForClient }
     * 
     */
    public NonFlightRedeemApplyResponseForClient createNonFlightRedeemApplyResponseForClient() {
        return new NonFlightRedeemApplyResponseForClient();
    }

    /**
     * Create an instance of {@link NonFlightRedeemConfirmOrCancelRequestFromClient }
     * 
     */
    public NonFlightRedeemConfirmOrCancelRequestFromClient createNonFlightRedeemConfirmOrCancelRequestFromClient() {
        return new NonFlightRedeemConfirmOrCancelRequestFromClient();
    }

    /**
     * Create an instance of {@link NonFlightRedeemConfirmOrCancelResponseForClient }
     * 
     */
    public NonFlightRedeemConfirmOrCancelResponseForClient createNonFlightRedeemConfirmOrCancelResponseForClient() {
        return new NonFlightRedeemConfirmOrCancelResponseForClient();
    }

    /**
     * Create an instance of {@link FlightConsumeRefundRequestFromClient }
     * 
     */
    public FlightConsumeRefundRequestFromClient createFlightConsumeRefundRequestFromClient() {
        return new FlightConsumeRefundRequestFromClient();
    }

    /**
     * Create an instance of {@link FlightConsumeRefundResponseForClient }
     * 
     */
    public FlightConsumeRefundResponseForClient createFlightConsumeRefundResponseForClient() {
        return new FlightConsumeRefundResponseForClient();
    }

    /**
     * Create an instance of {@link MemberFlightActivityQueryRequestFromClient }
     * 
     */
    public MemberFlightActivityQueryRequestFromClient createMemberFlightActivityQueryRequestFromClient() {
        return new MemberFlightActivityQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberFlightActivityQueryResponseForClient }
     * 
     */
    public MemberFlightActivityQueryResponseForClient createMemberFlightActivityQueryResponseForClient() {
        return new MemberFlightActivityQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberNonFlightActivityQueryRequestFromClient }
     * 
     */
    public MemberNonFlightActivityQueryRequestFromClient createMemberNonFlightActivityQueryRequestFromClient() {
        return new MemberNonFlightActivityQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberNonFlightActivityQueryResponseForClient }
     * 
     */
    public MemberNonFlightActivityQueryResponseForClient createMemberNonFlightActivityQueryResponseForClient() {
        return new MemberNonFlightActivityQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberPromotionQueryRequestFromClient }
     * 
     */
    public MemberPromotionQueryRequestFromClient createMemberPromotionQueryRequestFromClient() {
        return new MemberPromotionQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberPromotionQueryResponseForClient }
     * 
     */
    public MemberPromotionQueryResponseForClient createMemberPromotionQueryResponseForClient() {
        return new MemberPromotionQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberExtraMilesQueryRequestFromClient }
     * 
     */
    public MemberExtraMilesQueryRequestFromClient createMemberExtraMilesQueryRequestFromClient() {
        return new MemberExtraMilesQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberExtraMilesQueryResponseForClient }
     * 
     */
    public MemberExtraMilesQueryResponseForClient createMemberExtraMilesQueryResponseForClient() {
        return new MemberExtraMilesQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberAdjustmentQueryRequestFromClient }
     * 
     */
    public MemberAdjustmentQueryRequestFromClient createMemberAdjustmentQueryRequestFromClient() {
        return new MemberAdjustmentQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberAdjustmentQueryResponseForClient }
     * 
     */
    public MemberAdjustmentQueryResponseForClient createMemberAdjustmentQueryResponseForClient() {
        return new MemberAdjustmentQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberFlightConsumeQueryRequestFromClient }
     * 
     */
    public MemberFlightConsumeQueryRequestFromClient createMemberFlightConsumeQueryRequestFromClient() {
        return new MemberFlightConsumeQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberFlightConsumeQueryResponseForClient }
     * 
     */
    public MemberFlightConsumeQueryResponseForClient createMemberFlightConsumeQueryResponseForClient() {
        return new MemberFlightConsumeQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberNonFlightConsumeQueryRequestFromClient }
     * 
     */
    public MemberNonFlightConsumeQueryRequestFromClient createMemberNonFlightConsumeQueryRequestFromClient() {
        return new MemberNonFlightConsumeQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberNonFlightConsumeQueryResponseForClient }
     * 
     */
    public MemberNonFlightConsumeQueryResponseForClient createMemberNonFlightConsumeQueryResponseForClient() {
        return new MemberNonFlightConsumeQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberExpireMilesQueryRequestFromClient }
     * 
     */
    public MemberExpireMilesQueryRequestFromClient createMemberExpireMilesQueryRequestFromClient() {
        return new MemberExpireMilesQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberExpireMilesQueryResponseForClient }
     * 
     */
    public MemberExpireMilesQueryResponseForClient createMemberExpireMilesQueryResponseForClient() {
        return new MemberExpireMilesQueryResponseForClient();
    }

    /**
     * Create an instance of {@link TempRetroByNetQueryRequestFromClient }
     * 
     */
    public TempRetroByNetQueryRequestFromClient createTempRetroByNetQueryRequestFromClient() {
        return new TempRetroByNetQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link TempRetroByNetQueryResponseForClient }
     * 
     */
    public TempRetroByNetQueryResponseForClient createTempRetroByNetQueryResponseForClient() {
        return new TempRetroByNetQueryResponseForClient();
    }

    /**
     * Create an instance of {@link PurchaseHistoryByNetQueryRequestFromClient }
     * 
     */
    public PurchaseHistoryByNetQueryRequestFromClient createPurchaseHistoryByNetQueryRequestFromClient() {
        return new PurchaseHistoryByNetQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link PurchaseHistoryByNetQueryResponseForClient }
     * 
     */
    public PurchaseHistoryByNetQueryResponseForClient createPurchaseHistoryByNetQueryResponseForClient() {
        return new PurchaseHistoryByNetQueryResponseForClient();
    }

    /**
     * Create an instance of {@link FlightRedeemByNetQueryRequestFromClient }
     * 
     */
    public FlightRedeemByNetQueryRequestFromClient createFlightRedeemByNetQueryRequestFromClient() {
        return new FlightRedeemByNetQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link FlightRedeemByNetQueryResponseForClient }
     * 
     */
    public FlightRedeemByNetQueryResponseForClient createFlightRedeemByNetQueryResponseForClient() {
        return new FlightRedeemByNetQueryResponseForClient();
    }

    /**
     * Create an instance of {@link CreateVerificationCodeRequestFromClient }
     * 
     */
    public CreateVerificationCodeRequestFromClient createCreateVerificationCodeRequestFromClient() {
        return new CreateVerificationCodeRequestFromClient();
    }

    /**
     * Create an instance of {@link CreateVerificationCodeResponseForClient }
     * 
     */
    public CreateVerificationCodeResponseForClient createCreateVerificationCodeResponseForClient() {
        return new CreateVerificationCodeResponseForClient();
    }

    /**
     * Create an instance of {@link VerifyVerificationCodeRequestFromClient }
     * 
     */
    public VerifyVerificationCodeRequestFromClient createVerifyVerificationCodeRequestFromClient() {
        return new VerifyVerificationCodeRequestFromClient();
    }

    /**
     * Create an instance of {@link VerifyVerificationCodeResponseForClient }
     * 
     */
    public VerifyVerificationCodeResponseForClient createVerifyVerificationCodeResponseForClient() {
        return new VerifyVerificationCodeResponseForClient();
    }

    /**
     * Create an instance of {@link BeneficInfoQueryRequestFromClient }
     * 
     */
    public BeneficInfoQueryRequestFromClient createBeneficInfoQueryRequestFromClient() {
        return new BeneficInfoQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link BeneficInfoQueryResponseForClient }
     * 
     */
    public BeneficInfoQueryResponseForClient createBeneficInfoQueryResponseForClient() {
        return new BeneficInfoQueryResponseForClient();
    }

    /**
     * Create an instance of {@link BeneficInfoUpdateRequestFromClient }
     * 
     */
    public BeneficInfoUpdateRequestFromClient createBeneficInfoUpdateRequestFromClient() {
        return new BeneficInfoUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link BeneficInfoUpdateResponseForClient }
     * 
     */
    public BeneficInfoUpdateResponseForClient createBeneficInfoUpdateResponseForClient() {
        return new BeneficInfoUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link PromotionActiveQueryRequestFromClient }
     * 
     */
    public PromotionActiveQueryRequestFromClient createPromotionActiveQueryRequestFromClient() {
        return new PromotionActiveQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link PromotionActiveQueryResponseForClient }
     * 
     */
    public PromotionActiveQueryResponseForClient createPromotionActiveQueryResponseForClient() {
        return new PromotionActiveQueryResponseForClient();
    }

    /**
     * Create an instance of {@link PromotionCustomRequestFromClient }
     * 
     */
    public PromotionCustomRequestFromClient createPromotionCustomRequestFromClient() {
        return new PromotionCustomRequestFromClient();
    }

    /**
     * Create an instance of {@link PromotionCustomResponseForClient }
     * 
     */
    public PromotionCustomResponseForClient createPromotionCustomResponseForClient() {
        return new PromotionCustomResponseForClient();
    }

    /**
     * Create an instance of {@link EmailAndSMSCustomQueryRequestFromClient }
     * 
     */
    public EmailAndSMSCustomQueryRequestFromClient createEmailAndSMSCustomQueryRequestFromClient() {
        return new EmailAndSMSCustomQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link EmailAndSMSCustomQueryResponseForClient }
     * 
     */
    public EmailAndSMSCustomQueryResponseForClient createEmailAndSMSCustomQueryResponseForClient() {
        return new EmailAndSMSCustomQueryResponseForClient();
    }

    /**
     * Create an instance of {@link EmailAndSMSCustomUpdateRequestFromClient }
     * 
     */
    public EmailAndSMSCustomUpdateRequestFromClient createEmailAndSMSCustomUpdateRequestFromClient() {
        return new EmailAndSMSCustomUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link EmailAndSMSCustomUpdateResponseForClient }
     * 
     */
    public EmailAndSMSCustomUpdateResponseForClient createEmailAndSMSCustomUpdateResponseForClient() {
        return new EmailAndSMSCustomUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link LoginTypeUpdateRequestFromClient }
     * 
     */
    public LoginTypeUpdateRequestFromClient createLoginTypeUpdateRequestFromClient() {
        return new LoginTypeUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link LoginTypeUpdateResponseForClient }
     * 
     */
    public LoginTypeUpdateResponseForClient createLoginTypeUpdateResponseForClient() {
        return new LoginTypeUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link AddInvalidTicketHisRequestFromClient }
     * 
     */
    public AddInvalidTicketHisRequestFromClient createAddInvalidTicketHisRequestFromClient() {
        return new AddInvalidTicketHisRequestFromClient();
    }

    /**
     * Create an instance of {@link AddInvalidTicketHisResponseForClient }
     * 
     */
    public AddInvalidTicketHisResponseForClient createAddInvalidTicketHisResponseForClient() {
        return new AddInvalidTicketHisResponseForClient();
    }

    /**
     * Create an instance of {@link VerifyConsumeMilesRequestFromClient }
     * 
     */
    public VerifyConsumeMilesRequestFromClient createVerifyConsumeMilesRequestFromClient() {
        return new VerifyConsumeMilesRequestFromClient();
    }

    /**
     * Create an instance of {@link VerifyConsumeMilesResponseForClient }
     * 
     */
    public VerifyConsumeMilesResponseForClient createVerifyConsumeMilesResponseForClient() {
        return new VerifyConsumeMilesResponseForClient();
    }

    /**
     * Create an instance of {@link GetDataInfoRequestFromClient }
     * 
     */
    public GetDataInfoRequestFromClient createGetDataInfoRequestFromClient() {
        return new GetDataInfoRequestFromClient();
    }

    /**
     * Create an instance of {@link GetDataInfoResponseForClient }
     * 
     */
    public GetDataInfoResponseForClient createGetDataInfoResponseForClient() {
        return new GetDataInfoResponseForClient();
    }

    /**
     * Create an instance of {@link GetMemberIdByPhoneFromClient }
     * 
     */
    public GetMemberIdByPhoneFromClient createGetMemberIdByPhoneFromClient() {
        return new GetMemberIdByPhoneFromClient();
    }

    /**
     * Create an instance of {@link GetMemberIdByPhoneForClient }
     * 
     */
    public GetMemberIdByPhoneForClient createGetMemberIdByPhoneForClient() {
        return new GetMemberIdByPhoneForClient();
    }

    /**
     * Create an instance of {@link GetMemberInfoRequestFromClient }
     * 
     */
    public GetMemberInfoRequestFromClient createGetMemberInfoRequestFromClient() {
        return new GetMemberInfoRequestFromClient();
    }

    /**
     * Create an instance of {@link GetMemberInfoResponseForClient }
     * 
     */
    public GetMemberInfoResponseForClient createGetMemberInfoResponseForClient() {
        return new GetMemberInfoResponseForClient();
    }

    /**
     * Create an instance of {@link MemberUnitRegistRequestFromClient }
     * 
     */
    public MemberUnitRegistRequestFromClient createMemberUnitRegistRequestFromClient() {
        return new MemberUnitRegistRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberUnitRegistResponseForClient }
     * 
     */
    public MemberUnitRegistResponseForClient createMemberUnitRegistResponseForClient() {
        return new MemberUnitRegistResponseForClient();
    }

    /**
     * Create an instance of {@link MemberB2CRegistRequestFromClient }
     * 
     */
    public MemberB2CRegistRequestFromClient createMemberB2CRegistRequestFromClient() {
        return new MemberB2CRegistRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberB2CRegistResponseForClient }
     * 
     */
    public MemberB2CRegistResponseForClient createMemberB2CRegistResponseForClient() {
        return new MemberB2CRegistResponseForClient();
    }

    /**
     * Create an instance of {@link MemberChangeB2CPasswordRequestFromClient }
     * 
     */
    public MemberChangeB2CPasswordRequestFromClient createMemberChangeB2CPasswordRequestFromClient() {
        return new MemberChangeB2CPasswordRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberChangeB2CPasswordResponseForClient }
     * 
     */
    public MemberChangeB2CPasswordResponseForClient createMemberChangeB2CPasswordResponseForClient() {
        return new MemberChangeB2CPasswordResponseForClient();
    }

    /**
     * Create an instance of {@link MemberRebuildB2CPasswordRequestFromClient }
     * 
     */
    public MemberRebuildB2CPasswordRequestFromClient createMemberRebuildB2CPasswordRequestFromClient() {
        return new MemberRebuildB2CPasswordRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberRebuildB2CPasswordResponseForClient }
     * 
     */
    public MemberRebuildB2CPasswordResponseForClient createMemberRebuildB2CPasswordResponseForClient() {
        return new MemberRebuildB2CPasswordResponseForClient();
    }

    /**
     * Create an instance of {@link MemberBaseInfoUpdateRequestFromClient }
     * 
     */
    public MemberBaseInfoUpdateRequestFromClient createMemberBaseInfoUpdateRequestFromClient() {
        return new MemberBaseInfoUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberBaseInfoUpdateResponseForClient }
     * 
     */
    public MemberBaseInfoUpdateResponseForClient createMemberBaseInfoUpdateResponseForClient() {
        return new MemberBaseInfoUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link PurchaseTicketAddPointRequestFromClient }
     * 
     */
    public PurchaseTicketAddPointRequestFromClient createPurchaseTicketAddPointRequestFromClient() {
        return new PurchaseTicketAddPointRequestFromClient();
    }

    /**
     * Create an instance of {@link PurchaseTicketAddPointResponseForClient }
     * 
     */
    public PurchaseTicketAddPointResponseForClient createPurchaseTicketAddPointResponseForClient() {
        return new PurchaseTicketAddPointResponseForClient();
    }

    /**
     * Create an instance of {@link VerifyCardPasswdRequestFromClient }
     * 
     */
    public VerifyCardPasswdRequestFromClient createVerifyCardPasswdRequestFromClient() {
        return new VerifyCardPasswdRequestFromClient();
    }

    /**
     * Create an instance of {@link VerifyCardPasswdResponseForClient }
     * 
     */
    public VerifyCardPasswdResponseForClient createVerifyCardPasswdResponseForClient() {
        return new VerifyCardPasswdResponseForClient();
    }

    /**
     * Create an instance of {@link MemberUnionQueryRequestFromClient }
     * 
     */
    public MemberUnionQueryRequestFromClient createMemberUnionQueryRequestFromClient() {
        return new MemberUnionQueryRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberUnionQueryResponseForClient }
     * 
     */
    public MemberUnionQueryResponseForClient createMemberUnionQueryResponseForClient() {
        return new MemberUnionQueryResponseForClient();
    }

    /**
     * Create an instance of {@link RemainSiteQueryRequestFromCLient }
     * 
     */
    public RemainSiteQueryRequestFromCLient createRemainSiteQueryRequestFromCLient() {
        return new RemainSiteQueryRequestFromCLient();
    }

    /**
     * Create an instance of {@link RemainSiteQueryResponseForClient }
     * 
     */
    public RemainSiteQueryResponseForClient createRemainSiteQueryResponseForClient() {
        return new RemainSiteQueryResponseForClient();
    }

    /**
     * Create an instance of {@link MemberGenderUpdateRequestFromClient }
     * 
     */
    public MemberGenderUpdateRequestFromClient createMemberGenderUpdateRequestFromClient() {
        return new MemberGenderUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberGenderUpdateResponseForClient }
     * 
     */
    public MemberGenderUpdateResponseForClient createMemberGenderUpdateResponseForClient() {
        return new MemberGenderUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link MemberCreatePasswordRequestFromClient }
     * 
     */
    public MemberCreatePasswordRequestFromClient createMemberCreatePasswordRequestFromClient() {
        return new MemberCreatePasswordRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberCreatePasswordResponseForClient }
     * 
     */
    public MemberCreatePasswordResponseForClient createMemberCreatePasswordResponseForClient() {
        return new MemberCreatePasswordResponseForClient();
    }

    /**
     * Create an instance of {@link BatchQueryMemberRequestFromClient }
     * 
     */
    public BatchQueryMemberRequestFromClient createBatchQueryMemberRequestFromClient() {
        return new BatchQueryMemberRequestFromClient();
    }

    /**
     * Create an instance of {@link BatchQueryMemberResponseForClient }
     * 
     */
    public BatchQueryMemberResponseForClient createBatchQueryMemberResponseForClient() {
        return new BatchQueryMemberResponseForClient();
    }

    /**
     * Create an instance of {@link EmailRejectRequestFromClient }
     * 
     */
    public EmailRejectRequestFromClient createEmailRejectRequestFromClient() {
        return new EmailRejectRequestFromClient();
    }

    /**
     * Create an instance of {@link EmailRejectResponseForClient }
     * 
     */
    public EmailRejectResponseForClient createEmailRejectResponseForClient() {
        return new EmailRejectResponseForClient();
    }

    /**
     * Create an instance of {@link PointMarketActivityRequestFromClient }
     * 
     */
    public PointMarketActivityRequestFromClient createPointMarketActivityRequestFromClient() {
        return new PointMarketActivityRequestFromClient();
    }

    /**
     * Create an instance of {@link PointMarketActivityResponseForClient }
     * 
     */
    public PointMarketActivityResponseForClient createPointMarketActivityResponseForClient() {
        return new PointMarketActivityResponseForClient();
    }

    /**
     * Create an instance of {@link MemberRegistBindRequestFromClient }
     * 
     */
    public MemberRegistBindRequestFromClient createMemberRegistBindRequestFromClient() {
        return new MemberRegistBindRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberRegistBindResponseForClient }
     * 
     */
    public MemberRegistBindResponseForClient createMemberRegistBindResponseForClient() {
        return new MemberRegistBindResponseForClient();
    }

    /**
     * Create an instance of {@link MemberRealVerifyRequestFromClient }
     * 
     */
    public MemberRealVerifyRequestFromClient createMemberRealVerifyRequestFromClient() {
        return new MemberRealVerifyRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberRealVerifyResponseForClient }
     * 
     */
    public MemberRealVerifyResponseForClient createMemberRealVerifyResponseForClient() {
        return new MemberRealVerifyResponseForClient();
    }

    /**
     * Create an instance of {@link PromotionApplyInfoRequestFromClient }
     * 
     */
    public PromotionApplyInfoRequestFromClient createPromotionApplyInfoRequestFromClient() {
        return new PromotionApplyInfoRequestFromClient();
    }

    /**
     * Create an instance of {@link PromotionApplyInfoResponseForClient }
     * 
     */
    public PromotionApplyInfoResponseForClient createPromotionApplyInfoResponseForClient() {
        return new PromotionApplyInfoResponseForClient();
    }

    /**
     * Create an instance of {@link TicketSaleRequestFromClient }
     * 
     */
    public TicketSaleRequestFromClient createTicketSaleRequestFromClient() {
        return new TicketSaleRequestFromClient();
    }

    /**
     * Create an instance of {@link TicketSaleResponseForClient }
     * 
     */
    public TicketSaleResponseForClient createTicketSaleResponseForClient() {
        return new TicketSaleResponseForClient();
    }

    /**
     * Create an instance of {@link MileageBudgetRequestFromClient }
     * 
     */
    public MileageBudgetRequestFromClient createMileageBudgetRequestFromClient() {
        return new MileageBudgetRequestFromClient();
    }

    /**
     * Create an instance of {@link MileageBudgetResponseForClient }
     * 
     */
    public MileageBudgetResponseForClient createMileageBudgetResponseForClient() {
        return new MileageBudgetResponseForClient();
    }

    /**
     * Create an instance of {@link TempMemberLoginRequestFromClient }
     * 
     */
    public TempMemberLoginRequestFromClient createTempMemberLoginRequestFromClient() {
        return new TempMemberLoginRequestFromClient();
    }

    /**
     * Create an instance of {@link TempMemberLoginResponseForClient }
     * 
     */
    public TempMemberLoginResponseForClient createTempMemberLoginResponseForClient() {
        return new TempMemberLoginResponseForClient();
    }

    /**
     * Create an instance of {@link CertificateUpdateRequestFromClient }
     * 
     */
    public CertificateUpdateRequestFromClient createCertificateUpdateRequestFromClient() {
        return new CertificateUpdateRequestFromClient();
    }

    /**
     * Create an instance of {@link CertificateUpdateResponseForClient }
     * 
     */
    public CertificateUpdateResponseForClient createCertificateUpdateResponseForClient() {
        return new CertificateUpdateResponseForClient();
    }

    /**
     * Create an instance of {@link MemberVIPRegistRequestFromClient }
     * 
     */
    public MemberVIPRegistRequestFromClient createMemberVIPRegistRequestFromClient() {
        return new MemberVIPRegistRequestFromClient();
    }

    /**
     * Create an instance of {@link MemberVIPRegistResponseForClient }
     * 
     */
    public MemberVIPRegistResponseForClient createMemberVIPRegistResponseForClient() {
        return new MemberVIPRegistResponseForClient();
    }

    /**
     * Create an instance of {@link RequestMessageHeaderType }
     * 
     */
    public RequestMessageHeaderType createRequestMessageHeaderType() {
        return new RequestMessageHeaderType();
    }

    /**
     * Create an instance of {@link MemberRegistInfoType }
     * 
     */
    public MemberRegistInfoType createMemberRegistInfoType() {
        return new MemberRegistInfoType();
    }

    /**
     * Create an instance of {@link ResponseMessageHeaderType }
     * 
     */
    public ResponseMessageHeaderType createResponseMessageHeaderType() {
        return new ResponseMessageHeaderType();
    }

    /**
     * Create an instance of {@link MemberMostRegInfoType }
     * 
     */
    public MemberMostRegInfoType createMemberMostRegInfoType() {
        return new MemberMostRegInfoType();
    }

    /**
     * Create an instance of {@link CustomerInfoType }
     * 
     */
    public CustomerInfoType createCustomerInfoType() {
        return new CustomerInfoType();
    }

    /**
     * Create an instance of {@link CustomerAddressInfoType }
     * 
     */
    public CustomerAddressInfoType createCustomerAddressInfoType() {
        return new CustomerAddressInfoType();
    }

    /**
     * Create an instance of {@link CustomerContactInfoType }
     * 
     */
    public CustomerContactInfoType createCustomerContactInfoType() {
        return new CustomerContactInfoType();
    }

    /**
     * Create an instance of {@link CustomerCertificateInfoType }
     * 
     */
    public CustomerCertificateInfoType createCustomerCertificateInfoType() {
        return new CustomerCertificateInfoType();
    }

    /**
     * Create an instance of {@link MemberPasswordInfoType }
     * 
     */
    public MemberPasswordInfoType createMemberPasswordInfoType() {
        return new MemberPasswordInfoType();
    }

    /**
     * Create an instance of {@link MemberLoginInfoType }
     * 
     */
    public MemberLoginInfoType createMemberLoginInfoType() {
        return new MemberLoginInfoType();
    }

    /**
     * Create an instance of {@link SmsAndEmailCustomType }
     * 
     */
    public SmsAndEmailCustomType createSmsAndEmailCustomType() {
        return new SmsAndEmailCustomType();
    }

    /**
     * Create an instance of {@link AccountType }
     * 
     */
    public AccountType createAccountType() {
        return new AccountType();
    }

    /**
     * Create an instance of {@link MemberQueryInfoType }
     * 
     */
    public MemberQueryInfoType createMemberQueryInfoType() {
        return new MemberQueryInfoType();
    }

    /**
     * Create an instance of {@link MemberCardInfoType }
     * 
     */
    public MemberCardInfoType createMemberCardInfoType() {
        return new MemberCardInfoType();
    }

    /**
     * Create an instance of {@link BeneficInfoType }
     * 
     */
    public BeneficInfoType createBeneficInfoType() {
        return new BeneficInfoType();
    }

    /**
     * Create an instance of {@link MemberUnitedCardType }
     * 
     */
    public MemberUnitedCardType createMemberUnitedCardType() {
        return new MemberUnitedCardType();
    }

    /**
     * Create an instance of {@link MemberAccountType }
     * 
     */
    public MemberAccountType createMemberAccountType() {
        return new MemberAccountType();
    }

    /**
     * Create an instance of {@link MileageRetroInfoType }
     * 
     */
    public MileageRetroInfoType createMileageRetroInfoType() {
        return new MileageRetroInfoType();
    }

    /**
     * Create an instance of {@link PurchaseMileageInfoType }
     * 
     */
    public PurchaseMileageInfoType createPurchaseMileageInfoType() {
        return new PurchaseMileageInfoType();
    }

    /**
     * Create an instance of {@link MemberRedeemInfoType }
     * 
     */
    public MemberRedeemInfoType createMemberRedeemInfoType() {
        return new MemberRedeemInfoType();
    }

    /**
     * Create an instance of {@link FlightConsumeInfoType }
     * 
     */
    public FlightConsumeInfoType createFlightConsumeInfoType() {
        return new FlightConsumeInfoType();
    }

    /**
     * Create an instance of {@link PassengerFlightInfoType }
     * 
     */
    public PassengerFlightInfoType createPassengerFlightInfoType() {
        return new PassengerFlightInfoType();
    }

    /**
     * Create an instance of {@link PassengerInfoType }
     * 
     */
    public PassengerInfoType createPassengerInfoType() {
        return new PassengerInfoType();
    }

    /**
     * Create an instance of {@link PassengerTicketInfoType }
     * 
     */
    public PassengerTicketInfoType createPassengerTicketInfoType() {
        return new PassengerTicketInfoType();
    }

    /**
     * Create an instance of {@link FlightInfoType }
     * 
     */
    public FlightInfoType createFlightInfoType() {
        return new FlightInfoType();
    }

    /**
     * Create an instance of {@link FlightRedeemInfoType }
     * 
     */
    public FlightRedeemInfoType createFlightRedeemInfoType() {
        return new FlightRedeemInfoType();
    }

    /**
     * Create an instance of {@link NonFlightInfoType }
     * 
     */
    public NonFlightInfoType createNonFlightInfoType() {
        return new NonFlightInfoType();
    }

    /**
     * Create an instance of {@link NonFlightRedeemInfoType }
     * 
     */
    public NonFlightRedeemInfoType createNonFlightRedeemInfoType() {
        return new NonFlightRedeemInfoType();
    }

    /**
     * Create an instance of {@link ConnectInfoType }
     * 
     */
    public ConnectInfoType createConnectInfoType() {
        return new ConnectInfoType();
    }

    /**
     * Create an instance of {@link MemberFlightActivityInfoType }
     * 
     */
    public MemberFlightActivityInfoType createMemberFlightActivityInfoType() {
        return new MemberFlightActivityInfoType();
    }

    /**
     * Create an instance of {@link MemberPromotionInfoType }
     * 
     */
    public MemberPromotionInfoType createMemberPromotionInfoType() {
        return new MemberPromotionInfoType();
    }

    /**
     * Create an instance of {@link MemberNonFlightActivityInfoType }
     * 
     */
    public MemberNonFlightActivityInfoType createMemberNonFlightActivityInfoType() {
        return new MemberNonFlightActivityInfoType();
    }

    /**
     * Create an instance of {@link MemberExtraMilesInfoType }
     * 
     */
    public MemberExtraMilesInfoType createMemberExtraMilesInfoType() {
        return new MemberExtraMilesInfoType();
    }

    /**
     * Create an instance of {@link MemberAdjustmentInfoType }
     * 
     */
    public MemberAdjustmentInfoType createMemberAdjustmentInfoType() {
        return new MemberAdjustmentInfoType();
    }

    /**
     * Create an instance of {@link MemberFlightConsumeInfoType }
     * 
     */
    public MemberFlightConsumeInfoType createMemberFlightConsumeInfoType() {
        return new MemberFlightConsumeInfoType();
    }

    /**
     * Create an instance of {@link FlightRedeemDetailType }
     * 
     */
    public FlightRedeemDetailType createFlightRedeemDetailType() {
        return new FlightRedeemDetailType();
    }

    /**
     * Create an instance of {@link MemberNonFlightConsumeInfoType }
     * 
     */
    public MemberNonFlightConsumeInfoType createMemberNonFlightConsumeInfoType() {
        return new MemberNonFlightConsumeInfoType();
    }

    /**
     * Create an instance of {@link MemberExpireMilesInfoType }
     * 
     */
    public MemberExpireMilesInfoType createMemberExpireMilesInfoType() {
        return new MemberExpireMilesInfoType();
    }

    /**
     * Create an instance of {@link TempRetroByeNetInfoType }
     * 
     */
    public TempRetroByeNetInfoType createTempRetroByeNetInfoType() {
        return new TempRetroByeNetInfoType();
    }

    /**
     * Create an instance of {@link PurchaseHistoryByNetInfoType }
     * 
     */
    public PurchaseHistoryByNetInfoType createPurchaseHistoryByNetInfoType() {
        return new PurchaseHistoryByNetInfoType();
    }

    /**
     * Create an instance of {@link FlightRedeemByNetInfoType }
     * 
     */
    public FlightRedeemByNetInfoType createFlightRedeemByNetInfoType() {
        return new FlightRedeemByNetInfoType();
    }

    /**
     * Create an instance of {@link MemberBeneficInfoType }
     * 
     */
    public MemberBeneficInfoType createMemberBeneficInfoType() {
        return new MemberBeneficInfoType();
    }

    /**
     * Create an instance of {@link PromotionActiveInfoType }
     * 
     */
    public PromotionActiveInfoType createPromotionActiveInfoType() {
        return new PromotionActiveInfoType();
    }

    /**
     * Create an instance of {@link AddInvalidTicketHisInfoType }
     * 
     */
    public AddInvalidTicketHisInfoType createAddInvalidTicketHisInfoType() {
        return new AddInvalidTicketHisInfoType();
    }

    /**
     * Create an instance of {@link EntryType }
     * 
     */
    public EntryType createEntryType() {
        return new EntryType();
    }

    /**
     * Create an instance of {@link MemberUnitRegistInfoType }
     * 
     */
    public MemberUnitRegistInfoType createMemberUnitRegistInfoType() {
        return new MemberUnitRegistInfoType();
    }

    /**
     * Create an instance of {@link MemberB2CRegistInfoType }
     * 
     */
    public MemberB2CRegistInfoType createMemberB2CRegistInfoType() {
        return new MemberB2CRegistInfoType();
    }

    /**
     * Create an instance of {@link CustomerUpdateInfoType }
     * 
     */
    public CustomerUpdateInfoType createCustomerUpdateInfoType() {
        return new CustomerUpdateInfoType();
    }

    /**
     * Create an instance of {@link PurchaseTicketInfo }
     * 
     */
    public PurchaseTicketInfo createPurchaseTicketInfo() {
        return new PurchaseTicketInfo();
    }

    /**
     * Create an instance of {@link MemberMilesType }
     * 
     */
    public MemberMilesType createMemberMilesType() {
        return new MemberMilesType();
    }

    /**
     * Create an instance of {@link MemberUnionMilesType }
     * 
     */
    public MemberUnionMilesType createMemberUnionMilesType() {
        return new MemberUnionMilesType();
    }

    /**
     * Create an instance of {@link SubmitCustomer }
     * 
     */
    public SubmitCustomer createSubmitCustomer() {
        return new SubmitCustomer();
    }

    /**
     * Create an instance of {@link ReturnMemberResult }
     * 
     */
    public ReturnMemberResult createReturnMemberResult() {
        return new ReturnMemberResult();
    }

    /**
     * Create an instance of {@link MemberRegistBindInfoType }
     * 
     */
    public MemberRegistBindInfoType createMemberRegistBindInfoType() {
        return new MemberRegistBindInfoType();
    }

    /**
     * Create an instance of {@link TempMemberLoginInfo }
     * 
     */
    public TempMemberLoginInfo createTempMemberLoginInfo() {
        return new TempMemberLoginInfo();
    }

    /**
     * Create an instance of {@link CertificateUpdateInfoType }
     * 
     */
    public CertificateUpdateInfoType createCertificateUpdateInfoType() {
        return new CertificateUpdateInfoType();
    }

    /**
     * Create an instance of {@link MemberVIPRegistInfoType }
     * 
     */
    public MemberVIPRegistInfoType createMemberVIPRegistInfoType() {
        return new MemberVIPRegistInfoType();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRegistRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_001Request")
    public JAXBElement<MemberRegistRequestFromClient> createBC2MW001Request(MemberRegistRequestFromClient value) {
        return new JAXBElement<MemberRegistRequestFromClient>(_BC2MW001Request_QNAME, MemberRegistRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRegistResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_001Response")
    public JAXBElement<MemberRegistResponseForClient> createMW2BC001Response(MemberRegistResponseForClient value) {
        return new JAXBElement<MemberRegistResponseForClient>(_MW2BC001Response_QNAME, MemberRegistResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRegistMostRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_002Request")
    public JAXBElement<MemberRegistMostRequestFromClient> createBC2MW002Request(MemberRegistMostRequestFromClient value) {
        return new JAXBElement<MemberRegistMostRequestFromClient>(_BC2MW002Request_QNAME, MemberRegistMostRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRegistMostResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_002Response")
    public JAXBElement<MemberRegistMostResponseForClient> createMW2BC002Response(MemberRegistMostResponseForClient value) {
        return new JAXBElement<MemberRegistMostResponseForClient>(_MW2BC002Response_QNAME, MemberRegistMostResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberLoginRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_003Request")
    public JAXBElement<MemberLoginRequestFromClient> createBC2MW003Request(MemberLoginRequestFromClient value) {
        return new JAXBElement<MemberLoginRequestFromClient>(_BC2MW003Request_QNAME, MemberLoginRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberLoginResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_003Response")
    public JAXBElement<MemberLoginResponseForClient> createMW2BC003Response(MemberLoginResponseForClient value) {
        return new JAXBElement<MemberLoginResponseForClient>(_MW2BC003Response_QNAME, MemberLoginResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link B2CLoginRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_004Request")
    public JAXBElement<B2CLoginRequestFromClient> createBC2MW004Request(B2CLoginRequestFromClient value) {
        return new JAXBElement<B2CLoginRequestFromClient>(_BC2MW004Request_QNAME, B2CLoginRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link B2CLoginResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_004Response")
    public JAXBElement<B2CLoginResponseForClient> createMW2BC004Response(B2CLoginResponseForClient value) {
        return new JAXBElement<B2CLoginResponseForClient>(_MW2BC004Response_QNAME, B2CLoginResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberInfoQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_005Request")
    public JAXBElement<MemberInfoQueryRequestFromClient> createBC2MW005Request(MemberInfoQueryRequestFromClient value) {
        return new JAXBElement<MemberInfoQueryRequestFromClient>(_BC2MW005Request_QNAME, MemberInfoQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberInfoQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_005Response")
    public JAXBElement<MemberInfoQueryResponseForClient> createMW2BC005Response(MemberInfoQueryResponseForClient value) {
        return new JAXBElement<MemberInfoQueryResponseForClient>(_MW2BC005Response_QNAME, MemberInfoQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberAddressInfoUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_006Request")
    public JAXBElement<MemberAddressInfoUpdateRequestFromClient> createBC2MW006Request(MemberAddressInfoUpdateRequestFromClient value) {
        return new JAXBElement<MemberAddressInfoUpdateRequestFromClient>(_BC2MW006Request_QNAME, MemberAddressInfoUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberAddressInfoUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_006Response")
    public JAXBElement<MemberAddressInfoUpdateResponseForClient> createMW2BC006Response(MemberAddressInfoUpdateResponseForClient value) {
        return new JAXBElement<MemberAddressInfoUpdateResponseForClient>(_MW2BC006Response_QNAME, MemberAddressInfoUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberContactInfoUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_007Request")
    public JAXBElement<MemberContactInfoUpdateRequestFromClient> createBC2MW007Request(MemberContactInfoUpdateRequestFromClient value) {
        return new JAXBElement<MemberContactInfoUpdateRequestFromClient>(_BC2MW007Request_QNAME, MemberContactInfoUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberContactInfoUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_007Response")
    public JAXBElement<MemberContactInfoUpdateResponseForClient> createMW2BC007Response(MemberContactInfoUpdateResponseForClient value) {
        return new JAXBElement<MemberContactInfoUpdateResponseForClient>(_MW2BC007Response_QNAME, MemberContactInfoUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRebuildPasswordRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_008Request")
    public JAXBElement<MemberRebuildPasswordRequestFromClient> createBC2MW008Request(MemberRebuildPasswordRequestFromClient value) {
        return new JAXBElement<MemberRebuildPasswordRequestFromClient>(_BC2MW008Request_QNAME, MemberRebuildPasswordRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRebuildPasswordResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_008Response")
    public JAXBElement<MemberRebuildPasswordResponseForClient> createMW2BC008Response(MemberRebuildPasswordResponseForClient value) {
        return new JAXBElement<MemberRebuildPasswordResponseForClient>(_MW2BC008Response_QNAME, MemberRebuildPasswordResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberChangePasswordRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_009Request")
    public JAXBElement<MemberChangePasswordRequestFromClient> createBC2MW009Request(MemberChangePasswordRequestFromClient value) {
        return new JAXBElement<MemberChangePasswordRequestFromClient>(_BC2MW009Request_QNAME, MemberChangePasswordRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberChangePasswordResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_009Response")
    public JAXBElement<MemberChangePasswordResponseForClient> createMW2BC009Response(MemberChangePasswordResponseForClient value) {
        return new JAXBElement<MemberChangePasswordResponseForClient>(_MW2BC009Response_QNAME, MemberChangePasswordResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberAccountQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_010Request")
    public JAXBElement<MemberAccountQueryRequestFromClient> createBC2MW010Request(MemberAccountQueryRequestFromClient value) {
        return new JAXBElement<MemberAccountQueryRequestFromClient>(_BC2MW010Request_QNAME, MemberAccountQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberAccountQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_010Response")
    public JAXBElement<MemberAccountQueryResponseForClient> createMW2BC010Response(MemberAccountQueryResponseForClient value) {
        return new JAXBElement<MemberAccountQueryResponseForClient>(_MW2BC010Response_QNAME, MemberAccountQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MileageRetroRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_011Request")
    public JAXBElement<MileageRetroRequestFromClient> createBC2MW011Request(MileageRetroRequestFromClient value) {
        return new JAXBElement<MileageRetroRequestFromClient>(_BC2MW011Request_QNAME, MileageRetroRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MileageRetroResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_011Response")
    public JAXBElement<MileageRetroResponseForClient> createMW2BC011Response(MileageRetroResponseForClient value) {
        return new JAXBElement<MileageRetroResponseForClient>(_MW2BC011Response_QNAME, MileageRetroResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseMileageApplyRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_012Request")
    public JAXBElement<PurchaseMileageApplyRequestFromClient> createBC2MW012Request(PurchaseMileageApplyRequestFromClient value) {
        return new JAXBElement<PurchaseMileageApplyRequestFromClient>(_BC2MW012Request_QNAME, PurchaseMileageApplyRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseMileageApplyResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_012Response")
    public JAXBElement<PurchaseMileageApplyResponseForClient> createMW2BC012Response(PurchaseMileageApplyResponseForClient value) {
        return new JAXBElement<PurchaseMileageApplyResponseForClient>(_MW2BC012Response_QNAME, PurchaseMileageApplyResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseMileageConfirmOrCancelRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_013Request")
    public JAXBElement<PurchaseMileageConfirmOrCancelRequestFromClient> createBC2MW013Request(PurchaseMileageConfirmOrCancelRequestFromClient value) {
        return new JAXBElement<PurchaseMileageConfirmOrCancelRequestFromClient>(_BC2MW013Request_QNAME, PurchaseMileageConfirmOrCancelRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseMileageConfirmOrCancelResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_013Response")
    public JAXBElement<PurchaseMileageConfirmOrCancelResponseForClient> createMW2BC013Response(PurchaseMileageConfirmOrCancelResponseForClient value) {
        return new JAXBElement<PurchaseMileageConfirmOrCancelResponseForClient>(_MW2BC013Response_QNAME, PurchaseMileageConfirmOrCancelResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyConsumePasswdRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_014Request")
    public JAXBElement<VerifyConsumePasswdRequestFromClient> createBC2MW014Request(VerifyConsumePasswdRequestFromClient value) {
        return new JAXBElement<VerifyConsumePasswdRequestFromClient>(_BC2MW014Request_QNAME, VerifyConsumePasswdRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyConsumePasswdResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_014Response")
    public JAXBElement<VerifyConsumePasswdResponseForClient> createMW2BC014Response(VerifyConsumePasswdResponseForClient value) {
        return new JAXBElement<VerifyConsumePasswdResponseForClient>(_MW2BC014Response_QNAME, VerifyConsumePasswdResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeApplyRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_015Request")
    public JAXBElement<FlightConsumeApplyRequestFromClient> createBC2MW015Request(FlightConsumeApplyRequestFromClient value) {
        return new JAXBElement<FlightConsumeApplyRequestFromClient>(_BC2MW015Request_QNAME, FlightConsumeApplyRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeApplyResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_015Response")
    public JAXBElement<FlightConsumeApplyResponseForClient> createMW2BC015Response(FlightConsumeApplyResponseForClient value) {
        return new JAXBElement<FlightConsumeApplyResponseForClient>(_MW2BC015Response_QNAME, FlightConsumeApplyResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeConfirmOrCancelRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_016Request")
    public JAXBElement<FlightConsumeConfirmOrCancelRequestFromClient> createBC2MW016Request(FlightConsumeConfirmOrCancelRequestFromClient value) {
        return new JAXBElement<FlightConsumeConfirmOrCancelRequestFromClient>(_BC2MW016Request_QNAME, FlightConsumeConfirmOrCancelRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeConfirmOrCancelResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_016Response")
    public JAXBElement<FlightConsumeConfirmOrCancelResponseForClient> createMW2BC016Response(FlightConsumeConfirmOrCancelResponseForClient value) {
        return new JAXBElement<FlightConsumeConfirmOrCancelResponseForClient>(_MW2BC016Response_QNAME, FlightConsumeConfirmOrCancelResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightRedeemRuleRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_017Request")
    public JAXBElement<FlightRedeemRuleRequestFromClient> createBC2MW017Request(FlightRedeemRuleRequestFromClient value) {
        return new JAXBElement<FlightRedeemRuleRequestFromClient>(_BC2MW017Request_QNAME, FlightRedeemRuleRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightRedeemRuleResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_017Response")
    public JAXBElement<FlightRedeemRuleResponseForClient> createMW2BC017Response(FlightRedeemRuleResponseForClient value) {
        return new JAXBElement<FlightRedeemRuleResponseForClient>(_MW2BC017Response_QNAME, FlightRedeemRuleResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightRedeemApplyRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_018Request")
    public JAXBElement<FlightRedeemApplyRequestFromClient> createBC2MW018Request(FlightRedeemApplyRequestFromClient value) {
        return new JAXBElement<FlightRedeemApplyRequestFromClient>(_BC2MW018Request_QNAME, FlightRedeemApplyRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightRedeemApplyResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_018Response")
    public JAXBElement<FlightRedeemApplyResponseForClient> createMW2BC018Response(FlightRedeemApplyResponseForClient value) {
        return new JAXBElement<FlightRedeemApplyResponseForClient>(_MW2BC018Response_QNAME, FlightRedeemApplyResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightRedeemConfirmOrCancelRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_019Request")
    public JAXBElement<FlightRedeemConfirmOrCancelRequestFromClient> createBC2MW019Request(FlightRedeemConfirmOrCancelRequestFromClient value) {
        return new JAXBElement<FlightRedeemConfirmOrCancelRequestFromClient>(_BC2MW019Request_QNAME, FlightRedeemConfirmOrCancelRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeConfirmOrCancelResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_019Response")
    public JAXBElement<FlightConsumeConfirmOrCancelResponseForClient> createMW2BC019Response(FlightConsumeConfirmOrCancelResponseForClient value) {
        return new JAXBElement<FlightConsumeConfirmOrCancelResponseForClient>(_MW2BC019Response_QNAME, FlightConsumeConfirmOrCancelResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NonFlightRedeemRuleRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_020Request")
    public JAXBElement<NonFlightRedeemRuleRequestFromClient> createBC2MW020Request(NonFlightRedeemRuleRequestFromClient value) {
        return new JAXBElement<NonFlightRedeemRuleRequestFromClient>(_BC2MW020Request_QNAME, NonFlightRedeemRuleRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NonFlightRedeemRuleResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_020Response")
    public JAXBElement<NonFlightRedeemRuleResponseForClient> createMW2BC020Response(NonFlightRedeemRuleResponseForClient value) {
        return new JAXBElement<NonFlightRedeemRuleResponseForClient>(_MW2BC020Response_QNAME, NonFlightRedeemRuleResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NonFlightRedeemApplyRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_021Request")
    public JAXBElement<NonFlightRedeemApplyRequestFromClient> createBC2MW021Request(NonFlightRedeemApplyRequestFromClient value) {
        return new JAXBElement<NonFlightRedeemApplyRequestFromClient>(_BC2MW021Request_QNAME, NonFlightRedeemApplyRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NonFlightRedeemApplyResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_021Response")
    public JAXBElement<NonFlightRedeemApplyResponseForClient> createMW2BC021Response(NonFlightRedeemApplyResponseForClient value) {
        return new JAXBElement<NonFlightRedeemApplyResponseForClient>(_MW2BC021Response_QNAME, NonFlightRedeemApplyResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NonFlightRedeemConfirmOrCancelRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_022Request")
    public JAXBElement<NonFlightRedeemConfirmOrCancelRequestFromClient> createBC2MW022Request(NonFlightRedeemConfirmOrCancelRequestFromClient value) {
        return new JAXBElement<NonFlightRedeemConfirmOrCancelRequestFromClient>(_BC2MW022Request_QNAME, NonFlightRedeemConfirmOrCancelRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NonFlightRedeemConfirmOrCancelResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_022Response")
    public JAXBElement<NonFlightRedeemConfirmOrCancelResponseForClient> createMW2BC022Response(NonFlightRedeemConfirmOrCancelResponseForClient value) {
        return new JAXBElement<NonFlightRedeemConfirmOrCancelResponseForClient>(_MW2BC022Response_QNAME, NonFlightRedeemConfirmOrCancelResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeRefundRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_023Request")
    public JAXBElement<FlightConsumeRefundRequestFromClient> createBC2MW023Request(FlightConsumeRefundRequestFromClient value) {
        return new JAXBElement<FlightConsumeRefundRequestFromClient>(_BC2MW023Request_QNAME, FlightConsumeRefundRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeRefundResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_023Response")
    public JAXBElement<FlightConsumeRefundResponseForClient> createMW2BC023Response(FlightConsumeRefundResponseForClient value) {
        return new JAXBElement<FlightConsumeRefundResponseForClient>(_MW2BC023Response_QNAME, FlightConsumeRefundResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberFlightActivityQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_024Request")
    public JAXBElement<MemberFlightActivityQueryRequestFromClient> createBC2MW024Request(MemberFlightActivityQueryRequestFromClient value) {
        return new JAXBElement<MemberFlightActivityQueryRequestFromClient>(_BC2MW024Request_QNAME, MemberFlightActivityQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberFlightActivityQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_024Response")
    public JAXBElement<MemberFlightActivityQueryResponseForClient> createMW2BC024Response(MemberFlightActivityQueryResponseForClient value) {
        return new JAXBElement<MemberFlightActivityQueryResponseForClient>(_MW2BC024Response_QNAME, MemberFlightActivityQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberNonFlightActivityQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_025Request")
    public JAXBElement<MemberNonFlightActivityQueryRequestFromClient> createBC2MW025Request(MemberNonFlightActivityQueryRequestFromClient value) {
        return new JAXBElement<MemberNonFlightActivityQueryRequestFromClient>(_BC2MW025Request_QNAME, MemberNonFlightActivityQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberNonFlightActivityQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_025Response")
    public JAXBElement<MemberNonFlightActivityQueryResponseForClient> createMW2BC025Response(MemberNonFlightActivityQueryResponseForClient value) {
        return new JAXBElement<MemberNonFlightActivityQueryResponseForClient>(_MW2BC025Response_QNAME, MemberNonFlightActivityQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberPromotionQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_026Request")
    public JAXBElement<MemberPromotionQueryRequestFromClient> createBC2MW026Request(MemberPromotionQueryRequestFromClient value) {
        return new JAXBElement<MemberPromotionQueryRequestFromClient>(_BC2MW026Request_QNAME, MemberPromotionQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberPromotionQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_026Response")
    public JAXBElement<MemberPromotionQueryResponseForClient> createMW2BC026Response(MemberPromotionQueryResponseForClient value) {
        return new JAXBElement<MemberPromotionQueryResponseForClient>(_MW2BC026Response_QNAME, MemberPromotionQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberExtraMilesQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_027Request")
    public JAXBElement<MemberExtraMilesQueryRequestFromClient> createBC2MW027Request(MemberExtraMilesQueryRequestFromClient value) {
        return new JAXBElement<MemberExtraMilesQueryRequestFromClient>(_BC2MW027Request_QNAME, MemberExtraMilesQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberExtraMilesQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_027Response")
    public JAXBElement<MemberExtraMilesQueryResponseForClient> createMW2BC027Response(MemberExtraMilesQueryResponseForClient value) {
        return new JAXBElement<MemberExtraMilesQueryResponseForClient>(_MW2BC027Response_QNAME, MemberExtraMilesQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberAdjustmentQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_028Request")
    public JAXBElement<MemberAdjustmentQueryRequestFromClient> createBC2MW028Request(MemberAdjustmentQueryRequestFromClient value) {
        return new JAXBElement<MemberAdjustmentQueryRequestFromClient>(_BC2MW028Request_QNAME, MemberAdjustmentQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberAdjustmentQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_028Response")
    public JAXBElement<MemberAdjustmentQueryResponseForClient> createMW2BC028Response(MemberAdjustmentQueryResponseForClient value) {
        return new JAXBElement<MemberAdjustmentQueryResponseForClient>(_MW2BC028Response_QNAME, MemberAdjustmentQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberFlightConsumeQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_029Request")
    public JAXBElement<MemberFlightConsumeQueryRequestFromClient> createBC2MW029Request(MemberFlightConsumeQueryRequestFromClient value) {
        return new JAXBElement<MemberFlightConsumeQueryRequestFromClient>(_BC2MW029Request_QNAME, MemberFlightConsumeQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberFlightConsumeQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_029Response")
    public JAXBElement<MemberFlightConsumeQueryResponseForClient> createMW2BC029Response(MemberFlightConsumeQueryResponseForClient value) {
        return new JAXBElement<MemberFlightConsumeQueryResponseForClient>(_MW2BC029Response_QNAME, MemberFlightConsumeQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberNonFlightConsumeQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_030Request")
    public JAXBElement<MemberNonFlightConsumeQueryRequestFromClient> createBC2MW030Request(MemberNonFlightConsumeQueryRequestFromClient value) {
        return new JAXBElement<MemberNonFlightConsumeQueryRequestFromClient>(_BC2MW030Request_QNAME, MemberNonFlightConsumeQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberNonFlightConsumeQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_030Response")
    public JAXBElement<MemberNonFlightConsumeQueryResponseForClient> createMW2BC030Response(MemberNonFlightConsumeQueryResponseForClient value) {
        return new JAXBElement<MemberNonFlightConsumeQueryResponseForClient>(_MW2BC030Response_QNAME, MemberNonFlightConsumeQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberExpireMilesQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_031Request")
    public JAXBElement<MemberExpireMilesQueryRequestFromClient> createBC2MW031Request(MemberExpireMilesQueryRequestFromClient value) {
        return new JAXBElement<MemberExpireMilesQueryRequestFromClient>(_BC2MW031Request_QNAME, MemberExpireMilesQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberExpireMilesQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_031Response")
    public JAXBElement<MemberExpireMilesQueryResponseForClient> createMW2BC031Response(MemberExpireMilesQueryResponseForClient value) {
        return new JAXBElement<MemberExpireMilesQueryResponseForClient>(_MW2BC031Response_QNAME, MemberExpireMilesQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TempRetroByNetQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_032Request")
    public JAXBElement<TempRetroByNetQueryRequestFromClient> createBC2MW032Request(TempRetroByNetQueryRequestFromClient value) {
        return new JAXBElement<TempRetroByNetQueryRequestFromClient>(_BC2MW032Request_QNAME, TempRetroByNetQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TempRetroByNetQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_032Response")
    public JAXBElement<TempRetroByNetQueryResponseForClient> createMW2BC032Response(TempRetroByNetQueryResponseForClient value) {
        return new JAXBElement<TempRetroByNetQueryResponseForClient>(_MW2BC032Response_QNAME, TempRetroByNetQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseHistoryByNetQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_033Request")
    public JAXBElement<PurchaseHistoryByNetQueryRequestFromClient> createBC2MW033Request(PurchaseHistoryByNetQueryRequestFromClient value) {
        return new JAXBElement<PurchaseHistoryByNetQueryRequestFromClient>(_BC2MW033Request_QNAME, PurchaseHistoryByNetQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseHistoryByNetQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_033Response")
    public JAXBElement<PurchaseHistoryByNetQueryResponseForClient> createMW2BC033Response(PurchaseHistoryByNetQueryResponseForClient value) {
        return new JAXBElement<PurchaseHistoryByNetQueryResponseForClient>(_MW2BC033Response_QNAME, PurchaseHistoryByNetQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightRedeemByNetQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_034Request")
    public JAXBElement<FlightRedeemByNetQueryRequestFromClient> createBC2MW034Request(FlightRedeemByNetQueryRequestFromClient value) {
        return new JAXBElement<FlightRedeemByNetQueryRequestFromClient>(_BC2MW034Request_QNAME, FlightRedeemByNetQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightRedeemByNetQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_034Response")
    public JAXBElement<FlightRedeemByNetQueryResponseForClient> createMW2BC034Response(FlightRedeemByNetQueryResponseForClient value) {
        return new JAXBElement<FlightRedeemByNetQueryResponseForClient>(_MW2BC034Response_QNAME, FlightRedeemByNetQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CreateVerificationCodeRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_035Request")
    public JAXBElement<CreateVerificationCodeRequestFromClient> createBC2MW035Request(CreateVerificationCodeRequestFromClient value) {
        return new JAXBElement<CreateVerificationCodeRequestFromClient>(_BC2MW035Request_QNAME, CreateVerificationCodeRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CreateVerificationCodeResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_035Response")
    public JAXBElement<CreateVerificationCodeResponseForClient> createMW2BC035Response(CreateVerificationCodeResponseForClient value) {
        return new JAXBElement<CreateVerificationCodeResponseForClient>(_MW2BC035Response_QNAME, CreateVerificationCodeResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyVerificationCodeRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_036Request")
    public JAXBElement<VerifyVerificationCodeRequestFromClient> createBC2MW036Request(VerifyVerificationCodeRequestFromClient value) {
        return new JAXBElement<VerifyVerificationCodeRequestFromClient>(_BC2MW036Request_QNAME, VerifyVerificationCodeRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyVerificationCodeResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_036Response")
    public JAXBElement<VerifyVerificationCodeResponseForClient> createMW2BC036Response(VerifyVerificationCodeResponseForClient value) {
        return new JAXBElement<VerifyVerificationCodeResponseForClient>(_MW2BC036Response_QNAME, VerifyVerificationCodeResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BeneficInfoQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_037Request")
    public JAXBElement<BeneficInfoQueryRequestFromClient> createBC2MW037Request(BeneficInfoQueryRequestFromClient value) {
        return new JAXBElement<BeneficInfoQueryRequestFromClient>(_BC2MW037Request_QNAME, BeneficInfoQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BeneficInfoQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_037Response")
    public JAXBElement<BeneficInfoQueryResponseForClient> createMW2BC037Response(BeneficInfoQueryResponseForClient value) {
        return new JAXBElement<BeneficInfoQueryResponseForClient>(_MW2BC037Response_QNAME, BeneficInfoQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BeneficInfoUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_038Request")
    public JAXBElement<BeneficInfoUpdateRequestFromClient> createBC2MW038Request(BeneficInfoUpdateRequestFromClient value) {
        return new JAXBElement<BeneficInfoUpdateRequestFromClient>(_BC2MW038Request_QNAME, BeneficInfoUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BeneficInfoUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_038Response")
    public JAXBElement<BeneficInfoUpdateResponseForClient> createMW2BC038Response(BeneficInfoUpdateResponseForClient value) {
        return new JAXBElement<BeneficInfoUpdateResponseForClient>(_MW2BC038Response_QNAME, BeneficInfoUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PromotionActiveQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_039Request")
    public JAXBElement<PromotionActiveQueryRequestFromClient> createBC2MW039Request(PromotionActiveQueryRequestFromClient value) {
        return new JAXBElement<PromotionActiveQueryRequestFromClient>(_BC2MW039Request_QNAME, PromotionActiveQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PromotionActiveQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_039Response")
    public JAXBElement<PromotionActiveQueryResponseForClient> createMW2BC039Response(PromotionActiveQueryResponseForClient value) {
        return new JAXBElement<PromotionActiveQueryResponseForClient>(_MW2BC039Response_QNAME, PromotionActiveQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PromotionCustomRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_040Request")
    public JAXBElement<PromotionCustomRequestFromClient> createBC2MW040Request(PromotionCustomRequestFromClient value) {
        return new JAXBElement<PromotionCustomRequestFromClient>(_BC2MW040Request_QNAME, PromotionCustomRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PromotionCustomResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_040Response")
    public JAXBElement<PromotionCustomResponseForClient> createMW2BC040Response(PromotionCustomResponseForClient value) {
        return new JAXBElement<PromotionCustomResponseForClient>(_MW2BC040Response_QNAME, PromotionCustomResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EmailAndSMSCustomQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_041Request")
    public JAXBElement<EmailAndSMSCustomQueryRequestFromClient> createBC2MW041Request(EmailAndSMSCustomQueryRequestFromClient value) {
        return new JAXBElement<EmailAndSMSCustomQueryRequestFromClient>(_BC2MW041Request_QNAME, EmailAndSMSCustomQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EmailAndSMSCustomQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_041Response")
    public JAXBElement<EmailAndSMSCustomQueryResponseForClient> createMW2BC041Response(EmailAndSMSCustomQueryResponseForClient value) {
        return new JAXBElement<EmailAndSMSCustomQueryResponseForClient>(_MW2BC041Response_QNAME, EmailAndSMSCustomQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EmailAndSMSCustomUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_042Request")
    public JAXBElement<EmailAndSMSCustomUpdateRequestFromClient> createBC2MW042Request(EmailAndSMSCustomUpdateRequestFromClient value) {
        return new JAXBElement<EmailAndSMSCustomUpdateRequestFromClient>(_BC2MW042Request_QNAME, EmailAndSMSCustomUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EmailAndSMSCustomUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_042Response")
    public JAXBElement<EmailAndSMSCustomUpdateResponseForClient> createMW2BC042Response(EmailAndSMSCustomUpdateResponseForClient value) {
        return new JAXBElement<EmailAndSMSCustomUpdateResponseForClient>(_MW2BC042Response_QNAME, EmailAndSMSCustomUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LoginTypeUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_043Request")
    public JAXBElement<LoginTypeUpdateRequestFromClient> createBC2MW043Request(LoginTypeUpdateRequestFromClient value) {
        return new JAXBElement<LoginTypeUpdateRequestFromClient>(_BC2MW043Request_QNAME, LoginTypeUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LoginTypeUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_043Response")
    public JAXBElement<LoginTypeUpdateResponseForClient> createMW2BC043Response(LoginTypeUpdateResponseForClient value) {
        return new JAXBElement<LoginTypeUpdateResponseForClient>(_MW2BC043Response_QNAME, LoginTypeUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AddInvalidTicketHisRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_046Request")
    public JAXBElement<AddInvalidTicketHisRequestFromClient> createBC2MW046Request(AddInvalidTicketHisRequestFromClient value) {
        return new JAXBElement<AddInvalidTicketHisRequestFromClient>(_BC2MW046Request_QNAME, AddInvalidTicketHisRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AddInvalidTicketHisResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_046Response")
    public JAXBElement<AddInvalidTicketHisResponseForClient> createMW2BC046Response(AddInvalidTicketHisResponseForClient value) {
        return new JAXBElement<AddInvalidTicketHisResponseForClient>(_MW2BC046Response_QNAME, AddInvalidTicketHisResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyConsumeMilesRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_047Request")
    public JAXBElement<VerifyConsumeMilesRequestFromClient> createBC2MW047Request(VerifyConsumeMilesRequestFromClient value) {
        return new JAXBElement<VerifyConsumeMilesRequestFromClient>(_BC2MW047Request_QNAME, VerifyConsumeMilesRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyConsumeMilesResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_047Response")
    public JAXBElement<VerifyConsumeMilesResponseForClient> createMW2BC047Response(VerifyConsumeMilesResponseForClient value) {
        return new JAXBElement<VerifyConsumeMilesResponseForClient>(_MW2BC047Response_QNAME, VerifyConsumeMilesResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetDataInfoRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_048Request")
    public JAXBElement<GetDataInfoRequestFromClient> createBC2MW048Request(GetDataInfoRequestFromClient value) {
        return new JAXBElement<GetDataInfoRequestFromClient>(_BC2MW048Request_QNAME, GetDataInfoRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetDataInfoResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_048Response")
    public JAXBElement<GetDataInfoResponseForClient> createMW2BC048Response(GetDataInfoResponseForClient value) {
        return new JAXBElement<GetDataInfoResponseForClient>(_MW2BC048Response_QNAME, GetDataInfoResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetMemberIdByPhoneFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_049Request")
    public JAXBElement<GetMemberIdByPhoneFromClient> createBC2MW049Request(GetMemberIdByPhoneFromClient value) {
        return new JAXBElement<GetMemberIdByPhoneFromClient>(_BC2MW049Request_QNAME, GetMemberIdByPhoneFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetMemberIdByPhoneForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_049Response")
    public JAXBElement<GetMemberIdByPhoneForClient> createMW2BC049Response(GetMemberIdByPhoneForClient value) {
        return new JAXBElement<GetMemberIdByPhoneForClient>(_MW2BC049Response_QNAME, GetMemberIdByPhoneForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetMemberInfoRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_050Request")
    public JAXBElement<GetMemberInfoRequestFromClient> createBC2MW050Request(GetMemberInfoRequestFromClient value) {
        return new JAXBElement<GetMemberInfoRequestFromClient>(_BC2MW050Request_QNAME, GetMemberInfoRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetMemberInfoResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_050Response")
    public JAXBElement<GetMemberInfoResponseForClient> createMW2BC050Response(GetMemberInfoResponseForClient value) {
        return new JAXBElement<GetMemberInfoResponseForClient>(_MW2BC050Response_QNAME, GetMemberInfoResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberUnitRegistRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_051Request")
    public JAXBElement<MemberUnitRegistRequestFromClient> createBC2MW051Request(MemberUnitRegistRequestFromClient value) {
        return new JAXBElement<MemberUnitRegistRequestFromClient>(_BC2MW051Request_QNAME, MemberUnitRegistRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberUnitRegistResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_051Response")
    public JAXBElement<MemberUnitRegistResponseForClient> createMW2BC051Response(MemberUnitRegistResponseForClient value) {
        return new JAXBElement<MemberUnitRegistResponseForClient>(_MW2BC051Response_QNAME, MemberUnitRegistResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberB2CRegistRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_052Request")
    public JAXBElement<MemberB2CRegistRequestFromClient> createBC2MW052Request(MemberB2CRegistRequestFromClient value) {
        return new JAXBElement<MemberB2CRegistRequestFromClient>(_BC2MW052Request_QNAME, MemberB2CRegistRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberB2CRegistResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_052Response")
    public JAXBElement<MemberB2CRegistResponseForClient> createMW2BC052Response(MemberB2CRegistResponseForClient value) {
        return new JAXBElement<MemberB2CRegistResponseForClient>(_MW2BC052Response_QNAME, MemberB2CRegistResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberChangeB2CPasswordRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_053Request")
    public JAXBElement<MemberChangeB2CPasswordRequestFromClient> createBC2MW053Request(MemberChangeB2CPasswordRequestFromClient value) {
        return new JAXBElement<MemberChangeB2CPasswordRequestFromClient>(_BC2MW053Request_QNAME, MemberChangeB2CPasswordRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberChangeB2CPasswordResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_053Response")
    public JAXBElement<MemberChangeB2CPasswordResponseForClient> createMW2BC053Response(MemberChangeB2CPasswordResponseForClient value) {
        return new JAXBElement<MemberChangeB2CPasswordResponseForClient>(_MW2BC053Response_QNAME, MemberChangeB2CPasswordResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRebuildB2CPasswordRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_054Request")
    public JAXBElement<MemberRebuildB2CPasswordRequestFromClient> createBC2MW054Request(MemberRebuildB2CPasswordRequestFromClient value) {
        return new JAXBElement<MemberRebuildB2CPasswordRequestFromClient>(_BC2MW054Request_QNAME, MemberRebuildB2CPasswordRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRebuildB2CPasswordResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_054Response")
    public JAXBElement<MemberRebuildB2CPasswordResponseForClient> createMW2BC054Response(MemberRebuildB2CPasswordResponseForClient value) {
        return new JAXBElement<MemberRebuildB2CPasswordResponseForClient>(_MW2BC054Response_QNAME, MemberRebuildB2CPasswordResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberBaseInfoUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_055Request")
    public JAXBElement<MemberBaseInfoUpdateRequestFromClient> createBC2MW055Request(MemberBaseInfoUpdateRequestFromClient value) {
        return new JAXBElement<MemberBaseInfoUpdateRequestFromClient>(_BC2MW055Request_QNAME, MemberBaseInfoUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberBaseInfoUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_055Response")
    public JAXBElement<MemberBaseInfoUpdateResponseForClient> createMW2BC055Response(MemberBaseInfoUpdateResponseForClient value) {
        return new JAXBElement<MemberBaseInfoUpdateResponseForClient>(_MW2BC055Response_QNAME, MemberBaseInfoUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseTicketAddPointRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_056Request")
    public JAXBElement<PurchaseTicketAddPointRequestFromClient> createBC2MW056Request(PurchaseTicketAddPointRequestFromClient value) {
        return new JAXBElement<PurchaseTicketAddPointRequestFromClient>(_BC2MW056Request_QNAME, PurchaseTicketAddPointRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PurchaseTicketAddPointResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_056Response")
    public JAXBElement<PurchaseTicketAddPointResponseForClient> createMW2BC056Response(PurchaseTicketAddPointResponseForClient value) {
        return new JAXBElement<PurchaseTicketAddPointResponseForClient>(_MW2BC056Response_QNAME, PurchaseTicketAddPointResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyCardPasswdRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_057Request")
    public JAXBElement<VerifyCardPasswdRequestFromClient> createBC2MW057Request(VerifyCardPasswdRequestFromClient value) {
        return new JAXBElement<VerifyCardPasswdRequestFromClient>(_BC2MW057Request_QNAME, VerifyCardPasswdRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerifyCardPasswdResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_057Response")
    public JAXBElement<VerifyCardPasswdResponseForClient> createMW2BC057Response(VerifyCardPasswdResponseForClient value) {
        return new JAXBElement<VerifyCardPasswdResponseForClient>(_MW2BC057Response_QNAME, VerifyCardPasswdResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberUnionQueryRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_058Request")
    public JAXBElement<MemberUnionQueryRequestFromClient> createBC2MW058Request(MemberUnionQueryRequestFromClient value) {
        return new JAXBElement<MemberUnionQueryRequestFromClient>(_BC2MW058Request_QNAME, MemberUnionQueryRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberUnionQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_058Response")
    public JAXBElement<MemberUnionQueryResponseForClient> createMW2BC058Response(MemberUnionQueryResponseForClient value) {
        return new JAXBElement<MemberUnionQueryResponseForClient>(_MW2BC058Response_QNAME, MemberUnionQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RemainSiteQueryRequestFromCLient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_059Request")
    public JAXBElement<RemainSiteQueryRequestFromCLient> createBC2MW059Request(RemainSiteQueryRequestFromCLient value) {
        return new JAXBElement<RemainSiteQueryRequestFromCLient>(_BC2MW059Request_QNAME, RemainSiteQueryRequestFromCLient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RemainSiteQueryResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_059Response")
    public JAXBElement<RemainSiteQueryResponseForClient> createMW2BC059Response(RemainSiteQueryResponseForClient value) {
        return new JAXBElement<RemainSiteQueryResponseForClient>(_MW2BC059Response_QNAME, RemainSiteQueryResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberGenderUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_060Request")
    public JAXBElement<MemberGenderUpdateRequestFromClient> createBC2MW060Request(MemberGenderUpdateRequestFromClient value) {
        return new JAXBElement<MemberGenderUpdateRequestFromClient>(_BC2MW060Request_QNAME, MemberGenderUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberGenderUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_060Response")
    public JAXBElement<MemberGenderUpdateResponseForClient> createMW2BC060Response(MemberGenderUpdateResponseForClient value) {
        return new JAXBElement<MemberGenderUpdateResponseForClient>(_MW2BC060Response_QNAME, MemberGenderUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeRefundRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_061Request")
    public JAXBElement<FlightConsumeRefundRequestFromClient> createBC2MW061Request(FlightConsumeRefundRequestFromClient value) {
        return new JAXBElement<FlightConsumeRefundRequestFromClient>(_BC2MW061Request_QNAME, FlightConsumeRefundRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link FlightConsumeRefundResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_061Response")
    public JAXBElement<FlightConsumeRefundResponseForClient> createMW2BC061Response(FlightConsumeRefundResponseForClient value) {
        return new JAXBElement<FlightConsumeRefundResponseForClient>(_MW2BC061Response_QNAME, FlightConsumeRefundResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberCreatePasswordRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_062Request")
    public JAXBElement<MemberCreatePasswordRequestFromClient> createBC2MW062Request(MemberCreatePasswordRequestFromClient value) {
        return new JAXBElement<MemberCreatePasswordRequestFromClient>(_BC2MW062Request_QNAME, MemberCreatePasswordRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberCreatePasswordResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_062Response")
    public JAXBElement<MemberCreatePasswordResponseForClient> createMW2BC062Response(MemberCreatePasswordResponseForClient value) {
        return new JAXBElement<MemberCreatePasswordResponseForClient>(_MW2BC062Response_QNAME, MemberCreatePasswordResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BatchQueryMemberRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_063Request")
    public JAXBElement<BatchQueryMemberRequestFromClient> createBC2MW063Request(BatchQueryMemberRequestFromClient value) {
        return new JAXBElement<BatchQueryMemberRequestFromClient>(_BC2MW063Request_QNAME, BatchQueryMemberRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BatchQueryMemberResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_063Response")
    public JAXBElement<BatchQueryMemberResponseForClient> createMW2BC063Response(BatchQueryMemberResponseForClient value) {
        return new JAXBElement<BatchQueryMemberResponseForClient>(_MW2BC063Response_QNAME, BatchQueryMemberResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EmailRejectRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_064Request")
    public JAXBElement<EmailRejectRequestFromClient> createBC2MW064Request(EmailRejectRequestFromClient value) {
        return new JAXBElement<EmailRejectRequestFromClient>(_BC2MW064Request_QNAME, EmailRejectRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EmailRejectResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_064Response")
    public JAXBElement<EmailRejectResponseForClient> createMW2BC064Response(EmailRejectResponseForClient value) {
        return new JAXBElement<EmailRejectResponseForClient>(_MW2BC064Response_QNAME, EmailRejectResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PointMarketActivityRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_065Request")
    public JAXBElement<PointMarketActivityRequestFromClient> createBC2MW065Request(PointMarketActivityRequestFromClient value) {
        return new JAXBElement<PointMarketActivityRequestFromClient>(_BC2MW065Request_QNAME, PointMarketActivityRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PointMarketActivityResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_065Response")
    public JAXBElement<PointMarketActivityResponseForClient> createMW2BC065Response(PointMarketActivityResponseForClient value) {
        return new JAXBElement<PointMarketActivityResponseForClient>(_MW2BC065Response_QNAME, PointMarketActivityResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRegistBindRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_066Request")
    public JAXBElement<MemberRegistBindRequestFromClient> createBC2MW066Request(MemberRegistBindRequestFromClient value) {
        return new JAXBElement<MemberRegistBindRequestFromClient>(_BC2MW066Request_QNAME, MemberRegistBindRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRegistBindResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_066Response")
    public JAXBElement<MemberRegistBindResponseForClient> createMW2BC066Response(MemberRegistBindResponseForClient value) {
        return new JAXBElement<MemberRegistBindResponseForClient>(_MW2BC066Response_QNAME, MemberRegistBindResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRealVerifyRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_067Request")
    public JAXBElement<MemberRealVerifyRequestFromClient> createBC2MW067Request(MemberRealVerifyRequestFromClient value) {
        return new JAXBElement<MemberRealVerifyRequestFromClient>(_BC2MW067Request_QNAME, MemberRealVerifyRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberRealVerifyResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_067Response")
    public JAXBElement<MemberRealVerifyResponseForClient> createMW2BC067Response(MemberRealVerifyResponseForClient value) {
        return new JAXBElement<MemberRealVerifyResponseForClient>(_MW2BC067Response_QNAME, MemberRealVerifyResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PromotionApplyInfoRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_068Request")
    public JAXBElement<PromotionApplyInfoRequestFromClient> createBC2MW068Request(PromotionApplyInfoRequestFromClient value) {
        return new JAXBElement<PromotionApplyInfoRequestFromClient>(_BC2MW068Request_QNAME, PromotionApplyInfoRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PromotionApplyInfoResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_068Response")
    public JAXBElement<PromotionApplyInfoResponseForClient> createMW2BC068Response(PromotionApplyInfoResponseForClient value) {
        return new JAXBElement<PromotionApplyInfoResponseForClient>(_MW2BC068Response_QNAME, PromotionApplyInfoResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TicketSaleRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_069Request")
    public JAXBElement<TicketSaleRequestFromClient> createBC2MW069Request(TicketSaleRequestFromClient value) {
        return new JAXBElement<TicketSaleRequestFromClient>(_BC2MW069Request_QNAME, TicketSaleRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TicketSaleResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_069Response")
    public JAXBElement<TicketSaleResponseForClient> createMW2BC069Response(TicketSaleResponseForClient value) {
        return new JAXBElement<TicketSaleResponseForClient>(_MW2BC069Response_QNAME, TicketSaleResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MileageBudgetRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_070Request")
    public JAXBElement<MileageBudgetRequestFromClient> createBC2MW070Request(MileageBudgetRequestFromClient value) {
        return new JAXBElement<MileageBudgetRequestFromClient>(_BC2MW070Request_QNAME, MileageBudgetRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MileageBudgetResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_070Response")
    public JAXBElement<MileageBudgetResponseForClient> createMW2BC070Response(MileageBudgetResponseForClient value) {
        return new JAXBElement<MileageBudgetResponseForClient>(_MW2BC070Response_QNAME, MileageBudgetResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TempMemberLoginRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_071Request")
    public JAXBElement<TempMemberLoginRequestFromClient> createBC2MW071Request(TempMemberLoginRequestFromClient value) {
        return new JAXBElement<TempMemberLoginRequestFromClient>(_BC2MW071Request_QNAME, TempMemberLoginRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TempMemberLoginResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_071Response")
    public JAXBElement<TempMemberLoginResponseForClient> createMW2BC071Response(TempMemberLoginResponseForClient value) {
        return new JAXBElement<TempMemberLoginResponseForClient>(_MW2BC071Response_QNAME, TempMemberLoginResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CertificateUpdateRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_072Request")
    public JAXBElement<CertificateUpdateRequestFromClient> createBC2MW072Request(CertificateUpdateRequestFromClient value) {
        return new JAXBElement<CertificateUpdateRequestFromClient>(_BC2MW072Request_QNAME, CertificateUpdateRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CertificateUpdateResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_072Response")
    public JAXBElement<CertificateUpdateResponseForClient> createMW2BC072Response(CertificateUpdateResponseForClient value) {
        return new JAXBElement<CertificateUpdateResponseForClient>(_MW2BC072Response_QNAME, CertificateUpdateResponseForClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberVIPRegistRequestFromClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "BC2MW_073Request")
    public JAXBElement<MemberVIPRegistRequestFromClient> createBC2MW073Request(MemberVIPRegistRequestFromClient value) {
        return new JAXBElement<MemberVIPRegistRequestFromClient>(_BC2MW073Request_QNAME, MemberVIPRegistRequestFromClient.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MemberVIPRegistResponseForClient }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "JuneyaoFFP2013.Service.Schema", name = "MW2BC_073Response")
    public JAXBElement<MemberVIPRegistResponseForClient> createMW2BC073Response(MemberVIPRegistResponseForClient value) {
        return new JAXBElement<MemberVIPRegistResponseForClient>(_MW2BC073Response_QNAME, MemberVIPRegistResponseForClient.class, null, value);
    }

}
