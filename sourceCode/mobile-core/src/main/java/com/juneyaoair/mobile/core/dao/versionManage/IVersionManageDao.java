package com.juneyaoair.mobile.core.dao.versionManage;

import com.juneyaoair.mobile.core.bean.admin.DICTValue;
import com.juneyaoair.mobile.core.bean.versionManage.AppInfo;
import com.juneyaoair.mobile.core.bean.versionManage.VersionManage;
import com.juneyaoair.mobile.pagenation.domain.PageList;

import java.util.List;

/**
 * Created by lzg on 2016-05-24.
 */
public interface IVersionManageDao {
    int addVersion(VersionManage versionManage);
    int deletedVersion(VersionManage versionManage);
    int queryVersionManageInfoCount(VersionManage versionManage);
    int updateVersion(VersionManage versionManage);
    List<VersionManage> getList(VersionManage versionManage);
    List<VersionManage> checkVersion(VersionManage versionManage);

    int addAppInfo(AppInfo appinfo);

    /**
     * 分页查询
     * @param versionManage
     */
    PageList<VersionManage> getListByPage(VersionManage versionManage);
}
