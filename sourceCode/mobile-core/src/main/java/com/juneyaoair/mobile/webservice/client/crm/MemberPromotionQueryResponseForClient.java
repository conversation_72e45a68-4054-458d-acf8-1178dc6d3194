
package com.juneyaoair.mobile.webservice.client.crm;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>MemberPromotionQueryResponseForClient complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="MemberPromotionQueryResponseForClient"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MessageHeader" type="{JuneyaoFFP2013.Service.Schema}ResponseMessageHeaderType" minOccurs="0"/&gt;
 *         &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="PageSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="CurrentPage" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TotalPage" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TotalRecord" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="MemberPromotionInfo" type="{JuneyaoFFP2013.Service.Schema}MemberPromotionInfoType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MemberPromotionQueryResponseForClient", propOrder = {
    "messageHeader",
    "fromDate",
    "toDate",
    "pageSize",
    "currentPage",
    "totalPage",
    "totalRecord",
    "memberPromotionInfo"
})
public class MemberPromotionQueryResponseForClient {

    @XmlElement(name = "MessageHeader")
    protected ResponseMessageHeaderType messageHeader;
    @XmlElement(name = "FromDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar fromDate;
    @XmlElement(name = "ToDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar toDate;
    @XmlElement(name = "PageSize")
    protected int pageSize;
    @XmlElement(name = "CurrentPage")
    protected int currentPage;
    @XmlElement(name = "TotalPage")
    protected int totalPage;
    @XmlElement(name = "TotalRecord")
    protected long totalRecord;
    @XmlElement(name = "MemberPromotionInfo")
    protected List<MemberPromotionInfoType> memberPromotionInfo;

    /**
     * 获取messageHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public ResponseMessageHeaderType getMessageHeader() {
        return messageHeader;
    }

    /**
     * 设置messageHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public void setMessageHeader(ResponseMessageHeaderType value) {
        this.messageHeader = value;
    }

    /**
     * 获取fromDate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFromDate() {
        return fromDate;
    }

    /**
     * 设置fromDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFromDate(XMLGregorianCalendar value) {
        this.fromDate = value;
    }

    /**
     * 获取toDate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getToDate() {
        return toDate;
    }

    /**
     * 设置toDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setToDate(XMLGregorianCalendar value) {
        this.toDate = value;
    }

    /**
     * 获取pageSize属性的值。
     * 
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 设置pageSize属性的值。
     * 
     */
    public void setPageSize(int value) {
        this.pageSize = value;
    }

    /**
     * 获取currentPage属性的值。
     * 
     */
    public int getCurrentPage() {
        return currentPage;
    }

    /**
     * 设置currentPage属性的值。
     * 
     */
    public void setCurrentPage(int value) {
        this.currentPage = value;
    }

    /**
     * 获取totalPage属性的值。
     * 
     */
    public int getTotalPage() {
        return totalPage;
    }

    /**
     * 设置totalPage属性的值。
     * 
     */
    public void setTotalPage(int value) {
        this.totalPage = value;
    }

    /**
     * 获取totalRecord属性的值。
     * 
     */
    public long getTotalRecord() {
        return totalRecord;
    }

    /**
     * 设置totalRecord属性的值。
     * 
     */
    public void setTotalRecord(long value) {
        this.totalRecord = value;
    }

    /**
     * Gets the value of the memberPromotionInfo property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the memberPromotionInfo property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMemberPromotionInfo().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link MemberPromotionInfoType }
     * 
     * 
     */
    public List<MemberPromotionInfoType> getMemberPromotionInfo() {
        if (memberPromotionInfo == null) {
            memberPromotionInfo = new ArrayList<MemberPromotionInfoType>();
        }
        return this.memberPromotionInfo;
    }

}
