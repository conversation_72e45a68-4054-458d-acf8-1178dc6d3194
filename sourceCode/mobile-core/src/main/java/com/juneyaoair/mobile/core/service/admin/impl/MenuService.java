
/**
 * @标题: MenuService.java
 * @包名： com.cares.mcp.services.admin
 * @功能描述：TODO
 * @作者： jason
 * @创建时间： 2014年10月6日 下午5:48:01
 * @version v0.0.1
 */

package com.juneyaoair.mobile.core.service.admin.impl;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


import com.juneyaoair.mobile.core.bean.authentication.Menu;
import com.juneyaoair.mobile.core.bean.authentication.RoleMenu;
import com.juneyaoair.mobile.core.dao.admin.IMenuDao;
import com.juneyaoair.mobile.core.service.admin.IMenuService;
import com.juneyaoair.utils.exception.BusinessException;
import com.juneyaoair.utils.util.DeleteUtil;
import com.juneyaoair.utils.util.StrLengthUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.validation.ObjectError;
import org.springframework.validation.Validator;


/**
 * @类描述： 菜单  菜单角色权限控制 服务类
 * @项目名称：com-juneyaoair-preparation-war
 * @包名： com.cares.mcp.services.admin
 * @类名称：MenuService
 * @创建人：jason
 * @创建时间：2014年10月6日下午5:48:01
 * @修改人：jason
 * @修改时间：2014年10月6日下午5:48:01
 * @修改备注：
 * @version v0.0.1
 */
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
@Service("menuService")
public class MenuService implements IMenuService {
	// 菜单类数据访问对象
	@Autowired
	private IMenuDao menuDao;
	// 菜单的校验类  jason add 2014-11-20
	@Autowired
	private Validator menuValidation;

//	@Resource(name="baseDao")
//	private BaseDao<Menu> baseDao;
	
	/**
	 * @描述:  根据条件 查询菜单项
	 * @方法名: selectMenus
	 * @param menu
	 * @return
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */
	@Override
	public List<Menu> selectMenus(Menu menu) {
		if(menu == null) {
            menu = new Menu();
        }
		return this.menuDao.selectMenus(menu);
	}
	
	/**
	 * 
	 * @描述: 查询菜单的树形结构
	 * @方法名: selectMenusTree
	 * @param menu
	 * @return
	 * @返回类型 List<Menu>
	 * @创建人 jason
	 * @创建时间 2014年11月14日下午3:29:14
	 * @修改人 jason
	 * @修改时间 2014年11月14日下午3:29:14
	 * @修改备注
	 * @since
	 * @throws
	 */
	@Override
	public Map<String, Object> selectMenusTree(Menu menu){
//		List<Menu> tree = new ArrayList<Menu>();
		List<Menu> menus = this.menuDao.selectMenus(menu);
		List<Menu> rootMenu = this.menuDao.findRootMenu();
		//remove掉rootmenu根菜单
//		for(int i=0; i<menus.size(); i++){
//			if(menus.get(i).getMenuId().equals(ProjectConstant.EMP_UUID)){
//				menus.remove(i);
//				break;
//			}
//		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("menus", menus);
		map.put("rootMenu", rootMenu);
//		Map<String, Menu> map = new HashMap<String, Menu>(); 
//		// 进行索引
//		for(Menu m : menus){
//			map.put(m.getMenuId(), m);
//		}
//		// 进行树的组装
//		for(Menu m : menus){
//			// 根节点
//			if(StringUtils.isBlank(m.getParentId()) 
//					|| ProjectConstant.EMP_UUID.equals(m.getParentId())){
//				tree.add(m);
//			}
//			// 
//			else{
//				if(map.containsKey(m.getParentId())){
//					Menu p = map.get(m.getParentId());
//					if(p.getMenus() == null){
//						p.setMenus(new ArrayList<Menu>());
//					}
//					p.getMenus().add(m);
//				}
//				else{
//					tree.add(m);
//				}
//			}
//		}
		return map;
	}
	/**
	 * @描述:
	 * @方法名: updateMenu
	 * @param menu
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */
	@Override
	public void updateMenu(Menu menu) {
		Errors error = new BindException (menu, Menu.class.getName());
		menuValidation.validate(menu, error);
		if(error.hasErrors()){
			throw new BusinessException("校验出错");//将异常抛出
		}
		// 校验菜单的合法性
		if(StringUtils.isBlank(menu.getMenuId())){
			throw new BusinessException("要修改的菜单信息为空");
		}
		List<Menu> rootMenu = this.menuDao.findRootMenu();
		//根据父菜单的menuCode设置parentId
		for(int i=0; i<rootMenu.size(); i++){
			if(menu.getParentCode().equals(rootMenu.get(i).getMenuCode())){
				menu.setParentId(rootMenu.get(i).getMenuId());
				break;
			}
		}
		//后台字符串长度校验
		if(StrLengthUtil.StrLengthValidate(menu.getMenuName(), 31)){
			throw new BusinessException("菜单名称不能超过15个字！");
		}
		if(StrLengthUtil.StrLengthValidate(menu.getUrl(), 63)){
			throw new BusinessException("URL地址不能超过60个字母！");
		}
		if(StrLengthUtil.StrLengthValidate(String.valueOf(menu.getRowIndex()), 2)){
			throw new BusinessException("优先级不能超过2位数字");
		}
		
		menuDao.updateMenu(menu);
	}

	/**
	 * @描述:   新增菜单信息  新增的菜单信息必须是叶子节点
	 *         如果存在父节点  父节点的isleaf属性要重新设置
	 * @方法名: createMenu
	 * @param menu
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */

	@Override
	public void createMenu(Menu menu) {
		Errors error = new BindException (menu, Menu.class.getName());
		menuValidation.validate(menu, error);
		if(error.hasErrors()){
			List<ObjectError> errs = error.getAllErrors();
			for(Object err : errs){
				System.out.println(err.toString());
			}
			throw new BusinessException("校验出错");//将异常抛出
		}
		
		List<Menu> rootMenu = this.menuDao.findRootMenu();
		//根据父菜单的menuCode设置parentId
		for(int i=0; i<rootMenu.size(); i++){
			if(menu.getParentCode().equals(rootMenu.get(i).getMenuCode())){
				menu.setParentId(rootMenu.get(i).getMenuId());
				break;
			}
		}
		
		// 判断父节点是否为空
//		if(StringUtils.isNotBlank(menu.getParentId())
//				&& !menu.getParentId().equals(ProjectConstant.EMP_UUID)){
//			// 修改父节点的isleaf 属性为N；
//			Menu parent = new Menu();
//			parent.setMenuId(menu.getMenuId());
//			parent.setIsLeaf(ProjectConstant.NO);
//			menuDao.updateMenu(parent);
//		}
		
		// 生成菜单的编号
		Timestamp ts = new Timestamp(System.currentTimeMillis());
		DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss"); 
		menu.setMenuCode("MENU" + sdf.format(ts));
		// 设置菜单的id
		menu.setMenuId(UUID.randomUUID().toString());
		//后台字符串长度校验
		if(StrLengthUtil.StrLengthValidate(menu.getMenuName(), 31)){
			throw new BusinessException("菜单名称不能超过15个字！");
		}
		if(StrLengthUtil.StrLengthValidate(menu.getUrl(), 63)){
			throw new BusinessException("URL地址不能超过60个字母！");
		}
		if(StrLengthUtil.StrLengthValidate(String.valueOf(menu.getRowIndex()), 2)){
			throw new BusinessException("优先级不能超过2位数字");
		}
		
		// 保存菜单信息
		menuDao.createMenu(menu);
	}

	/**
	 * @描述:  删除菜单信息 不是叶子节点不允许删除 
	 * @方法名: deleteMenu
	 * @param menu
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */

	@Override
	public void deleteMenu(Menu menu) {
		if(StringUtils.isBlank(menu.getMenuId())){
			throw new BusinessException("删除的菜单不存在");
		}
		
		List<Menu> list = menuDao.selectMenus(new Menu());
		//判断用户选择删除的菜单是否有子菜单
		for(int i=0; i<list.size(); i++){
			if(menu.getMenuId().equals(list.get(i).getParentId())){
				throw new BusinessException("删除的菜单还有子菜单，不能删除！");
			}
		}
		
		this.menuDao.deleteMenu(menu);
		RoleMenu roleMenu = new RoleMenu();
		//删除角色菜单对应关系表中的对应关系数据
		roleMenu.setMenuId(menu.getMenuId());
		List<RoleMenu> rmList = this.menuDao.selectRoleMenus(roleMenu);
		//判断菜单角色表中是否存在该菜单的数据
		if(rmList != null && !rmList.isEmpty()){
			//删除角色和菜单对应关系表中的相应菜单数据
			this.menuDao.deleteRoleMenu(roleMenu);
		}
		
		// 查看菜单是否存在
//		Menu param = new Menu();
//		param.setParentId(menu.getMenuId());
//		List<Menu> lst = this.menuDao.selectMenus(menu);
//		if(lst != null && lst.size() > 0){
//			Menu m = lst.get(0);
//			if(m.getIsLeaf().equals(ProjectConstant.NO)){
//				throw new BusinessException("删除的菜单还有子菜单不允许删除");
//			}
//			else{
//				this.menuDao.deleteMenu(menu);
//			}
//		}
		// 还有没有兄弟菜单 如果没有 就改为子菜单
//		param.setParentId(menu.getParentId());
//		lst = this.menuDao.selectMenus(menu);
//		if(lst == null || lst.size() == 0){
//			param.setParentId(null);
//			param.setMenuId(menu.getParentId());
//			param.setIsLeaf(ProjectConstant.YES);
//			this.menuDao.updateMenu(param);
//		}
		
	}

	/**
	 * @描述:  查询菜单对应的角色信息
	 * @方法名: selectRoleUnAuthMenus
	 * @param roleId
	 * @return
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */

	@Override
	public List<Menu> selectRoleUnAuthMenus(String roleId) {
		
		return null;
	}

	/**
	 * @描述:
	 * @方法名: selectRoleMenus
	 * @param roleMenu
	 * @return
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */
	@Override
	public List<RoleMenu> selectRoleMenus(RoleMenu roleMenu) {
		List<RoleMenu> list = menuDao.selectRoleMenus(roleMenu);
		return list;
	}
	
	/**
	 * @作者：wang
	 * @方法：selectMenuForRole 查询不同角色的菜单信息
	 * @参数：roleMenu 用户输入的角色信息
	 * @返回值：查询到的菜单信息
	 */
	@Override
	public List<Menu> selectMenuForRole(Menu menu){
		List<Menu> menuList = menuDao.selectMenus(menu);

		RoleMenu roleMenu = new RoleMenu();
		roleMenu.setRoleId(menu.getRoleId());
		List<RoleMenu> roleMenuList = menuDao.selectRoleMenus(roleMenu);
//		for(int i=0; i<menuList.size(); i++){
//			if(menuList.get(i).getMenuId().equals(ProjectConstant.EMP_UUID)){
//				menuList.remove(i);
//				break;
//			};
//		}
		if(roleMenuList != null && !roleMenuList.isEmpty()){
			for(int i=0; i<menuList.size(); i++){
				for(int j=0; j<roleMenuList.size(); j++){
					if(menuList.get(i).getMenuId().equals(roleMenuList.get(j).getMenuId())){
						menuList.get(i).setIsRoleMenu("Y");
					}
				}
			}
		}
		return menuList;
	}

	/**
	 * @描述:
	 * @方法名: createRoleMenu
	 * @param
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */
	@Override
	public void createRoleMenu(String roleId, String menuId) {
		if(StringUtils.isBlank(roleId)){
			throw new BusinessException("请选择需要分配菜单的角色！");
		}
		RoleMenu roleMenu = new RoleMenu();
		roleMenu.setRoleId(roleId);
		//判断用户是否添加了菜单信息，若用户没有添加任何菜单信息，则将角色菜单对应表中相应角色的对应菜单全部删除
		if(StringUtils.isNotBlank(menuId)){
			//查询数据库表中该角色对应的菜单信息
			List<RoleMenu> selectList = menuDao.selectRoleMenus(roleMenu);
			//如果该角色存在对应菜单，删除该角色的所有对应菜单信息
			if(selectList != null && !selectList.isEmpty()){
				this.menuDao.deleteRoleMenu(roleMenu);
			}
			//获取用户选择的菜单ID
			List<String> list = DeleteUtil.getDeleteData(menuId);
			//存放需要添加的角色对应菜单信息
			List<RoleMenu> insertList = new ArrayList<RoleMenu>();
			for(int i=0; i<list.size(); i++){
				RoleMenu rm = new RoleMenu();
				rm.setRmid(UUID.randomUUID().toString());
				rm.setMenuId(list.get(i));
				rm.setRoleId(roleId);
				insertList.add(rm);
			}
			this.menuDao.createRoleMenu(insertList);
		} else {
			List<RoleMenu> rmList = menuDao.selectRoleMenus(roleMenu);
			if(rmList != null && !rmList.isEmpty()){
				this.menuDao.deleteRoleMenu(roleMenu);
			}
		}
	}

	/**
	 * @描述:
	 * @方法名: deleteRoleMenu
	 * @param roleMenu
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */
/*
	@Override
	public void deleteRoleMenu(RoleMenu roleMenu) {
		

	}
*/
	/**
	 * @描述:
	 * @方法名: updateRoleMenu
	 * @param roleMenu
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午5:48:01
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午5:48:01
	 * @修改备注：
	 * @throws
	 */
/*
	@Override
	public void updateRoleMenu(RoleMenu roleMenu) {
		
	}
*/
}
