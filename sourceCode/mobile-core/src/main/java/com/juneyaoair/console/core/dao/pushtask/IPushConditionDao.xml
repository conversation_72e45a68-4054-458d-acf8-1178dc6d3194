<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pushConditionDao">
    <resultMap id="pushCondition" type="PushCondition">
        <id column="GUID" property="guid" jdbcType="VARCHAR"></id>
        <result column="CONDITION_JSON" property="conditionJson" jdbcType="VARCHAR"></result>
        <result column="LINK_ID" property="linkId" jdbcType="VARCHAR"></result>
        <result column="CONDITION_STATE" property="conditionState" jdbcType="VARCHAR"></result>
        <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"></result>
    </resultMap>
    <insert id="insertPushCondition" parameterType="PushCondition">
        INSERT INTO T_PUSH_CONDITION (
        GUID,
        CONDITION_JSON,
        CREATE_TIME,
        UPDATE_TIME,
        CONDITION_STATE,
        SOURCE_TYPE,
        LINK_ID)
        VALUES(
        #{guid,jdbcType=VARCHAR},
        #{conditionJson,jdbcType=VARCHAR},
        #{createTime},
        #{updateTime},
        #{conditionState,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{linkId,jdbcType=VARCHAR}
        )
    </insert>
    <select id="queryPushCondition" parameterType="PushCondition" resultType="PushCondition">
      SELECT
      GUID,
      CONDITION_JSON,
      CONDITION_STATE,
      SOURCE_TYPE
      FROM T_PUSH_CONDITION
      WHERE 1=1
        <if test="linkId !=null and linkId != ''">
            and LINK_ID = #{linkId}
        </if>
        <if test="sourceType !=null and sourceType != ''">
            and SOURCE_TYPE = #{sourceType}
        </if>
    </select>
    <!--查询未执行的计划任务-->
    <select id="queryNotDo" parameterType="PushCondition" resultMap="pushCondition">
        SELECT
        GUID,
        CONDITION_JSON,
        CONDITION_STATE,
        SOURCE_TYPE,
        LINK_ID
        FROM T_PUSH_CONDITION t
        WHERE t.CONDITION_STATE = 'N'
    </select>
    <!--更新计划任务状态-->
    <update id="updatePushCondition" parameterType="PushCondition">
        UPDATE  T_PUSH_CONDITION t
        SET
        t.CONDITION_STATE = 'Y',
        t.UPDATE_TIME = #{updateTime,jdbcType=DATE}
        WHERE t.GUID = #{guid}
        AND t.CONDITION_STATE = 'N'
    </update>
</mapper>