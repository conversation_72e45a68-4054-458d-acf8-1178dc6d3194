package com.juneyaoair.baseclass.coupon.request.v2;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.coupon.response.v2.PhoneCard;
import com.juneyaoair.baseclass.coupon.response.v2.PickupAddressDto;
import com.juneyaoair.baseclass.delivery.DeliveryAddress;
import com.juneyaoair.pattern.PatternCommon;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @description 电话卡购买参数
 * @date 2019/4/28  13:38.
 */
@Data
public class PhoneCardProBuy extends UserInfoMust {
    @NotBlank(message = "渠道订单号不可为空")
    private String channelOrderNo;
    @Min(value = 1,message = "至少购买一张")
    private int bookingCount;
    private String pickUpdate;
    @NotNull(message = "总金额不可为空")
    private Double totalAmount;
    @NotBlank(message = "联系人信息不可为空")
    private String linker;
    @NotBlank(message = "联系电话不可为空")
    @Pattern(regexp = PatternCommon.MOBILE_PHONE,message = "请输入正确的手机号")
    private String linkPhone;
    /**
     * selfTaking-自取，express-快递
     */
    @NotBlank(message = "取卡方式不可为空")
    private String pickUpType;
    @Valid
    @NotNull(message = "电话卡产品不可为空")
    private PhoneCard phoneCard;
    /**
     * 邮寄时使用
     */
    private DeliveryAddress deliveryAddress;
    /**
     * 自取时使用
     */
    private PickupAddressDto pickupAddressDto;

}
