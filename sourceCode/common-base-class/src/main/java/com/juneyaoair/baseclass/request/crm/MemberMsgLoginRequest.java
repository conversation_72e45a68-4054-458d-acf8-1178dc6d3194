package com.juneyaoair.baseclass.request.crm;

import com.juneyaoair.pattern.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/26.
 * 短信验证码登录
 */
@ApiModel(value="MemberMsgLoginRequest",description="短信登录请求结构")
@XmlRootElement(name = "MemberMsgLoginRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class MemberMsgLoginRequest {
    @ApiModelProperty(value="渠道号",name="channelCode",example="MOBILE",required=true)
    @NotNull(message="渠道用户号不能为空")
    private String channelCode; // 渠道用户号B2C,CC,WEIXIN,MOBILE等
    @NotNull(message="用户名不能为空")
    @Pattern(regexp = PatternCommon.MOBILE_PHONE,message = "请输入正确的手机号")
    @ApiModelProperty(value="手机号",name="loginCode",example="18221329918",required=true)
    private String loginCode;
    @NotNull(message="短信验证码不能为空")
    @ApiModelProperty(value="短信验证码",name="veriCode",example="123456",required=true)
    private String veriCode;
    @ApiModelProperty(value="设备唯一号",name="deviceId",example="864260027372072",required=true)
    private String deviceId;//设备信息
    @ApiModelProperty(value="操作系统信息",name="systemInfo",example="Android 4.4.4",required=true)
    private String systemInfo;//操作系统信息
    @ApiModelProperty(value="推送号",name="pushNum",example="160a3797c800a0eb207",required=true)
    private String pushNum;
    @ApiModelProperty(hidden = true)
    private String ip;
    @ApiModelProperty(value="客户端版本号",name="clientVersion",example="5.0",required=true)
    private String clientVersion;//当前发起请求的客户端版本
    @ApiModelProperty(hidden = true)
    private String loginMethodToken;



    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getLoginCode() {
        return loginCode;
    }

    public void setLoginCode(String loginCode) {
        this.loginCode = loginCode;
    }

    public String getVeriCode() {
        return veriCode;
    }

    public void setVeriCode(String veriCode) {
        this.veriCode = veriCode;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSystemInfo() {
        return systemInfo;
    }

    public void setSystemInfo(String systemInfo) {
        this.systemInfo = systemInfo;
    }

    public String getPushNum() {
        return pushNum;
    }

    public void setPushNum(String pushNum) {
        this.pushNum = pushNum;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public String getLoginMethodToken() {
        return loginMethodToken;
    }

    public void setLoginMethodToken(String loginMethodToken) {
        this.loginMethodToken = loginMethodToken;
    }
}
