package com.juneyaoair.baseclass.request.speedRefund;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * Created by yaocf on 2017/1/18.
 * 客票快退请求
 */
public class TicketRefundReq {
    @NotNull(message="预定人常客ID不能为空")
    private String ffpId; //预订人常客ID
    @NotNull(message="渠道用户号不能为空")
    private String channelCode;
    @NotNull(message="预定人卡号不能为空")
    private String ffpCardNo;
    @NotNull(message="渠道退款编号不能为空")
    private String channelPayoutNo;
    @NotNull(message="航程类型不能为空")
    private String routeType;
    @NotNull(message="国内国际标识不能为空")
    private String interFlag;
    @NotNull(message="币种代码不能为空")
    private String currencyCode;
    @NotNull(message="手机号不能为空")
    private String handphoneNo;
    @NotNull(message="退款总金额不能为空")
    private Double refundAmount;
    private String pNR;
    @NotNull(message="银行账户不能为空")
    private String bankAccountNO;
    @NotNull(message="乘客信息不能为空")
    @Size(min=1,message="乘客信息不能少于1人")
    private List<PassengerInfo> passengerInfoList;
    @NotNull(message="退票航段信息不能为空")
    @Size(min=1,message="退票航段信息不能少于1段")
    private List<RefundSegmentInfo> refundSegmentInfoList;
    @NotNull(message="验证信息不能为空")
    private String loginKeyInfo;
    private String refundType;//退票类型
    private String ip;


    private String bankName;//银行名称
    private String clientVersion;//APP 版本号
    private String platformInfo; //平台信息

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getChannelPayoutNo() {
        return channelPayoutNo;
    }

    public void setChannelPayoutNo(String channelPayoutNo) {
        this.channelPayoutNo = channelPayoutNo;
    }

    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }

    public String getInterFlag() {
        return interFlag;
    }

    public void setInterFlag(String interFlag) {
        this.interFlag = interFlag;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getHandphoneNo() {
        return handphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        this.handphoneNo = handphoneNo;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getpNR() {
        return pNR;
    }

    public void setpNR(String pNR) {
        this.pNR = pNR;
    }

    public String getBankAccountNO() {
        return bankAccountNO;
    }

    public void setBankAccountNO(String bankAccountNO) {
        this.bankAccountNO = bankAccountNO;
    }

    public List<PassengerInfo> getPassengerInfoList() {
        return passengerInfoList;
    }

    public void setPassengerInfoList(List<PassengerInfo> passengerInfoList) {
        this.passengerInfoList = passengerInfoList;
    }

    public List<RefundSegmentInfo> getRefundSegmentInfoList() {
        return refundSegmentInfoList;
    }

    public void setRefundSegmentInfoList(List<RefundSegmentInfo> refundSegmentInfoList) {
        this.refundSegmentInfoList = refundSegmentInfoList;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public String getPlatformInfo() {
        return platformInfo;
    }

    public void setPlatformInfo(String platformInfo) {
        this.platformInfo = platformInfo;
    }
}
