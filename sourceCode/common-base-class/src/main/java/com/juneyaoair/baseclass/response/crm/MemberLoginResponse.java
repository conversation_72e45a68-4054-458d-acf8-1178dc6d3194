package com.juneyaoair.baseclass.response.crm;


import java.util.List;

/**
 * Created by qinxiaoming on 2016-4-26.
 */
public class MemberLoginResponse {
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息

    private long id;
    private String memberID;
    private String name;
    private String title;
    private String levelName;
    private String statusName;
    private String addressState;
    private String memberPasswordState;
    private String mobileVerifyState;
    private String emailVerifyState;
    private long balanceOfMileage;
    private long last10MonthClubMiles;
    private long last10MonthSegments;
    private long nearingExpiredMiles;
    private long nearingExpiredMiles2;
    private long nearingExpiredMiles3;
    private String guid;
    private String loginKeyInfo;
    private String certType;
    private String certNumber;
    private String memberLevelCode;
    private String memberStatusCode;
    private String memberType;
    private String memberTel;
    private String memberEmail;
    private String sex;

    // 中文姓
    private String cLastName;

    // 中文名
    private String cFirstName;

    // 英文姓
    private String eLastName;

    // 英文名
    private String eFirstName;

    // 出生日期
    private String birthday;

    // 国家代码
    private String countryCode;

    // 省份代码
    private String provinceCode;

    // 城市代码
    private String addressCityCode;

    // 地址
    private String addressContent;

    // 邮编
    private String postCode;

    // 实名认证状态
    private String realVerifyStatus;

    // 实名认证方式
    private String realVerifyChannel;

    private List<CustomerCertificateInfo> customerCertificateInfos;
    /**
     * 是否是首次注册登录用户
     * true 为新用户
     */
    private boolean newUser;
    //token
    private String token;
    //失效日期
    private String expiryTime;

    //头像URL
    private String headImageUrl;

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getMemberID() {
        return memberID;
    }

    public void setMemberID(String memberID) {
        this.memberID = memberID;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getAddressState() {
        return addressState;
    }

    public void setAddressState(String addressState) {
        this.addressState = addressState;
    }

    public String getMemberPasswordState() {
        return memberPasswordState;
    }

    public void setMemberPasswordState(String memberPasswordState) {
        this.memberPasswordState = memberPasswordState;
    }

    public String getMobileVerifyState() {
        return mobileVerifyState;
    }

    public void setMobileVerifyState(String mobileVerifyState) {
        this.mobileVerifyState = mobileVerifyState;
    }

    public String getEmailVerifyState() {
        return emailVerifyState;
    }

    public void setEmailVerifyState(String emailVerifyState) {
        this.emailVerifyState = emailVerifyState;
    }

    public long getBalanceOfMileage() {
        return balanceOfMileage;
    }

    public void setBalanceOfMileage(long balanceOfMileage) {
        this.balanceOfMileage = balanceOfMileage;
    }

    public long getLast10MonthClubMiles() {
        return last10MonthClubMiles;
    }

    public void setLast10MonthClubMiles(long last10MonthClubMiles) {
        this.last10MonthClubMiles = last10MonthClubMiles;
    }

    public long getLast10MonthSegments() {
        return last10MonthSegments;
    }

    public void setLast10MonthSegments(long last10MonthSegments) {
        this.last10MonthSegments = last10MonthSegments;
    }

    public long getNearingExpiredMiles() {
        return nearingExpiredMiles;
    }

    public void setNearingExpiredMiles(long nearingExpiredMiles) {
        this.nearingExpiredMiles = nearingExpiredMiles;
    }

    public long getNearingExpiredMiles2() {
        return nearingExpiredMiles2;
    }

    public void setNearingExpiredMiles2(long nearingExpiredMiles2) {
        this.nearingExpiredMiles2 = nearingExpiredMiles2;
    }

    public long getNearingExpiredMiles3() {
        return nearingExpiredMiles3;
    }

    public void setNearingExpiredMiles3(long nearingExpiredMiles3) {
        this.nearingExpiredMiles3 = nearingExpiredMiles3;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCertNumber() {
        return certNumber;
    }

    public void setCertNumber(String certNumber) {
        this.certNumber = certNumber;
    }

    public String getMemberLevelCode() {
        return memberLevelCode;
    }

    public void setMemberLevelCode(String memberLevelCode) {
        this.memberLevelCode = memberLevelCode;
    }

    public String getMemberStatusCode() {
        return memberStatusCode;
    }

    public void setMemberStatusCode(String memberStatusCode) {
        this.memberStatusCode = memberStatusCode;
    }

    public String getMemberType() {
        return memberType;
    }

    public void setMemberType(String memberType) {
        this.memberType = memberType;
    }

    public String getMemberTel() {
        return memberTel;
    }

    public void setMemberTel(String memberTel) {
        this.memberTel = memberTel;
    }

    public String getMemberEmail() {
        return memberEmail;
    }

    public void setMemberEmail(String memberEmail) {
        this.memberEmail = memberEmail;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public List<CustomerCertificateInfo> getCustomerCertificateInfos() {
        return customerCertificateInfos;
    }

    public void setCustomerCertificateInfos(List<CustomerCertificateInfo> customerCertificateInfos) {
        this.customerCertificateInfos = customerCertificateInfos;
    }

    public String getcFirstName() {
        return cFirstName;
    }

    public void setcFirstName(String cFirstName) {
        this.cFirstName = cFirstName;
    }

    public String getcLastName() {
        return cLastName;
    }

    public void setcLastName(String cLastName) {
        this.cLastName = cLastName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String geteFirstName() {
        return eFirstName;
    }

    public void seteFirstName(String eFirstName) {
        this.eFirstName = eFirstName;
    }

    public String geteLastName() {
        return eLastName;
    }

    public void seteLastName(String eLastName) {
        this.eLastName = eLastName;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getAddressCityCode() {
        return addressCityCode;
    }

    public void setAddressCityCode(String addressCityCode) {
        this.addressCityCode = addressCityCode;
    }

    public String getAddressContent() {
        return addressContent;
    }

    public void setAddressContent(String addressContent) {
        this.addressContent = addressContent;
    }

    public String getRealVerifyChannel() {
        return realVerifyChannel;
    }

    public void setRealVerifyChannel(String realVerifyChannel) {
        this.realVerifyChannel = realVerifyChannel;
    }

    public String getRealVerifyStatus() {
        return realVerifyStatus;
    }

    public void setRealVerifyStatus(String realVerifyStatus) {
        this.realVerifyStatus = realVerifyStatus;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(String expiryTime) {
        this.expiryTime = expiryTime;
    }

    public boolean isNewUser() {
        return newUser;
    }

    public void setNewUser(boolean newUser) {
        this.newUser = newUser;
    }

    public String getHeadImageUrl() {
        return headImageUrl;
    }

    public void setHeadImageUrl(String headImageUrl) {
        this.headImageUrl = headImageUrl;
    }
}
