package com.juneyaoair.baseclass.common.response;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/22 14:11
 */
public enum ResponseCode implements ResultCode {
    SUCCESS(200, "成功"),
    FAIL(200, "访问失败"),
    REQUEST_VALIDATION_FAILED(400, "请求数据格式验证失败"),
    LOCK_OCCUPIED(409, "任务已被锁定，请稍后重试"),
    SYSTEM_ERROR(500, "系统繁忙，请稍后再试"),
    NO_REAL_NAME(500, "请先完成实名认证"),
    REAL_NAME_EXPIRED(500,"实名认证超过期限"),
    NET_ERROR(500,"网络调用繁忙"),
    ;

    private int status;
    private String message;

    ResponseCode(int status, String message) {
        this.status = status;
        this.message = message;
    }

    @Override
    public int getStatus() {
        return this.status;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
