package com.juneyaoair.baseclass.request.booking;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.av.comm.PrivilegeDto;
import com.juneyaoair.thirdentity.response.tax.InternatTaxInfo;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PassengerInfo {
    @SerializedName("PassengerID")
    private int passengerID; // //乘客ID保存在统一订单数据库中的ID

    @SerializedName("PassengerNO")
    private int passengerNO; //乘客序号第一位乘客为0开始，第二位为1，依次增加1

    @SerializedName("PassengerName")
    private String passengerName; //乘客姓名

    @SerializedName("PassengerType")
    private String passengerType; //乘客类型ADT － 成人，CHD － 儿童，INF － 婴儿
    private String passengerIdentity; //乘客身份VIP，教师，警殘，革伤  留学生（STU）
    private List<String> passengerIdentityUrl; //乘客身份资质证明url

    @SerializedName("CertType")
    private String certType; //证件类型身份证:NI,护照:PP,其它证件:CC

    @SerializedName("CertNo")
    private String certNo; //证件号码

    /**
     * common乘机人id
     */
    @SerializedName("CommonContactId")
    @ApiModelProperty("common乘机人id")
    private Integer commonContactId;

    /**
     * 证件id
     */
    @SerializedName("GeneralContactCertId")
    @ApiModelProperty("证件id")
    private Integer generalContactCertId;

    /**
     * general乘机人id
     */
    @SerializedName("GeneralContactId")
    @ApiModelProperty("general乘机人id")
    private Integer generalContactId;


    /**
     * 一单多券CouponCode, 前端单个CouponCode,下单时逗号分隔合并下单
     * <p>
     * 由此couponCode聚合
     */
    private String couponCode;

    @SerializedName("FfCardNo")
    private String ffCardNo; //常客卡号HO+卡号
    private String countryTelCode; //手机号国际区号默认值86
    private String handphoneNo; //手机号购买保险必填
    private double ticketPrice; //票面价为Price.RSP销售参考价加总
    private double pricePaid; //实际支付票价当有积分支付时的实际支付票价是Price.PriceValue - 积分抵用金额
    private double yQTax; //燃油费
    private double cNTax; //建设税
    private double otherTax; //其它税费
    private double qFee; //Q费
    private String adtNameToInf; //婴儿绑定的成人姓名

    @SerializedName("Birthdate")
    private String birthdate; //出生日期yyyy-MM-dd儿童、婴儿和国际票必填
    @SerializedName("Sex")
    private String sex; //性别国际票必填

    private String nationality; //国籍国际票必填
    public String nationalityName; //国籍名称 中文名 国际票必填
    public String nationalityEName; //国籍名称 英文名 国际票必填
    private String belongCountry; //发证国国际票必填
    public String belongCountryName; //发证国国际票 中文名 （非必须）
    public String belongCountryEName; //发证国国际票 英文名 国际票必填
    private String certValidity; //证件有效期yyyy-MM-dd国际票必填
    private String isBuyInsurance; //是否购买保险 - 购买，N - 未购买
    private double insuranceAmount;

    @JsonProperty("isBeneficiary")
    private boolean beneficiary;//是否受益人标记

    private List<InsuranceInfo> insuranceList; //保险信息列表保险购买时只能按人购买
    //private ScoreUse[] scoreUseInfoList; //积分使用信息列表
    private ScoreGift scoreGiftInfo; //积分赠送信息
    private String isSaveCommon;
    private String saCardNo;//星盟卡号 XX+卡号
    //2017-12-15 新增
    private String gjCertType;//军警证件类型  //选择军警证件类时使用 NI
    private String gjCertNo;//军警证件号码
    private String isMeal;//是否选择餐食  Y-购买   N-未购买
    private List<MealProduct> mealProductList;//餐食信息列表  选择时只能按人航段，如果两个说明，按顺序为两个航段
    private List<WeightProduct> weightProductList;//逾重行李列表 选择时只能按人航段，如果两个说明，按顺序为两个航段
    private List<InternatTaxInfo> OtherTaxList;//税费明细  国际税费，国际有
    //总差价费用
    private double totalDiff;
    // 是否是本人
    @JsonProperty("isOwn")
    private boolean owner;
    private String unlimitedFlyCardNo;// 无限畅飞卡卡号

    //2021-11-19
    private String ticketNo;//票号
    private String pnrNo;//pnr标号

    private String couponType;


    private List<PrivilegeDto> privilegeList;



    /**
     * 产品卡号
     */
    private String voucherNo;

    /**
     * 产品类型
     */
    private String voucherType;
    /**
     * 主题卡具体子类型
     */
    private String voucherResource;

    private boolean valid;//证件是否生效

    /**
     * 排序优先级
     * <p>
     * 10：本人
     * <p>
     * 20：受益人
     * <p>
     * 30：非本人
     * <p>
     * 100：未知
     */
    @SerializedName("Priority")
    @ApiModelProperty("排序优先级")
    private int priority;


    public Integer getCommonContactId() {
        return commonContactId;
    }

    public void setCommonContactId(Integer commonContactId) {
        this.commonContactId = commonContactId;
    }

    public Integer getGeneralContactCertId() {
        return generalContactCertId;
    }

    public void setGeneralContactCertId(Integer generalContactCertId) {
        this.generalContactCertId = generalContactCertId;
    }

    public Integer getGeneralContactId() {
        return generalContactId;
    }

    public void setGeneralContactId(Integer generalContactId) {
        this.generalContactId = generalContactId;
    }


    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public PassengerInfo() {
    }
    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }
    public boolean isBeneficiary() {
        return beneficiary;
    }

    public void setBeneficiary(boolean beneficiary) {
        this.beneficiary = beneficiary;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getSaCardNo() {
        return saCardNo;
    }

    public void setSaCardNo(String saCardNo) {
        this.saCardNo = saCardNo;
    }

    public int getPassengerNO() {
        return passengerNO;
    }

    public void setPassengerNO(int passengerNO) {
        this.passengerNO = passengerNO;
    }

    public String getPassengerName() {
        return passengerName;
    }

    public void setPassengerName(String passengerName) {
        this.passengerName = passengerName;
    }

    public String getPassengerType() {
        return passengerType;
    }

    public void setPassengerType(String passengerType) {
        this.passengerType = passengerType;
    }

    public String getPassengerIdentity() {
        return passengerIdentity;
    }

    public void setPassengerIdentity(String passengerIdentity) {
        this.passengerIdentity = passengerIdentity;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getFfCardNo() {
        return ffCardNo;
    }

    public void setFfCardNo(String ffCardNo) {
        this.ffCardNo = ffCardNo;
    }

    public String getCountryTelCode() {
        return countryTelCode;
    }

    public void setCountryTelCode(String countryTelCode) {
        this.countryTelCode = countryTelCode;
    }

    public String getHandphoneNo() {
        return handphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        this.handphoneNo = handphoneNo;
    }

    public double getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(double ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public double getPricePaid() {
        return pricePaid;
    }

    public void setPricePaid(double pricePaid) {
        this.pricePaid = pricePaid;
    }

    public double getyQTax() {
        return yQTax;
    }

    public void setyQTax(double yQTax) {
        this.yQTax = yQTax;
    }

    public double getcNTax() {
        return cNTax;
    }

    public void setcNTax(double cNTax) {
        this.cNTax = cNTax;
    }

    public double getOtherTax() {
        return otherTax;
    }

    public void setOtherTax(double otherTax) {
        this.otherTax = otherTax;
    }

    public double getqFee() {
        return qFee;
    }

    public void setqFee(double qFee) {
        this.qFee = qFee;
    }

    public String getAdtNameToInf() {
        return adtNameToInf;
    }

    public void setAdtNameToInf(String adtNameToInf) {
        this.adtNameToInf = adtNameToInf;
    }

    public String getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(String birthdate) {
        this.birthdate = birthdate;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getBelongCountry() {
        return belongCountry;
    }

    public void setBelongCountry(String belongCountry) {
        this.belongCountry = belongCountry;
    }

    public String getCertValidity() {
        return certValidity;
    }

    public void setCertValidity(String certValidity) {
        this.certValidity = certValidity;
    }

    public String getIsBuyInsurance() {
        return isBuyInsurance;
    }

    public void setIsBuyInsurance(String isBuyInsurance) {
        this.isBuyInsurance = isBuyInsurance;
    }

    public double getInsuranceAmount() {
        return insuranceAmount;
    }

    public void setInsuranceAmount(double insuranceAmount) {
        this.insuranceAmount = insuranceAmount;
    }

    public List<InsuranceInfo> getInsuranceList() {
        return insuranceList;
    }

    public void setInsuranceList(List<InsuranceInfo> insuranceList) {
        this.insuranceList = insuranceList;
    }

    public ScoreGift getScoreGiftInfo() {
        return scoreGiftInfo;
    }

    public void setScoreGiftInfo(ScoreGift scoreGiftInfo) {
        this.scoreGiftInfo = scoreGiftInfo;
    }

    public String getIsSaveCommon() {
        return isSaveCommon;
    }

    public void setIsSaveCommon(String isSaveCommon) {
        this.isSaveCommon = isSaveCommon;
    }

    public String getGjCertType() {
        return gjCertType;
    }

    public void setGjCertType(String gjCertType) {
        this.gjCertType = gjCertType;
    }

    public String getGjCertNo() {
        return gjCertNo;
    }

    public void setGjCertNo(String gjCertNo) {
        this.gjCertNo = gjCertNo;
    }

    public String getIsMeal() {
        return isMeal;
    }

    public void setIsMeal(String isMeal) {
        this.isMeal = isMeal;
    }

    public List<MealProduct> getMealProductList() {
        return mealProductList;
    }

    public void setMealProductList(List<MealProduct> mealProductList) {
        this.mealProductList = mealProductList;
    }

    public List<WeightProduct> getWeightProductList() {
        return weightProductList;
    }

    public void setWeightProductList(List<WeightProduct> weightProductList) {
        this.weightProductList = weightProductList;
    }

    public double getTotalDiff() {
        return totalDiff;
    }

    public void setTotalDiff(double totalDiff) {
        this.totalDiff = totalDiff;
    }

    public int getPassengerID() {
        return passengerID;
    }

    public void setPassengerID(int passengerID) {
        this.passengerID = passengerID;
    }

    public List<InternatTaxInfo> getOtherTaxList() {
        return OtherTaxList;
    }

    public void setOtherTaxList(List<InternatTaxInfo> otherTaxList) {
        OtherTaxList = otherTaxList;
    }

    public boolean isOwner() {
        return owner;
    }

    public void setOwner(boolean owner) {
        this.owner = owner;
    }

    public String getUnlimitedFlyCardNo() {
        return unlimitedFlyCardNo;
    }

    public void setUnlimitedFlyCardNo(String unlimitedFlyCardNo) {
        this.unlimitedFlyCardNo = unlimitedFlyCardNo;
    }

    public List<String> getPassengerIdentityUrl() {
        return passengerIdentityUrl;
    }

    public void setPassengerIdentityUrl(List<String> passengerIdentityUrl) {
        this.passengerIdentityUrl = passengerIdentityUrl;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getPnrNo() {
        return pnrNo;
    }

    public void setPnrNo(String pnrNo) {
        this.pnrNo = pnrNo;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getVoucherType() {
        return voucherType;
    }

    public void setVoucherType(String voucherType) {
        this.voucherType = voucherType;
    }

    public String getVoucherResource() {
        return voucherResource;
    }

    public void setVoucherResource(String voucherResource) {
        this.voucherResource = voucherResource;
    }

    public String getNationalityName() {
        return nationalityName;
    }

    public void setNationalityName(String nationalityName) {
        this.nationalityName = nationalityName;
    }

    public String getNationalityEName() {
        return nationalityEName;
    }

    public void setNationalityEName(String nationalityEName) {
        this.nationalityEName = nationalityEName;
    }

    public String getBelongCountryName() {
        return belongCountryName;
    }

    public void setBelongCountryName(String belongCountryName) {
        this.belongCountryName = belongCountryName;
    }

    public String getBelongCountryEName() {
        return belongCountryEName;
    }

    public void setBelongCountryEName(String belongCountryEName) {
        this.belongCountryEName = belongCountryEName;
    }

    public List<PrivilegeDto> getPrivilegeList() {
        return privilegeList;
    }

    public void setPrivilegeList(List<PrivilegeDto> privilegeList) {
        this.privilegeList = privilegeList;
    }

}