package com.juneyaoair.baseclass.request.push;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Created by yaocf on 2016/10/31.
 */
public class Notification implements Serializable {
    @NotNull(message="通知标题不能为空")
    private String notificationTitle;//通知标题
    private String pushTitle;//推送标题
    @NotNull(message="通知内容不能为空")
    private String notificationContent;//通知内容
    private String linkUrl;//活动URL
    private List<String> deviceIdList;//推送目标设备号集合  //废弃
    private List<UserDevice> userDeviceList;//推送目标集合
    private String target;//目标选中方式 all表示全部推送， specified指定目标
    private String messageType;  //消息类型 CUSTOM,MSG_CD_NOTICE  订单 BOOK,REFUND,ORDERMSG
    private String feedId;//回复用户反馈时，回复的问题
    private String linkId;//第三方的关联ID，推送至前端的ID
    private OrderInfo orderInfo;//方便APP上点击通知进入详情页


    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getNotificationTitle() {
        return notificationTitle;
    }

    public void setNotificationTitle(String notificationTitle) {
        this.notificationTitle = notificationTitle;
    }

    public String getPushTitle() {
        return pushTitle;
    }

    public void setPushTitle(String pushTitle) {
        this.pushTitle = pushTitle;
    }

    public String getNotificationContent() {
        return notificationContent;
    }

    public void setNotificationContent(String notificationContent) {
        this.notificationContent = notificationContent;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public List<String> getDeviceIdList() {
        return deviceIdList;
    }

    public List<UserDevice> getUserDeviceList() {
        return userDeviceList;
    }

    public void setUserDeviceList(List<UserDevice> userDeviceList) {
        this.userDeviceList = userDeviceList;
    }

    public void setDeviceIdList(List<String> deviceIdList) {
        this.deviceIdList = deviceIdList;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public OrderInfo getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(OrderInfo orderInfo) {
        this.orderInfo = orderInfo;
    }
}
