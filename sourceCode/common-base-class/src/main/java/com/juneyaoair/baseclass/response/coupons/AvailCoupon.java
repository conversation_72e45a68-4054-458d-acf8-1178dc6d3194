package com.juneyaoair.baseclass.response.coupons;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.request.booking.PassengerInfo;
import com.juneyaoair.thirdentity.salecoupon.common.VoucherInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-16.
 */
@Data
@NoArgsConstructor
public class AvailCoupon {

    private String CouponNo;  //优惠券序号

    private String Password;  //部分券需要密码

    private String CouponType;/// 优惠券类型

    private Double CouponRebate; /// 优惠券折扣率

    private int CouponPrice;/// 优惠券面值

    //2021-7-5 =TicketOrder打包下单  =CouponOrder单独下单
    private String SubOrderType;

    private String Remark;/// 子活动描述
    /**
     * 优惠券状态
     * R-已领取 E-已过期 N-已使用 C-赠送中 G-已赠送
     */
    private String CouponState;

    private String couponSubType;//升舱券二级分类  UpgradeUnlimited
    /**
     * 绑定状态
     * U-待绑定 B-已绑定 F-绑定失效
     */
    private String couponBindState;//绑定状态

    private String ChannelNm;/// 使用平台

    private String UsedStEndDt;/// 使用时段

    private String UsedLimit;/// 使用限制

    private String SegmentStAndEndDt;/// 航班时段

    private String CabinLimit;//舱位限制

    private String AirSegLimit;//航段限制

    private String CouponActivityName; //优惠券活动名称

    private String CouponSource;//优惠券来源 HO机票  PJ锦江 PZ中粮 PT度假(淘旅行)  PA安飞士

    private String APPLogoUrl;//APP端logo地址

    private String ReveiceTime;//在状态为G的情况下表示  被赠送者的领取日期

    private String ReveiceCardNo;//赠送的优惠券领取卡号

    @SerializedName("BookingLimit")
    private BookingLimit bookingLimit;


    private List<PassengerInfo> passengerInfoSuitList;
    /**
     * 航班限制时间起始
     */
    @SerializedName("FlightStartDate")
    private String flightStartDate;

    /**
     * 航班限制时间结束
     */
    @SerializedName("FlightEndDate")
    private String flightEndDate;
    //以下为自定义字段
    private String couponSourceName;//优惠券来源名称  机票 度假
    private String couponStyle;//页面样式
    private String couponName;//优惠券名称 如10元优惠券
    private String isGive;//是否可以转赠  Y
    private String receiveInfo;
    private String couponStateName;// 优惠券状态名称
    private boolean showQRcode;//是否展示二维码角标
    private String qrcodeData;//二维码数据
    private String useMode;//使用方式
    private String applyScope;//适用范围
    private String airPortCode;
    /**
     * 其他适用航线
     */
    private List<List<String>> otherApplyRoutes;
    private String startDate;//使用开始时间 yyyy-MM-dd
    private String endData;//使用结束时间 yyyy-MM-dd
    /**
     * 行李使用
     */
    private int overweight;//逾重量
    private String unit;//逾重单位
    /**
     * 贵宾休息室使用
     */
    private String applyFlightDate;//预约航班时间 yyyy-MM-dd
    private String applyFlightNo;//预约航班号
    private boolean hasApply;//是否已预约使用
    /**
     * 固包类子券信息(赠送)
     */
    private List<VoucherInfo> vouchers;
    /**
     * 是否为固包权益券
     */
    private boolean packageFlag;
    /**
     * 是否为赠送中的权益券
     */
    private boolean givingFlag;
    /**
     * 是否展示二维码
     */
    private boolean codeFlag;

    /**
     * 是否可用
     */
    private Boolean IsAvailable;

    /**
     * 标记是否往返特惠优惠券
     */
    private Boolean roundTripFlag;

    private String sign;// 赠送券时的加密信息

    private boolean expiring;// 是否即将过期   未使用状态，且过期日期距现在小于5天

    private String unAvailableMsg;// 不可使用原因

    private Boolean isCancel;//是否可以取消预约  true 可以取消预约  2020-06-19

    private int bookCancelCount; //贵宾休息室剩余可取消预约次数  2020-06-19

    private String orderNo; //订单号  2020-06-19

    private Boolean isDisplay; //是否显示查看订单按钮  2020-06-19

    private boolean unlimitedUpClass;// 是否是无限升舱卡

    private boolean unlimitedUpV2Flag; //是否无限升舱卡15周年版，和原本无限升舱卡一致，区分不同权益券券面使用

    private boolean bound;// 是否已绑定
    @SerializedName("UseDate")
    private String useDate; //使用时间 2020-07-23

    private String bindInfoDesc; //畅飞卡2.0 权益券列表增加绑定信息描述
    /**
     * 使用状态
     */
    private String UsedStatus;
    private String channelOrderNo; //渠道订单号 2020-11-22
    private boolean receiveStatus; //是否他人赠送标识   true:是  false:否 2020-11-24
    /**
     * 权益券是否限制本人使用  1:是  0:否  2021-04-07
     */
    private int selfUse;

    /**
     * 优惠券抵扣类型 CHD100:代表儿童100%免票
     */
    private String discountType;

    private String RescheduleType;
    /**
     * 航班起飞几小时前可用
     */
    private String AdvanceHour;

    /**
     * 到期信息提示
     */
    private Boolean expiration;

    /**
     * 1 绑定本人/他人   2 绑定本人  3 绑定他人  4无法绑定
     */
    private Integer themeBoundBut;

    /**
     * 总类型
     */
    private String totalType;


    @SerializedName("EquityCount")
    private Double  equityCount;


    private String availDate;//可用日期

    /**
     * 预约按钮是否显示
     */

    private Boolean isBookHoloShow = false;

    /**
     * 实名状态
     */
    private Boolean realNameStatus;

    private String ShowType;

    private Boolean availableButton =false;

    /**
     *generalVoucher- 普通代金券 generalCard-普通次卡 expiringCard-临期次卡
     */
    @SerializedName("PromotionType")
    private String  promotionType;


    private String proNum;

    /**
     * 获取bookingLimit中最小成人数
     *
     * @return
     */
    public int getMinAdults() {
        if (null == bookingLimit) {
            return 0;
        }
        return bookingLimit.getMinAdults();
    }
}


