package com.juneyaoair.baseclass.change;

import com.juneyaoair.baseclass.av.common.TransferInfo;
import com.juneyaoair.baseclass.response.av.CabinFare;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/28  13:48.
 */
@Data
public class ChangeFlightInfo {
    /**
     * 是否可改期
     */
    private boolean ableChange;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 实际承运号
     */
    private String carrierNo;
    /**
     * 飞行方向
     */
    private String flightDirection;
    /**
     * 出发城市三字码
     */
    private String depCityCode;
    /**
     * 出发城市名称
     */
    private String depCityName;
    /**
     * 到达城市三字码
     */
    private String arrCityCode;
    /**
     * 到达城市名称
     */
    private String arrCityName;
    /**
     * 出发机场三字码
     */
    private String depAirportCode;
    /**
     * 出发机场名称
     */
    private String depAirportName;
    /**
     * 到达机场三字码
     */
    private String arrAirportCode;
    /**
     * 到达机场名称
     */
    private String arrAirportName;
    /**
     *出发航班日期yyyy-MM-dd
     */
    private String depFlightDate;
    /**
     * 出发日期 MM-dd
     */
    private String depDate;
    /**
     * 出发时刻 HH:mm
     */
    private String depDateTime;//起飞日期时间
    /**
     * 到达航班日期yyyy-MM-dd
     */
    private String arrFlightDate;
    /**
     * 到达日期 MM-dd
     */
    private String arrDate;
    /**
     * 到达时间 HH:mm
     */
    private String arrDateTime;//到达日期时间
    /**
     * 起飞航站楼
     */
    private String depTerm;
    /**
     * 到达航站楼
     */
    private String arrTerm;
    /**
     * 成人
     */
    private CabinFare adtCabinFare;

    /**
     * 老人
     */
    private CabinFare oldCabinFare;
    /**
     * 儿童
     */
    private CabinFare chdCabinFare;
    /**
     * 婴儿
     */
    private CabinFare infCabinFare;
    /**
     * 军残警残
     */
    private CabinFare gmjcCabinFare;
    /**
     * 确认的改期升舱航段
     */
    private boolean selectedFlag;
    /**
     * 表示间隔天数
     */
    private int days;
    private Double ticketPrice;//票面价
    private String aircraftModel;  //飞机机型  空客A321(中)
    private long duration;//飞行时长  单位毫秒
    private String cabinName;  //经济舱 头等舱
    private String cabin;  //舱位代码
    private String cabinClass;  //舱位等级代码
    private String mealCode;//餐食代码
    private List<ChangeAndRefundRule> changeRuleList; //改期规则

    private String notVoluntaryDesc;//非自愿描述

    /**
     * 是否自愿改期 true - 非自愿改期
     */
    private boolean notVoluntaryChange;
    /**
     * 是否已经改期过
     **/
    private boolean changed;

    /**
     * 出票日期 yyyy-MM-dd
     */
    private String issueDate;

    private List<TransferInfo>   transferInfoList;

    private String fireBase;
}
