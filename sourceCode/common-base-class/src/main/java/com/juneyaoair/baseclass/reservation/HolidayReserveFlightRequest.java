package com.juneyaoair.baseclass.reservation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juneyaoair.baseclass.common.base.UserInfoNoMust;
import com.juneyaoair.pattern.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 假期候补车票登记信息
 * <AUTHOR>
 * @date 2020/4/8 17:08
 */
@Data
@ApiModel("假期候补机票预约信息")
public class HolidayReserveFlightRequest extends UserInfoNoMust {

    /**
     * 出发城市
     */
    @NotNull(message = "出发城市不能为空")
    @ApiModelProperty("出发城市")
    private String depCity;

    /**
     * 到达城市
     */
    @NotNull(message = "到达城市不能为空")
    @ApiModelProperty("到达城市")
    private String arrCity;

    /**
     * 到达城市三字码
     */
    @NotNull(message = "到达城市三字码不能为空")
    @ApiModelProperty("到达城市三字码")
    private String arrCityCode;

    /**
     * 出发城市三字码
     */
    @NotNull(message = "出发城市三字码不能为空")
    @ApiModelProperty("出发城市三字码")
    private String depCityCode;

    /**
     * 出发航班日期
     * yyyy-MM-dd
     */
    @NotNull(message = "出发航班日期不能为空")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @ApiModelProperty("出发航班日期")
    private Date flightDate;

    /**
     * 返回航班日期
     * yyyy-MM-dd
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @ApiModelProperty("返回航班日期")
    private Date returnFlightDate;

    /**
     * 候补人数
     */
    @Max(value = 9,message = "最大人数不能超过9人")
    @ApiModelProperty("候补人数")
    private Integer candidateNumber;


    /**
     * 航班号
     */
    @NotNull(message = "出发日期不能为空")
    private String flightNo;


    /**
     * 联系人
     */
    @NotNull(message = "姓名不能为空")
    @ApiModelProperty("联系人")
    private String applicantName;

    /**
     * 联系人电话
     */
    @NotNull(message = "联系人电话不能为空")
    @ApiModelProperty("联系人电话")
    private String mobile;

    /**
     * 电子邮箱
     */
    @NotNull(message = "电子邮箱不能为空")
    @ApiModelProperty("电子邮箱")
    @Pattern(regexp = PatternCommon.EMAIL,message = "请输入正确的邮箱")
    private String email;

}
