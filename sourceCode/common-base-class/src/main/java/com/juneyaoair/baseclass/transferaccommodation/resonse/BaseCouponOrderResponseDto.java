package com.juneyaoair.baseclass.transferaccommodation.resonse;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @ClassName BaseCouponOrderResponseDto
 * @Description 中转住宿统一订单响应结果
 * <AUTHOR>
 * @Date 2019/10/29 10:35
 **/
@Data
public class BaseCouponOrderResponseDto<T> {

    @SerializedName("Version")
    private String version;
    @SerializedName("ChannelCode")
    private String channelCode;
    @SerializedName("ResultCode")
    private String resultCode;
    @SerializedName("ErrorInfo")
    private String errorInfo;
    @SerializedName("Result")
    private T result;

}
