package com.juneyaoair.baseclass.newcoupon.req.protocol;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: caolei
 * @Description: 调用新的订单详情接口（/Order/BasicGetCouponOrder）返回子订单信息
 * @Date: 2021/8/12 14:17
 * @Modified by:
 */
@Data
public class NewBasicCouponOrderDetail {
    @ApiModelProperty(value = "优惠券订单id")
    private String CouponOrderId;
    @ApiModelProperty(value = "乘客姓名")
    private String PassengerName;
    @ApiModelProperty(value = "航班号")
    private String FlightNo;
    @ApiModelProperty(value = "优惠券销售金额")
    private String CouponSaleAmount;
    @ApiModelProperty(value = "优惠券抵扣金额")
    private String CouponValueAmount;
    @ApiModelProperty(value = "优惠券码")
    private String CouponCode;
    @ApiModelProperty(value = "优惠劵状态")
    private String CouponState;
    @ApiModelProperty(value = "退单状态")
    private String RebateState;
    @ApiModelProperty(value = "是否自愿退票")
    private String IsVoluntaryRefund;
    @ApiModelProperty(value = "优惠券活动号")
    private String CouponActivityNo;
    @ApiModelProperty(value = "优惠券活动名称")
    private String CouponActivityName;
    @ApiModelProperty(value = "优惠劵来源")
    private String CouponSource;
    @ApiModelProperty(value = "券有效期起")
    private String CouponUseSateStart;
    @ApiModelProperty(value = "券有效期止")
    private String CouponUseDateEnd;
    @ApiModelProperty(value = "优惠券说明")
    private String CouponRemark;
    @ApiModelProperty(value = "是否单独订单")
    private String IsSingleOrder;
    @ApiModelProperty(value = "渠道客户编号")
    private String ChannelCustomerNo;
    @ApiModelProperty(value = "常客卡号")
    private String FfCardNo;
    @ApiModelProperty(value = "备注")
    private String Remark;
    @ApiModelProperty(value = "使用积分")
    private String FfpUseScore;
    @ApiModelProperty(value = "现充积分")
    private String BoughtScore;
    @ApiModelProperty(value = "积分使用状态（申请，确认，取消，退还）")
    private String ScoreUseState;
    @ApiModelProperty(value = "航空兑奖记录编号")
    private String RedeemId;
    @ApiModelProperty(value = "支付状态")
    private String PayState;
    @ApiModelProperty(value = "订单id")
    private String OrderIdFk;
    @ApiModelProperty(value = "子订单id")
    private String OrderNo;
    @ApiModelProperty(value = "所属渠道用户号")
    private String ChannelNo;
    @ApiModelProperty(value = "创建人id")
    private String CreatorId;
    @ApiModelProperty(value = "创建人")
    private String CreatorName;
    @ApiModelProperty(value = "创建时间")
    private String CreateDatetime;
    @ApiModelProperty(value = "修改人id")
    private String ModiferId;
    @ApiModelProperty(value = "修改人")
    private String ModiferName;
    @ApiModelProperty(value = "修改时间")
    private String ModifyDatetime;
    @ApiModelProperty(value = "票号")
    private String TicketNo;
    @ApiModelProperty(value = "证件号码")
    private String CertNo;
    @ApiModelProperty(value = "起飞机场三字码")
    private String DepAirport;
    @ApiModelProperty(value = "到达机场三字码")
    private String ArrAirport;
    @ApiModelProperty(value = "航线类型")
    private String AirlineType;
    @ApiModelProperty(value = "航班日期")
    private String FlightDate;
    @ApiModelProperty(value = "客票类型")
    private String TicketType;
    @ApiModelProperty(value = "邀请码")
    private String InvitationCode;
    @ApiModelProperty(value = "是否打包销售:y-是,n-否")
    private String IsBundleSale;
    @ApiModelProperty(value = "包id")
    private String BundleId;
    @ApiModelProperty(value = "包名称")
    private String BundleName;
    @ApiModelProperty(value = "礼包购买数")
    private String BundleCount;
    @ApiModelProperty(value = "畅飞卡违规退票次数")
    private String RefundTimes;
    @ApiModelProperty(value = "无限卡使用次数")
    private String UsedTimes;
    @ApiModelProperty(value = "改期后的航班日期")
    private String ExchangeFlightDate;
    @ApiModelProperty(value = "评论状态")
    private String CommentStatel;
    @ApiModelProperty(value = "会员级别code")
    private String MemberLevelCode;
    @ApiModelProperty(value = "使用权益优惠券信息")
    private OrderCouponDto OrderCouponDto;
    private String PhoneCountryCode;
    private String CanRefundFlag;
    private String Amount;

    @ApiModelProperty(value = "服务管家参数")
    private ServiceButlerParam ServiceButlerParam;

    @ApiModelProperty(value = "退款规则")
    private String RefundRule;

    /**
     * 服务管家参数
     */
    @Data
    public static class ServiceButlerParam {
        private String SeatPreference;
        private String HasCheckedBaggage;
        private String ArrivalTransport;
        private String ArrivalTime;
        private String PhoneNo;
    }
}