package com.juneyaoair.baseclass.request.av;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

@Getter
@Setter
public class NearbyFlightsCheckReq {
    /**
     *起始地机场三字码
     */
    @NotBlank(message = "机场三字码不能为空")
    private String depAirport;
    /**
     * 出发时间
     */
    @NotBlank(message = "出发时间不能为空")
    private String depDateTime;

    /**
     * 旅行类别: D-国内,I-国际
     */
    @NotBlank(message = "国际,国内标识不能为空")
    private String  tripType;
}
