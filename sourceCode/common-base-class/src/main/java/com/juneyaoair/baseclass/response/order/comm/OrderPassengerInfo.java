package com.juneyaoair.baseclass.response.order.comm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.av.common.TravelPrivilege;
import com.juneyaoair.baseclass.request.booking.InsuranceInfoNew;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderPassengerInfo {
	//乘客信息
	private int PassengerID; //乘客ID保存在统一订单数据库中的ID
	private int PassengerNO;
	private String PassengerName; //乘客姓名
	private String passEnNameS; //乘客英文名称_姓 +++
	private String passEnNameF; //乘客英文名称_名 +++
	private String PassengerType; //乘客类型ADT － 成人，CHD － 儿童，INF － 婴儿
	private String Sex; //性别国际票必填
	private String Birthdate; //出生日期yyyy-MM-dd儿童、婴儿和国际票必填
	private String Nationality; //国籍名称code 中文名 国际票必填
	private String nationalityName; //国籍名称code 中文名 国际票必填 +++
	private String nationalityEName; //国籍名称英文名 国际票必填 +++

	private String BelongCountry; //发证国国际票code 必填
	private String BelongCountryName; //发证国国际票中文名 必填 +++
	private String BelongCountryEName; //发证国国际票英文名 必填 +++

	private String PassengerIdentity; //乘客身份VIP，教师，警殘，革伤
	private String CertType; //证件类型code 身份证:NI,护照:PP,其它证件:CC
	private String CertValidity; //证件有效期yyyy-MM-dd国际票必填
	private String CertNo; //证件号码
	private String FfCardNo; //常客卡号HO+卡号
	private String HandphoneNo; //手机号

	private Double TicketPrice; //票面价  票面价为Price.RSP销售参考价加总
	private Double PricePaid; //实际支付票价  （如有积分或优惠券，则是减过优惠之后的价格）
	private Double YQTax; //燃油费
	private Double CNTax; //建设税
	private Double OtherTax; //其它税费
	private Double QFee; //Q费
	private String IsBuyInsurance; //是否购买保险Y,N
	private Double InsuranceAmount; //保险金额保险金额 = 份数 * 单价 * 航段数
	private List<InsuranceInfo> InsuranceList;
	private Double UpgradeFee;
	private String RedeemId;
	private String ApprovalCode;
	private List<SegmentPriceInfo> SegmentPriceInfoList; //航段票价信息列表
	private List<TaxInfo> TaxInfoList; //税费信息列表
	@ApiModelProperty(value = "机票订单号")
	private String TicketOrderNo; //机票订单号
	@ApiModelProperty(value = "退票手续费")
	private Double DeductionTotal; //退票手续费总金额应退款 = 票价合计 - 已使用航段票价总金额 - 退票手续费总金额 + 应退票税费合计 + 应退其它费用合计
	private Double UseFlowdPriceTotal; //已使用航段票价总金额
	private Double RefundXTaxTotal; //应退税费合计
	private Double RefundOtherTotal; //应退其它费用合计
	private String RefundSign; //退票费验证串退票信息数组 + 该退票人不含税票价合计 + 退票手续费总金额 + 运价系统退票费计算私钥做SHA1
	private Double parPrice;//实际支付票面价   航段票价信息列表中的 PricePaid+UseScore+CouponAmount +++
	//每人购买机票赠送的券的信息
	private List<OrderGiveCouponInfo> passGiveCouponInfos;

	private List<InsuranceInfoNew> allInsurances;//全部保险信息，包括单独购保

	private String insuranceOrderNo; //保险订单号

	private String insuranceChannelOrderNo;//保险渠道订单号

	private double changeCouponAmount;//改期券抵扣金额

	@ApiModelProperty("客票是否允许退票")
	@JsonProperty("isRefundable")
	private boolean isRefundable;//是否可以退票

	/**
	 * 退款申请状态
	 * @see com.juneyaoair.appenum.order.RefundRebateStateEnum
	 */
	@JsonProperty("RefundRebateState")
	private String refundRebateState;

	/**
	 * 退款流程状态
	 * @see com.juneyaoair.appenum.order.OrderRefundStateEnum
	 */
	@JsonProperty("RefundState")
	private String refundState;

	/**
	 * 前端显示的乘客退票状态标签
	 * @see com.juneyaoair.appenum.order.RefundLabelEnum
	 */
	private String refundLabel;

	/**
	 * 是否使用无限飞
	 * Y/N
	 */
	private String useUnlimitedFlyFlag;
	/**
	 * 无限飞抵扣金额
	 */
	private BigDecimal useUnlimitedFlyValue;

	/**
	 * 无限飞卡号
	 */
	private String useUnlimitedFlyCardNo;

	/**
	 * 是否为单定儿童
	 */
	private String AdtIsConnectedChd;


	@JsonIgnore
	private List<TravelPrivilege> privilegeList; //出行尊享

	/**
	 * 是否选择退款
	 * 6.0.0版本新增
	 * Y/N 默认为Y
	 */
	private String selectRefund;

	/** 是否使用儿童免票券 */
	private String UseFreePass;
	/** 儿童免票券券号 */
	private String UseFreePassCouponNo;

	private String AdtNameToInf; //婴儿绑定的成人姓名如果乘客类型为婴儿，则必填

	//2021-11-23
	private String ticketNo;//票号
	//和ticketNo相同
	private String ETicketNo; //票号电子客票号(xxx-xxxxxxxxxx形式)
	private String pnrNo;//pnr标号
	private String ticketNoInf;//携带人信息(成人票号)

	/**
	 * 婴儿携带人票号
	 */
	@ApiModelProperty(value = "婴儿携带人票号")
	private String adtNameToInfTicketNo;

	@ApiModelProperty(value = "规则标签 ShippingRulesLabelEnum")
	private Set<String> shippingRulesLabel;

	@ApiModelProperty(value = "过期客票是否允许退票")
	private boolean EnableRefund;

	private String rejectText;//超过客票有效期拒绝退票的文案

	//单个航段优惠的金额(拥军)
	@SerializedName(value = "DiscountAmount")
	private BigDecimal  discountAmount;

	private BigDecimal  OriginalChangeFee;

	private double CouponAmount;

	public double getCouponAmount() {
		return CouponAmount;
	}

	public void setCouponAmount(double couponAmount) {
		CouponAmount = couponAmount;
	}
	public BigDecimal getOriginalChangeFee() {
		return OriginalChangeFee;
	}

	public void setOriginalChangeFee(BigDecimal originalChangeFee) {
		OriginalChangeFee = originalChangeFee;
	}

	private String cantRefundReason;

	private Boolean AdtisConnectedChdBtu;


	public String getCantRefundReason() {
		return cantRefundReason;
	}

	public void setCantRefundReason(String cantRefundReason) {
		this.cantRefundReason = cantRefundReason;
	}



	public String getAdtIsConnectedChd() {
		return AdtIsConnectedChd;
	}

	public void setAdtIsConnectedChd(String adtIsConnectedChd) {
		AdtIsConnectedChd = adtIsConnectedChd;
	}


	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getNationalityName() {
		return nationalityName;
	}

	public void setNationalityName(String nationalityName) {
		this.nationalityName = nationalityName;
	}

	public String getBelongCountryName() {
		return BelongCountryName;
	}

	public void setBelongCountryName(String belongCountryName) {
		BelongCountryName = belongCountryName;
	}

	public String getPassEnNameS() {
		return passEnNameS;
	}

	public void setPassEnNameS(String passEnNameS) {
		this.passEnNameS = passEnNameS;
	}

	public String getPassEnNameF() {
		return passEnNameF;
	}

	public void setPassEnNameF(String passEnNameF) {
		this.passEnNameF = passEnNameF;
	}

	public String getNationalityEName() {
		return nationalityEName;
	}

	public void setNationalityEName(String nationalityEName) {
		this.nationalityEName = nationalityEName;
	}

	public String getBelongCountryEName() {
		return BelongCountryEName;
	}

	public void setBelongCountryEName(String belongCountryEName) {
		BelongCountryEName = belongCountryEName;
	}

//	public String getCertTypeName() {
//		return certTypeName;
//	}
//
//	public void setCertTypeName(String certTypeName) {
//		this.certTypeName = certTypeName;
//	}

	public List<OrderGiveCouponInfo> getPassGiveCouponInfos() {
		return passGiveCouponInfos;
	}

	public void setPassGiveCouponInfos(List<OrderGiveCouponInfo> passGiveCouponInfos) {
		this.passGiveCouponInfos = passGiveCouponInfos;
	}

	public int getPassengerID(){
		return PassengerID;
	}
	public void setPassengerID(int PassengerID){
		this.PassengerID=PassengerID;
	}
	public int getPassengerNO() {
		return PassengerNO;
	}
	public void setPassengerNO(int passengerNO) {
		PassengerNO = passengerNO;
	}
	public String getPassengerName(){
		return PassengerName;
	}
	public void setPassengerName(String PassengerName){
		this.PassengerName=PassengerName;
	}
	public String getPassengerType(){
		return PassengerType;
	}
	public void setPassengerType(String PassengerType){
		this.PassengerType=PassengerType;
	}
	public String getPassengerIdentity(){
		return PassengerIdentity;
	}
	public void setPassengerIdentity(String PassengerIdentity){
		this.PassengerIdentity=PassengerIdentity;
	}
	public String getCertType(){
		return CertType;
	}
	public void setCertType(String CertType){
		this.CertType=CertType;
	}
	public String getCertNo(){
		return CertNo;
	}
	public void setCertNo(String CertNo){
		this.CertNo=CertNo;
	}
	public String getFfCardNo(){
		return FfCardNo;
	}
	public void setFfCardNo(String FfCardNo){
		this.FfCardNo=FfCardNo;
	}
	public String getHandphoneNo(){
		return HandphoneNo;
	}
	public void setHandphoneNo(String HandphoneNo){
		this.HandphoneNo=HandphoneNo;
	}
	public Double getTicketPrice(){
		return TicketPrice;
	}
	public void setTicketPrice(Double TicketPrice){
		this.TicketPrice=TicketPrice;
	}
	public Double getPricePaid(){
		return PricePaid;
	}
	public void setPricePaid(Double PricePaid){
		this.PricePaid=PricePaid;
	}
	public Double getYQTax(){
		return YQTax;
	}
	public void setYQTax(Double YQTax){
		this.YQTax=YQTax;
	}
	public Double getCNTax(){
		return CNTax;
	}
	public void setCNTax(Double CNTax){
		this.CNTax=CNTax;
	}
	public Double getOtherTax(){
		return OtherTax;
	}
	public void setOtherTax(Double OtherTax){
		this.OtherTax=OtherTax;
	}
	public Double getQFee(){
		return QFee;
	}
	public void setQFee(Double QFee){
		this.QFee=QFee;
	}
	public String getAdtNameToInf(){
		return AdtNameToInf;
	}
	public void setAdtNameToInf(String AdtNameToInf){
		this.AdtNameToInf=AdtNameToInf;
	}
	public String getBirthdate(){
		return Birthdate;
	}
	public void setBirthdate(String Birthdate){
		this.Birthdate=Birthdate;
	}
	public String getSex(){
		return Sex;
	}
	public void setSex(String Sex){
		this.Sex=Sex;
	}
	public String getNationality(){
		return Nationality;
	}
	public void setNationality(String Nationality){
		this.Nationality=Nationality;
	}
	public String getBelongCountry(){
		return BelongCountry;
	}
	public void setBelongCountry(String BelongCountry){
		this.BelongCountry=BelongCountry;
	}
	public String getCertValidity(){
		return CertValidity;
	}
	public void setCertValidity(String CertValidity){
		this.CertValidity=CertValidity;
	}
	public String getIsBuyInsurance(){
		return IsBuyInsurance;
	}
	public void setIsBuyInsurance(String IsBuyInsurance){
		this.IsBuyInsurance=IsBuyInsurance;
	}
	public Double getInsuranceAmount(){
		return InsuranceAmount;
	}
	public void setInsuranceAmount(Double InsuranceAmount){
		this.InsuranceAmount=InsuranceAmount;
	}
	public String getETicketNo(){
		return ETicketNo;
	}
	public void setETicketNo(String ETicketNo){
		this.ETicketNo=ETicketNo;
	}
	public List<SegmentPriceInfo> getSegmentPriceInfoList(){
		return SegmentPriceInfoList;
	}
	public void setSegmentPriceInfoList(List<SegmentPriceInfo> SegmentPriceInfoList){
		this.SegmentPriceInfoList=SegmentPriceInfoList;
	}
	public List<TaxInfo> getTaxInfoList(){
		return TaxInfoList;
	}
	public void setTaxInfoList(List<TaxInfo> TaxInfoList){
		this.TaxInfoList=TaxInfoList;
	}
	public String getTicketOrderNo(){
		return TicketOrderNo;
	}
	public void setTicketOrderNo(String TicketOrderNo){
		this.TicketOrderNo=TicketOrderNo;
	}
	public Double getDeductionTotal(){
		return DeductionTotal;
	}
	public void setDeductionTotal(Double DeductionTotal){
		this.DeductionTotal=DeductionTotal;
	}
	public Double getUseFlowdPriceTotal(){
		return UseFlowdPriceTotal;
	}
	public void setUseFlowdPriceTotal(Double UseFlowdPriceTotal){
		this.UseFlowdPriceTotal=UseFlowdPriceTotal;
	}
	public Double getRefundXTaxTotal(){
		return RefundXTaxTotal;
	}
	public void setRefundXTaxTotal(Double RefundXTaxTotal){
		this.RefundXTaxTotal=RefundXTaxTotal;
	}
	public Double getRefundOtherTotal(){
		return RefundOtherTotal;
	}
	public void setRefundOtherTotal(Double RefundOtherTotal){
		this.RefundOtherTotal=RefundOtherTotal;
	}
	public String getRefundSign(){
		return RefundSign;
	}
	public void setRefundSign(String RefundSign){
		this.RefundSign=RefundSign;
	}

	public List<InsuranceInfo> getInsuranceList() {
		return InsuranceList;
	}

	public void setInsuranceList(List<InsuranceInfo> insuranceList) {
		InsuranceList = insuranceList;
	}

	public Double getUpgradeFee() {
		return UpgradeFee;
	}

	public void setUpgradeFee(Double upgradeFee) {
		UpgradeFee = upgradeFee;
	}

	public String getRedeemId() {
		return RedeemId;
	}

	public void setRedeemId(String redeemId) {
		RedeemId = redeemId;
	}

	public String getApprovalCode() {
		return ApprovalCode;
	}

	public void setApprovalCode(String approvalCode) {
		ApprovalCode = approvalCode;
	}

	public Double getParPrice() {
		return parPrice;
	}

	public void setParPrice(Double parPrice) {
		this.parPrice = parPrice;
	}

	public List<InsuranceInfoNew> getAllInsurances() {
		return allInsurances;
	}

	public void setAllInsurances(List<InsuranceInfoNew> allInsurances) {
		this.allInsurances = allInsurances;
	}

	public String getInsuranceOrderNo() {
		return insuranceOrderNo;
	}

	public void setInsuranceOrderNo(String insuranceOrderNo) {
		this.insuranceOrderNo = insuranceOrderNo;
	}

	public String getInsuranceChannelOrderNo() {
		return insuranceChannelOrderNo;
	}

	public void setInsuranceChannelOrderNo(String insuranceChannelOrderNo) {
		this.insuranceChannelOrderNo = insuranceChannelOrderNo;
	}

	public double getChangeCouponAmount() {
		return changeCouponAmount;
	}

	public void setChangeCouponAmount(double changeCouponAmount) {
		this.changeCouponAmount = changeCouponAmount;
	}

	public boolean isRefundable() {
		return isRefundable;
	}

	public void setRefundable(boolean refundable) {
		isRefundable = refundable;
	}

	public String getRefundRebateState() {
		return refundRebateState;
	}

	public void setRefundRebateState(String refundRebateState) {
		this.refundRebateState = refundRebateState;
	}

	public String getRefundState() {
		return refundState;
	}

	public void setRefundState(String refundState) {
		this.refundState = refundState;
	}

	public String getRefundLabel() {
		return refundLabel;
	}

	public void setRefundLabel(String refundLabel) {
		this.refundLabel = refundLabel;
	}

	public String getUseUnlimitedFlyFlag() {
		return useUnlimitedFlyFlag;
	}

	public void setUseUnlimitedFlyFlag(String useUnlimitedFlyFlag) {
		this.useUnlimitedFlyFlag = useUnlimitedFlyFlag;
	}

	public BigDecimal getUseUnlimitedFlyValue() {
		return useUnlimitedFlyValue;
	}

	public void setUseUnlimitedFlyValue(BigDecimal useUnlimitedFlyValue) {
		this.useUnlimitedFlyValue = useUnlimitedFlyValue;
	}

	public String getUseUnlimitedFlyCardNo() {
		return useUnlimitedFlyCardNo;
	}

	public void setUseUnlimitedFlyCardNo(String useUnlimitedFlyCardNo) {
		this.useUnlimitedFlyCardNo = useUnlimitedFlyCardNo;
	}

	public List<TravelPrivilege> getPrivilegeList() {
		return privilegeList;
	}

	public void setPrivilegeList(List<TravelPrivilege> privilegeList) {
		this.privilegeList = privilegeList;
	}

	public String getSelectRefund() {
		return selectRefund;
	}

	public void setSelectRefund(String selectRefund) {
		this.selectRefund = selectRefund;
	}

	public String getUseFreePass() {
		return UseFreePass;
	}

	public void setUseFreePass(String useFreePass) {
		UseFreePass = useFreePass;
	}

	public String getUseFreePassCouponNo() {
		return UseFreePassCouponNo;
	}

	public void setUseFreePassCouponNo(String useFreePassCouponNo) {
		UseFreePassCouponNo = useFreePassCouponNo;
	}

	public String getTicketNo() {
		return ticketNo;
	}

	public void setTicketNo(String ticketNo) {
		this.ticketNo = ticketNo;
	}

	public String getPnrNo() {
		return pnrNo;
	}

	public void setPnrNo(String pnrNo) {
		this.pnrNo = pnrNo;
	}

	public String getTicketNoInf() {
		return ticketNoInf;
	}

	public void setTicketNoInf(String ticketNoInf) {
		this.ticketNoInf = ticketNoInf;
	}

	public String getAdtNameToInfTicketNo() {
		return adtNameToInfTicketNo;
	}

	public void setAdtNameToInfTicketNo(String adtNameToInfTicketNo) {
		this.adtNameToInfTicketNo = adtNameToInfTicketNo;
	}

	public Set<String> getShippingRulesLabel() {
		return shippingRulesLabel;
	}

	public void setShippingRulesLabel(Set<String> shippingRulesLabel) {
		this.shippingRulesLabel = shippingRulesLabel;
	}

	public boolean isEnableRefund() {
		return EnableRefund;
	}

	public void setEnableRefund(boolean enableRefund) {
		EnableRefund = enableRefund;
	}

	public String getRejectText() {
		return rejectText;
	}

	public void setRejectText(String rejectText) {
		this.rejectText = rejectText;
	}
}