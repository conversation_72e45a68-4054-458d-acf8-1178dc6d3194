package com.juneyaoair.baseclass.newcoupon.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/2/25 11:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductInformation {
    /**
     * 产品编号
     */
    private String productId;
    /**
     * 价格
     */
    private Double price;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 资源编号
     */
    private String resourceId;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 产品所属规格类型
     * 0----0.5
     * 1----2
     * 2----4
     */
    private String code;

}
