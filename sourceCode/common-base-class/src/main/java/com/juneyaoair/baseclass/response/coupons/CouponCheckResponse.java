package com.juneyaoair.baseclass.response.coupons;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by qinxiaoming on 2016-5-16.
 */
@XmlRootElement(name = "CouponCheckResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class CouponCheckResponse {
    private String ChannelCode;//渠道号
    private String FfpId;//常旅客ID
    private String FfpCardNo;//常旅客卡号
    private String ResultCode;//结果代码
    private String ErrorInfo;//结果描述
    private String CouponState;/// 优惠券状态
    private String CouponNo;/// 优惠券序号
    private String CouponType;/// 优惠券类型
    private Double CouponRebate;/// 优惠券折扣率
    private int CouponPrice;/// 优惠券面值
    private String SuitAirline;

    public String getSuitAirline() {
        return SuitAirline;
    }

    public void setSuitAirline(String suitAirline) {
        SuitAirline = suitAirline;
    }

    public CouponCheckResponse() {
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getFfpId() {
        return FfpId;
    }

    public void setFfpId(String ffpId) {
        FfpId = ffpId;
    }

    public String getFfpCardNo() {
        return FfpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        FfpCardNo = ffpCardNo;
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public String getCouponState() {
        return CouponState;
    }

    public void setCouponState(String couponState) {
        CouponState = couponState;
    }

    public String getCouponNo() {
        return CouponNo;
    }

    public void setCouponNo(String couponNo) {
        CouponNo = couponNo;
    }

    public String getCouponType() {
        return CouponType;
    }

    public void setCouponType(String couponType) {
        CouponType = couponType;
    }

    public Double getCouponRebate() {
        return CouponRebate;
    }

    public void setCouponRebate(Double couponRebate) {
        CouponRebate = couponRebate;
    }

    public int getCouponPrice() {
        return CouponPrice;
    }

    public void setCouponPrice(int couponPrice) {
        CouponPrice = couponPrice;
    }
}
