package com.juneyaoair.baseclass.change;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.juneyaoair.baseclass.av.common.TrrDateLimit;
import com.juneyaoair.baseclass.request.booking.FlightInfo;
import com.juneyaoair.baseclass.response.order.query.DisneyTicket;
import com.juneyaoair.baseclass.ticket.req.TravelPartnerChangeFlightInfo;
import com.juneyaoair.thirdentity.av.comm.FareTaxInfo;
import com.juneyaoair.thirdentity.response.order.apply.PtOrderCouponDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 票面信息
 * @date 2018/11/28  13:47.
 */
@Data
@JsonIgnoreProperties({"notCanUseMessage"})//改字段不予前端展示
public class TicketInfo {
    /**
     * 是否可改期
     */
    private boolean ableChange;
    /**
     * 国内国际标志
     */
    private String interFlag;
    /**
     * 航程类型  单程  往返
     */
    private String routeType;
    /**
     * 订单号，订单改期时使用
     */
    private String orderNo;
    /**
     * 渠道订单号，订单改期时使用
     */
    private String channelOrderNo;
    /**
     * 原始渠道号
     */
    private String originChannelCode;
    private String linker; //联系人
    private String linkerHandphone;//联系人手机号
    /**
     * 订单类别
     */
    private String orderSort;
    /**
     * 客票改期时使用
     */
    private String pnrNo;
    /**
     * 客票号
     */
    private String ticketNo;
    /**
     * 航班信息,前端展示使用
     */
    private List<ChangeFlightInfo> changeFlightInfoList;
    /**
     * 儿童航班信息
     */
    private List<TravelPartnerChangeFlightInfo> chdChangeFlightInfoList;
    /**
     * 婴儿航班信息
     */
    private List<TravelPartnerChangeFlightInfo> infChangeFlightInfoList;
    /**
     * 军残警残航班信息
     */
    private List<TravelPartnerChangeFlightInfo> gmjcChangeFlightInfoList;
    /**
     * 乘客信息
     */
    private List<ChangePassengerInfo> passengerInfoList;
    /**
     * 老的航班信息
     */
    private List<FlightInfo> oldFlightInfoList;
    /**
     * 原始国际税费列表（只有国际航班使用）
     */
    private List<FareTaxInfo> oldFareTaxInfoList;
    /**
     * 是否自愿改期
     */
    private boolean notVoluntaryChange;  //true - 非自愿改期
    /**
     * 团队票标记
     */
    private boolean teamFlag;
    /**
     * 出票日期 yyyy-MM-dd
     */
    private String issueDate;

    private TrrDateLimit trrDateLimit; // TRR改期时限 天

    /**
     * 使用的权益信息
     * UnlimitFlyCard 畅飞卡
     */
    private String useRightsInfo;// 使用的权益信息

    /**
     * 订单使用的优惠券信息
     */
    private List<PtOrderCouponDto> orderCouponList;

    @ApiModelProperty("购买权益券响应信息")
    private String payCouponInfoDesc;

    @ApiModelProperty("携带儿童票号")
    private String ticketNoInf;

    //业务用 不可用原因
    private String notCanUseMessage;

    /**
     * 是否迪士尼
     */
    private Boolean  isDisneyFlag;

    private List<DisneyTicket>   disneyTicketList;

    private String fareType;

    private String TicketOrderNo;

    private String depDate;
}
