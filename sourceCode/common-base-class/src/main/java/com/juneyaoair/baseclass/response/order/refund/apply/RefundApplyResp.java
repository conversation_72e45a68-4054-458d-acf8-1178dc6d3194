package com.juneyaoair.baseclass.response.order.refund.apply;

import com.juneyaoair.baseclass.response.order.comm.OrderPassengerInfo;

import java.util.List;

public class RefundApplyResp {
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息
	private Double TicketAmount; //可退机票总金额
	private Double TaxFee; //可退税费
	private Double QOther; //可退其他税费
	private Double InsAmount; //可退保险
	private Double Deduction; //手续费
	private Double RefundAmount; //应退总金额
	private List<OrderPassengerInfo> OrderPassengerInfoList; //按人分组退票信息数组

	private double totalScore;// 订单总积分
	private double refundScore; // 自愿退票可退积分

	private double machineTravelCouponPrice;

	/**
	 * true-非自愿
	 * false-自愿退票
	 */
	private Boolean IsVoluntaryRefund;

	/**
	 * 客票使用情况
	 * 空 - 不使用该条件，0-全部未使用,1-部分已使用
	 */
	private String TicketUsage;


	public Boolean getVoluntaryRefund() {
		return IsVoluntaryRefund;
	}


	public String getTicketUsage() {
		return TicketUsage;
	}

	public void setTicketUsage(String ticketUsage) {
		TicketUsage = ticketUsage;
	}
	public void setVoluntaryRefund(Boolean voluntaryRefund) {
		IsVoluntaryRefund = voluntaryRefund;
	}
	public String getResultCode(){
		return ResultCode;
	}
	public void setResultCode(String ResultCode){
		this.ResultCode=ResultCode;
	}
	public String getErrorInfo(){
		return ErrorInfo;
	}
	public void setErrorInfo(String ErrorInfo){
		this.ErrorInfo=ErrorInfo;
	}
	public Double getTicketAmount(){
		return TicketAmount;
	}
	public void setTicketAmount(Double TicketAmount){
		this.TicketAmount=TicketAmount;
	}
	public Double getTaxFee(){
		return TaxFee;
	}
	public void setTaxFee(Double TaxFee){
		this.TaxFee=TaxFee;
	}
	public Double getQOther(){
		return QOther;
	}
	public void setQOther(Double QOther){
		this.QOther=QOther;
	}
	public Double getInsAmount(){
		return InsAmount;
	}
	public void setInsAmount(Double InsAmount){
		this.InsAmount=InsAmount;
	}
	public Double getDeduction(){
		return Deduction;
	}
	public void setDeduction(Double Deduction){
		this.Deduction=Deduction;
	}
	public Double getRefundAmount(){
		return RefundAmount;
	}
	public void setRefundAmount(Double RefundAmount){
		this.RefundAmount=RefundAmount;
	}
	public List<OrderPassengerInfo> getOrderPassengerInfoList(){
		return OrderPassengerInfoList;
	}
	public void setOrderPassengerInfoList(List<OrderPassengerInfo> list){
		this.OrderPassengerInfoList=list;
	}

	public double getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(double totalScore) {
		this.totalScore = totalScore;
	}

	public double getMachineTravelCouponPrice() {
		return machineTravelCouponPrice;
	}

	public void setMachineTravelCouponPrice(double machineTravelCouponPrice) {
		this.machineTravelCouponPrice = machineTravelCouponPrice;
	}
	public double getRefundScore() {
		return refundScore;
	}

	public void setRefundScore(double refundScore) {
		this.refundScore = refundScore;
	}


	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer(); 
		sb.append("RefundApplyResp [");
		sb.append("ResultCode="+ResultCode+",");
		sb.append("ErrorInfo="+ErrorInfo+",");
		sb.append("TicketAmount="+TicketAmount+",");
		sb.append("TaxFee="+TaxFee+",");
		sb.append("QOther="+QOther+",");
		sb.append("InsAmount="+InsAmount+",");
		sb.append("Deduction="+Deduction+",");
		sb.append("RefundAmount="+RefundAmount+",");
		sb.append("OrderPassengerInfoList="+OrderPassengerInfoList);
		sb.append("]");
		return sb.toString();
	}
}