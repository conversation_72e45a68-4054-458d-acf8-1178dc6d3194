package com.juneyaoair.exception;

/**
 * @ClassName DuplicateRequestException
 * @Description 重复请求异常
 * <AUTHOR>
 * @Date 2019/11/20 11:00
 **/
public class DuplicateRequestException extends ExpectableException {

    public DuplicateRequestException() {super();}

    public DuplicateRequestException(String message) {super(message);}

    public DuplicateRequestException(Throwable cause) {
        super(cause);
    }

    public DuplicateRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    protected DuplicateRequestException(String message, Throwable cause,
                                       boolean enableSuppression,
                                       boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
