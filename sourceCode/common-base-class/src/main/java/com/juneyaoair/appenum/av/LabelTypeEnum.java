package com.juneyaoair.appenum.av;

/**
 * <AUTHOR>
 * @description
 * @date 2019/3/6  15:39.
 */
public enum LabelTypeEnum {
    ACTIVITY("activity","活动标签"),
    MAIN("main","主要标签"),
    PROMOTION("promotion","主推标签"),
    NORMAL("normal","普通标签");
    private String type;
    private String desc;

    LabelTypeEnum(String type,String desc){
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
