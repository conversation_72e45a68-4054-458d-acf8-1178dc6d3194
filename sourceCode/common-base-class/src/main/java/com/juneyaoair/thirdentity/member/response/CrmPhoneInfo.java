package com.juneyaoair.thirdentity.member.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 手机号
 * @created 2023/9/13 10:18
 */
@Data
@ApiModel(value = "CrmPhoneInfo",description = "会员手机号")
public class CrmPhoneInfo {

    @ApiModelProperty(value = "crm原始数据")
    private String crmPhone;

    @ApiModelProperty(value = "解析后国际区号")
    private String areaId;

    @ApiModelProperty(value = "解析后手机号")
    private String phone;

    @ApiModelProperty(value = "是否实名")
    private boolean realName;

}
