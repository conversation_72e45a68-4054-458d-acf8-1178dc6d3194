package com.juneyaoair.thirdentity.comm;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;



@XmlRootElement(name = "RefundGroup")
@XmlAccessorType(XmlAccessType.FIELD)
public class RefundGroup {
	
	private String PassengerID; //乘客ID	
	private String PassengerType; //旅客类型	ADT-成人,CHD -儿童,INF - 婴儿
	private double SPriceTotal; //8(其中2位小数)	该退票人不含税票价金额合计
    /// 应退款 = 票价合计 - 已使用航段票价总金额 - 退票手续费总金额 + 应退票税费合计 + 应退其它费用合计
	private double DeductionTotal;// 退票手续费总金额
	private double UseFlowdPriceTotal;/// 已使用航段票价总金额
	private double RefundXTaxTotal;/// 应退税费合计
	private double RefundOtherTotal;/// 应退其它费用合计
	private String RefundSign;/// 退票费验证串 退票信息数组 + 该退票人不含税票价合计 + 退票手续费总金额 + 运价系统退票费计算私钥做SHA1	
	private List<RefundInfo> RefundInfoList; //退票信息数组	只能是一个订单下一个人一次订购的机票(同一票本)
	public String getPassengerID() {
		return PassengerID;
	}
	public void setPassengerID(String passengerID) {
		PassengerID = passengerID;
	}
	public String getPassengerType() {
		return PassengerType;
	}
	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}
	public double getSPriceTotal() {
		return SPriceTotal;
	}
	public void setSPriceTotal(double sPriceTotal) {
		SPriceTotal = sPriceTotal;
	}
	public double getDeductionTotal() {
			return DeductionTotal;
	}
	public void setDeductionTotal(double deductionTotal) {
		DeductionTotal = deductionTotal;
	}
	public double getUseFlowdPriceTotal() {
		return UseFlowdPriceTotal;
	}
	public void setUseFlowdPriceTotal(double useFlowdPriceTotal) {
		UseFlowdPriceTotal = useFlowdPriceTotal;
	}
	public double getRefundXTaxTotal() {
		return RefundXTaxTotal;
	}
	public void setRefundXTaxTotal(double refundXTaxTotal) {
		RefundXTaxTotal = refundXTaxTotal;
	}
	public double getRefundOtherTotal() {
		return RefundOtherTotal;
	}
	public void setRefundOtherTotal(double refundOtherTotal) {
		RefundOtherTotal = refundOtherTotal;
	}
	public String getRefundSign() {
		return RefundSign;
	}
	public void setRefundSign(String refundSign) {
		RefundSign = refundSign;
	}
	public List<RefundInfo> getRefundInfoList() {
		return RefundInfoList;
	}
	public void setRefundInfoList(List<RefundInfo> refundInfoList) {
		RefundInfoList = refundInfoList;
	}
	
}