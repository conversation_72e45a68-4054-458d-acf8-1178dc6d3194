package com.juneyaoair.thirdentity.change.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/29  15:14.
 */
@Data
public class OrderChangeBrief {
    private String ChannelCode;

    private String UserNo;

    private String ChannelOrderNo;

    private String OrderNo;

    private String OldChannelOrderNo;

    private String OldOrderNo;

    private String RouteType;

    private String FlightType;

    private String OrderState;

    private String PayState;

    private String CurrencyCode;

    private Double Amount;//实际支付金额

    private Double TicketAmount;

    private Double TaxAmount;

    private Double InsuranceAmount;

    private Double Deductibls;

    private Double UseScore;//使用积分

    private String OrderSort;

    private String CreateDatetime;
    /**
     * 是否可改期
     */
    private boolean IsChange;

    private List<ChangeSegmentInfo> SegmentInfoList;

    /**
     * 订单类型 MultiRange-多程
     */
    private String ticketRangeType;
}
