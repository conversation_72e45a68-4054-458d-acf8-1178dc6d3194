package com.juneyaoair.thirdentity.request.lostItems;



import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


@XmlRootElement(name = "LostItemsQueryRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class LostItemsQueryRequest {
    @NotEmpty(message = "航班号不能为空")
    private String lbFlightNo;
    @NotEmpty(message = "航班日期不能为空")
    private String lbFlightDate;
    //一级类型
    private String lbLevel;
    //二级类型
    private String lbTypeNew;

    private String lbType;

    @NotEmpty(message = "会员编号不能为空")
    private String ffpId;
    @NotEmpty(message = "验证信息不能为空")
    private String loginKeyInfo;
    @NotEmpty(message = "渠道号不能为空")
    private String channelCode;

}
