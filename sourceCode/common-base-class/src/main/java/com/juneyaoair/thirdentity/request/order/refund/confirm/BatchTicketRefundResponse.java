package com.juneyaoair.thirdentity.request.order.refund.confirm;

import com.juneyaoair.thirdentity.response.order.refund.confirm.PtRefundResp;
import lombok.Data;

import java.util.List;

/**
 * @Author: caolei
 * @Description: 批量退票结果
 * @Date: 2021/5/20 15:55
 * @Modified by:
 */
@Data
public class BatchTicketRefundResponse {
    /** 接口版本号 */
    private String Version;
    /** 渠道用 */
    private String ChannelCode;
    /** 结果详情 */
    private List<PtRefundResp> BatchTicketRefundInfo;
    /** 返回编码 */
    private String ResultCode;
    /** 错误信息 */
    private String ErrorInfo;
}