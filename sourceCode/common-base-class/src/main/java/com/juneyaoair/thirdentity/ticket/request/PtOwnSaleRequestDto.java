package com.juneyaoair.thirdentity.ticket.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/5/28  16:31.
 */
@Data
@NoArgsConstructor
public class PtOwnSaleRequestDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String RandCode;
    private String TicketNo;
    private List<String> ChannelCodes;

    public PtOwnSaleRequestDto(String version,String channelCode,String userNo,String randCode){
        this.Version =  version;
        this.ChannelCode =  channelCode;
        this.UserNo =  userNo;
        this.RandCode =  randCode;

    }
}
