package com.juneyaoair.thirdentity.response.detr;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.request.booking.InsuranceInfoNew;
import com.juneyaoair.thirdentity.response.speedRefund.CabinNumInfo;

import java.util.List;

/**
 * Created by yaocf on 2017/1/19.
 */
public class PtSegmentInfo {
    private String ArrAirportTerminal; //到达航站楼
    private String DepAirportTerminal; //起飞航站楼
    private String ArrAirportCode; //到达机场代码
    private String ArrAirportName;
    private String ArrCityName;
    private String ArrCity;//到达城市代码
    private String BaggageWeight; //允许携带的行李重量
    private String BaggagePiece; //允许携带的行李件数
    private String Cabin; //预订舱位
    private String CabinClass; //舱等  CommonUtil.showCabinClassName(CabinClass)
    private String DepAirportCode; //起飞机场代码
    private String DepAirportName;
    private String DepCityName;
    private String DepCity;//起飞城市代码
    private String StartValidityDate; //有效期起始时间(null表示无起始日期或情况不明)yyyy-MM-dd HH:mi
    private String EndValidityDate; //有效期终止时间(null表示无终止日期或情况不明)yyyy-MM-dd HH:mi
    private String DepTime; //起飞时间yyyy-MM-dd HH:mi
    private String FlightNo; //航班编号
    private String PnrNo; //PNR编号(ICS编号)
    private String CrsPnrNo; //代理人系统PNR编号
    private String CrsType; //代理人系统代码
    private String Rate; //适用的运价类型（如YB为Y舱B类运价）FireBase
    private String TicketStatus; //客票状态
    private String Airline; //
    private String BoardingNo; //旅客在已飞行航段中的登机牌号
    private String StopType; //停留原因停留原因 O 正常 X 中转联程
    private String Type; //航段类型
    private boolean IsFPC; //
    private String OperationAirline; //承运方航空公司代码
    private String MarketingAirline; //市场方航空公司代码
    private String ArrTime; //到达时间yyyy-MM-dd HH:mi
    private int SegmentIndex; //电子票票面航段序号
    private String McoNumber; //MCO单号
    private String BaggageWeightUnit; //行李重量单位
    private String SegmentStatus;//航段状态  C-取消 D-延误
    private Double TicketPrice; //客票金额
    private Double RefundDeductionB; //2小时前退票手续费
    private Double RefundDeductionA; //2小时后退票手续费
    private Double RefundDeduction;//退票手续费
    private String BrandCode;

    /**
     * 是否可升舱或改期
     */
    private boolean IsOperation;
    /**
     * 不可升舱或改期原因
     */
    private String IsOperationReason;

    /**
     * 非自愿描述
     */
    private String notVoluntaryDesc;


    private String FareBasic;
    /**
     * 座位状态 2021-09-14
     **/
    private String SeatStatus;

    /**
     * 升舱舱位余量信息
     */
    @SerializedName("CabinList")
    private List<CabinNumInfo> cabinList;

    @SerializedName("IRRFlag")
    private Boolean irrFlag;//

    @SerializedName("UsedScore")
    private double usedScore;

    @SerializedName("UsedCoupon")
    private double usedCoupon;

    private String depDate; //起飞日期yyyy-MM-dd
    private String arrDate; //到达日期yyyy-MM-dd
    private List<InsuranceInfoNew> InsuranceList; //已购保险信息
    private String depHm; //起飞时分 HH:mm
    private String arrHm; //到达时分 HH:mm
    private String weekDay; //周几

    public String getBrandCode() {
        return BrandCode;
    }

    public void setBrandCode(String brandCode) {
        BrandCode = brandCode;
    }
    public String getArrAirportTerminal() {
        return ArrAirportTerminal;
    }

    public void setArrAirportTerminal(String arrAirportTerminal) {
        ArrAirportTerminal = arrAirportTerminal;
    }

    public String getDepAirportTerminal() {
        return DepAirportTerminal;
    }

    public void setDepAirportTerminal(String depAirportTerminal) {
        DepAirportTerminal = depAirportTerminal;
    }

    public String getArrAirportCode() {
        return ArrAirportCode;
    }

    public void setArrAirportCode(String arrAirportCode) {
        ArrAirportCode = arrAirportCode;
    }

    public String getArrAirportName() {
        return ArrAirportName;
    }

    public void setArrAirportName(String arrAirportName) {
        ArrAirportName = arrAirportName;
    }

    public String getArrCityName() {
        return ArrCityName;
    }

    public void setArrCityName(String arrCityName) {
        ArrCityName = arrCityName;
    }

    public String getBaggageWeight() {
        return BaggageWeight;
    }

    public void setBaggageWeight(String baggageWeight) {
        BaggageWeight = baggageWeight;
    }

    public String getBaggagePiece() {
        return BaggagePiece;
    }

    public void setBaggagePiece(String baggagePiece) {
        BaggagePiece = baggagePiece;
    }

    public String getCabin() {
        return Cabin;
    }

    public void setCabin(String cabin) {
        Cabin = cabin;
    }

    public String getCabinClass() {
        return CabinClass;
    }

    public void setCabinClass(String cabinClass) {
        CabinClass = cabinClass;
    }

    public String getDepAirportCode() {
        return DepAirportCode;
    }

    public void setDepAirportCode(String depAirportCode) {
        DepAirportCode = depAirportCode;
    }

    public String getDepAirportName() {
        return DepAirportName;
    }

    public void setDepAirportName(String depAirportName) {
        DepAirportName = depAirportName;
    }

    public String getDepCityName() {
        return DepCityName;
    }

    public void setDepCityName(String depCityName) {
        DepCityName = depCityName;
    }

    public String getStartValidityDate() {
        return StartValidityDate;
    }

    public void setStartValidityDate(String startValidityDate) {
        StartValidityDate = startValidityDate;
    }

    public String getEndValidityDate() {
        return EndValidityDate;
    }

    public void setEndValidityDate(String endValidityDate) {
        EndValidityDate = endValidityDate;
    }

    public String getDepTime() {
        return DepTime;
    }

    public void setDepTime(String depTime) {
        DepTime = depTime;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getPnrNo() {
        return PnrNo;
    }

    public void setPnrNo(String pnrNo) {
        PnrNo = pnrNo;
    }

    public String getCrsPnrNo() {
        return CrsPnrNo;
    }

    public void setCrsPnrNo(String crsPnrNo) {
        CrsPnrNo = crsPnrNo;
    }

    public String getCrsType() {
        return CrsType;
    }

    public void setCrsType(String crsType) {
        CrsType = crsType;
    }

    public String getRate() {
        return Rate;
    }

    public void setRate(String rate) {
        Rate = rate;
    }

    public String getTicketStatus() {
        return TicketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        TicketStatus = ticketStatus;
    }

    public String getAirline() {
        return Airline;
    }

    public void setAirline(String airline) {
        Airline = airline;
    }

    public String getBoardingNo() {
        return BoardingNo;
    }

    public void setBoardingNo(String boardingNo) {
        BoardingNo = boardingNo;
    }

    public String getSegmentStatus() {
        return SegmentStatus;
    }

    public void setSegmentStatus(String segmentStatus) {
        SegmentStatus = segmentStatus;
    }

    public String getStopType() {
        return StopType;
    }

    public void setStopType(String stopType) {
        StopType = stopType;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public boolean isFPC() {
        return IsFPC;
    }

    public void setFPC(boolean FPC) {
        IsFPC = FPC;
    }

    public String getOperationAirline() {
        return OperationAirline;
    }

    public void setOperationAirline(String operationAirline) {
        OperationAirline = operationAirline;
    }

    public String getMarketingAirline() {
        return MarketingAirline;
    }

    public void setMarketingAirline(String marketingAirline) {
        MarketingAirline = marketingAirline;
    }

    public String getArrTime() {
        return ArrTime;
    }

    public void setArrTime(String arrTime) {
        ArrTime = arrTime;
    }

    public int getSegmentIndex() {
        return SegmentIndex;
    }

    public void setSegmentIndex(int segmentIndex) {
        SegmentIndex = segmentIndex;
    }

    public String getMcoNumber() {
        return McoNumber;
    }

    public void setMcoNumber(String mcoNumber) {
        McoNumber = mcoNumber;
    }

    public String getBaggageWeightUnit() {
        return BaggageWeightUnit;
    }

    public void setBaggageWeightUnit(String baggageWeightUnit) {
        BaggageWeightUnit = baggageWeightUnit;
    }

    public Double getTicketPrice() {
        return TicketPrice;
    }

    public void setTicketPrice(Double ticketPrice) {
        TicketPrice = ticketPrice;
    }

    public Double getRefundDeductionB() {
        return RefundDeductionB;
    }

    public void setRefundDeductionB(Double refundDeductionB) {
        RefundDeductionB = refundDeductionB;
    }

    public Double getRefundDeductionA() {
        return RefundDeductionA;
    }

    public void setRefundDeductionA(Double refundDeductionA) {
        RefundDeductionA = refundDeductionA;
    }

    public Double getRefundDeduction() {
        return RefundDeduction;
    }

    public void setRefundDeduction(Double refundDeduction) {
        RefundDeduction = refundDeduction;
    }

    public String getArrCity() {
        return ArrCity;
    }

    public void setArrCity(String arrCity) {
        ArrCity = arrCity;
    }

    public String getDepCity() {
        return DepCity;
    }

    public void setDepCity(String depCity) {
        DepCity = depCity;
    }

    public boolean isOperation() {
        return IsOperation;
    }

    public void setOperation(boolean operation) {
        IsOperation = operation;
    }

    public String getIsOperationReason() {
        return IsOperationReason;
    }

    public void setIsOperationReason(String isOperationReason) {
        IsOperationReason = isOperationReason;
    }

    public String getNotVoluntaryDesc() {
        return notVoluntaryDesc;
    }

    public void setNotVoluntaryDesc(String notVoluntaryDesc) {
        this.notVoluntaryDesc = notVoluntaryDesc;
    }

    public List<CabinNumInfo> getCabinList() {
        return cabinList;
    }

    public void setCabinList(List<CabinNumInfo> cabinList) {
        this.cabinList = cabinList;
    }

    public Boolean getIrrFlag() {
        return irrFlag;
    }

    public void setIrrFlag(Boolean irrFlag) {
        this.irrFlag = irrFlag;
    }

    public double getUsedScore() {
        return usedScore;
    }

    public void setUsedScore(double usedScore) {
        this.usedScore = usedScore;
    }

    public double getUsedCoupon() {
        return usedCoupon;
    }

    public void setUsedCoupon(double usedCoupon) {
        this.usedCoupon = usedCoupon;
    }


    public String getFareBasic() {
        return FareBasic;
    }

    public void setFareBasic(String fareBasic) {
        FareBasic = fareBasic;
    }

    public String getSeatStatus() {
        return SeatStatus;
    }

    public void setSeatStatus(String seatStatus) {
        SeatStatus = seatStatus;
    }

    public String getDepDate() {
        return depDate;
    }

    public void setDepDate(String depDate) {
        this.depDate = depDate;
    }

    public String getArrDate() {
        return arrDate;
    }

    public void setArrDate(String arrDate) {
        this.arrDate = arrDate;
    }

    public List<InsuranceInfoNew> getInsuranceList() {
        return InsuranceList;
    }

    public void setInsuranceList(List<InsuranceInfoNew> insuranceList) {
        InsuranceList = insuranceList;
    }

    public String getDepHm() {
        return depHm;
    }

    public void setDepHm(String depHm) {
        this.depHm = depHm;
    }

    public String getArrHm() {
        return arrHm;
    }

    public void setArrHm(String arrHm) {
        this.arrHm = arrHm;
    }

    public String getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(String weekDay) {
        this.weekDay = weekDay;
    }
}
