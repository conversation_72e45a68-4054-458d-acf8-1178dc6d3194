package com.juneyaoair.thirdentity.request.coupon;


import com.juneyaoair.baseclass.request.disney.DisneyProductInfo;
import com.juneyaoair.thirdentity.request.booking.PtPassengerInfo;
import com.juneyaoair.thirdentity.request.booking.PtSegmentInfo;

import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-6.
 */
public class CouponQueryRequest {
    private String Version; // 接口版本号10
    private String ChannelCode; // 渠道用户号B2C,CC等
    private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号
    private String FfpId;
    private String FfpCardNo;
    private String CouponState;  //R-已领取,U-已使用, E-已过期
    private String CouponSource; // 优惠券分类(null：机票 HO:机票 HOCAR：接送机 ALL：全部 多个以,分割)
    private int Sale;   //0：全部 1：仅非可售 2：仅可售
    /**
     * 是否包含有效但不可用的优惠券
     */
    private boolean IsContainUnavailable;
    /**
     * 航距
     */
    private int AirlineMileage;

    private List<PtSegmentInfo> SegmentInfoList;
    /**
     * 旅客信息 为空表示暂未知
     */
    private List<PtPassengerInfo> PassengerInfoList;


    /**
     * 使用场景
     * <p>
     * null:未知
     * <p>
     * 1:一单多券
     */
    private String Scene;

    private DisneyProductInfo DisneyProductInfo;


    private List<String>  ProductTypeLimitList;

    public List<String> getProductTypeLimitList() {
        return ProductTypeLimitList;
    }

    public void setProductTypeLimitList(List<String> productTypeLimitList) {
        ProductTypeLimitList = productTypeLimitList;
    }

    public CouponQueryRequest() {
    }

    public CouponQueryRequest(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }

    public CouponQueryRequest(String version, String channelCode, String userNo, String ffpId, String ffpCardNo, String couponState, List<PtSegmentInfo> segmentInfoList) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        FfpId = ffpId;
        FfpCardNo = ffpCardNo;
        CouponState = couponState;
        SegmentInfoList = segmentInfoList;
    }


    public com.juneyaoair.baseclass.request.disney.DisneyProductInfo getDisneyProductInfo() {
        return DisneyProductInfo;
    }

    public void setDisneyProductInfo(com.juneyaoair.baseclass.request.disney.DisneyProductInfo disneyProductInfo) {
        DisneyProductInfo = disneyProductInfo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getFfpId() {
        return FfpId;
    }

    public void setFfpId(String ffpId) {
        FfpId = ffpId;
    }

    public String getFfpCardNo() {
        return FfpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        FfpCardNo = ffpCardNo;
    }

    public String getCouponState() {
        return CouponState;
    }

    public void setCouponState(String couponState) {
        CouponState = couponState;
    }

    public List<PtSegmentInfo> getSegmentInfoList() {
        return SegmentInfoList;
    }

    public void setSegmentInfoList(List<PtSegmentInfo> segmentInfoList) {
        SegmentInfoList = segmentInfoList;
    }

    public String getCouponSource() {
        return CouponSource;
    }

    public void setCouponSource(String couponSource) {
        CouponSource = couponSource;
    }

    public int getSale() {
        return Sale;
    }

    public void setSale(int sale) {
        Sale = sale;
    }

    public List<PtPassengerInfo> getPassengerInfoList() {
        return PassengerInfoList;
    }

    public void setPassengerInfoList(List<PtPassengerInfo> passengerInfoList) {
        PassengerInfoList = passengerInfoList;
    }

    public int getAirlineMileage() {
        return AirlineMileage;
    }

    public void setAirlineMileage(int airlineMileage) {
        AirlineMileage = airlineMileage;
    }

    public boolean isContainUnavailable() {
        return IsContainUnavailable;
    }

    public void setContainUnavailable(boolean containUnavailable) {
        IsContainUnavailable = containUnavailable;
    }


    public String getScene() {
        return Scene;
    }

    public void setScene(String scene) {
        Scene = scene;
    }


}
