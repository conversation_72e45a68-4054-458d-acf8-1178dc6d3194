package com.juneyaoair.thirdentity.av.request;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.request.av.Segment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 航班运价查询请求参数
 * @date 2018/12/5  17:28.
 */
@Data
public class PtQueryFlightFareRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String RandCode;
    private String RouteType;
    private String CurrencyCode;
    private String LangCode;
    private String FareSource;  //运价来源（HO,Searchone）
    //是否提前资格
    private String IsAdvanced;
    private List<Segment> SegCondList;
    //是否查询缓存，默认查询。不通过缓存，参数N
    private String ReadRedis;
    //中转城市设置
    private List<String> TransferCitys;
    @SerializedName("QueryType")
    private String queryType;// 查询类型，1为机场查询，0为城市查询
    @SerializedName("QueryXCabin")
    private String queryXCabin;// 是否查询X舱  Y/N
    @SerializedName("TransferTimes")
    private Integer transferTimes; // 最大中转次数 为空时默认为1

    // 2021-04-28 运价查询增加查询乘客类型
    private List<String> PassengerType;
    //2021-05-10 查询品牌运价使用 searchOne 传true
    private boolean VirtualType;
    //查询类型 Y-改期
    private String SearchUse;
    /**
     * 特殊运价查询  STU-留学生运价
     */
    private String SpecialFareType;
    /**
     * 主题卡类型
     */
    private List<String> ThemeCardList;

    /**
     * 出票日期 yyyy-MM-dd
     */
    private String TicketOutDateTime;

    /**
     * 是否迪士尼改期运价
     */
    private Boolean IsDisneyChange;

    /**
     * @ApiModelProperty(value="是否奖励飞运价查询")
     */

    private boolean IsRewardFly;


    @ApiModelProperty(value = "渠道客户编号")
    private String ChannelCustomerNo;

    private String OriginalReqIP;

    public PtQueryFlightFareRequest(String version,String channelCode,String userNo,String routeType,String currencyCode,String langCode,List<Segment> segCondList,String readRedis){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
        this.RouteType = routeType;
        this.CurrencyCode = currencyCode;
        this.LangCode = langCode;
        this.SegCondList = segCondList;
        this.ReadRedis = readRedis;
    }
}
