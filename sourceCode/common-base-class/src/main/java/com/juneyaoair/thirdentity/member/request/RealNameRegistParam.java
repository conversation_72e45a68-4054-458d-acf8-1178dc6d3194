package com.juneyaoair.thirdentity.member.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/5/4 9:56
 */
@Data
public class RealNameRegistParam {

    @ApiModelProperty(value = "支付宝用户id")
    private String AlipayUserId;

    @ApiModelProperty(value = "身份证号码")
    private String IdCard;

    @ApiModelProperty(value = "是否匹配实名注册(Y:是;N:否)")
    private String IsRegister;

    @ApiModelProperty(value = "名（支持中英文或拼音）")
    private String FirstName;

    @ApiModelProperty(value = "姓（支持中英文或拼音）")
    private String LastName;

    @ApiModelProperty(value = "手机号")
    private String Mobile;
}
